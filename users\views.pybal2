from django.shortcuts import render, redirect
from django.contrib.auth import login, logout as auth_logout
from django.contrib.auth.forms import AuthenticationForm
from .forms import CustomUserCreationForm
from teams.models import Team, Player, PlayerStatsPhysical, PlayerStatsAbility, PlayerStatsAttributes
import random
from django.contrib.auth.decorators import login_required
from faker import Faker

def home_view(request):
    form = AuthenticationForm()
    return render(request, 'home.html', {'form': form})

fake = Faker()

def register(request):
    if request.method == 'POST':
        form = CustomUserCreationForm(request.POST)
        if form.is_valid():
            user = form.save()
            team_name = form.cleaned_data.get('team_name')
            print(f"Hit {team_name}") 
            # Check if the user already has a team
            team, created = Team.objects.get_or_create(user=user, defaults={'name': team_name})
        print(f"Hit before created")    
        if created:
            print(f"Hit if created")
            # Define weighted stats ranges for different AFL positions
            position_weights = {
                "full_forward": {
                    "height": (7, 9),
                    "agility": (4, 6),
                    "speed": (4, 6),
                    "strength": (6, 8),
                    "stamina": (5, 7),
                    "kicking": (6, 9),
                    "goal_kicking": (8, 10),
                    "handball": (3, 5),
                    "tackling": (3, 5),
                    "mental": (5, 7),
                    "tactical": (5, 7),
                    "versatility": (3, 5),
                    "consistency": (6, 8),
                    "marking": (8, 10),
                },
                "forward_pocket_1": {  # Same weights for both forward pockets
                    "height": (5, 7),
                    "agility": (6, 8),
                    "speed": (5, 8),
                    "strength": (5, 7),
                    "stamina": (5, 7),
                    "kicking": (5, 8),
                    "goal_kicking": (7, 9),
                    "handball": (5, 7),
                    "tackling": (5, 7),
                    "mental": (5, 7),
                    "tactical": (4, 6),
                    "versatility": (5, 7),
                    "consistency": (5, 7),
                    "marking": (6, 8),
                },
                "forward_pocket_2": {  # Same weights for both forward pockets
                    "height": (5, 7),
                    "agility": (6, 8),
                    "speed": (5, 8),
                    "strength": (5, 7),
                    "stamina": (5, 7),
                    "kicking": (5, 8),
                    "goal_kicking": (7, 9),
                    "handball": (5, 7),
                    "tackling": (5, 7),
                    "mental": (5, 7),
                    "tactical": (4, 6),
                    "versatility": (5, 7),
                    "consistency": (5, 7),
                    "marking": (6, 8),
                },
                "center_half_forward": {
                    "height": (7, 9),
                    "agility": (4, 6),
                    "speed": (5, 7),
                    "strength": (7, 9),
                    "stamina": (6, 8),
                    "kicking": (6, 8),
                    "goal_kicking": (7, 9),
                    "handball": (4, 6),
                    "tackling": (4, 6),
                    "mental": (6, 8),
                    "tactical": (5, 7),
                    "versatility": (4, 6),
                    "consistency": (6, 8),
                    "marking": (7, 9),
                },
                "half_forward_flank_1": {  # Same weights for both half-forward flanks
                    "height": (6, 8),
                    "agility": (5, 8),
                    "speed": (6, 8),
                    "strength": (5, 7),
                    "stamina": (6, 8),
                    "kicking": (6, 8),
                    "goal_kicking": (6, 8),
                    "handball": (5, 7),
                    "tackling": (5, 7),
                    "mental": (5, 7),
                    "tactical": (6, 8),
                    "versatility": (6, 8),
                    "consistency": (6, 8),
                    "marking": (6, 8),
                },
                "half_forward_flank_2": {  # Same weights for both half-forward flanks
                    "height": (6, 8),
                    "agility": (5, 8),
                    "speed": (6, 8),
                    "strength": (5, 7),
                    "stamina": (6, 8),
                    "kicking": (6, 8),
                    "goal_kicking": (6, 8),
                    "handball": (5, 7),
                    "tackling": (5, 7),
                    "mental": (5, 7),
                    "tactical": (6, 8),
                    "versatility": (6, 8),
                    "consistency": (6, 8),
                    "marking": (6, 8),
                },
                "ruck": {
                    "height": (8, 10),
                    "agility": (3, 5),
                    "speed": (4, 6),
                    "strength": (8, 10),
                    "stamina": (6, 8),
                    "kicking": (5, 7),
                    "goal_kicking": (5, 6),
                    "handball": (6, 8),
                    "tackling": (5, 7),
                    "mental": (6, 8),
                    "tactical": (5, 7),
                    "versatility": (3, 5),
                    "consistency": (6, 8),
                    "marking": (7, 9),
                },
                    "winger": {
                    "height": (5, 8),
                    "agility": (7, 10),
                    "speed": (8, 10),
                    "strength": (4, 6),
                    "stamina": (8, 10),
                    "kicking": (6, 8),
                    "goal_kicking": (3, 6),
                    "handball": (6, 9),
                    "tackling": (5, 7),
                    "mental": (6, 8),
                    "tactical": (7, 9),
                    "versatility": (6, 8),
                    "consistency": (6, 8),
                    "marking": (4, 7) 
                },
                "rover": {
                    "height": (5, 7),  
                    "agility": (8, 10),  
                    "speed": (7, 9), 
                    "strength": (6, 8),  
                    "stamina": (7, 9),  
                    "kicking": (7, 9), 
                    "goal_kicking": (5, 7),  
                    "handball": (8, 10),  
                    "tackling": (7, 9),  
                    "mental": (7, 9),  
                    "tactical": (7, 9),  
                    "versatility": (7, 9),
                    "consistency": (7, 9),
                    "marking": (4, 6)  
                },
                "ruck_rover": {
                    "height": (6, 8),  # Slightly taller than rover
                    "agility": (7, 9),  # High agility
                    "speed": (6, 8),  # Average speed
                    "strength": (7, 9),  # Strong
                    "stamina": (8, 10),  # High stamina
                    "kicking": (6, 8),  # Good kicking ability
                    "goal_kicking": (5, 7),  # Occasional goal kicker
                    "handball": (7, 9),  # Strong handball skills
                    "tackling": (7, 9),  # Strong tackling
                    "mental": (7, 9),  # High mental strength
                    "tactical": (8, 10),  # Excellent tactical awareness
                    "versatility": (7, 9),
                    "consistency": (7, 9),
                    "marking": (5, 7)  # Decent marking
                },
                "center": {
                    "height": (6, 9),  # Taller than average
                    "agility": (6, 8),  # Good agility
                    "speed": (6, 8),  # Average speed
                    "strength": (6, 8),  # Good strength
                    "stamina": (8, 10),  # High stamina for full-field coverage
                    "kicking": (7, 9),  # Good kicking ability
                    "goal_kicking": (4, 6),  # Occasionally scores
                    "handball": (7, 9),  # Good handball skills
                    "tackling": (6, 8),  # Strong tackling
                    "mental": (8, 10),  # Excellent mental strength
                    "tactical": (8, 10),  # Very strong tactical awareness
                    "versatility": (7, 9),
                    "consistency": (7, 9),
                    "marking": (6, 8)  # Good marking ability
                },
                "full_back": {
                    "height": (7, 9),  # Tall to contest high balls
                    "agility": (5, 7),  # Moderate agility
                    "speed": (5, 7),  # Moderate speed
                    "strength": (8, 10),  # High strength for physical contests
                    "stamina": (6, 8),  # Good stamina
                    "kicking": (5, 7),  # Decent kicking ability
                    "goal_kicking": (1, 3),  # Rarely scores
                    "handball": (5, 7),  # Average handball skills
                    "tackling": (8, 10),  # Strong tackling
                    "mental": (7, 9),  # Good mental strength
                    "tactical": (7, 9),  # Strong tactical awareness
                    "versatility": (5, 7),
                    "consistency": (7, 9),
                    "marking": (7, 10)  # Excellent marking ability
                },
                "back_pocket": {
                    "height": (6, 8),  # Average height
                    "agility": (6, 8),  # Good agility to match smaller forwards
                    "speed": (6, 8),  # Decent speed
                    "strength": (6, 8),  # Strong to contest
                    "stamina": (6, 8),  # Good stamina
                    "kicking": (5, 7),  # Average kicking ability
                    "goal_kicking": (1, 3),  # Rarely scores
                    "handball": (5, 7),  # Average handball skills
                    "tackling": (8, 10),  # Strong tackling ability
                    "mental": (7, 9),  # Good mental strength
                    "tactical": (6, 8),  # Strong tactical awareness
                    "versatility": (6, 8),
                    "consistency": (7, 9),
                    "marking": (6, 8)  # Good marking ability
                },
                "center_half_back": {
                    "height": (7, 9),  # Tall build for aerial contests
                    "agility": (5, 7),  # Moderate agility
                    "speed": (5, 7),  # Moderate speed
                    "strength": (8, 10),  # High strength for physical contests
                    "stamina": (7, 9),  # Good stamina for full-field coverage
                    "kicking": (6, 8),  # Decent kicking ability
                    "goal_kicking": (1, 3),  # Rarely scores
                    "handball": (6, 8),  # Good handball skills
                    "tackling": (8, 10),  # Strong tackling ability
                    "mental": (8, 10),  # High mental strength
                    "tactical": (8, 10),  # Very strong tactical awareness
                    "versatility": (7, 9),
                    "consistency": (7, 9),
                    "marking": (8, 10)  # Excellent marking ability
                },
                "half_back_flank": {
                    "height": (6, 8),  # Average height
                    "agility": (6, 8),  # Good agility
                    "speed": (7, 9),  # Quick to support offensive play
                    "strength": (6, 8),  # Good strength
                    "stamina": (7, 9),  # High stamina for transition play
                    "kicking": (7, 9),  # Strong kicking ability for rebounding
                    "goal_kicking": (2, 5),  # Occasionally scores
                    "handball": (6, 8),  # Good handball skills
                    "tackling": (7, 9),  # Strong tackling
                    "mental": (7, 9),  # High mental strength
                    "tactical": (7, 9),  # Good tactical awareness
                    "versatility": (7, 9),
                    "consistency": (7, 9),
                    "marking": (6, 8)  # Good marking ability
                }
            }

        def create_player(team, weights=None):
            def weighted_stat(min_value, max_value):
                return random.randint(min_value, max_value)
            print(f"Hit create player")
            if weights:
                # Generate stats based on position weights
                physical_stats = PlayerStatsPhysical.objects.create(
                    height=weighted_stat(*weights["height"]),
                    agility=weighted_stat(*weights["agility"]),
                    speed=weighted_stat(*weights["speed"]),
                    strength=weighted_stat(*weights["strength"]),
                    stamina=weighted_stat(*weights["stamina"]),
                    age=random.randint(17, 32)
                )
                ability_stats = PlayerStatsAbility.objects.create(
                    kicking=weighted_stat(*weights["kicking"]),
                    goal_kicking=weighted_stat(*weights["goal_kicking"]),
                    handball=weighted_stat(*weights["handball"]),
                    tackling=weighted_stat(*weights["tackling"]),
                    mental=weighted_stat(*weights["mental"]),
                    tactical=weighted_stat(*weights["tactical"]),
                    versatility=weighted_stat(*weights["versatility"]),
                    consistency=weighted_stat(*weights["consistency"]),
                    marking=weighted_stat(*weights["marking"])
                )
            else:
                # Randomized stats for other players
                physical_stats = PlayerStatsPhysical.objects.create(
                    height=random.randint(1, 9),
                    agility=random.randint(1, 9),
                    speed=random.randint(1, 9),
                    strength=random.randint(1, 9),
                    stamina=random.randint(1, 9),
                    age=random.randint(17, 32)
                )
                ability_stats = PlayerStatsAbility.objects.create(
                    kicking=random.randint(1, 9),
                    goal_kicking=random.randint(1, 9),
                    handball=random.randint(1, 9),
                    tackling=random.randint(1, 9),
                    mental=random.randint(1, 9),
                    tactical=random.randint(1, 9),
                    versatility=random.randint(1, 9),
                    consistency=random.randint(1, 9),
                    marking=random.randint(1, 9)
                )

            # Wage calculation based on stat totals
            print(f"Hit end of else")
            total_stats = sum(vars(physical_stats).values()) + sum(vars(ability_stats).values())
            max_stats = 20 * 10  # Max possible points if each stat is 20
            wage = 50000 + ((total_stats / max_stats) * (5000000 - 50000))

            attributes_stats = PlayerStatsAttributes.objects.create(
                experience=random.randint(1, 9),
                leadership=random.randint(1, 9),
                loyalty=random.randint(1, 20),
                wage=wage
            )

            # Create player with stats
            Player.objects.create(
                team=team,
                physical_stats=physical_stats,
                ability_stats=ability_stats,
                attributes_stats=attributes_stats,
                name=fake.name()
            )

        # Create 18 players based on position weights
        for position, weights in position_weights.items():
            create_player(team, weights)

        # Create 12 additional random players
        for _ in range(12):
            create_player(team)           
               
        login(request, user)  # Log in the user
        return redirect('team', team_id=team.id)
    else:
        form = CustomUserCreationForm()
    return render(request, 'registration/register.html', {'form': form})
    

def login_view(request):
    if request.method == 'POST':
        form = AuthenticationForm(request, data=request.POST)
        if form.is_valid():
            user = form.get_user()
            login(request, user)
            return redirect('dashboard')
    else:
        form = AuthenticationForm()
    return render(request, 'home.html', {'form': form})
    
def logout_view(request):
    auth_logout(request)
    return redirect('home')
    
