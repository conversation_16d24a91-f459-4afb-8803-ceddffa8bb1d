document.addEventListener('DOMContentLoaded', function () {
  console.log('DOM fully loaded and parsed');  // Debug

  const team1Select = document.getElementById('team1');
  const team1RuckSelect = document.getElementById('team1_ruck');
  const team2Select = document.getElementById('team2');
  const team2RuckSelect = document.getElementById('team2_ruck');

  if (team1Select) {
    console.log('Team 1 dropdown found');  // Debug
    team1Select.addEventListener('change', function () {
      const teamId = this.value;
      console.log(`Team 1 selected: ${teamId}`);  // Debug
      fetch(`/admin/ajax/load-players/?team_id=${teamId}`)
        .then(response => response.json())
        .then(data => {
          console.log(`Players for team 1: ${JSON.stringify(data)}`);  // Debug
          team1RuckSelect.innerHTML = '<option value="">Select a player</option>';
          data.forEach(player => {
            const option = document.createElement('option');
            option.value = player.id;
            option.textContent = player.name;
            team1RuckSelect.appendChild(option);
          });
        }).catch(error => console.error('Error fetching players:', error));  // Debug
    });
  } else {
    console.log('Team 1 dropdown not found');  // Debug
  }

  if (team2Select) {
    console.log('Team 2 dropdown found');  // Debug
    team2Select.addEventListener('change', function () {
      const teamId = this.value;
      console.log(`Team 2 selected: ${teamId}`);  // Debug
      fetch(`/admin/ajax/load-players/?team_id=${teamId}`)
        .then(response => response.json())
        .then(data => {
          console.log(`Players for team 2: ${JSON.stringify(data)}`);  // Debug
          team2RuckSelect.innerHTML = '<option value="">Select a player</option>';
          data.forEach(player => {
            const option = document.createElement('option');
            option.value = player.id;
            option.textContent = player.name;
            team2RuckSelect.appendChild(option);
          });
        }).catch(error => console.error('Error fetching players:', error));  // Debug
    });
  } else {
    console.log('Team 2 dropdown not found');  // Debug
  }
});
