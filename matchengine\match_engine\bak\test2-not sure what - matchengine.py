#from ladder.utils import record_match_result
#from teams.models import Player

import sys, random, math, time, traceback, asyncio
from asgiref.sync import async_to_sync
sys.setrecursionlimit(10000)

"""
class Match:
    #Manages the overall match state and flow
    def __init__(self, home_team, away_team):
        self.home_team = home_team
        self.away_team = away_team
        self.quarter = 1
        self.time = 0
        self.state = MatchState()  # Current match state/context
        self.events = EventManager()
        self.stats = StatsManager()
"""

class MatchEngine:
    def __init__(self, 
                 home_team, away_team,
                 home_team_players, away_team_players,
                 home_team_tactics, away_team_tactics,
                 match_group_name, channel_layer):
        
        # Initialize ground and position management
        weather_conditions='light_rain'
        self.weather_system = WeatherSystem(weather_conditions)
        self.home_team = home_team
        self.away_team = away_team
        self.home_team_players = home_team_players
        self.away_team_players = away_team_players
        #print(f"Home team tactics: {home_team_tactics}")
        self.home_team_tactics = home_team_tactics
        self.away_team_tactics = away_team_tactics

        #print(home_team_players)
        ground_length=160
        ground_width=130
        self.quarter = 1
        self.time = 0       
        self.ground = Ground(ground_length, ground_width)
        self.events = EventManager(match_group_name, channel_layer)
        self.stats = StatsManager()
        self.position_manager = PositionManager(self.ground)
        self.movement_patternClass = MovementPattern(self.ground, self.position_manager)          
        self.action_engine = ActionEngine(self.ground, self.position_manager, self.movement_patternClass)
        self.movement_engine = MovementEngine(self.ground, self.position_manager)
        self.scoring_engine = ScoringEngine(self.ground)

      

        #self.movement_pattern = MovementPattern(self.ground)

        # Initialize action engine for both teams
        #TeamMovementCoordinator.setup_team(home_team, home_team_players)
        #TeamMovementCoordinator.setup_team(away_team, away_team_players)

        self.home_coordinator = TeamMovementCoordinator(self.home_team, self.ground, self.position_manager, 'home')
        self.away_coordinator = TeamMovementCoordinator(self.away_team, self.ground, self.position_manager, 'away')
        
        self.state = GameState(home_team, home_team_players, away_team, away_team_players, self.ground, weather_conditions)


        self.home_coordinator.setup_team(home_team, home_team_players, self.home_team_tactics)
        self.away_coordinator.setup_team(away_team,away_team_players, self.away_team_tactics)
        self.movement_engine.base_positions = {
            home_team.name: self.home_coordinator.base_positions[home_team.name],
            away_team.name: self.away_coordinator.base_positions[away_team.name]
        }
 
        self.position_manager.game_state = self.state

         # Debug prints
        #print("State type:", type(self.state))
        #print("Position manager game_state type:", type(self.position_manager.game_state))
        #print("Are they the same object?", self.state is self.position_manager.game_state)

    """        
        # Initialize teams
        self.home_team = Team(
            name=home_team,
            players=self.initialize_players(home_team_players, 'home'),
            tactics=home_team_tactics
        )
        self.away_team = Team(
            name=away_team,
            players=self.initialize_players(away_team_players, 'away'),
            tactics=away_team_tactics
        )
        
        # Match state and management

    
    def initialize_players(self, player_data, team_side):
        #Initialize players with positions based on team side
        players = []
        for data in player_data:
            player = Player(
                name=data['name'],
                position=data['position'],
                attributes=data['attributes'],
                team_side=team_side
            )
            # Set initial position on ground
            initial_pos = self.ground.get_position_coordinates(data['position'], team_side)
            self.position_manager.update_player_position(player, initial_pos)
            players.append(player)
        return players
    """
    async def simulate_match(self):
        """Main match simulation loop"""
        for quarter in range(1, 5):
            self.quarter = quarter
            self.events.create_event('quarter_start', quarter=quarter)
            
            async for event in self.simulate_quarter():
                    yield event
            
        # Update and broadcast stats
        quarter_stats = self.stats.get_quarter_stats(quarter)
        self.events.create_event('quarter_end', quarter=quarter, stats=quarter_stats)

    async def simulate_quarter(self):
        """Simulate a quarter of play"""
        quarter_time = 20 * 60  # 20 minutes in seconds

            # Start quarter with center bounce
        self.state.ball_position = (self.ground.rows // 2, self.ground.cols // 2)  # Set ball at center    
        self.state.play_phase = 'Centre_Bounce'  # Special case for center bounce
        async for event in self.simulate_play():  # This will handle the center bounce setup
            yield event

        while self.time < quarter_time:
            # Update player positions
            self.update_player_positions()
            
            # Get current game situation
            situation = self._determine_game_situation()
            self.state.play_phase = situation
            
            # Handle the current situation
            if situation == 'contest':
                    # Use the contest handling system
                    async for event in self._handle_contest():
                        yield event
            else:  # open_play or set_shot
                async for event in self.simulate_play():
                    yield event
            
            # Track movements and update stats
            self.movement_engine.record_movements(self.time, self.quarter)
            self.stats.update_stats()
            
            self.time += 1

    def _determine_game_situation(self):
        """Determine the current game situation"""
        if not self.state.ball_carrier:
            return 'contest'
        elif self.state.play_phase == 'set_shot':
            return 'set_shot'
        else:
            return 'open_play'
        
    """
    def _update_match_state(self, action_result):
        #Update match state based on action result
        if not action_result:
            return
            
        # Update ball carrier and play phase
        if 'new_state' in action_result:
            self.state.update_state(**action_result['new_state'])
            
        # Handle specific outcomes
        if action_result['type'] == 'kick':
            if action_result['outcome'] == 'success':
                # Update stats
                action_result['player'].update_stats('kicks')
                # Check if in scoring position
                if self.scoring_engine._is_scoring_position(action_result['target']):
                    self.state.play_phase = 'set_shot'
            else:
                # Failed kick leads to contest
                self.state.play_phase = 'contest'
                self.state.ball_carrier = None
                
        elif action_result['type'] == 'handball':
            action_result['player'].update_stats('handballs')
            
        # Update momentum
        self._update_momentum(action_result)
    """

    def _update_match_state(self, action_result):
        """Update match state based on action result"""
        # Example game situations:
        
        # Situation 1: Kick results in marking contest
        if action_result['type'] == 'kick' and action_result['outcome'] == 'contest':
            self.state.update_state(
                play_phase='contest',
                ball_position=action_result['contest_position'],
                ball_carrier=None
            )
            
        # Situation 2: Clean mark taken
        elif action_result['type'] == 'mark' and action_result['outcome'] == 'success':
            self.state.update_state(
                play_phase='set_shot' if self._is_scoring_position(action_result['position']) else 'open_play',
                ball_carrier=action_result['player'],
                ball_position=action_result['position']
            )
            
        # Situation 3: Ball up / Bounce down
        elif action_result['type'] == 'ball_up':
            self.state.update_state(
                play_phase='contest',
                ball_position=action_result['position'],
                ball_carrier=None
            )
            
        # Situation 4: Tackle breaks into open play
        elif action_result['type'] == 'tackle' and action_result['outcome'] == 'ball_spill':
            self.state.update_state(
                play_phase='open_play',
                ball_carrier=None,
                ball_position=action_result['ball_position']
            )

    """
    def _update_momentum(self, action_result):
        #Update game momentum based on action outcome
        if action_result['outcome'] == 'success':
            # Successful actions boost momentum for the team
            momentum_change = 10
            if action_result['player'].team_side == 'home':
                self.state.momentum = min(100, self.state.momentum + momentum_change)
            else:
                self.state.momentum = max(-100, self.state.momentum - momentum_change)
    """
    
    """
    Move to class ScoringEngine:
    def _is_scoring_position(self, player):
        #Check if player is in scoring position
        forward_50 = self.ground.zones['forward']
        player_pos = player.current_position
        
        # Check if player is in forward 50
        if player_pos in forward_50:
            # Calculate angle to goal
            angle = self._calculate_goal_angle(player_pos)
            return angle < 45  # 45 degree angle or less is considered scoring position
        return False    
    """
    async def _handle_contest(self):
        """Handle contested situations"""
        nearby_players = self.position_manager.get_nearby_players(self.state.ball_position, radius=2)
        
        # Group players by team
        home_players = [p for p in nearby_players if p.team_side == 'home']
        away_players = [p for p in nearby_players if p.team_side == 'away']
        
        # Calculate contest advantage based on numbers, position, and attributes
        home_advantage = self._calculate_contest_advantage(home_players)
        away_advantage = self._calculate_contest_advantage(away_players)
        
        # Factor in team tactics
        home_advantage *= self._get_tactical_modifier(self.home_team.tactics, 'contest')
        away_advantage *= self._get_tactical_modifier(self.away_team.tactics, 'contest')
        
        # Determine winner
        if home_advantage > away_advantage:
            winner = self._select_contest_winner(home_players)
            yield self.events.create_event('contest_won', 
                player=winner.name,
                team='home',
                type='ground_ball'
            )
            self.state.update_state(ball_carrier=winner, play_phase='open_play')
        else:
            winner = self._select_contest_winner(away_players)
            yield self.events.create_event('contest_won',
                player=winner.name,
                team='away',
                type='ground_ball'
            )
            self.state.update_state(ball_carrier=winner, play_phase='open_play')

    def _calculate_contest_winner(self, home_players, away_players):
        """Calculate winner of contested situation"""
        home_advantage = self._calculate_contest_advantage(home_players)
        away_advantage = self._calculate_contest_advantage(away_players)
        
        # Apply tactical modifiers
        home_advantage *= self._get_tactical_modifier(self.home_team.tactics, 'contest')
        away_advantage *= self._get_tactical_modifier(self.away_team.tactics, 'contest')
        
        # Random factor to add unpredictability
        home_advantage *= random.uniform(0.8, 1.2)
        away_advantage *= random.uniform(0.8, 1.2)
        
        return 'home' if home_advantage > away_advantage else 'away'

    def _calculate_contest_advantage(self, players):
        """Calculate contest advantage based on player attributes and numbers"""
        if not players:
            return 0
            
        advantage = 0
        for player in players:
            # Base contest rating from player attributes
            contest_rating = (
                player.attributes['strength'] * 0.3 +
                player.attributes['agility'] * 0.3 +
                player.attributes['tackling'] * 0.2 +
                player.attributes['awareness'] * 0.2
            )
            
            # Adjust for fatigue
            contest_rating *= (100 - player.fatigue) / 100
            advantage += contest_rating
            
        return advantage

    def _get_tactical_modifier(self, tactics, situation):
        """Get tactical modifier based on team tactics"""
        modifiers = {
            'attacking': {
                'contest': 0.9,  # Less emphasis on contests
                'transition': 1.2,  # Faster transition
                'pressure': 0.8  # Less defensive pressure
            },
            'defensive': {
                'contest': 1.1,  # More emphasis on contests
                'transition': 0.8,  # Slower, more controlled transition
                'pressure': 1.2  # Higher defensive pressure
            },
            'balanced': {
                'contest': 1.0,
                'transition': 1.0,
                'pressure': 1.0
            }
        }
        return modifiers.get(tactics.style, {}).get(situation, 1.0)
    
    def _transition_state(self, current_state, action_result):
        """Handle state transitions based on action results"""
        if action_result['type'] == 'kick':
            if action_result['outcome'] == 'success':
                if self.scoring_engine._is_scoring_position(action_result['target']):
                    return 'set_shot'
                return 'open_play'
            return 'contest'
            
        elif action_result['type'] == 'handball':
            if action_result['outcome'] == 'success':
                return 'open_play'
            return 'contest'
            
        elif action_result['type'] == 'tackle':
            if action_result['outcome'] == 'success':
                return 'holding_ball'
            return 'open_play'
            
        return current_state

    def update_player_positions(self):
        """Update player positions based on current play phase"""
        # Handle home team players
        for position, player in self.home_team_players.items():
            new_position = self.movement_engine.calculate_new_position(
                player,
                position,  # Pass the position to inform movement
                self.state.ball_position,
                self.state.play_phase,
                'home',
                self.home_team
            )
            self.position_manager.update_player_position(player, new_position)
        
        # Handle away team players
        for position, player in self.away_team_players.items():
            new_position = self.movement_engine.calculate_new_position(
                player,
                position,  # Pass the position to inform movement
                self.state.ball_position,
                self.state.play_phase,
                'away',
                self.away_team
            )
            self.position_manager.update_player_position(player, new_position)

    async def simulate_play(self):
        """Main simulation loop"""
        # Update game state with time delta
        delta_time = self._get_time_delta()
        self.state.update(delta_time)
        
        # Get coordinated movements (existing code)
        
        home_movements = self.home_coordinator.coordinate_movements(self.state)
        away_movements = self.away_coordinator.coordinate_movements(self.state)
        print(f"Home movements: {home_movements}")
        print(f"Away movements: {away_movements}")
        
        # Apply coordinated movements to individual player actions
        for team in [self.home_team, self.away_team]:
            coordinator = (self.home_coordinator 
                         if team == self.home_team 
                         else self.away_coordinator)
            
            # Get team-level instructions from coordinator
            team_instructions = coordinator.get_team_instructions(self.state)
            #print(f"Team instructions: {team_instructions} for team {team.name}")
            for position, player in (self.home_team_players if team == self.home_team 
                                   else self.away_team_players).items():
                # Update player state
                player_state = self.state.player_states[player.id]
                
                # Get coordinated movement for player
                coord_movement = (home_movements if team == self.home_team 
                                else away_movements).get(player.id)
                print(f"Coordination movement: {coord_movement} for player {player.name}")

                # Apply team coordination to player's movement pattern

                for position, player in (self.home_team_players if team == self.home_team 
                                    else self.away_team_players).items():
                    # Get or create movement pattern for player
                    if not hasattr(self, 'movement_patterns'):
                        self.movement_patterns = {}
                        
                    if player.id not in self.movement_patterns:
                        self.movement_patterns[player.id] = self.movement_patternClass(self.ground, self.position_manager, player)
                    self.movement_patterns[player.id].current_phase = 'Centre_Bounce' #Temporary

                    movement_pattern = self.movement_patterns[player.id]
                    print(f"Movement pattern should return none? {movement_pattern}")
                    # Calculate new pattern based on game context
                    context = {
                        'phase': self.state.play_phase,
                        'ball_position': self.state.ball_position,
                        'ball_carrier': self.state.ball_carrier,
                        'player_position': position,
                        'team_side': 'home' if team == self.home_team else 'away'
                        #'field_position': self.state.field_position
                    }
                    
                    # Calculate movement using pattern
                    new_movement = movement_pattern.calculate_movement(context)  # This internally calls should_start_new_pattern and start_new_pattern if needed
                    print(f"New movement {new_movement}")
                    # Apply team coordination
                    if new_movement:
                        movement_pattern.apply_team_coordination(
                            coord_movement,
                            team_instructions,
                            coordinator.player_roles[player.id]
                        )
                
                # Process player action with coordination context
                action_result = self.action_engine.process_player_action(
                    player,
                    self.state,
                    coordination_context={
                        'movement': new_movement,
                        'team_instructions': team_instructions,
                        'role': coordinator.player_roles[player.id]
                    }
                )
                
                if action_result:
                    # Update player state with action
                    player_state.add_action(action_result)
                    
                    # Update match state
                    self._update_match_state(action_result)
                    
                    # Create and yield event
                    yield self.events.create_event(
                        action_result['type'],
                        player=player.name,
                        team=player.team_side,
                        outcome=action_result['outcome'],
                        quarter=self.state.quarter,
                        time=self.state.time_remaining,
                        position=player.current_position,
                        start_position=action_result.get('start_position'),
                        end_position=action_result.get('end_position'),
                        distance=action_result.get('distance'),
                        kick_type=action_result.get('kick_type'),
                        pressure_level=action_result.get('pressure_level')
                    )
                
                # Check for interrupts
                self._check_for_interrupts(self.state)
                
            # Check for phase transitions after each team's actions
            self._check_phase_transitions()
    
    
    def _check_for_interrupts(self, state):
        """Check for and register various types of interrupts"""
        # Check for tackles
        self._check_tackle_situations(state)
        
        # Check for pressure
        self._check_pressure_situations(state)
        
        # Check for umpire decisions
        self._check_umpire_decisions(state)
        
        # Check for collisions
        self._check_collisions(state)
        
    def _check_tackle_situations(self, state):
        """Check for and register tackle interrupts"""
        if state.ball_carrier:
            nearby_opponents = self.ground.get_players_in_radius(
                state.ball_carrier.current_position,
                2  # 2 meter tackle range
            )
            
            for opponent in nearby_opponents:
                if opponent.team_side != state.ball_carrier.team_side:
                    if self._is_valid_tackle_attempt(opponent, state.ball_carrier):
                        self.action_engine.register_interrupt(
                            state.ball_carrier.id,
                            'tackle',
                            opponent,
                            priority='high'
                        )
                        
    def _check_pressure_situations(self, state):
        """Check for and register pressure interrupts"""
        for player in self.get_all_players():
            if player == state.ball_carrier:
                pressure_level = self._calculate_pressure_level(player, state)
                if pressure_level > 0.7:  # High pressure threshold
                    self.action_engine.register_interrupt(
                        player.id,
                        'pressure',
                        'opposition_pressure',
                        priority='medium'
                    )
                    
    def _check_umpire_decisions(self, state):
        """Check for and register whistle interrupts"""
        if state.umpire_signal:  # If umpire has made a decision
            affected_players = self._get_affected_players(state.umpire_signal)
            for player in affected_players:
                self.action_engine.register_interrupt(
                    player.id,
                    'whistle',
                    state.umpire_signal,
                    priority='critical'
                )
                
    def _check_collisions(self, state):
        """Check for and register collision interrupts"""
        for player in self.get_all_players():
            colliding_players = self._detect_collisions(player, state)
            if colliding_players:
                self.action_engine.register_interrupt(
                    player.id,
                    'collision',
                    colliding_players,
                    priority='high'
                )
                
    def _is_valid_tackle_attempt(self, tackler, target):
        """Determine if tackle attempt is valid"""
        # Check if tackler is in valid position
        if not self._is_valid_tackle_position(tackler, target):
            return False
            
        # Check if tackler has momentum/timing
        if not self._has_tackle_momentum(tackler):
            return False
            
        # Check if target is vulnerable
        if not self._is_vulnerable_to_tackle(target):
            return False
            
        return True
        
    def _calculate_pressure_level(self, player, state):
        """Calculate level of pressure on a player"""
        pressure = 0
        
        # Get nearby opponents
        nearby_opponents = self.ground.get_players_in_radius(
            player.current_position,
            10  # 10 meter pressure radius
        )
        
        for opponent in nearby_opponents:
            if opponent.team_side != player.team_side:
                # Add pressure based on distance
                distance = self.ground.get_distance(
                    player.current_position,
                    opponent.current_position
                )
                pressure += max(0, (10 - distance) / 10)
                
                # Add pressure based on opponent's position
                if self._is_blocking_path(opponent, player, state):
                    pressure += 0.3
                    
                # Add pressure based on opponent's attributes
                pressure += (opponent.attributes.get('pressure', 50) / 100) * 0.2
                
        return min(1.0, pressure)  # Cap pressure at 1.0
    
    def _get_time_delta(self):
        """Calculate time passed since last update"""
        current_time = time.time()
        if not hasattr(self, '_last_update_time'):
            self._last_update_time = current_time
            return 0
            
        delta = current_time - self._last_update_time
        self._last_update_time = current_time
        return delta
    
    def _check_phase_transitions(self):
        """Check and handle any necessary phase transitions"""
        current_phase = self.state.play_phase
        
        # Check for phase-ending conditions
        if current_phase == 'contest':
            if self.state.ball_carrier:
                self.state.transition_phase('open_play')
                
        elif current_phase == 'set_shot':
            if not self.state.phase_data['set_shot']['player']:
                self.state.transition_phase('open_play')
                
        elif current_phase == 'ball_up':
            if self.state.ball_carrier:
                self.state.transition_phase('open_play')
                
        elif current_phase == 'kick_in':
            if self.state.ball_carrier and self.state.ball_carrier != self.state.phase_data.get('kicker'):
                self.state.transition_phase('open_play')
                
        elif current_phase == 'free_kick':
            if self.state.ball_carrier and self.state.ball_carrier != self.state.phase_data.get('free_kick_taker'):
                self.state.transition_phase('open_play')
                
        elif current_phase == 'boundary_throw':
            if self.state.ball_carrier:
                self.state.transition_phase('open_play')
                
        elif current_phase == 'open_play':
            # Check for conditions that would end open play
            if not self.state.ball_carrier:
                self.state.transition_phase('contest')
            elif self.scoring_engine._is_scoring_position(self.state.ball_carrier):
                # Get scoring evaluation from ScoringEngine
                goal_pos = self.ground.get_goal_position(self.state.ball_carrier.team_side)
                angle = self.scoring_engine._calculate_goal_angle(
                    self.state.ball_carrier.current_position,
                    goal_pos
                )
                distance = self.ground.get_distance(
                    self.state.ball_carrier.current_position,
                    goal_pos
                )
                
                self.state.transition_phase('set_shot', {
                    'player': self.state.ball_carrier,
                    'position': self.state.ball_carrier.current_position,
                    'angle': angle,
                    'distance': distance
                })


class Ground:
    """Manages the playing field, zones, and spatial calculations"""
    def __init__(self, length=160, width=130):
        self.length = length
        self.width = width
        self.grid_size = 5  # 5-meter grid cells for finer movement
        self.rows = self.length // self.grid_size
        self.cols = self.width // self.grid_size
        
        # Initialize grid and zones
        self.grid = self.initialize_grid()

        
        # Initialize zones dictionary first
        self.zones = {
            'forward': {
                'left': [], 'center': [], 'right': [],
                'pocket_left': [], 'pocket_right': []
            },
            'midfield': {
                'left': [], 'center': [], 'right': [],
                'center_square': []
            },
            'defensive': {
                'left': [], 'center': [], 'right': [],
                'pocket_left': [], 'pocket_right': []
            }
        }
        
        # Now define zones
        self.define_zones()
        
        # Define standard positions mapping
        self.position_map = self.initialize_position_map()
        
    def define_zones(self):
        """Define playing zones with more granular subdivision"""
        # Calculate zone boundaries
        third_length = self.rows // 3
        third_width = self.cols // 3
        
        # Populate zones with cells
        for y in range(self.rows):
            for x in range(self.cols):
                cell = self.grid[y][x]
                
                # Determine zone based on position
                if y < third_length:  # Defensive third
                    self._assign_cell_to_zone(cell, x, 'defensive', third_width)
                elif y < 2 * third_length:  # Midfield third
                    self._assign_cell_to_zone(cell, x, 'midfield', third_width)
                else:  # Forward third
                    self._assign_cell_to_zone(cell, x, 'forward', third_width)
        
    
    def initialize_grid(self):
        """Create grid system for tracking"""
        return [[Cell(x, y, self.grid_size) for x in range(self.cols)] 
                for y in range(self.rows)]
    
    def _assign_cell_to_zone(self, cell, x_pos, zone_type, third_width):
        """Helper method to assign cells to specific zones"""
        if x_pos < third_width:  # Left side
            self.zones[zone_type]['left'].append(cell)
            if zone_type != 'midfield' and x_pos < third_width // 2:
                self.zones[zone_type]['pocket_left'].append(cell)
        elif x_pos < 2 * third_width:  # Center
            self.zones[zone_type]['center'].append(cell)
            if zone_type == 'midfield':
                # Define center square
                center_start = (self.rows - 10) // 2
                center_end = (self.rows + 10) // 2
                if center_start <= cell.y <= center_end:
                    self.zones['midfield']['center_square'].append(cell)
        else:  # Right side
            self.zones[zone_type]['right'].append(cell)
            if zone_type != 'midfield' and x_pos > (self.cols - third_width // 2):
                self.zones[zone_type]['pocket_right'].append(cell)
    
    def initialize_position_map(self):
        """Map standard positions to grid coordinates"""
        return {
            'FF': {'home': (self.rows - 5, self.cols // 2),
                  'away': (5, self.cols // 2)},
            'CHF': {'home': (self.rows - 15, self.cols // 2),
                   'away': (15, self.cols // 2)},
            'C': {'home': (self.rows // 2, self.cols // 2),
                 'away': (self.rows // 2, self.cols // 2)},
            # Add more positions...
        }
    
    def get_position_coordinates(self, position_name, team_side):
        """Get grid coordinates for a given position and team"""
        base_pos = self.position_map.get(position_name, {}).get(team_side)
        if not base_pos:
            # Default to center if position not found
            return (self.rows // 2, self.cols // 2)
        
        # Add small random variation to prevent players stacking
        x = base_pos[0] + random.randint(-1, 1)
        y = base_pos[1] + random.randint(-1, 1)
        return (max(0, min(x, self.rows-1)), max(0, min(y, self.cols-1)))
    
    def get_distance(self, pos1, pos2):
        """Calculate actual distance between two positions in meters"""
        x1, y1 = pos1
        x2, y2 = pos2
        grid_distance = ((x2 - x1) ** 2 + (y2 - y1) ** 2) ** 0.5
        return grid_distance * self.grid_size
    
    def get_cells_in_range(self, position, range_meters):
        """Get all cells within a certain range of a position"""
        cells = []
        range_grid = range_meters // self.grid_size
        x, y = position
        
        for dx in range(-range_grid, range_grid + 1):
            for dy in range(-range_grid, range_grid + 1):
                new_x, new_y = x + dx, y + dy
                if (0 <= new_x < self.rows and 0 <= new_y < self.cols and
                    (dx**2 + dy**2)**0.5 <= range_grid):
                    cells.append(self.grid[new_x][new_y])
        return cells
    
    def get_players_in_radius(self, position, radius):
        """Get all players within a certain radius of a position"""
        # Convert to grid coordinates
        range_grid = radius // self.grid_size
        cells = self.get_cells_in_range(position, radius)
        
        # Collect all players from cells
        players = []
        for cell in cells:
            players.extend(cell.players)
        return players
    
    def get_goal_position(self, team_side):
        """Get goal position for specified team"""
        if team_side == 'home':
            return (self.rows - 1, self.cols // 2)
        else:
            return (0, self.cols // 2)
    
    def is_valid_position(self, position):
        """Check if position is within ground boundaries"""
        x, y = position
        return (0 <= x < self.rows and 0 <= y < self.cols)
    
    def get_zone(self, position):
        """
        Get detailed zone name for a position.
        Divides field into 9 tactical zones:
        - defensive_left, defensive_center, defensive_right
        - midfield_left, midfield_center, midfield_right
        - forward_left, forward_center, forward_right
        """
        x, y = position
        
        # Calculate thirds of field
        third_length = self.rows // 3
        third_width = self.cols // 3
        
        # Vertical zones (defensive to forward)
        if y < third_length:
            zone_type = 'defensive'
        elif y < 2 * third_length:
            zone_type = 'midfield'
        else:
            zone_type = 'forward'
            
        # Horizontal zones (left to right)
        if x < third_width:
            zone_side = 'left'
        elif x < 2 * third_width:
            zone_side = 'center'
        else:
            zone_side = 'right'
            
        return f"{zone_type}_{zone_side}" 
    
    def get_zone_properties(self, zone):
        """Get tactical properties for each zone"""
        zone_properties = {
            'defensive_left': {
                'risk_factor': 0.3,
                'attacking_value': 0.1,
                'defensive_value': 0.9,
                'preferred_actions': ['clear', 'short_pass']
            },
            'defensive_center': {
                'risk_factor': 0.2,
                'attacking_value': 0.2,
                'defensive_value': 1.0,
                'preferred_actions': ['clear', 'long_pass']
            },
            'defensive_right': {
                'risk_factor': 0.3,
                'attacking_value': 0.1,
                'defensive_value': 0.9,
                'preferred_actions': ['clear', 'short_pass']
            },
            'midfield_left': {
                'risk_factor': 0.5,
                'attacking_value': 0.5,
                'defensive_value': 0.5,
                'preferred_actions': ['pass', 'run']
            },
            'midfield_center': {
                'risk_factor': 0.6,
                'attacking_value': 0.6,
                'defensive_value': 0.6,
                'preferred_actions': ['pass', 'run', 'contest']
            },
            'midfield_right': {
                'risk_factor': 0.5,
                'attacking_value': 0.5,
                'defensive_value': 0.5,
                'preferred_actions': ['pass', 'run']
            },
            'forward_left': {
                'risk_factor': 0.7,
                'attacking_value': 0.9,
                'defensive_value': 0.1,
                'preferred_actions': ['shot', 'pass']
            },
            'forward_center': {
                'risk_factor': 0.8,
                'attacking_value': 1.0,
                'defensive_value': 0.2,
                'preferred_actions': ['shot', 'mark']
            },
            'forward_right': {
                'risk_factor': 0.7,
                'attacking_value': 0.9,
                'defensive_value': 0.1,
                'preferred_actions': ['shot', 'pass']
            }
        }
        return zone_properties.get(zone, zone_properties['midfield_center'])    

class Cell:
    """Represents a single grid cell on the ground"""
    def __init__(self, x, y, grid_size):
        self.x = x
        self.y = y
        self.real_x = x * grid_size  # Real position in meters
        self.real_y = y * grid_size
        self.players = []  # Players currently in this cell
        self.contested = False
        self.zone = None  # Will be set by Ground.define_zones()
        
    def add_player(self, player):
        self.players.append(player)
        self.contested = len(self.players) > 1
        
    def remove_player(self, player):
        if player in self.players:
            self.players.remove(player)
            self.contested = len(self.players) > 1

"""
class MatchState:
    #Tracks current match state/context
    def __init__(self):
        self.ball_position = None
        self.ball_carrier = None
        self.play_phase = None  # Contest, Open Play, Set Shot etc
        self.field_position = None  # Forward, Mid, Defense
        self.momentum = 0  # -100 to 100 for game momentum
        
    def update_state(self, **kwargs):
        #Update state attributes
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
"""
class Team:
    """Team composition and tactics"""
    def __init__(self, name, players, tactics):
        self.name = name
        self.players = players
        self.score = {'goals': 0, 'behinds': 0, 'total': 0}
        self.tactics = tactics
        self.team_side = 'home' if name == players[0].team_side else 'away'
        
    def update_score(self, score_type):
        """Update team score"""
        if score_type == 'goal':
            self.score['goals'] += 1
            self.score['total'] += 6
        elif score_type == 'behind':
            self.score['behinds'] += 1
            self.score['total'] += 1

class Player:
    """Individual player capabilities and state"""
    def __init__(self, name, position, attributes, team_side):
        self.name = name
        self.position = position
        self.attributes = attributes
        self.team_side = team_side
        self.fatigue = 0
        self.morale = 100
        self.current_position = position
        self.stats = {
            'kicks': 0, 'handballs': 0, 'marks': 0,
            'tackles': 0, 'hitouts': 0, 'clearances': 0,
            'goals': 0, 'behinds': 0
        }
        
    def update_stats(self, stat_type):
        """Update player statistics"""
        if stat_type in self.stats:
            self.stats[stat_type] += 1

# Supporting Classes
class EventManager:
    """Handles all match events, statistics, and broadcasting"""
    def __init__(self, match_group_name, channel_layer):
        self.match_group_name = match_group_name
        self.channel_layer = channel_layer
        self.events = []
        self.stats = {}  # Player and team statistics
        self.commentary_engine = CommentaryEngine()
        
    async def create_event(self, event_type, **data):
        """Create and store an event"""
        print(f"Creating event: {event_type}")
        event = {
            'type': event_type,
            'timestamp': time.time(),
            **data
        }
        
        # Add event to history
        self.events.append(event)
        
        # Update statistics
        #self._update_stats(event)
        self.stats_manager.update_stats(event)
        
        # Generate commentary
        commentary = self.commentary_engine.generate_commentary(event)
        if commentary:
            event['commentary'] = commentary
            
        return event
        
    def _update_stats(self, event):
        """Update player and team statistics based on event"""
        player_name = event.get('player')
        team = event.get('team')
        event_type = event.get('type')
        outcome = event.get('outcome')
        
        if player_name:
            # Initialize player stats if needed
            if player_name not in self.stats:
                self.stats[player_name] = self._initialize_player_stats()
                
            # Update player stats
            player_stats = self.stats[player_name]
            
            if event_type == 'kick':
                player_stats['kicks'] += 1
                if outcome == 'goal':
                    player_stats['goals'] += 1
                elif outcome == 'behind':
                    player_stats['behinds'] += 1
                    
            elif event_type == 'handball':
                player_stats['handballs'] += 1
                
            elif event_type == 'mark':
                player_stats['marks'] += 1
                
            elif event_type == 'tackle':
                player_stats['tackles'] += 1
                
            # Update disposals
            if event_type in ['kick', 'handball']:
                player_stats['disposals'] += 1
                if outcome == 'success':
                    player_stats['effective_disposals'] += 1
                    
    def _initialize_player_stats(self):
        """Initialize statistics dictionary for a player"""
        return {
            'kicks': 0,
            'handballs': 0,
            'marks': 0,
            'tackles': 0,
            'goals': 0,
            'behinds': 0,
            'disposals': 0,
            'effective_disposals': 0,
            'contested_possessions': 0,
            'uncontested_possessions': 0,
            'clearances': 0,
            'inside_50s': 0,
            'rebound_50s': 0
        }
    """        
    async def _broadcast_event(self, event):
        #Broadcast event to all connected clients
        try:
            await self.channel_layer.group_send(
                self.match_group_name,
                {
                    'type': 'match.event',
                    'event': event
                }
            )
        except Exception as e:
            print(f"Error broadcasting event: {e}")
    """            
    def get_player_stats(self, player_name, quarter=None):
        """Get statistics for a specific player"""
        if quarter:
            return self.stats_manager.get_player_quarter_stats(player_name, quarter)
        return self.stats_manager.get_player_stats(player_name)
        
    def get_team_stats(self, team, quarter=None):
        """Get aggregated team statistics"""
        if quarter:
            return self.stats_manager.get_quarter_stats(quarter)
        return self.stats_manager.get_team_stats(team)
        
    def get_player_influence(self, player_name):
        """Get player influence metrics"""
        return self.stats_manager.get_player_influence(player_name)
        
    def get_team_pressure_map(self, team):
        """Get team pressure map"""
        return self.stats_manager.get_team_pressure_map(team)
        
    def get_possession_chains(self, team=None):
        """Get possession chains, optionally filtered by team"""
        chains = self.stats_manager.possession_chains
        if team:
            return [chain for chain in chains if chain.team == team]
        return chains
        
    def get_quarter_events(self, quarter):
        """Get all events for a specific quarter"""
        return [e for e in self.events if e.get('quarter') == quarter]
        
    def get_highlight_events(self):
        """Get significant match events for highlights"""
        highlight_types = {'goal', 'behind', 'spectacular_mark', 'big_tackle'}
        return [e for e in self.events if e.get('type') in highlight_types]

class CommentaryEngine:
    """Enhanced natural language commentary for match events"""
    def __init__(self):
        self.templates = self._initialize_templates()
        self.recent_comments = []
        self.momentum = 0  # Track game momentum (-1 to 1)
        self.last_possession_team = None
        self.possession_chain_length = 0
        
    def generate_commentary(self, event):
        """Generate context-aware commentary for an event"""
        event_type = event.get('type')
        player = event.get('player')
        team = event.get('team')
        outcome = event.get('outcome')
        quarter = event.get('quarter')
        time = event.get('time')
        
        # Update game context
        self._update_context(event)
        
        # Get appropriate templates
        templates = self._get_contextual_templates(event)
        if not templates:
            return None
            
        # Select template avoiding recent repetition
        template = self._select_template(templates)
        
        # Get additional context for template
        context = self._get_commentary_context(event)
        
        # Format template with event data and context
        try:
            commentary = template.format(
                player=player,
                team=team,
                **context
            )
            
            # Store in recent comments
            self._update_recent_comments(commentary)
            
            return commentary
        except KeyError as e:
            print(f"Error formatting commentary: {e}")
            return None
            
    def _update_context(self, event):
        """Update game context based on event"""
        team = event.get('team')
        event_type = event.get('type')
        outcome = event.get('outcome')
        
        # Update possession tracking
        if event_type in ['mark', 'gather', 'free_kick']:
            if team != self.last_possession_team:
                self.possession_chain_length = 1
            else:
                self.possession_chain_length += 1
            self.last_possession_team = team
            
        # Update momentum
        if event_type == 'goal':
            self.momentum = max(-1, min(1, self.momentum + (0.3 if team == 'home' else -0.3)))
        elif outcome == 'success':
            self.momentum = max(-1, min(1, self.momentum + (0.1 if team == 'home' else -0.1)))
            
    def _get_contextual_templates(self, event):
        """Get templates appropriate for current context"""
        event_type = event.get('type')
        outcome = event.get('outcome')
        base_templates = self.templates.get(event_type, [])
        
        # Filter templates based on context
        if self.possession_chain_length > 3:
            return self.templates.get(f'{event_type}_chain', base_templates)
        elif abs(self.momentum) > 0.7:
            return self.templates.get(f'{event_type}_momentum', base_templates)
        
        return base_templates
        
    def _get_commentary_context(self, event):
        """Get additional context for commentary"""
        return {
            'momentum_desc': self._get_momentum_description(),
            'chain_desc': self._get_chain_description(),
            'pressure_desc': self._get_pressure_description(event),
            'position_desc': self._get_position_description(event.get('position')),
            'time_desc': self._get_time_description(event.get('time'))
        }
        
    def _initialize_templates(self):
        """Initialize enhanced commentary templates"""
        return {
            'kick': [
                "{player} sends it long with a kick",
                "Beautiful kick from {player}",
                "{player} puts boot to ball"
            ],
            'kick_chain': [
                "Brilliant chain of play continues as {player} kicks forward",
                "{team} maintaining possession as {player} kicks",
                "The {momentum_desc} continues with {player}'s kick"
            ],
            'kick_momentum': [
                "{team} dominating as {player} kicks {position_desc}",
                "The momentum is with {team} as {player} kicks"
            ],
            'mark': [
                "Strong mark taken by {player}",
                "{player} climbs high for the mark",
                "Brilliant grab by {player}"
            ],
            'mark_pressure': [
                "{player} takes a courageous mark under {pressure_desc}",
                "Under intense pressure, {player} holds the mark"
            ],
            'goal': [
                "GOAL! {player} kicks truly {time_desc}",
                "{player} slots it through for a major",
                "Brilliant finish from {player}"
            ],
            'goal_momentum': [
                "{team} piling on the pressure with another goal from {player}",
                "The floodgates are opening as {player} kicks another!"
            ],
            'tackle': [
                "Strong tackle from {player}",
                "{player} with a bone-crunching tackle"
            ],
            'contest': [
                "Fierce contest {position_desc}",
                "Players going hard at it in the {position_desc}"
            ]
        }
        
    def _get_momentum_description(self):
        """Get description of current game momentum"""
        if self.momentum > 0.7:
            return "dominant spell"
        elif self.momentum > 0.3:
            return "good patch"
        elif self.momentum < -0.7:
            return "against the flow"
        return "balanced play"
        
    def _get_chain_description(self):
        """Get description of possession chain"""
        if self.possession_chain_length > 5:
            return "magnificent chain of possession"
        elif self.possession_chain_length > 3:
            return "good string of possessions"
        return ""
        
    def _get_position_description(self, position):
        """Get description of field position"""
        if not position:
            return ""
            
        x, y = position
        if x < 0.2:
            return "deep in defense"
        elif x < 0.4:
            return "in defensive territory"
        elif x > 0.8:
            return "deep in attack"
        elif x > 0.6:
            return "in attacking territory"
        return "in the middle"
        
    def _get_time_description(self, time):
        """Get description of match time"""
        if not time:
            return ""
            
        minutes = time // 60
        if minutes < 5:
            return "early in the quarter"
        elif minutes > 15:
            return "late in the quarter"
        return f"{minutes} minutes into the quarter"

class StatsManager:
    """Enhanced statistics tracking system with quarter-by-quarter breakdown"""
    def __init__(self):
        # Existing quarter tracking
        self.quarter_stats = {1: {}, 2: {}, 3: {}, 4: {}}
        self.match_stats = {}
        
        # New tracking features
        self.player_stats = {}  # Detailed player stats
        self.team_stats = {}    # Team-level stats
        self.possession_chains = []  # Track possession sequences
        self.heat_maps = {}     # Position-based activity tracking
        self.current_chain = None
        
    def update_player_stats(self, player, quarter):
        """Update player statistics for the current quarter"""
        # Maintain existing quarter stats
        if player.name not in self.quarter_stats[quarter]:
            self.quarter_stats[quarter][player.name] = {
                'name': player.name,
                'team': player.team_side,
                **player.stats
            }
        else:
            self.quarter_stats[quarter][player.name].update(player.stats)
            
    def update_stats(self, event):
        """Update all statistics based on event"""
        player_name = event.get('player')
        team = event.get('team')
        quarter = event.get('quarter')
        
        # Update quarter-specific stats
        if player_name and quarter:
            if player_name not in self.quarter_stats[quarter]:
                self.quarter_stats[quarter][player_name] = {
                    'name': player_name,
                    'team': team,
                }
            self._update_quarter_stats(self.quarter_stats[quarter][player_name], event)
        
        # Update detailed player stats
        if player_name:
            self._update_player_stats(player_name, event)
            self._update_heat_map(player_name, event.get('position'))
            
        # Update team stats
        if team:
            self._update_team_stats(team, event)
            
        # Update possession chains
        self._update_possession_chain(event)
        
    def get_quarter_stats(self, quarter):
        """Get all player stats for a specific quarter"""
        return list(self.quarter_stats[quarter].values())
        
    def get_player_quarter_stats(self, player_name, quarter):
        """Get player stats for specific quarter"""
        return self.quarter_stats[quarter].get(player_name, {})

    def update_stats(self, event):
        """Update all statistics based on event"""
        player_name = event.get('player')
        team = event.get('team')
        event_type = event.get('type')
        position = event.get('position')
        
        # Update player stats
        if player_name:
            self._update_player_stats(player_name, event)
            self._update_heat_map(player_name, position)
            
        # Update team stats
        if team:
            self._update_team_stats(team, event)
            
        # Update possession chains
        self._update_possession_chain(event)
        
    def _update_player_stats(self, player_name, event):
        """Update detailed player statistics"""
        if player_name not in self.player_stats:
            self.player_stats[player_name] = self._initialize_player_stats()
            
        stats = self.player_stats[player_name]
        event_type = event.get('type')
        outcome = event.get('outcome')
        position = event.get('position')
        
        # Basic stats
        stats['total_actions'] += 1
        stats[event_type] = stats.get(event_type, 0) + 1
        
        # Detailed stats based on event type
        if event_type == 'kick':
            self._update_kick_stats(stats, event)
        elif event_type == 'handball':
            self._update_handball_stats(stats, event)
        elif event_type == 'mark':
            self._update_mark_stats(stats, event)
        elif event_type == 'tackle':
            self._update_tackle_stats(stats, event)
            
        # Update influence metrics
        self._update_influence_metrics(stats, event)
        
    def _update_kick_stats(self, stats, event):
        """Track detailed kicking statistics"""
        outcome = event.get('outcome')
        distance = event.get('distance', 0)
        
        stats['kick_meters'] += distance
        
        if outcome == 'goal':
            stats['goals'] += 1
            stats['score_involvements'] += 1
        elif outcome == 'behind':
            stats['behinds'] += 1
            stats['score_involvements'] += 1
        elif outcome == 'success':
            stats['effective_kicks'] += 1
            
        # Track kick types
        kick_type = event.get('kick_type', 'standard')
        stats[f'{kick_type}_kicks'] = stats.get(f'{kick_type}_kicks', 0) + 1
        
    def _update_influence_metrics(self, stats, event):
        """Calculate player influence metrics"""
        position = event.get('position')
        event_type = event.get('type')
        outcome = event.get('outcome')
        
        # Base influence points
        influence = 0
        
        # Scoring influence
        if event_type == 'kick' and outcome in ['goal', 'behind']:
            influence += 5 if outcome == 'goal' else 2
            
        # Possession influence
        if event_type in ['mark', 'gather']:
            influence += 2
            
        # Territory influence
        if position:
            zone = self._get_zone(position)
            if zone == 'forward_50':
                influence += 2
            elif zone == 'defensive_50':
                influence += 1
                
        stats['influence_points'] += influence
        
    def _update_heat_map(self, player_name, position):
        """Update player heat map"""
        if position:
            if player_name not in self.heat_maps:
                self.heat_maps[player_name] = self._initialize_heat_map()
                
            x, y = position
            # Convert position to heat map grid
            grid_x = int(x / 10)  # 10-meter grid squares
            grid_y = int(y / 10)
            
            self.heat_maps[player_name][grid_x][grid_y] += 1
            
    def _update_possession_chain(self, event):
        """Track possession chains"""
        event_type = event.get('type')
        team = event.get('team')
        
        if event_type in ['gather', 'mark', 'free_kick']:
            # New possession
            if self.current_chain:
                self.possession_chains.append(self.current_chain)
            self.current_chain = PossessionChain(team)
            
        if self.current_chain and event_type in ['kick', 'handball']:
            self.current_chain.add_disposal(event)
            
    def get_player_influence(self, player_name):
        """Get comprehensive player influence stats"""
        if player_name not in self.player_stats:
            return None
            
        stats = self.player_stats[player_name]
        return {
            'influence_points': stats['influence_points'],
            'heat_map': self.heat_maps.get(player_name),
            'score_involvements': stats['score_involvements'],
            'meters_gained': stats['kick_meters'] + stats.get('run_meters', 0),
            'possession_chains': self._get_player_chains(player_name)
        }
        
    def get_team_pressure_map(self, team):
        """Get team pressure map"""
        pressure_map = self._initialize_heat_map()
        
        # Combine all player heat maps for the team
        for player_name, heat_map in self.heat_maps.items():
            if player_name.startswith(team):  # Assuming player names are prefixed with team
                for x in range(len(heat_map)):
                    for y in range(len(heat_map[0])):
                        pressure_map[x][y] += heat_map[x][y]
                        
        return pressure_map

class PossessionChain:
    """Tracks a sequence of linked possessions"""
    def __init__(self, team):
        self.team = team
        self.disposals = []
        self.meters_gained = 0
        self.duration = 0
        self.score_result = None
        
    def add_disposal(self, event):
        """Add disposal to the chain"""
        self.disposals.append(event)
        
        # Calculate meters gained
        if 'start_position' in event and 'end_position' in event:
            self.meters_gained += self._calculate_distance(
                event['start_position'],
                event['end_position']
            )
            
        # Update duration
        if len(self.disposals) > 1:
            self.duration = (
                event['timestamp'] - 
                self.disposals[0]['timestamp']
            )    

class BasePositions:
    """Manages team structure and dynamic positioning"""
    def __init__(self, team_side, style='balanced'):
        self.team_side = team_side  # 'home' or 'away'
        #self.style = style  # 'balanced', 'attacking', 'defensive'
        self.base_formation = self._initialize_base_positions()
        #self.dynamic_zones = self._initialize_dynamic_zones()
        
    def _initialize_base_positions(self):
        """Define standard positions for AFL with mirrored forward/back positions"""
        return {
            # Forwards
            'FF': {'x_rel': 0.9, 'y_rel': 0.5, 'zone': 'forward'},    # Mirrors FB's 0.1
            'CHF': {'x_rel': 0.75, 'y_rel': 0.5, 'zone': 'forward'},  # Mirrors CHB's 0.25
            'LHF': {'x_rel': 0.75, 'y_rel': 0.3, 'zone': 'forward'},  # Mirrors RHB's position
            'RHF': {'x_rel': 0.75, 'y_rel': 0.7, 'zone': 'forward'},  # Mirrors LHB's position
            'LF': {'x_rel': 0.85, 'y_rel': 0.2, 'zone': 'forward'},   # Mirrors RB's 0.15
            'RF': {'x_rel': 0.85, 'y_rel': 0.8, 'zone': 'forward'},   # Mirrors LB's 0.15
            
            # Midfield (stays the same - symmetrical around 0.5)
            'Centre': {'x_rel': 0.5, 'y_rel': 0.5, 'zone': 'midfield'},
            'Ruck': {'x_rel': 0.5, 'y_rel': 0.5, 'zone': 'midfield'},
            'RuckRover': {'x_rel': 0.5, 'y_rel': 0.4, 'zone': 'midfield'},
            'Rover': {'x_rel': 0.5, 'y_rel': 0.6, 'zone': 'midfield'},
            'LWing': {'x_rel': 0.5, 'y_rel': 0.2, 'zone': 'wing'},
            'RWing': {'x_rel': 0.5, 'y_rel': 0.8, 'zone': 'wing'},
            
            # Defenders
            'FB': {'x_rel': 0.1, 'y_rel': 0.5, 'zone': 'defensive'},
            'CHB': {'x_rel': 0.25, 'y_rel': 0.5, 'zone': 'defensive'},
            'LHB': {'x_rel': 0.25, 'y_rel': 0.3, 'zone': 'defensive'},
            'RHB': {'x_rel': 0.25, 'y_rel': 0.7, 'zone': 'defensive'},
            'LB': {'x_rel': 0.15, 'y_rel': 0.2, 'zone': 'defensive'},
            'RB': {'x_rel': 0.15, 'y_rel': 0.8, 'zone': 'defensive'}
        }


    def get_position(self, player_position, play_phase, context):
        """Get ideal position based on formation, phase and context"""
        base_pos = self.base_formation[player_position]
        
        # Adjust for team side
        if self.team_side == 'away':
            base_pos['x_rel'] = 1 - base_pos['x_rel']
            
        # Apply dynamic adjustments based on play phase and context
        adjusted_pos = self._adjust_for_phase(base_pos, play_phase, context)
        return adjusted_pos

    def _adjust_for_phase(self, base_pos, play_phase, context):
        """Adjust position based on play phase and match context"""
        adjustments = {
            'contest': self._contest_adjustment,
            'open_play': self._open_play_adjustment,
            'set_shot': self._set_shot_adjustment,
            'transition': self._transition_adjustment
        }
        
        if play_phase in adjustments:
            return adjustments[play_phase](base_pos, context)
        return base_pos

class ActionEngine:
    """Handles all player actions and outcomes"""
    def __init__(self, ground, position_manager, movement_pattern):
        self.ground = ground
        self.decision_engine = DecisionEngine(ground)
        self.position_manager = position_manager     
        self.movement_patterns = {}
        self.MovementPatternClass = movement_pattern  # Store the class, not an instance
        #self.base_positions = {}  # team_id -> BasePositions
        self.set_play_patterns = {}  # Store set play patterns for each team 
        self.active_interrupts = {}  # player_id -> ActionInterrupt
        self.active_recoveries = {}  # player_id -> RecoveryAction
        self.interrupt_handlers = {
            'tackle': self._handle_tackle_interrupt,
            'pressure': self._handle_pressure_interrupt,
            'contest': self._handle_contest_interrupt,
            'whistle': self._handle_whistle_interrupt,
            'collision': self._handle_collision_interrupt
        }
               
        
    def process_player_action(self, player, state, coordination_context=None):
        """Process and execute player actions based on state"""
        # Check for active recovery
        active_recovery = self.active_recoveries.get(player.id)
        if active_recovery:
            recovery_step = active_recovery.get_next_step()
            if recovery_step:
                result = self._execute_recovery_action(player, recovery_step, state)
                if result['outcome'] == 'success':
                    if not active_recovery.advance():
                        # Recovery complete
                        del self.active_recoveries[player.id]
                return result
        print(f"coordination_context {coordination_context}")
        #print(f"Player pattern {player.currnt_pattern}")
        # Initialize movement pattern if not exists
        if player.id not in self.movement_patterns:
            print(f"Creating movement pattern for player {player.id}")
            self.movement_patterns[player.id] = self.MovementPatternClass(self.ground, self.position_manager, player)
            
        movement_pattern = self.movement_patterns[player.id]
        print(f"Movement pattern {movement_pattern}")
        # Check for active interrupts
        active_interrupt = self.active_interrupts.get(player.id)
        if active_interrupt:
            interrupt_response = self._handle_interrupt(player, active_interrupt, state)
            if interrupt_response:
                return interrupt_response
        
        # Check for phase transition
        print(f"State play phase {state.play_phase}")
        print(f"Movement pattern current phase {movement_pattern.current_phase}")
        if state.play_phase != movement_pattern.current_phase:
            movement_pattern.transition_phase(state.play_phase, state)

        if state.play_phase in ['ball_up', 'kick_in', 'set_shot', 'free_kick', 'boundary_throw', 'Centre_Bounce']:
            set_play = self._get_or_create_set_play(player.team)
            setup = set_play.generate_setup(state.play_phase, state)
            
            # Get player's role in set play
            player_role = set_play.player_assignments.get(player.id)
            
            # Apply set play pattern if player has a role
            if player_role:
                movement_pattern.apply_set_play(player_role, setup)            
        
        # Apply team coordination if provided
        if coordination_context:
            movement_pattern.apply_team_coordination(
                coordination_context.get('movement'),
                coordination_context.get('team_instructions'),
                coordination_context.get('role')
            )
            
        # Check for opposition movement and generate response if needed
        opposition_movement = self._get_opposition_movement(player, state)
        if opposition_movement:
            response_pattern = movement_pattern.generate_opposition_response(
                state,
                opposition_movement
            )
            if response_pattern:
                return self._execute_response_pattern(
                    player,
                    response_pattern,
                    state
                )
        
        # Check if player has ongoing action combination
        current_combination = getattr(player, 'current_combination', None)
        
        if current_combination and current_combination.status == 'in_progress':
            next_action = current_combination.get_next_action()
            if next_action:
                result = self._execute_action(player, next_action, state)
                current_combination.advance(result)
                return result
                
        # Normal action processing
        possible_actions = self._get_possible_actions(player, state)
        chosen_action = self.decision_engine.choose_action(
            player, 
            possible_actions, 
            state
        )
        
        # Check if chosen action should be part of a combination
        if self._should_be_combination(chosen_action, state):
            combination = self._create_action_combination(player, chosen_action, state)
            player.current_combination = combination
            combination.status = 'in_progress'
            return self.process_player_action(player, state, coordination_context)
            
        return self._execute_action(player, chosen_action, state)
    

    def _should_be_combination(self, action, state):
        """Determine if action should be part of a combination"""
        if not action:
            return False
            
        # Actions that typically need combinations
        combination_types = ['gather', 'mark', 'break_tackle']
        
        # Check action type
        if action['type'] in combination_types:
            return True
            
        # Check if kick needs setup
        if action['type'] == 'kick' and self._needs_setup(action, state):
            return True
            
        return False
    
    def _get_opposition_movement(self, player, state):
        """Get relevant opposition movement to respond to"""
        if not state.ball_carrier:
            return None
            
        # Don't respond to teammates
        if state.ball_carrier.team_side == player.team_side:
            return None
            
        # Get opposition player movements in our zone of influence
        zone_radius = 30  # meters
        nearby_players = self.ground.get_players_in_radius(
            player.current_position,
            zone_radius
        )
        
        opposition_players = [
            p for p in nearby_players 
            if p.team_side != player.team_side
        ]
        
        if not opposition_players:
            return None
            
        # Determine most threatening movement
        threats = []
        for opp in opposition_players:
            threat_level = self._calculate_threat_level(
                player,
                opp,
                state
            )
            if threat_level > 0:
                threats.append({
                    'player_id': opp.id,
                    'position': opp.current_position,
                    'target': self._predict_player_movement(opp, state),
                    'threat_level': threat_level,
                    'estimated_time': self._estimate_movement_time(
                        opp.current_position,
                        self._predict_player_movement(opp, state)
                    )
                })
                
        if not threats:
            return None
            
        # Return the most threatening movement
        return max(threats, key=lambda x: x['threat_level'])
        
    def _calculate_threat_level(self, player, opposition, state):
        """Calculate how threatening an opposition player's movement is"""
        threat_level = 0
        
        # Base threat on proximity to goal
        goal_pos = self.ground.get_goal_position(player.team_side)
        distance_to_goal = self.ground.get_distance(
            opposition.current_position,
            goal_pos
        )
        threat_level += max(0, (100 - distance_to_goal)) / 100
        
        # Increase threat if opposition has ball
        if opposition == state.ball_carrier:
            threat_level *= 1.5
            
        # Increase threat based on opposition attributes
        threat_level *= (opposition.attributes.get('speed', 50) / 100)
        
        # Modify based on game context
        if state.play_phase == 'open_play':
            threat_level *= 1.2
        elif state.play_phase == 'contest':
            threat_level *= 0.8
            
        return threat_level
        
    def _predict_player_movement(self, player, state):
        """Predict where an opposition player is likely to move"""
        if player == state.ball_carrier:
            # Predict based on game situation
            return self._predict_ball_carrier_movement(player, state)
        else:
            # Predict based on likely support position
            return self._predict_support_movement(player, state)
            
    def _predict_ball_carrier_movement(self, player, state):
        """Predict ball carrier's likely movement"""
        goal_pos = self.ground.get_goal_position(player.team_side)
        current_pos = player.current_position
        
        # Consider direct route to goal
        direct_vector = (
            goal_pos[0] - current_pos[0],
            goal_pos[1] - current_pos[1]
        )
        
        # Modify based on defensive pressure
        pressure = self._calculate_defensive_pressure(player, state)
        if pressure > 0.7:  # High pressure
            # Predict lateral movement to escape
            return self._calculate_escape_position(player, pressure)
        else:
            # Predict forward movement towards goal
            return (
                current_pos[0] + direct_vector[0] * 0.2,  # 20% towards goal
                current_pos[1] + direct_vector[1] * 0.2
            )
            
    def _predict_support_movement(self, player, state):
        """Predict supporting player's likely movement"""
        if not state.ball_carrier:
            return player.current_position
            
        # Predict based on common support patterns
        ball_pos = state.ball_carrier.current_position
        return self._calculate_support_position(
            player,
            ball_pos,
            state
        )
        
    def _estimate_movement_time(self, start_pos, end_pos):
        """Estimate time needed for movement"""
        distance = self.ground.get_distance(start_pos, end_pos)
        average_speed = 6  # meters per second
        return distance / average_speed
        
    def _execute_response_pattern(self, player, response_pattern, state):
        """Execute the opposition response pattern"""
        # Convert response pattern to action
        action = {
            'type': response_pattern['type'],
            'target_position': response_pattern['target_position'],
            'priority': response_pattern.get('priority', 'medium')
        }
        return self._execute_action(player, action, state)    

    def _get_or_create_set_play(self, team):
        """Get or create set play pattern for team"""
        #if team.id not in self.set_play_patterns:
        self.set_play_patterns[team.id] = SetPlayPattern(team, self.ground)
        return self.set_play_patterns[team.id]    

    def _get_possible_actions(self, player, context):
        """Determine all possible actions in current context"""
        actions = []
        
        if context.play_phase == 'open_play':
            if player == context.ball_carrier:
                actions.extend(self._get_ball_carrier_actions(player, context))
            else:
                actions.extend(self._get_off_ball_actions(player, context))
        elif context.play_phase == 'contest':
            actions.extend(self._get_contest_actions(player, context))
        elif context.play_phase == 'set_shot':
            actions.extend(self._get_set_shot_actions(player, context))
            
        return actions
    
    def _get_ball_carrier_actions(self, player, state):
        """Get possible actions for ball carrier"""
        actions = []
        
        # Basic actions always available to ball carrier
        actions.append({'type': 'run', 'risk': 0.2})
        
        # Add kick option if within range of teammates
        kick_targets = self._get_kick_targets(player, state)
        if kick_targets:
            actions.append({
                'type': 'kick',
                'targets': kick_targets,
                'risk': 0.4
            })
        
        # Add handball option if teammates nearby
        handball_targets = self._get_handball_targets(player, state)
        if handball_targets:
            actions.append({
                'type': 'handball',
                'targets': handball_targets,
                'risk': 0.3
            })
        
        return actions
    
    def _execute_core_action(self, player, action, state):
        """Core action execution logic"""
        action_handlers = {
            'kick': self._execute_kick,
            'handball': self._execute_handball,
            'run': self._execute_run,
            'contest': self._execute_contest,
            'mark': self._execute_mark,
            'tackle': self._execute_tackle,
            'shepherd': self._execute_shepherd
        }
        
        handler = action_handlers.get(action['type'])
        if handler:
            return handler(player, action, state)
        return None

    def execute_action(self, player, action, state):
        """Execute player action considering position and role"""
        # Get ideal position for current phase
        ideal_pos = self.position_manager.get_ideal_position(
            player, 
            state.play_phase, 
            state
        )
        
        # Check if movement is needed
        if ideal_pos != player.current_position:
            self._handle_movement(player, ideal_pos, state)
            
        # Now execute the core action
        return self._execute_core_action(player, action, state)  
    

    def _handle_interrupt(self, player, interrupt, state):
        """Handle an active interrupt"""
        handler = self.interrupt_handlers.get(interrupt.type)
        if handler:
            response = handler(player, interrupt, state)
            if response and response['outcome'] == 'failure':
                # Initiate recovery if interrupt handling failed
                self.initiate_recovery(player, state, interrupt)
            return response
        return None
        
    def _handle_tackle_interrupt(self, player, interrupt, state):
        """Handle being tackled"""
        if player == state.ball_carrier:
            # Attempt to dispose of ball
            disposal_action = self._generate_emergency_disposal(player, state)
            if disposal_action:
                return self._execute_action(player, disposal_action, state)
                
        # Default to tackle response
        return self._execute_action(player, {
            'type': 'tackle_response',
            'style': 'break' if player.attributes.get('strength', 50) > 70 else 'release'
        }, state)
        
    def _handle_pressure_interrupt(self, player, interrupt, state):
        """Handle being under pressure"""
        if player == state.ball_carrier:
            # Look for quick disposal option
            quick_disposal = self._find_quick_disposal_option(player, state)
            if quick_disposal:
                return self._execute_action(player, quick_disposal, state)
                
        # Evade pressure
        return self._execute_action(player, {
            'type': 'evade',
            'direction': self._find_space_direction(player, state)
        }, state)
        
    def _handle_whistle_interrupt(self, player, interrupt, state):
        """Handle umpire's whistle"""
        # Stop current action
        if hasattr(player, 'current_combination'):
            player.current_combination.status = 'interrupted'
            
        return self._execute_action(player, {
            'type': 'stop_play',
            'position': player.current_position
        }, state)
    
    def _handle_contest_interrupt(self, player, interrupt, state):
        """Handle contest situation"""
        # Generate appropriate contest response
        if player == state.ball_carrier:
            # Ball carrier in contest
            return self._execute_action(player, {
                'type': 'protect_ball',
                'style': 'strength' if player.attributes.get('strength', 50) > 70 else 'evasive'
            }, state)
        else:
            # Player trying to win contest
            return self._execute_action(player, {
                'type': 'contest_ball',
                'style': 'aggressive' if player.attributes.get('aggression', 50) > 70 else 'tactical'
            }, state)
            
    def _handle_collision_interrupt(self, player, interrupt, state):
        """Handle player collision"""
        # Generate appropriate collision response
        return self._execute_action(player, {
            'type': 'brace_collision',
            'style': 'absorb' if player.attributes.get('strength', 50) > 70 else 'deflect'
        }, state)    
        
    def register_interrupt(self, player_id, interrupt_type, source, priority='medium'):
        """Register a new interrupt for a player"""
        interrupt = ActionInterrupt(interrupt_type, source, priority)
        
        # Check if should override existing interrupt
        existing = self.active_interrupts.get(player_id)
        if not existing or self._should_override_interrupt(interrupt, existing):
            self.active_interrupts[player_id] = interrupt
            
    def _should_override_interrupt(self, new_interrupt, existing_interrupt):
        """Determine if new interrupt should override existing one"""
        priority_levels = {
            'low': 1,
            'medium': 2,
            'high': 3,
            'critical': 4
        }
        
        new_priority = priority_levels.get(new_interrupt.priority, 0)
        existing_priority = priority_levels.get(existing_interrupt.priority, 0)
        
        if new_priority > existing_priority:
            return True
            
        if new_priority == existing_priority:
            # Use more recent interrupt
            return new_interrupt.timestamp > existing_interrupt.timestamp
            
        return False
        
    def _generate_emergency_disposal(self, player, state):
        """Generate emergency disposal action under pressure"""
        # Look for safest disposal option
        disposal_options = self._get_emergency_disposal_options(player, state)
        if not disposal_options:
            return None
            
        return min(disposal_options, 
                  key=lambda x: x.get('risk', float('inf')))
                  
    def _find_quick_disposal_option(self, player, state):
        """Find quick disposal option under pressure"""
        nearby_teammates = self.ground.get_players_in_radius(
            player.current_position,
            15  # 15 meter radius for quick disposal
        )
        
        disposal_options = []
        for teammate in nearby_teammates:
            if teammate.team_side == player.team_side:
                risk = self._calculate_disposal_risk(
                    player,
                    teammate,
                    state
                )
                if risk < 0.7:  # Only consider relatively safe options
                    disposal_options.append({
                        'type': 'handball' if risk < 0.4 else 'kick',
                        'target': teammate,
                        'risk': risk
                    })
                    
        return min(disposal_options, 
                  key=lambda x: x['risk']) if disposal_options else None    


    def _execute_kick(self, player, action, state):
        """Execute a kick action"""
        # Get target
        target = self._select_best_target(action['targets'])
        if not target:
            return {
                'type': 'kick',
                'outcome': 'fail',
                'reason': 'no_target',
                'player': player,
                'new_state': {
                    'ball_carrier': None,
                    'play_phase': 'contest'
                }
            }
            
        # Check position and congestion
        if self._is_congested_area(player.current_position, state):
            # Find better position for kick
            kick_position = self._find_kick_position(player, target, state)
            if kick_position != player.current_position:
                # Move to better position first
                return {
                    'type': 'composite',
                    'primary': 'kick',
                    'setup': {
                        'type': 'movement',
                        'to': kick_position
                    },
                    'player': player,
                    'target': target
                }
        
        # Calculate success chance considering multiple factors
        success_chance = self._calculate_kick_success(
            player, 
            target, 
            state,
            space_score=self._calculate_space_score(player.current_position, state)
        )
        
        if random.random() < success_chance:
            return {
                'type': 'kick',
                'outcome': 'success',
                'player': player,
                'target': target,
                'new_state': {
                    'ball_carrier': target,
                    'play_phase': 'open_play'
                }
            }
        else:
            return {
                'type': 'kick',
                'outcome': 'fail',
                'player': player,
                'new_state': {
                    'ball_carrier': None,
                    'play_phase': 'contest'
                }
            }
        
    def _calculate_space_score(self, position, state):
        """Calculate space availability for contest situations"""
        nearby_players = self.position_manager.get_nearby_players(position, radius=2)  # Smaller radius for contests
        
        space_score = 1.0
        for player in nearby_players:
            distance = self.ground.get_distance(position, player.current_position)
            
            # In contests, we care more about immediate space
            if distance < 1:  # Very close players
                space_score -= 0.3
            elif distance < 2:  # Nearby players
                space_score -= 0.1
                
        return max(0.1, min(1.0, space_score))

    def _calculate_kick_success(self, player, target, state, space_score):
        """Calculate probability of successful kick"""
        base_chance = 0.6  # Base 60% chance
        
        # Player skill factors
        kicking_skill = player.attributes.get('kicking', 50) / 100
        base_chance += kicking_skill * 0.2  # Up to 20% bonus for skill
        
        # Distance factor
        distance = self.ground.get_distance(player.current_position, target.current_position)
        distance_penalty = min(0.3, distance / 100)  # Up to 30% penalty for long kicks
        base_chance -= distance_penalty
        
        # Space and pressure
        base_chance += space_score * 0.15  # Up to 15% bonus for space
        pressure_score = self._calculate_pressure_score(player, state)
        base_chance -= pressure_score * 0.2  # Up to 20% penalty for pressure
        
        # Weather effects
        if hasattr(state, 'weather_system'):
            # Get kick direction based on positions
            kick_direction = self._get_cardinal_direction(
                player.current_position,
                target.current_position
            )
            weather_modifier = state.weather_system.get_kick_modifier(kick_direction, distance)
            base_chance *= weather_modifier
            
        # Fatigue impact
        fatigue_penalty = player.fatigue * 0.15
        base_chance -= fatigue_penalty
        
        return max(0.1, min(0.9, base_chance))        
            
    def _execute_handball(self, player, action, state):
        """Execute a handball action"""
        # Get target
        target = self._select_best_target(action['targets'])
        if not target:
            return {
                'type': 'handball',
                'outcome': 'fail',
                'reason': 'no_target',
                'player': player,
                'new_state': {
                    'ball_carrier': None,
                    'play_phase': 'contest'
                }
            }
            
        # Consider space and pressure
        space_score = self._calculate_space_score(player.current_position, state)
        pressure_score = self._calculate_pressure_score(player, state)
        
        success_chance = self._calculate_handball_success(
            player,
            target,
            state,
            space_score=space_score,
            pressure_score=pressure_score
        )
        
        if random.random() < success_chance:
            return {
                'type': 'handball',
                'outcome': 'success',
                'player': player,
                'target': target,
                'new_state': {
                    'ball_carrier': target,
                    'play_phase': 'open_play'
                }
            }
        else:
            return {
                'type': 'handball',
                'outcome': 'fail',
                'player': player,
                'new_state': {
                    'ball_carrier': None,
                    'play_phase': 'contest'
                }
            }
    def _calculate_handball_success(self, player, target, state, space_score, pressure_score):
        """Calculate probability of successful handball"""
        base_chance = 0.7  # Base 70% chance (handballs generally easier than kicks)
        
        # Player skill factors
        handball_skill = player.attributes.get('handball', 50) / 100
        base_chance += handball_skill * 0.15  # Up to 15% bonus for skill
        
        # Distance is less important for handballs
        distance = self.ground.get_distance(player.current_position, target.current_position)
        distance_penalty = min(0.2, distance / 50)  # Up to 20% penalty for distance
        base_chance -= distance_penalty
        
        # Space and pressure are more important for handballs
        base_chance += space_score * 0.2  # Up to 20% bonus for space
        base_chance -= pressure_score * 0.25  # Up to 25% penalty for pressure

        # Weather effects
        if hasattr(state, 'weather_system'):
            ball_handling_modifier = state.weather_system.effects['ball_handling']
            base_chance *= ball_handling_modifier        
        
        # Fatigue impact
        fatigue_penalty = player.fatigue * 0.1
        base_chance -= fatigue_penalty
        
        return max(0.2, min(0.95, base_chance))        

    def _execute_run(self, player, action, state):
            """Execute a run action"""
            current_pos = player.current_position
            next_position = self.position_manager.get_ideal_position(player, state.play_phase, state)
            
            # Check if bounce needed
            distance_run = self.ground.get_distance(
                player.last_bounce_position or current_pos,
                next_position
            )
            
            if distance_run > 15 and player == state.ball_carrier:  # 15m bounce rule
                return {
                    'type': 'bounce',
                    'player': player,
                    'position': current_pos,
                    'next_position': next_position,
                    'new_state': {
                        'ball_carrier': player,
                        'play_phase': 'bounce'
                    }
                }
                
            # Check for tackle risk
            space_score = self._calculate_space_score(next_position, state)
            if space_score < 0.3 and player == state.ball_carrier:
                return {
                    'type': 'run',
                    'outcome': 'tackled',
                    'player': player,
                    'from': current_pos,
                    'to': current_pos,  # Stay in position when tackled
                    'new_state': {
                        'ball_carrier': None,
                        'play_phase': 'contest'
                    }
                }
                
            return {
                'type': 'run',
                'outcome': 'success',
                'player': player,
                'from': current_pos,
                'to': next_position,
                'new_state': {
                    'ball_carrier': state.ball_carrier,
                    'play_phase': state.play_phase
                }
            }


    def _execute_contest(self, player, action, state):
        """Execute a contest action"""
        contest_position = action.get('position', player.current_position)
        
        # Calculate optimal contest position
        optimal_position = self._calculate_contest_position(contest_position, state)
        space_score = self._calculate_space_score(optimal_position, state)
        
        # Calculate contest success chance
        success_chance = self._calculate_contest_success(
            player,
            action.get('participants', []),
            space_score,
            state
        )
        
        if random.random() < success_chance:
            return {
                'type': 'contest',
                'outcome': 'success',
                'player': player,
                'position': optimal_position,
                'new_state': {
                    'ball_carrier': player,
                    'play_phase': 'open_play'
                }
            }
        
        return {
            'type': 'contest',
            'outcome': 'fail',
            'player': player,
            'position': optimal_position,
            'new_state': {
                'ball_carrier': None,
                'play_phase': 'contest'
            }
        }
    
    def _calculate_contest_position(self, ball_pos, contest_type):
        """Calculate optimal position for specific contest types"""
        if contest_type == 'marking_contest':
            return (ball_pos[0] - 1, ball_pos[1] - 1)
        elif contest_type == 'spoil_opportunity':
            return (ball_pos[0] + 1, ball_pos[1])
        else:  # ground_ball
            return (ball_pos[0], ball_pos[1] + 2)

    def _calculate_contest_success(self, player, participants, space_score, state):
        """Calculate probability of winning a contest"""
        base_chance = 0.4  # Base 40% chance
        
        # Player attributes
        contest_skill = player.attributes.get('contested_possession', 50) / 100
        strength = player.attributes.get('strength', 50) / 100
        base_chance += (contest_skill * 0.25 + strength * 0.15)  # Up to 40% bonus
        
        # Compare against other participants
        if participants:
            avg_opponent_contest = sum(p.attributes.get('contested_possession', 50) 
                                     for p in participants) / len(participants) / 100
            avg_opponent_strength = sum(p.attributes.get('strength', 50) 
                                      for p in participants) / len(participants) / 100
            base_chance -= (avg_opponent_contest * 0.2 + avg_opponent_strength * 0.1)

        # Weather effects
        if hasattr(state, 'weather_system'):
            ground_modifier = state.weather_system.get_ground_movement_modifier()
            base_chance *= ground_modifier            
        
        # Space affects contest probability
        base_chance += space_score * 0.15
        
        # Height advantage in aerial contests
        if state.play_phase == 'aerial_contest':
            height_advantage = self._calculate_height_advantage(player, player.current_position, state)
            base_chance += height_advantage * 0.2
        
        return max(0.1, min(0.9, base_chance))   

    def _execute_mark(self, player, action, state):
        """Execute a mark attempt"""
        mark_position = action.get('position', player.current_position)
        space_score = self._calculate_space_score(mark_position, state)
        
        # Calculate mark success chance
        success_chance = self._calculate_mark_success(
            player,
            mark_position,
            space_score,
            state
        )
        
        if random.random() < success_chance:
            return {
                'type': 'mark',
                'outcome': 'success',
                'player': player,
                'position': mark_position,
                'new_state': {
                    'ball_carrier': player,
                    'play_phase': 'set_shot'
                }
            }
            
        return {
            'type': 'mark',
            'outcome': 'fail',
            'player': player,
            'position': mark_position,
            'new_state': {
                'ball_carrier': None,
                'play_phase': 'contest'
            }
        }

    def _calculate_mark_success(self, player, mark_position, space_score, state):
        """Calculate probability of successful mark"""
        base_chance = 0.5  # Base 50% chance
        
        # Adjust for player attributes
        marking_skill = player.attributes.get('marking', 50) / 100
        base_chance += marking_skill * 0.2  # Up to 20% bonus for skill
        
        # Adjust for space
        base_chance += space_score * 0.1  # Up to 10% bonus for space
        
        # Adjust for height advantage
        height_advantage = self._calculate_height_advantage(player, mark_position, state)
        base_chance += height_advantage * 0.1  # Up to 10% bonus for height
        
        # Adjust for fatigue
        fatigue_penalty = player.fatigue * 0.1  # Up to 10% penalty for fatigue
        base_chance -= fatigue_penalty
        
        # Adjust for weather conditions if available
        if hasattr(state, 'weather_system'):
                marking_modifier = state.weather_system.get_marking_modifier()
                base_chance *= marking_modifier
        
        # Adjust for pressure
        pressure_score = self._calculate_pressure_score(player, state)
        base_chance -= pressure_score * 0.15  # Up to 15% penalty for pressure
        
        return max(0.1, min(0.9, base_chance))  # Clamp between 10% and 90%
        
    def _calculate_height_advantage(self, player, mark_position, state):
        """Calculate height advantage in marking contest"""
        nearby_opponents = [p for p in 
                          self.position_manager.get_nearby_players(mark_position, radius=1)
                          if p.team_id != player.team_id]
        
        if not nearby_opponents:
            return 0.5  # No opposition, medium advantage
            
        # Compare heights with nearby opponents
        player_height = player.attributes.get('height', 180)
        opponent_heights = [opp.attributes.get('height', 180) for opp in nearby_opponents]
        avg_opponent_height = sum(opponent_heights) / len(opponent_heights)
        
        height_diff = player_height - avg_opponent_height
        # Convert height difference to advantage score (-0.5 to 0.5)
        return max(-0.5, min(0.5, height_diff / 20))
    
    def _get_cardinal_direction(self, start_pos, end_pos):
        """Convert two positions into cardinal direction"""
        dx = end_pos[0] - start_pos[0]
        dy = end_pos[1] - start_pos[1]
        
        # Calculate angle
        angle = math.degrees(math.atan2(dy, dx))
        
        # Convert angle to cardinal direction
        if -22.5 <= angle <= 22.5:
            return 'E'
        elif 22.5 < angle <= 67.5:
            return 'NE'
        elif 67.5 < angle <= 112.5:
            return 'N'
        elif 112.5 < angle <= 157.5:
            return 'NW'
        elif angle > 157.5 or angle <= -157.5:
            return 'W'
        elif -157.5 < angle <= -112.5:
            return 'SW'
        elif -112.5 < angle <= -67.5:
            return 'S'
        else:
            return 'SE'

    """    
    def _calculate_weather_penalty(self, weather):
        #Calculate penalty based on weather conditions
        penalties = {
            'rain': 0.1,
            'heavy_rain': 0.2,
            'wind': 0.15,
            'strong_wind': 0.25,
            'clear': 0
        }
        return penalties.get(weather, 0)    
    """
    
    def _execute_tackle(self, player, action, state):
        """Execute a tackle attempt"""
        target = action.get('target')
        if not target:
            return {
                'type': 'tackle',
                'outcome': 'fail',
                'reason': 'no_target',
                'player': player
            }
            
        space_score = self._calculate_space_score(target.current_position, state)
        
        # Calculate tackle success chance
        success_chance = self._calculate_tackle_success(
            player,
            target,
            space_score,
            state
        )
        
        if random.random() < success_chance:
            return {
                'type': 'tackle',
                'outcome': 'success',
                'player': player,
                'target': target,
                'new_state': {
                    'ball_carrier': None,
                    'play_phase': 'contest'
                }
            }
            
        return {
            'type': 'tackle',
            'outcome': 'fail',
            'player': player,
            'target': target,
            'new_state': state  # No change to game state
        }
    
    def _calculate_tackle_success(self, player, target, space_score, state):
        """Calculate probability of successful tackle"""
        base_chance = 0.5  # Base 50% chance
        
        # Player attributes
        tackle_skill = player.attributes.get('tackling', 50) / 100
        strength = player.attributes.get('strength', 50) / 100
        base_chance += (tackle_skill * 0.2 + strength * 0.15)  # Up to 35% bonus
        
        # Target's attributes affect difficulty
        target_agility = target.attributes.get('agility', 50) / 100
        target_strength = target.attributes.get('strength', 50) / 100
        base_chance -= (target_agility * 0.15 + target_strength * 0.1)  # Up to 25% penalty
        
        # Space affects tackle probability
        base_chance += space_score * 0.1  # Up to 10% bonus for space
        
        # Fatigue impacts both players
        fatigue_diff = target.fatigue - player.fatigue
        base_chance += fatigue_diff * 0.1  # Bonus if target more fatigued
        
        return max(0.1, min(0.9, base_chance))    

    def _execute_shepherd(self, player, action, state):
        """Execute a shepherd action"""
        teammate = action.get('teammate')
        opponent = action.get('opponent')
        
        if not teammate or not opponent:
            return {
                'type': 'shepherd',
                'outcome': 'fail',
                'reason': 'invalid_targets',
                'player': player
            }
            
        space_score = self._calculate_space_score(player.current_position, state)
        
        # Calculate shepherd success chance
        success_chance = self._calculate_shepherd_success(
            player,
            teammate,
            opponent,
            space_score,
            state
        )
        
        if random.random() < success_chance:
            return {
                'type': 'shepherd',
                'outcome': 'success',
                'player': player,
                'teammate': teammate,
                'opponent': opponent,
                'new_state': state  # No change to game state
            }
            
        return {
            'type': 'shepherd',
            'outcome': 'fail',
            'player': player,
            'teammate': teammate,
            'opponent': opponent,
            'new_state': state  # No change to game state
        }

    def _calculate_shepherd_success(self, player, teammate, opponent, space_score, state):
        """Calculate probability of successful shepherd"""
        base_chance = 0.6  # Base 60% chance
        
        # Player attributes
        strength = player.attributes.get('strength', 50) / 100
        positioning = player.attributes.get('positioning', 50) / 100
        base_chance += (strength * 0.2 + positioning * 0.15)  # Up to 35% bonus
        
        # Opponent attributes
        opponent_strength = opponent.attributes.get('strength', 50) / 100
        opponent_agility = opponent.attributes.get('agility', 50) / 100
        base_chance -= (opponent_strength * 0.15 + opponent_agility * 0.1)
        
        # Space consideration
        base_chance += space_score * 0.1
        
        # Distance to teammate
        teammate_distance = self.ground.get_distance(player.current_position, teammate.current_position)
        distance_penalty = min(0.2, teammate_distance / 30)  # Up to 20% penalty
        base_chance -= distance_penalty
        
        # Fatigue impact
        fatigue_penalty = player.fatigue * 0.1
        base_chance -= fatigue_penalty
        
        return max(0.2, min(0.9, base_chance))              
            
    def _create_action_combination(self, player, initial_action, state):
        """Create a complex action combination"""
        combination = ActionCombination(initial_action, player, state)
        
        # Add setup actions if needed
        if self._needs_setup(initial_action, state):
            setup_actions = self._generate_setup_actions(player, initial_action, state)
            for setup in setup_actions:
                combination.add_setup(setup)
                
        # Add follow-up actions if appropriate
        if self._should_add_follow_up(initial_action, state):
            follow_ups = self._generate_follow_up_actions(player, initial_action, state)
            for follow_up in follow_ups:
                combination.add_follow_up(follow_up)
                
        return combination
        
    def _needs_setup(self, action, state):
        """Determine if action needs setup actions"""
        if action['type'] == 'kick':
            return self._needs_kick_setup(action, state)
        elif action['type'] == 'mark':
            return self._needs_marking_setup(action, state)
        return False
        
    def _should_add_follow_up(self, action, state):
        """Determine if action should have follow-up actions"""
        if action['type'] == 'gather':
            return True  # Always follow gather with next action
        elif action['type'] == 'mark':
            return True  # Follow mark with play-on or set-shot
        return False
        
    def _generate_setup_actions(self, player, action, state):
        """Generate appropriate setup actions"""
        setup_actions = []
        
        if action['type'] == 'kick':
            # Check if we need to create space
            if self._is_congested_area(player.current_position, state):
                setup_actions.append({
                    'type': 'evade',
                    'direction': self._find_space_direction(player, state)
                })
                
            # Check if we need to bounce
            if self._needs_bounce(player, state):
                setup_actions.append({
                    'type': 'bounce',
                    'position': player.current_position
                })
                
        elif action['type'] == 'mark':
            # Add positioning setup
            setup_actions.append({
                'type': 'position',
                'target': self._calculate_marking_position(action['target'], state)
            })
            
        return setup_actions
        
    def _generate_follow_up_actions(self, player, action, state):
        """Generate appropriate follow-up actions"""
        follow_ups = []
        
        if action['type'] == 'gather':
            # Add immediate disposal option
            best_disposal = self._determine_best_disposal(player, state)
            if best_disposal:
                follow_ups.append(best_disposal)
                
        elif action['type'] == 'mark':
            if self.scoring_engine._is_scoring_position(player.current_position):
                follow_ups.append({
                    'type': 'set_shot',
                    'target': self._calculate_goal_target(player)
                })
            else:
                follow_ups.append({
                    'type': 'play_on',
                    'options': self._get_play_on_options(player, state)
                })
                
        return follow_ups
    
    def _get_ball_carrier_actions(self, player, context):
        """Get possible actions for ball carrier"""
        actions = []
        
        # Get all players in kick range
        kick_targets = self._get_kick_targets(player, context)
        if kick_targets:
            actions.append({
                'type': 'kick',
                'targets': kick_targets,
                'risk': self._calculate_kick_risk(player, kick_targets)
            })
            
        # Get handball options
        handball_targets = self._get_handball_targets(player, context)
        if handball_targets:
            actions.append({
                'type': 'handball',
                'targets': handball_targets,
                'risk': self._calculate_handball_risk(player, handball_targets)
            })
            
        # Check if bounce is needed
        if self._needs_bounce(player, context):
            actions.append({
                'type': 'bounce',
                'risk': self._calculate_bounce_risk(player, context)
            })
            
        return actions
    
    def _score_actions(self, player, possible_actions, state):
        """Score actions considering the specific team tactics"""
        scored_actions = []
        team_tactics = (self.home_team_tactics 
                   if player.team_side == self.home_team.side 
                   else self.away_team_tactics)
        
        for action in possible_actions:
            score = 0
            
            # Base tactical score
            score += self._get_tactical_score(action, state)
            
            # Apply team tactical weighting
            tactical_weight = team_tactics.get_action_weighting(
                action['type'],
                state
            )
            score *= tactical_weight
            
            # Consider defensive line height
            if action['type'] in ['mark', 'run']:
                target_pos = action.get('target_position')
                if target_pos:
                    defensive_line = team_tactics.get_defensive_line_height()
                    if self._is_beyond_defensive_line(target_pos, defensive_line):
                        score *= 0.8  # Reduce score if too far forward
                        
            # Consider offensive width
            if action['type'] in ['run', 'lead']:
                target_pos = action.get('target_position')
                if target_pos:
                    preferred_width = team_tactics.get_offensive_width()
                    if preferred_width == 'wide':
                        # Encourage wide positioning
                        if self._is_central_position(target_pos):
                            score *= 0.8
                    else:
                        # Encourage direct/central positioning
                        if not self._is_central_position(target_pos):
                            score *= 0.8
                            
            scored_actions.append({
                'action': action,
                'score': score
            })
            
        return scored_actions
    
    def _get_tactical_score(self, action, state):
        """Calculate tactical score for an action"""
        base_score = 1.0
        
        # Get current zone and its properties
        current_zone = state.ground.get_zone(action['player'].current_position)
        zone_props = state.ground.get_zone_properties(current_zone)
        
        score_diff = self.get_score_difference(action['player'].team_side)
        
        # Consider game context and zone
        if score_diff < -24:  # Two goals down
            if action['type'] in ['kick', 'run']:
                base_score *= 1.2 * zone_props['risk_factor']  # Scale aggression by zone
        elif score_diff > 24:
            if action['type'] == 'handball':
                base_score *= 1.1  # Encourage possession retention
                
        # Consider time remaining
        if state.time_remaining < 300:  # Last 5 minutes
            if score_diff < 0:
                base_score *= 1.3  # Encourage risk-taking when behind
            else:
                base_score *= 0.8  # Encourage conservative play when ahead
                
        return base_score

    def _select_best_action(self, scored_actions):
        """Select the best action based on scores"""
        if not scored_actions:
            return None
            
        # Add small random variation to prevent predictability
        for action in scored_actions:
            action['score'] *= random.uniform(0.9, 1.1)
            
        return max(scored_actions, key=lambda x: x['score'])['action']
          
    def initiate_recovery(self, player, state, cause):
        """Start a recovery action sequence"""
        recovery = RecoveryAction(player, state, cause)
        self.active_recoveries[player.id] = recovery
        
    def _execute_recovery_action(self, player, recovery_step, state):
        """Execute a recovery action step"""
        # Convert recovery step to executable action
        action = self._convert_recovery_to_action(recovery_step)
        
        # Execute with recovery context
        return self._execute_action(
            player, 
            action, 
            state,
            context={'is_recovery': True}
        )
        
    def _convert_recovery_to_action(self, recovery_step):
        """Convert recovery step to standard action format"""
        return {
            'type': recovery_step['type'],
            'priority': recovery_step['priority'],
            **{k:v for k,v in recovery_step.items() 
               if k not in ['type', 'priority']}
        }
        
    def _handle_action_failure(self, player, action, state):
        """Handle failed actions by initiating recovery if needed"""
        if self._needs_recovery(action):
            self.initiate_recovery(player, state, {
                'type': 'failed_disposal' if action['type'] in ['kick', 'handball']
                else 'default_failure',
                'action': action
            })    

    
class DecisionEngine:
    """Enhanced decision engine with tactical integration"""
    def __init__(self, ground):
        self.ground = ground
        self.decision_history = {}  # Track recent decisions for each player

    def choose_action(self, player, possible_actions, context):
        """Choose best action based on multiple factors"""
        if not possible_actions:
            return None
            
        scored_actions = []
        for action in possible_actions:
            # Calculate comprehensive score
            skill_score = self._calculate_skill_score(player, action)
            tactical_score = self._calculate_tactical_score(player, action, context)
            risk_score = self._calculate_risk_score(action, context)
            reward_score = self._calculate_reward_score(action, context)
            attribute_score = self._calculate_attribute_score(player, action)
            
            # Get tactical weighting from team tactics
            team_tactics = (context.home_team_tactics 
                          if player.team_side == context.home_team.side 
                          else context.away_team_tactics)
            
            tactical_weight = team_tactics.get_action_weighting(
                action['type'],
                context
            )
            
            # Combine scores with weights
            final_score = (
                skill_score * 0.2 +
                tactical_score * 0.3 * tactical_weight +
                risk_score * 0.2 +
                reward_score * 0.2 +
                attribute_score * 0.1
            )
            
            # Consider decision history to avoid repetition
            if self._is_repetitive_action(player, action):
                final_score *= 0.8
                
            scored_actions.append((final_score, action))
            
        # Choose highest scored action with some randomness
        return self._select_action_with_variance(scored_actions)
    
    def _calculate_action_score(self, player, action, context):
        """Calculate action score with tactical considerations"""
        # Base scores
        skill_score = self._calculate_skill_score(player, action)
        tactical_score = self._calculate_tactical_score(player, action, context)
        risk_score = self._calculate_risk_score(player, action, context)
        
        # Get tactical weighting
        tactical_weight = player.team.tactics.get_action_weighting(
            action['type'], 
            context
        )
        
        # Combine scores with tactical weighting
        final_score = (
            skill_score * 0.3 +
            tactical_score * 0.4 * tactical_weight +
            risk_score * 0.3 * tactical_weight
        )
        
        return final_score
    
    def _calculate_risk_score(self, action, context):
        """Calculate risk factor of an action"""
        base_risk = action.get('risk', 0.5)
        
        # Adjust risk based on context
        if context.play_phase == 'contest':
            base_risk *= 1.2  # Higher risk in contests
        elif context.play_phase == 'set_shot':
            base_risk *= 0.8  # Lower risk in set shots
            
        return 1 - base_risk  # Convert risk to score (lower risk = higher score)
    
    def _calculate_reward_score(self, action, context):
        """Calculate potential reward of an action"""
        reward = 0.0
        
        if action['type'] == 'kick':
            reward = self._calculate_kick_reward(action, context)
        elif action['type'] == 'handball':
            reward = self._calculate_handball_reward(action, context)
        elif action['type'] == 'mark':
            reward = self._calculate_mark_reward(action, context)
            
        return reward
    
    def _calculate_kick_reward(self, action, context):
        """Calculate potential reward of a kick"""
        reward = 0.5  # Base reward
        
        if 'target' in action:
            target_pos = action['target'].current_position
            
            # Reward forward movement
            if self._is_forward_movement(context.player_position, target_pos):
                reward += 0.2
                
            # Extra reward for potential scoring opportunity
            if self.scoring_engine._is_scoring_position(target_pos):
                reward += 0.3
                
        return reward
    
    def _calculate_handball_reward(self, action, context):
        """Calculate potential reward of a handball"""
        reward = 0.4  # Lower base reward than kick (shorter range)
        
        if 'target' in action:
            target_pos = action['target'].current_position
            
            # Reward forward movement
            if self._is_forward_movement(context.player_position, target_pos):
                reward += 0.2
                
            # Reward clearing congestion
            if not self._is_congested_area(target_pos):
                reward += 0.2
                
            # Reward maintaining possession
            if self._is_safe_handball(action, context):
                reward += 0.2
                
        return reward
    
    def _calculate_mark_reward(self, action, context):
        """Calculate potential reward of a mark"""
        reward = 0.5  # Base reward
        
        if 'target_position' in action:
            target_pos = action['target_position']
            
            # Reward intercepting forward movement
            if self._is_forward_movement(context.ball_position, target_pos):
                reward += 0.3
                
            # Extra reward for marking in scoring position
            if self.scoring_engine._is_scoring_position(target_pos):
                reward += 0.3
                
            # Reward breaking opposition possession chain
            if context.ball_carrier and context.ball_carrier.team_side != context.player.team_side:
                reward += 0.2
                
        return reward    
        
    def _is_repetitive_action(self, player, action):
        """Check if action is being repeated too often"""
        if player.id not in self.decision_history:
            self.decision_history[player.id] = []
            
        history = self.decision_history[player.id]
        if len(history) >= 3:  # Check last 3 actions
            similar_actions = sum(1 for h in history[-3:] 
                                if h['type'] == action['type'])
            return similar_actions >= 2
            
        return False
        
    def _select_action_with_variance(self, scored_actions):
        """Select action with some randomness to avoid predictability"""
        if not scored_actions:
            return None
            
        # Sort by score
        scored_actions.sort(reverse=True, key=lambda x: x[0])
        
        # Get top 3 actions if available
        top_actions = scored_actions[:3]
        
        # Add some randomness to selection
        weights = [0.7, 0.2, 0.1][:len(top_actions)]  # Probability weights
        selected = random.choices(top_actions, weights=weights, k=1)[0]
        
        return selected[1]  # Return the action
        
    def update_decision_history(self, player, action):
        """Update decision history for a player"""
        if player.id not in self.decision_history:
            self.decision_history[player.id] = []
            
        history = self.decision_history[player.id]
        history.append(action)
        
        # Keep only last 5 actions
        if len(history) > 5:
            history.pop(0)
    
    def _calculate_strategy_score(self, action, team_tactics):
        """Calculate how well action aligns with team strategy"""
        if team_tactics.style == 'attacking':
            return self._score_attacking_action(action)
        elif team_tactics.style == 'defensive':
            return self._score_defensive_action(action)
        else:
            return self._score_balanced_action(action)
    
    def _calculate_attribute_score(self, player, action):
        """Calculate how well action suits player attributes"""
        if action['type'] == 'kick':
            return player.attributes['kicking'] / 100
        elif action['type'] == 'handball':
            return player.attributes['handballing'] / 100
        # Add more action types...
        
        return 0.5  # Default score for unknown actions
    
    def _calculate_tactical_score(self, player, action, context):
        """Calculate how well action fits team tactics"""
        tactics = player.team.tactics
        
        # Base tactical alignment
        if tactics.style == 'attacking':
            return self._score_attacking_action(action, context)
        elif tactics.style == 'defensive':
            return self._score_defensive_action(action, context)
        else:
            return self._score_balanced_action(action, context)
    
    def _score_attacking_action(self, action, context):
        """Score action for attacking tactics"""
        scores = {
            'kick': {
                'forward': 1.2,  # Encourage forward kicks
                'sideways': 0.8,
                'backward': 0.6
            },
            'handball': {
                'forward': 1.1,
                'sideways': 0.9,
                'backward': 0.7
            },
            'run': {
                'forward': 1.3,
                'sideways': 1.0,
                'backward': 0.7
            }
        }
        
        direction = self._determine_action_direction(action, context)
        return scores.get(action['type'], {}).get(direction, 1.0)
    
    def _score_defensive_action(self, action, context):
        """Score action for defensive tactics"""
        scores = {
            'kick': {
                'forward': 0.8,  # More cautious with forward kicks
                'sideways': 1.0,
                'backward': 1.1  # Sometimes better to go backward
            },
            'handball': {
                'forward': 0.9,
                'sideways': 1.1,
                'backward': 1.0
            },
            'run': {
                'forward': 0.7,
                'sideways': 0.9,
                'backward': 0.8
            }
        }
        
        direction = self._determine_action_direction(action, context)
        return scores.get(action['type'], {}).get(direction, 1.0)
    
    def _determine_action_direction(self, action, context):
        """Determine if action is moving forward/sideways/backward"""
        if 'target' not in action:
            return 'forward'  # Default to forward for non-targeted actions
            
        current_pos = context.ball_position
        target_pos = action['target']
        
        # Calculate angle relative to goal
        angle = self._calculate_angle_to_goal(current_pos, target_pos)
        
        if abs(angle) < 45:
            return 'forward'
        elif abs(angle) > 135:
            return 'backward'
        else:
            return 'sideways'
        
    def _calculate_skill_score(self, player, action):
        """Calculate how well player can execute action"""
        if action['type'] == 'kick':
            return player.attributes['kicking'] / 100
        elif action['type'] == 'handball':
            return player.attributes['handballing'] / 100
        elif action['type'] == 'mark':
            return player.attributes['marking'] / 100
        elif action['type'] == 'tackle':
            return player.attributes['tackling'] / 100
        elif action['type'] == 'run':
            return player.attributes['speed'] / 100
        return 0.5  # Default score for unknown actions        

class MovementEngine:
    """Handles all player movement and positioning"""
    def __init__(self, ground, position_manager):
        self.ground = ground
        self.movement_history = {}  # Track player movements
        self.position_manager = position_manager
        self.base_positions = {}  # Align with TeamMovementCoordinator structure
        self.team_side = None
    """
    def setup_team(self, team, team_side):  # Matches how TeamMovementCoordinator gets team_side
        #Setup team-specific base positions
        self.base_positions[team.name] = BasePositions(team_side)    
    """    
    def calculate_new_position(self, player, position, ball_position, play_phase, team_side, team):
        """Calculate new position based on game context and player position"""
        if PlayPhases.is_set_play(play_phase):
            return self._calculate_set_play_position(player, position, ball_position, play_phase, team)
        elif PlayPhases.is_contest(play_phase):
            return self._calculate_contest_position(player, position, ball_position, team_side, team)
        elif PlayPhases.is_open_play(play_phase):
            return self._calculate_open_play_position(player, position, ball_position, team_side)
        else:  # Game status phases
            return self._get_structural_position(position, team, team_side) 
            
    
    def _calculate_contest_position(self, player, position, ball_position, team_side, team):
        """Calculate position during contested situations"""
        if position in ['Ruck', 'RuckRover']:  # Using actual position names
            return ball_position
            
        # Get base position for this role
        base_pos = self.base_positions.base_formation[position]
        
        # Adjust based on contest type
        if self._is_ball_in_zone(ball_position, base_pos['zone']):
            # If ball is in player's zone, get closer
            return self._get_supporting_position(ball_position, position)
        else:
            # Otherwise maintain structure
            return self._get_structural_position(position, team, team_side)
    
    def _calculate_open_play_position(self, player, position, ball_position, team_side):
        """Calculate position during open play"""
        base_pos = self.base_positions.base_formation[position]
        
        # Different movement patterns based on position group
        if base_pos['zone'] == 'forward':
            return self._calculate_forward_movement(position, ball_position, team_side)
        elif base_pos['zone'] == 'defensive':
            return self._calculate_defender_movement(position, ball_position, team_side)
        elif base_pos['zone'] == 'midfield':  # midfield
            return self._calculate_midfield_movement(position, ball_position, team_side)
    
    def _calculate_forward_movement(self, position, ball_position, team_side):
        """Calculate forward movement patterns"""
        base_pos = self.base_positions.base_formation[position]
        x_rel = base_pos['x_rel'] if team_side == 'home' else (1 - base_pos['x_rel'])
        
        # Convert to grid coordinates
        x = int(x_rel * (self.ground.rows - 1))
        y = int(base_pos['y_rel'] * (self.ground.cols - 1))
        
        return (x, y)
    
    def _is_ball_in_zone(self, ball_position, zone):
        """Check if ball is in given zone"""
        # Convert ball position to relative coordinates
        x_rel = ball_position[0] / (self.ground.rows - 1)
        
        # Define zone boundaries
        zones = {
            'forward': (0.7, 1.0),
            'midfield': (0.3, 0.7),
            'defensive': (0.0, 0.3)
        }
        
        zone_bounds = zones.get(zone)
        return zone_bounds[0] <= x_rel <= zone_bounds[1]
    
    def calculate_possible_moves(self, player, current_position, action_type):
        """Calculate possible movement based on player attributes and action"""
        if action_type == 'kick':
            return self._calculate_kick_range(player, current_position)
        elif action_type == 'handball':
            return self._calculate_handball_range(player, current_position)
        elif action_type == 'run':
            return self._calculate_run_range(player, current_position)
    
    def _calculate_kick_range(self, player, position):
        """Calculate possible kick distances based on player attributes"""
        base_range = 45  # Base kick range in meters
        attribute_modifier = (player.attributes['kicking'] / 100) * 15  # Up to 15m bonus
        fatigue_modifier = (100 - player.fatigue) / 100  # Reduce range with fatigue
        
        kick_range = (base_range + attribute_modifier) * fatigue_modifier
        return self.ground.get_cells_in_range(position, kick_range)
    
    def _calculate_handball_range(self, player, position):
        """Calculate possible handball distances"""
        base_range = 15  # Base handball range in meters
        attribute_modifier = (player.attributes['handballing'] / 100) * 5
        fatigue_modifier = (100 - player.fatigue) / 100
        
        handball_range = (base_range + attribute_modifier) * fatigue_modifier
        return self.ground.get_cells_in_range(position, handball_range)
    
    def _get_supporting_position(self, ball_position, position):
        """Get position to support contest"""
        # Calculate position 2-3 meters behind ball position
        x = ball_position[0] - 2 if ball_position[0] > 2 else ball_position[0]
        y = ball_position[1]
        return (x, y)

    def _get_structural_position(self, position, team, team_side):
        """Get base structural position"""
        base_pos = self.base_positions[team.name].base_formation[position]
        return self._convert_relative_to_absolute(base_pos, team_side)
    """
    def _convert_relative_to_absolute(self, base_pos, team_side):
        #Convert relative position to absolute grid coordinates
        x_rel = base_pos['x_rel']
        if team_side == 'away':
            x_rel = 1 - x_rel
            
        x = int(x_rel * (self.ground.rows - 1))
        y = int(base_pos['y_rel'] * (self.ground.cols - 1))
        return (x, y)
    """

    def _convert_relative_to_absolute(self, base_pos, team_side):
        """Convert relative position to absolute grid coordinates"""
        x_rel = base_pos['x_rel']
        y_rel = base_pos['y_rel']
        
        # Flip x-coordinate for away team
        if team_side == 'away':
            x_rel = 1 - x_rel
            
        # Convert to grid coordinates
        x = int(x_rel * (self.ground.rows - 1))  # Use rows instead of length
        y = int(y_rel * (self.ground.cols - 1))  # Use cols instead of width
        
        # Ensure within grid bounds
        x = min(max(0, x), self.ground.rows - 1)
        y = min(max(0, y), self.ground.cols - 1)
        
        return (x, y) 

    def _calculate_defender_movement(self, position, ball_position, team_side):
        """Calculate defender movement patterns"""
        base_pos = self.base_positions[team_side].base_formation[position]
        x_rel = base_pos['x_rel'] if team_side == 'home' else (1 - base_pos['x_rel'])
        
        # Convert to grid coordinates
        x = int(x_rel * (self.ground.rows - 1))
        y = int(base_pos['y_rel'] * (self.ground.cols - 1))
        
        return (x, y)

    def _calculate_midfield_movement(self, position, ball_position, team_side):
        """Calculate midfield movement patterns"""
        base_pos = self.base_positions[team_side].base_formation[position]
        x_rel = base_pos['x_rel'] if team_side == 'home' else (1 - base_pos['x_rel'])
        
        # Convert to grid coordinates
        x = int(x_rel * (self.ground.rows - 1))
        y = int(base_pos['y_rel'] * (self.ground.cols - 1))
        
        return (x, y)

    def _calculate_run_range(self, player, current_position):
        """Calculate possible run distances"""
        base_range = 10  # Base run range in meters
        speed_modifier = (player.attributes['speed'] / 100) * 5
        fatigue_modifier = (100 - player.fatigue) / 100
        
        run_range = (base_range + speed_modifier) * fatigue_modifier
        return self.ground.get_cells_in_range(current_position, run_range)

    def _calculate_set_play_position(self, player, position, ball_position, play_phase, team):
        """Calculate position during set plays"""
        if play_phase == 'set_shot':
            # Other players clear space
            return self._get_structural_position(position, team)
        else:  # ball_up, kick_in, free_kick, boundary_throw
            # Get into position based on set play type
            return self._get_supporting_position(ball_position, position)
    
    def record_movement(self, player, from_pos, to_pos, timestamp, quarter):
        """Record player movement for analysis and visualization"""
        if player.id not in self.movement_history:
            self.movement_history[player.id] = []
            
        self.movement_history[player.id].append({
            'from': {
                'x': from_pos[0] * self.ground.grid_size,
                'y': from_pos[1] * self.ground.grid_size
            },
            'to': {
                'x': to_pos[0] * self.ground.grid_size,
                'y': to_pos[1] * self.ground.grid_size
            },
            'time': timestamp,
            'quarter': quarter
        })
    
    def get_player_movement_data(self, player_id, quarter=None):
        """Get movement data for visualization"""
        movements = self.movement_history.get(player_id, [])
        if quarter:
            movements = [m for m in movements if m['quarter'] == quarter]
        return movements
    


class MovementPattern:
    """Defines sophisticated movement patterns for players"""
    def __init__(self, ground, position_manager, player=None):
        print(f"Creating new MovementPattern instance for player: {player.name if player else 'None'}")
        # Base instance attributes
        self.ground = ground
        self.position_manager = position_manager
        self.player_patterns = {}  # Store patterns for each player 
        
        # Player-specific instance attributes
        self.player = player
        self.current_pattern = None
        self.current_phase = None
        self.pattern_progress = 0
        self.transition_state = None  # Tracks if we're mid-transition
 

    def __call__(self, ground, position_manager, player):
        """Get or create a movement pattern for a specific player"""
        if player.id not in self.player_patterns:
            print(f"Creating new pattern for player {player.name}")
            new_pattern = MovementPattern(ground, position_manager, player)
            self.player_patterns[player.id] = new_pattern
        return self.player_patterns[player.id]

    def __str__(self):
        """String representation of MovementPattern"""
        # If this is the base instance (no player)
        if not self.player:
            sys.exit()
            return f"MovementPattern(base_instance with {len(self.player_patterns)} players)"
        
        # If this is a player-specific instance
        return (f"player={self.player.name}, phase={self.current_phase}, pattern={self.current_pattern}, progress={self.pattern_progress}")       
        
    def calculate_movement(self, context):
        """Calculate movement based on current pattern and context"""
        if self.should_start_new_pattern(context):
            self.start_new_pattern(context)
            
        return self.get_next_movement()
    
    def should_start_new_pattern(self, context):
        """Determine if player should start a new movement pattern"""
        if not self.current_pattern:
            print(f"No current pattern for player {self.player.id} and player {self.player.name}")
            return True
            
        # Check if current pattern is complete
        if self.pattern_progress >= len(self.current_pattern):
            print(f"Current pattern {self.current_pattern} is complete for player {self.player.id}")
            return True
            
        # Check if context has significantly changed
        if self.pattern_invalidated_by_context(context):
            print(f"Context has significantly changed for player {self.player.id}")
            return True
        print(f"No significant context change for player {self.player.id}")    
        return False
    
    """
    def start_new_pattern(self, context):
       #Start a new movement pattern based on context
        
        patterns = {
            

            'fb_intercept': self._full_back_intercept_pattern,
            'fb_marking': self._full_back_marking_pattern,
            'fb_ground': self._full_back_ground_pattern,
            'fb_cover': self._full_back_cover_pattern,
            'fb_rebound': self._full_back_rebound_pattern,
            'fb_link': self._full_back_link_pattern
        }
        




        pattern_type = self._select_pattern_type(context)
        self.current_pattern = patterns[pattern_type](context)
        self.pattern_progress = 0

        if pattern_type == 'fb_intercept':
            return self._full_back_intercept_pattern(context)
        elif pattern_type == 'fb_marking':
            return self._full_back_marking_pattern(context)
        # etc...
    """

    def start_new_pattern(self, context):
        """Start a new movement pattern based on context"""
        player_position = context['player_position']
        print(f"Starting new pattern for player {self.player.name}")
        pattern_type = self._select_pattern_type(context)
        print(f"Pattern type: {pattern_type}")
        if pattern_type:
            pattern_func = self.position_manager.position_patterns[player_position][pattern_type]
            self.current_pattern = pattern_func(context, self.player)  # Execute pattern function
            self.pattern_progress = 0
            
            # Get initial position from pattern phases if available
            if isinstance(self.current_pattern, dict) and 'phases' in self.current_pattern:
                for phase in self.current_pattern['phases']:
                    if 'target' in phase:
                        return phase['target']
            
            # If no valid position found, return current position
            return self.player.current_position
        return None

    def apply_team_coordination(self, coord_movement, team_instructions, role):
        """Apply team coordination to movement pattern"""
        self.current_role = role
        self.team_instructions = team_instructions
        
        if coord_movement:
            #print(f"Coordination movement: {coord_movement}")
            # Modify current pattern based on coordination
            self._adjust_pattern_for_coordination(coord_movement)
            
    def _adjust_pattern_for_coordination(self, coord_movement):
        """Adjust movement pattern based on team coordination"""
        print(f"Coordination movement: {coord_movement}")
        
        return
            



    """
    def _generate_lead_pattern(self, context):
        #Generate a leading pattern towards goal
        start_pos = self.player.current_position
        pattern = []
        
        # Initial burst towards space
        lead_direction = self._calculate_lead_direction(context)
        for i in range(3):  # 3-step lead
            next_pos = (
                start_pos[0] + lead_direction[0] * (i + 1),
                start_pos[1] + lead_direction[1] * (i + 1)
            )
            pattern.append(next_pos)
        
        return pattern
    
    def _generate_spread_pattern(self, context):
        #Generate a pattern to spread the defense
        pattern = []
        start_pos = self.player.current_position
        
        # Move wide first
        wide_pos = (start_pos[0], start_pos[1] + 3)
        pattern.append(wide_pos)
        
        # Then forward
        forward_pos = (wide_pos[0] + 2, wide_pos[1])
        pattern.append(forward_pos)
        
        return pattern        
    
    def _generate_block_pattern(self, context):
        #Generate a blocking pattern
        pattern = []
        start_pos = self.player.current_position
        ball_carrier = context.ball_carrier
        
        if ball_carrier:
            # Calculate blocking position between ball carrier and their likely target
            likely_target = self._predict_target(ball_carrier, context)
            block_pos = self._calculate_block_position(
                ball_carrier.current_position,
                likely_target
            )
            pattern.append(block_pos)
            
            # Add follow-up position to maintain block
            follow_up = self._adjust_block_position(block_pos, context)
            pattern.append(follow_up)
            
        return pattern
    
    def _generate_switch_pattern(self, context):
        #Generate a switch of play pattern
        pattern = []
        start_pos = self.player.current_position
        
        # First move backwards to create space
        back_pos = (
            start_pos[0] - 2,
            start_pos[1]
        )
        pattern.append(back_pos)
        
        # Then move wide to opposite side
        switch_direction = 1 if start_pos[1] < self.ground.width/2 else -1
        wide_pos = (
            back_pos[0],
            back_pos[1] + (4 * switch_direction)
        )
        pattern.append(wide_pos)
        
        # Finally move forward
        forward_pos = (
            wide_pos[0] + 3,
            wide_pos[1]
        )
        pattern.append(forward_pos)
        
        return pattern
    """

        
    def _handle_movement(self, player, target_pos, state):
        """Handle player movement to target position"""
        # Check for obstacles
        nearby_players = self.position_manager.get_nearby_players(target_pos)
        
        if nearby_players:
            # Adjust target position if needed
            target_pos = self._adjust_for_congestion(target_pos, nearby_players)
            
        # Update player position
        self.position_manager.update_player_position(player, target_pos)
    """    
    def _perform_action(self, player, action_type, state):
        #Perform specific action based on type and context
        action_handlers = {
            'kick': self._handle_kick_action,
            'handball': self._handle_handball_action,
            'mark': self._handle_mark_action,
            'tackle': self._handle_tackle_action,
            'shepherd': self._handle_shepherd_action,
            'contest': self._handle_contest_action
        }
        
        handler = action_handlers.get(action_type)
        if handler:
            return handler(player, state)
        return None
    """    
    def _handle_kick_action(self, player, state):
        """Handle kicking action"""
        # Get potential targets based on position patterns
        targets = self._get_potential_targets(player, state)
        
        # Choose best target
        best_target = self.decision_engine.choose_kick_target(
            player, 
            targets, 
            state
        )
        
        if best_target:
            return {
                'type': 'kick',
                'target': best_target,
                'success_chance': self._calculate_kick_success(
                    player, 
                    best_target, 
                    state
                )
            }
        return None
        
    def _get_potential_targets(self, player, state):
        """Get potential targets based on position patterns"""
        teammates = [p for p in state.all_players 
                    if p.team_id == player.team_id and p != player]
        
        targets = []
        for teammate in teammates:
            ideal_pos = self.position_manager.get_ideal_position(
                teammate,
                state.play_phase,
                state
            )
            targets.append({
                'player': teammate,
                'position': ideal_pos,
                'rating': self._rate_target_position(
                    player,
                    teammate,
                    ideal_pos,
                    state
                )
            })
            
        return sorted(targets, key=lambda x: x['rating'], reverse=True)    

    
    def transition_phase(self, new_phase, context):
        """Handle transition between game phases"""
        if new_phase == self.current_phase:
            return
            
        self.transition_state = {
            'from_phase': self.current_phase,
            'to_phase': new_phase,
            'progress': 0
        }
        
        # Get transition pattern based on phase change
        transition_pattern = self._get_transition_pattern(new_phase, context)
        if transition_pattern:
            self.current_pattern = transition_pattern
            self.pattern_progress = 0
            
        self.current_phase = new_phase
        
    def _get_transition_pattern(self, new_phase, context):
        """Get appropriate transition pattern based on phase change"""
        transitions = {
            ('open_play', 'contest'): self._transition_to_contest,
            ('contest', 'open_play'): self._transition_to_open_play,
            ('open_play', 'set_shot'): self._transition_to_set_shot,
            ('contest', 'set_shot'): self._transition_to_set_shot,
        }
        
        if not self.current_phase:  # Initial phase
            return self._initialize_phase_pattern(new_phase, context)
            
        transition_key = (self.current_phase, new_phase)
        transition_func = transitions.get(transition_key)
        
        if transition_func:
            return transition_func(context)
        return None
        
    def _transition_to_contest(self, context):
        """Transition pattern when moving to contest phase"""
        ball_pos = context.ball_position
        current_pos = self.player.current_position
        
        # Full Back specific patterns
        if self.player.position == 'FB':
            if self._is_dangerous_position(ball_pos):
                return self._full_back_intercept_pattern(context)
            else:
                return self._full_back_marking_pattern(context)
        
        # Existing logic for other positions
        elif self.player.position in ['RK', 'RR']:  # Ruckmen
            return self._generate_ruck_contest_pattern(ball_pos)
        elif self.ground.get_distance(current_pos, ball_pos) < 15:
            return self._generate_close_contest_pattern(ball_pos)
        else:
            return self._generate_support_contest_pattern(ball_pos)

    def _transition_to_open_play(self, context):
        """Transition pattern when moving to open play"""
        ball_carrier = context.ball_carrier
        
        # Full Back specific patterns
        if self.player.position == 'FB':
            if ball_carrier and ball_carrier.team_side != self.player.team_side:
                return self._full_back_cover_pattern(context)
            else:
                return self._full_back_link_pattern(context)
        
        # Existing logic for other positions
        elif ball_carrier:
            if ball_carrier.team_side == self.player.team_side:
                return self._generate_support_pattern(ball_carrier)
            else:
                return self._generate_defensive_pattern(ball_carrier)
        return self._generate_spread_pattern(context)
        
    def _transition_to_set_shot(self, context):
        """Transition pattern when moving to set shot phase"""
        kicker = context.ball_carrier
        
        if kicker == self.player:
            return self._generate_set_shot_pattern()
        elif kicker.team_side == self.player.team_side:
            return self._generate_set_shot_support_pattern(kicker)
        else:
            return self._generate_set_shot_defense_pattern(kicker)
            
    def _generate_ruck_contest_pattern(self, ball_pos):
        """Generate pattern for ruckmen entering contest"""
        return [{
            'type': 'direct_move',
            'position': ball_pos,
            'priority': 'high'
        }]
        
    def _generate_close_contest_pattern(self, ball_pos):
        """Generate pattern for players close to contest"""
        return [
            {
                'type': 'approach',
                'target': ball_pos,
                'angle': self._calculate_optimal_approach_angle(ball_pos)
            },
            {
                'type': 'prepare',
                'style': 'contest'
            }
        ]
        
    def _generate_support_contest_pattern(self, ball_pos):
        """Generate pattern for supporting players in contest"""
        optimal_pos = self._calculate_support_position(ball_pos)
        return [
            {
                'type': 'move',
                'position': optimal_pos,
                'style': 'support'
            }
        ]    
    
    def _select_pattern_type(self, context):
        """Select appropriate pattern based on context"""
        #print(f"context: {context}")
        # Get current tactical situation
        ball_pos = context['ball_position']
        ball_carrier = context['ball_carrier']
        phase = context['phase']
        #self.current_phase = phase
        player_position = context['player_position']
        team_side = context['team_side']
        print(f"Phase {phase}")
        # Get position-specific patterns
        #position_patterns = self.position_manager._initialize_position_patterns()[self.player.position]
        #current_patterns = position_patterns.get(self.player.position, {})
        current_patterns = self.position_manager.position_patterns.get(player_position, {})
        #print(f"Current patterns: {current_patterns} for player {self.player.name} and position {player_position} and team side {team_side}")


        # Select pattern based on phase and situation
        if phase in ['ball_up', 'boundary_throw_in', 'Centre_Bounce']:
            return 'stoppage_contest'
        elif phase == 'marking_contest':
            return 'marking_contest'
        elif phase == 'ground_ball':
            return 'ground_contest'
        elif phase == 'open_play':
            if ball_carrier and ball_carrier.team_side == team_side:
                # Offensive patterns
                if player_position.startswith(('F', 'HF')):  # Forward positions
                    return 'lead_pattern'
                elif player_position.startswith('H'):  # Half positions
                    return 'link_up'
                else:
                    return 'spread'
            else:
                # Defensive patterns
                return 'defensive_press'
                
        #return 'stoppage_contest'  # Default pattern
    
    def _calculate_lead_direction(self, context):
        """Calculate optimal leading direction based on context"""
        ball_pos = context.ball_position
        goal_pos = self.ground.get_goal_position(self.player.team_side)
        current_pos = self.player.current_position
        
        # Consider space, opponents, and teammates
        space_score = self._calculate_space_score(current_pos, context)
        opponent_pressure = self._calculate_opponent_pressure()
        
        # Combine factors to determine optimal direction
        direction = self._optimize_direction(
            space_score,
            opponent_pressure,
            ball_pos,
            goal_pos
        )
        
        return direction
    
    def _calculate_block_position(self, source_pos, target_pos):
        """Calculate optimal blocking position"""
        # Find midpoint between source and target
        mid_x = (source_pos[0] + target_pos[0]) / 2
        mid_y = (source_pos[1] + target_pos[1]) / 2
        
        # Adjust based on tactical considerations
        return (mid_x, mid_y)
        
    def _predict_target(self, ball_carrier, context):
        """Predict likely target of ball carrier"""
        # Get possible targets
        targets = self._get_potential_targets(ball_carrier, context)
        if not targets:
            return self.ground.get_goal_position(ball_carrier.team_side)
            
        # Return most likely target based on position and context
        return max(targets, key=lambda t: self._calculate_target_likelihood(t, context))

    def _is_congested_area(self, position):
        """Check if an area is congested with players"""
        nearby_players = self.ground.get_players_in_radius(position, radius=15)  # 15m radius
        zone = self.ground.get_zone(position)
        
        # Different congestion thresholds based on zone
        thresholds = {
            'forward_center': 5,
            'forward_left': 4,
            'forward_right': 4,
            'midfield_center': 6,
            'midfield_left': 4,
            'midfield_right': 4,
            'defensive_center': 5,
            'defensive_left': 4,
            'defensive_right': 4
        }
        
        return len(nearby_players) > thresholds.get(zone, 4)
    
    def _is_dangerous_position(self, position):
        """Determine if position is dangerous for defending team"""
        zone = self.ground.get_zone(position)
        attacking_goal = self.ground.get_goal_position(
            'away' if self.player.team_side == 'home' else 'home'
        )
        
        # Immediate danger in forward zones
        if zone.startswith('forward'):
            return True
            
        # Calculate distance to goal
        distance = self.ground.get_distance(position, attacking_goal)
        
        # Consider position dangerous if within 40m of goal or in certain zones
        return distance < 40 or zone in ['midfield_center', 'forward_pocket_left', 'forward_pocket_right']
    
    def _calculate_space_score(self, position, state):
        """Calculate space availability around position"""
        zone = self.ground.get_zone(position)
        nearby_players = self.ground.get_players_in_radius(position, radius=25)  # 25m radius
        
        space_score = 1.0
        for player in nearby_players:
            distance = self.ground.get_distance(position, player.current_position)
            # Adjust space reduction based on zone
            zone_multiplier = {
                'forward_center': 1.2,
                'midfield_center': 1.0,
                'defensive_center': 0.8,
                'forward_pocket_left': 1.3,
                'forward_pocket_right': 1.3
            }.get(zone, 1.0)
            
            space_score -= (1 / distance) * 0.1 * zone_multiplier
            
        return max(0.1, min(1.0, space_score))
    
    def _calculate_pressure_score(self, player, state):
        """Calculate pressure on player"""
        nearby_opponents = [p for p in 
                          self.position_manager.get_nearby_players(player.current_position, radius=2)
                          if p.team_id != player.team_id]
        pressure_score = len(nearby_opponents) * 0.2
        return min(1.0, pressure_score)
        
    
    def _calculate_opponent_pressure(self):
        """Calculate pressure from opposing players"""
        current_pos = self.player.current_position
        opponents = [p for p in self.ground.get_players_in_radius(current_pos, radius=5)
                    if p.team_side != self.player.team_side]
        
        pressure = 0
        for opponent in opponents:
            distance = self._calculate_distance(current_pos, opponent.current_position)
            # More pressure from closer opponents
            pressure += 1 / (distance + 1)
            
        return min(1.0, pressure)
    
    def _optimize_direction(self, space_score, opponent_pressure, ball_pos, goal_pos):
        """Calculate optimal direction considering all factors"""
        current_pos = self.player.current_position
        #ground_zone = self.ground.get_zone(ball_pos)
        current_zone = self.ground.get_zone(ball_pos)
        #current_zone = state.get_field_position_zone(current_pos)
        
        # Get base direction towards goal
        goal_direction = self._calculate_direction(current_pos, goal_pos)
        
        # Get direction away from pressure
        pressure_direction = self._calculate_pressure_avoidance(opponent_pressure)
        
        # Get direction towards space
        space_direction = self._calculate_space_direction(space_score)
        
        # Adjust weights based on zone and context
        weights = self._get_zone_based_weights(current_zone)
        
        final_direction = (
            goal_direction[0] * weights['goal'] + 
            pressure_direction[0] * weights['pressure'] * opponent_pressure +
            space_direction[0] * weights['space'] * space_score,
            goal_direction[1] * weights['goal'] + 
            pressure_direction[1] * weights['pressure'] * opponent_pressure +
            space_direction[1] * weights['space'] * space_score
        )
        
        return self._normalize_direction(final_direction)
    
    def _calculate_direction(self, from_pos, to_pos):
        """Calculate normalized direction vector between positions"""
        dx = to_pos[0] - from_pos[0]
        dy = to_pos[1] - from_pos[1]
        magnitude = (dx**2 + dy**2)**0.5
        
        if magnitude > 0:
            return (dx/magnitude, dy/magnitude)
        return (0, 0)
    
    def _calculate_distance(self, pos1, pos2):
        """Calculate distance between two positions"""
        return ((pos1[0] - pos2[0])**2 + (pos1[1] - pos2[1])**2)**0.5
    
    def _calculate_pressure_avoidance(self, opponent_pressure):
        """Calculate direction to avoid pressure"""
        current_pos = self.player.current_position
        opponents = [p for p in self.ground.get_players_in_radius(current_pos, radius=5)
                    if p.team_side != self.player.team_side]
        
        if not opponents:
            return (0, 0)
            
        # Calculate weighted average direction away from opponents
        dx, dy = 0, 0
        for opponent in opponents:
            direction = self._calculate_direction(opponent.current_position, current_pos)
            distance = self._calculate_distance(current_pos, opponent.current_position)
            weight = 1 / (distance + 1)
            dx += direction[0] * weight
            dy += direction[1] * weight
            
        magnitude = (dx**2 + dy**2)**0.5
        if magnitude > 0:
            return (dx/magnitude, dy/magnitude)
        return (0, 0)
    
    def _calculate_space_direction(self, space_score):
        """Calculate direction towards available space"""
        current_pos = self.player.current_position
        
        # Check space in 8 directions
        directions = [
            (1, 0), (1, 1), (0, 1), (-1, 1),
            (-1, 0), (-1, -1), (0, -1), (1, -1)
        ]
        
        best_direction = (0, 0)
        best_space = 0
        
        for direction in directions:
            check_pos = (
                current_pos[0] + direction[0] * 3,
                current_pos[1] + direction[1] * 3
            )
            
            if self.ground.is_valid_position(check_pos):
                space = self._calculate_space_score_at_position(check_pos)
                if space > best_space:
                    best_space = space
                    best_direction = direction
                    
        return best_direction
    
    def _calculate_space_score_at_position(self, position):
        """Calculate space score at a specific position"""
        nearby_players = self.ground.get_players_in_radius(position, radius=3)
        
        space_score = 1.0
        for player in nearby_players:
            distance = self._calculate_distance(position, player.current_position)
            space_score -= (1 / distance) * 0.1
            
        return max(0.1, min(1.0, space_score))        

    def get_next_movement(self):
        """Get next position in current pattern"""
        print(f"Current pattern: {self.current_pattern}")
        if not self.current_pattern:
            return self.player.current_position
            
        # If we have phases, get the next position from the appropriate phase
        if isinstance(self.current_pattern, dict) and 'phases' in self.current_pattern:
            phases = self.current_pattern['phases']
            if self.pattern_progress < len(phases):
                phase = phases[self.pattern_progress]
                self.pattern_progress += 1
                print(f"Phase: {phase} and returning target: {phase.get('target', self.player.current_position)}")
                return phase.get('target', self.player.current_position)
        print(f"Returning current position: {self.player.current_position}")        
        return self.player.current_position
    
    """
    def get_next_movement(self):
    #Get next position in current pattern
    if not self.pattern_positions:
        return self.player.current_position
        
    if self.pattern_progress < len(self.pattern_positions):
        next_pos = self.pattern_positions[self.pattern_progress]
        self.pattern_progress += 1
        return next_pos
    return self.player.current_position
    """
    
    def _get_zone_based_weights(self, zone):
        """Get directional weights based on field zone"""
        weights = {
            'forward_center': {'goal': 0.6, 'pressure': 0.2, 'space': 0.2},
            'forward_left': {'goal': 0.5, 'pressure': 0.2, 'space': 0.3},
            'forward_right': {'goal': 0.5, 'pressure': 0.2, 'space': 0.3},
            'forward_pocket_left': {'goal': 0.7, 'pressure': 0.2, 'space': 0.1},
            'forward_pocket_right': {'goal': 0.7, 'pressure': 0.2, 'space': 0.1},
            'midfield_center': {'goal': 0.4, 'pressure': 0.3, 'space': 0.3},
            'midfield_left': {'goal': 0.3, 'pressure': 0.3, 'space': 0.4},
            'midfield_right': {'goal': 0.3, 'pressure': 0.3, 'space': 0.4},
            'defensive_center': {'goal': 0.3, 'pressure': 0.4, 'space': 0.3},
            'defensive_left': {'goal': 0.2, 'pressure': 0.4, 'space': 0.4},
            'defensive_right': {'goal': 0.2, 'pressure': 0.4, 'space': 0.4}
        }
        return weights.get(zone, {'goal': 0.4, 'pressure': 0.3, 'space': 0.3})
    
    def _normalize_direction(self, direction):
        """Normalize a direction vector"""
        x, y = direction
        magnitude = (x**2 + y**2)**0.5
        if magnitude > 0:
            return (x/magnitude, y/magnitude)
        return (0, 0)

    def generate_opposition_response(self, state, opposition_movement):
        """Generate response pattern to opposition movement"""
        if not opposition_movement:
            return None
            
        response_type = self._determine_response_type(
            opposition_movement,
            state
        )
        
        responses = {
            'mark': self._generate_marking_response,
            'intercept': self._generate_intercept_response,
            'zone': self._generate_zone_response,
            'press': self._generate_press_response,
            'delay': self._generate_delay_response
        }
        
        response_func = responses.get(response_type)
        if response_func:
            return response_func(opposition_movement, state)
            
    def _determine_response_type(self, opposition_movement, state):
        """Determine appropriate response to opposition movement"""
        # Get relevant context
        ball_pos = state['ball_position']
        opp_pos = opposition_movement['target']
        distance_to_ball = self.ground.get_distance(self.player.current_position, ball_pos)
        
        # Check if we're in marking position
        if self._should_mark_player(opposition_movement):
            return 'mark'
            
        # Check if intercept is possible
        elif self._can_intercept(opposition_movement, state):
            return 'intercept'
            
        # Check if we should maintain zone
        elif self._should_maintain_zone(state):
            return 'zone'
            
        # Check if we should press
        elif self._should_press(opposition_movement, state):
            return 'press'
            
        # Default to delay response
        return 'delay'
        
    def _generate_marking_response(self, opposition_movement, state):
        """Generate pattern to mark opposition player"""
        target_pos = opposition_movement['target']
        marking_distance = 2  # 2 meters
        
        return {
            'type': 'mark',
            'target_position': self._calculate_marking_position(
                target_pos,
                marking_distance,
                state
            ),
            'priority': 'high' if self._is_dangerous_position(target_pos) else 'medium'
        }
        
    def _generate_intercept_response(self, opposition_movement, state):
        """Generate pattern to intercept opposition movement"""
        return {
            'type': 'intercept',
            'target_position': self._calculate_intercept_position(
                opposition_movement,
                state
            ),
            'timing': self._calculate_intercept_timing(opposition_movement)
        }
        
    def _generate_zone_response(self, opposition_movement, state):
        """Generate pattern to maintain defensive zone"""
        zone_pos = self._calculate_zone_position(state)
        return {
            'type': 'zone',
            'target_position': zone_pos,
            'orientation': self._calculate_defensive_orientation(
                zone_pos,
                opposition_movement
            )
        }
        
    def _generate_press_response(self, opposition_movement, state):
        """Generate pattern to press opposition"""
        return {
            'type': 'press',
            'target_position': self._calculate_press_position(
                opposition_movement,
                state
            ),
            'intensity': self._determine_press_intensity(state)
        }
        
    def _generate_delay_response(self, opposition_movement, state):
        """Generate pattern to delay opposition progress"""
        return {
            'type': 'delay',
            'target_position': self._calculate_delay_position(
                opposition_movement,
                state
            ),
            'coverage': self._calculate_delay_coverage(state)
        }
        
    def _should_mark_player(self, opposition_movement):
        """Determine if we should mark this player"""
        if not self.team_instructions:
            return False
            
        defensive_setup = self.team_instructions['defensive_setup']
        if defensive_setup['style'] == 'man_on_man':
            return opposition_movement['player_id'] in defensive_setup['matchups'].get(
                self.player.id, []
            )
        return False
        
    def _can_intercept(self, opposition_movement, state):
        """Determine if intercept is possible"""
        intercept_pos = self._calculate_intercept_position(
            opposition_movement,
            state
        )
        if not intercept_pos:
            return False
            
        # Check if we can reach intercept position in time
        time_to_intercept = self._calculate_time_to_position(
            self.player.current_position,
            intercept_pos
        )
        return time_to_intercept < opposition_movement.get('estimated_time', float('inf'))
        
    def _should_maintain_zone(self, state):
        """Determine if we should maintain zoning position"""
        if not self.team_instructions:
            return True  # Default to zone if no instructions
            
        return (
            self.team_instructions['defensive_setup']['style'] == 'zone' and
            self.player.id in self.team_instructions['defensive_setup']['zones']
        )    



class TeamMovementCoordinator:
    """Coordinates movement patterns between teammates"""
    def __init__(self, team, ground, position_manager, team_side):
        self.team = team
        #print(f"TeamMovementCoordinator initialized for {self.team}")
        self.ground = ground
        self.team_side = team_side
        self.position_manager = position_manager or PositionManager(ground)
        self.player_positions = {}  # Store player positions
        self.player_roles = {}    # Current tactical roles
        self.base_positions = {}  # team_id -> BasePositions
        self.team_tactics = {}

    """
    def setup_player_positions(self, team_players):
        #Setup player positions from team_players dictionary
        for position, player in team_players.items():
            self.player_positions[player.id] = position
            # Define role based on position
            self.player_roles[player.id] = self._get_role_from_position(position)
    """
    

    def setup_team(self, team, team_players, team_tactics):
        #Setup team-specific components
        self.base_positions[team.name] = BasePositions(self.team_side)  # Using team.name instead of team.id
        self.team_tactics[team.name] = TeamTactics(team_tactics)
        print(f"Team tactics: {team_tactics} for team {team.name}")
        # Initialize player positions
        for position, player in team_players.items():
            self.player_positions[player.id] = position
            print(f"Player position: {position} for {player.id} and {player.name}")
            self.player_roles[player.id] = self._get_role_from_position(position)
            base_pos = self.base_positions[team.name].base_formation[position]  # Changed to base_formation
            initial_pos = self._convert_relative_to_absolute(base_pos, self.team_side)
            self.position_manager.update_player_position(player, initial_pos)
        
    def _convert_relative_to_absolute(self, base_pos, team_side):
        """Convert relative position to absolute grid coordinates"""
        x_rel = base_pos['x_rel']
        y_rel = base_pos['y_rel']
        
        # Flip x-coordinate for away team
        if team_side == 'away':
            x_rel = 1 - x_rel
            
        # Convert to grid coordinates
        x = int(x_rel * (self.ground.rows - 1))  # Use rows instead of length
        y = int(y_rel * (self.ground.cols - 1))  # Use cols instead of width
        
        # Ensure within grid bounds
        x = min(max(0, x), self.ground.rows - 1)
        y = min(max(0, y), self.ground.cols - 1)
        
        return (x, y)                   
            
    def _get_role_from_position(self, position):
        """Convert AFL position to role definition"""
        # Forward positions
        if position in ['FF', 'CHF', 'LHF', 'RHF', 'RF', 'LF']:
            return 'forward'
        # Midfield positions
        elif position in ['Center', 'RWing', 'LWing','RuckRover', 'Rover']:
            return 'midfielder'
        # Defensive positions
        elif position in ['FB', 'CHB', 'LHB', 'RHB', 'RB', 'LB']:
            return 'defender'
        # Ruck
        elif position in ['Ruck']:
            return 'ruck'
        #return 'general'  # Default role        

  
    
    """        
    def coordinate_movements(self, state):
        #Coordinate team movements based on game state
        # Update tactical roles based on situation
        self._update_player_roles(state)
        
        # Get coordinated movements for each player
        coordinated_moves = {}
        
        if state.play_phase == 'open_play':
            coordinated_moves = self._coordinate_open_play(state)
        elif state.play_phase == 'contest':
            coordinated_moves = self._coordinate_contest(state)
        elif state.play_phase == 'set_shot':
            coordinated_moves = self._coordinate_set_shot(state)
            
        return coordinated_moves
    """

    def coordinate_movements(self, state):
        """Coordinate team movements based on game state and tactics"""
        coordinated_moves = {}
        
        # Update roles based on current situation
        self._update_player_roles(state)
        
        # Get base tactical setup from TeamTactics
        defensive_line = self.team_tactics[self.team.name].get_defensive_line_height()
        offensive_width = self.team_tactics[self.team.name].get_offensive_width()
        
        for player_id, role in self.player_roles.items():
            # Determine movement type based on role and tactics
            print(f"Player ID: {player_id} and role: {role}")
            if state.ball_carrier and state.ball_carrier.team_side == self.team_side:
                # Offensive movements
                coord_movement = self._coordinate_offensive_movement(
                    player_id, 
                    role,
                    offensive_width,
                    state
                )
            else:
                # Defensive movements
                coord_movement = self._coordinate_defensive_movement(
                    player_id,
                    role,
                    defensive_line,
                    state
                )
                
            if coord_movement:
                coordinated_moves[player_id] = coord_movement
        print(f"Coordination movement from TeamMovementCoordinator: {coord_movement} for player {player_id}")

        return coordinated_moves


    def _update_player_roles(self, state):
        """Update tactical roles based on game situation"""
        ball_pos = state.ball_position
        print(f"Ball position: {ball_pos}")
        ground_zone = self.ground.get_zone(ball_pos)
        #ground_zone = state.get_field_position_zone(ball_pos)

        # Get the correct team_players dictionary based on team side
        team_players = (state.home_team_players if self.team_side == 'home' 
                    else state.away_team_players)
        
        for position, player in team_players.items():
            # Assign roles based on position and situation
            self.player_roles[player.id] = self._determine_role(
                player, state, ground_zone
            )
            
    def _determine_role(self, player, state, ground_zone):
        """Determine player's current tactical role"""
        position = self.player_positions[player.id]
        print(f"Player position: {position}")
        position_patterns = self.position_manager._initialize_position_patterns()[position]
        
        # Match role to play phase
        if state.play_phase in ['Centre_Bounce', 'Ball_Up']:
            if 'stoppage_contest' in position_patterns:
                return 'stoppage_contest'
                
        elif state.play_phase == 'open_play':
            if state.ball_carrier and state.ball_carrier.team_side == self.team.name:
                if 'lead_pattern' in position_patterns:
                    return 'lead_pattern'
                elif 'link_up' in position_patterns:
                    return 'link_up'
                return 'attack'
            else:
                if 'defensive_cover' in position_patterns:
                    return 'defensive_cover'
                elif 'intercept' in position_patterns:
                    return 'intercept'
                return 'defend'
                
        elif state.play_phase == 'contest':
            if 'marking_contest' in position_patterns:
                return 'marking_contest'
            elif 'ground_contest' in position_patterns:
                return 'ground_contest'
                
        elif state.play_phase == 'set_shot':
            if position_patterns.get('goal_square'):
                return 'goal_square'
            elif 'defensive_press' in position_patterns:
                return 'defensive_press'
    """        
    def _coordinate_open_play(self, state):
        #Coordinate movements during open play
        movements = {}
        ball_carrier = state.ball_carrier
        
        if ball_carrier and ball_carrier.team_side == self.team.name:
            # Offensive coordination
            movements = self._coordinate_offensive_movement(state)
        else:
            # Defensive coordination
            movements = self._coordinate_defensive_movement(state)
            
        return movements
    """    
    def _coordinate_offensive_movement(self, state):
        """Coordinate offensive team movement"""
        movements = {}
        ball_carrier = state.ball_carrier
        
        # Get tactical preferences
        offensive_width = self.team_tactics[self.team.name].get_offensive_width()
        
        # Get optimal support positions considering tactics
        support_positions = self._calculate_support_positions(state, offensive_width)
        
        # Assign players to positions based on roles and attributes
        assigned_positions = self._assign_support_positions(
            support_positions,
            state
        )
        
        # Generate coordinated movements
        for player_id, target_pos in assigned_positions.items():
            movements[player_id] = {
                'type': 'support_run',
                'target': target_pos,
                'priority': self._get_movement_priority(player_id, state)
            }
            
        return movements
        
    def _calculate_support_positions(self, state):
        """Calculate optimal support positions"""
        ball_pos = state['ball_position']
        positions = []
        
        # Short option
        positions.append(self._calculate_short_option(ball_pos))
        
        # Medium options
        positions.extend(self._calculate_medium_options(ball_pos))
        
        # Long options
        positions.extend(self._calculate_long_options(ball_pos))
        
        return positions
     
    def _assign_support_positions(self, positions, state):
        """Assign players to support positions"""
        assignments = {}

        team_players = (state.home_team_players if self.team_side == 'home' 
                    else state.away_team_players)

        available_players = [p for p in team_players 
                           if p.id != state.ball_carrier.id]
        
        # Sort positions by priority
        sorted_positions = sorted(
            positions,
            key=lambda p: self._get_position_priority(p, state)
        )
        
        # Assign players based on suitability
        for pos in sorted_positions:
            best_player = self._find_best_player_for_position(
                pos,
                available_players,
                state
            )
            if best_player:
                assignments[best_player.id] = pos
                available_players.remove(best_player)
                
        return assignments
        
    def _find_best_player_for_position(self, position, players, state):
        """Find best player to fill a support position"""
        scored_players = []
        
        for player in players:
            score = self._calculate_position_suitability(
                player,
                position,
                state
            )
            scored_players.append((score, player))
            
        if scored_players:
            return max(scored_players, key=lambda x: x[0])[1]
        return None
        
    def _calculate_position_suitability(self, player, position, state):
        """Calculate how suitable a player is for a position"""
        score = 0
        
        # Distance factor
        distance = self.ground.get_distance(
            player.current_position,
            position
        )
        score -= distance * 0.1
        
        # Role suitability
        role = self.player_roles[player.id]
        score += self._get_role_position_score(role, position)
        
        # Player attributes
        score += self._get_attribute_position_score(
            player.attributes,
            position,
            state
        )
        
        return score
    
    def _calculate_short_option(self, ball_pos):
        """Calculate short support option position"""
        # Short option 10-15 meters away
        angle = 45 if self.team_side == 'home' else 135  # Angle towards goal
        distance = 12
        
        x = ball_pos[0] + (distance * math.cos(math.radians(angle)))
        y = ball_pos[1] + (distance * math.sin(math.radians(angle)))
        
        return self._ensure_valid_position((x, y))

    def _calculate_medium_options(self, ball_pos):
        """Calculate medium range support options"""
        positions = []
        base_distance = 25
        angles = [30, 60] if self.team_side == 'home' else [120, 150]
        
        for angle in angles:
            x = ball_pos[0] + (base_distance * math.cos(math.radians(angle)))
            y = ball_pos[1] + (base_distance * math.sin(math.radians(angle)))
            positions.append(self._ensure_valid_position((x, y)))
        
        return positions

    def _calculate_long_options(self, ball_pos):
        """Calculate long range support options"""
        positions = []
        base_distance = 40
        angles = [15, 45] if self.team_side == 'home' else [135, 165]
        
        for angle in angles:
            x = ball_pos[0] + (base_distance * math.cos(math.radians(angle)))
            y = ball_pos[1] + (base_distance * math.sin(math.radians(angle)))
            positions.append(self._ensure_valid_position((x, y)))
        
        return positions

    def _ensure_valid_position(self, pos):
        """Ensure position is within ground boundaries"""
        x = min(max(0, pos[0]), self.ground.rows - 1)
        y = min(max(0, pos[1]), self.ground.cols - 1)
        return (x, y)

    def _get_position_priority(self, position, state):
        """Calculate priority score for a support position"""
        score = 0
        
        # Distance to goal
        goal_pos = self.ground.get_goal_position(self.team_side)
        goal_distance = self.ground.get_distance(position, goal_pos)
        score -= goal_distance * 0.05  # Prefer positions closer to goal
        
        # Space available
        nearby_players = self.ground.get_players_in_radius(position, radius=5)
        score -= len(nearby_players) * 2  # Prefer less crowded spaces
        
        # Tactical considerations from team_tactics
        if self.team_tactics:
            if self.team_tactics[self.team.name].offense_strategy == 'Direct':
                score += (100 - goal_distance) * 0.1  # Prefer more direct positions
            elif self.team_tactics[self.team.name].offense_strategy == 'stay_wide':
                # Prefer wider positions
                center_y = self.ground.cols / 2
                width_factor = abs(position[1] - center_y)
                score += width_factor * 0.1
        
        return score

    def _get_role_position_score(self, role, position):
        """Calculate how suitable a role is for a position"""
        score = 0
        goal_pos = self.ground.get_goal_position(self.team_side)
        
        if role == 'forward':
            # Forwards prefer positions closer to goal
            score += 50 - self.ground.get_distance(position, goal_pos)
        elif role == 'midfielder':
            # Midfielders prefer central positions
            center_y = self.ground.cols / 2
            score += 30 - abs(position[1] - center_y)
        elif role == 'defender':
            # Defenders prefer deeper positions
            defensive_line = self.team_tactics[self.team.name].get_defensive_line_height() * self.ground.rows
            score += 30 - abs(position[0] - defensive_line)
        
        return score

    def _get_attribute_position_score(self, attributes, position, state):
        """Calculate position score based on player attributes"""
        score = 0
        
        # Speed consideration for distance
        if 'speed' in attributes:
            current_pos = state.get_player_position(attributes['player_id'])
            distance = self.ground.get_distance(current_pos, position)
            speed_factor = attributes['speed'] / 100
            score += speed_factor * (50 - distance)
        
        # Endurance for longer runs
        if 'endurance' in attributes:
            endurance_factor = attributes['endurance'] / 100
            score += endurance_factor * 20
        
        return score

    def _coordinate_defensive_movement(self, player_id, role, defensive_line, state):
        """Generate defensive movement coordination"""
        if not state.ball_carrier or state.ball_carrier.team_side == self.team_side:
            #print(f"No ball carrier or ball carrier is on the same team")
            #print(f"Ball carrier: {state.ball_carrier}")
            #print(f"Team side: {self.team_side}")
            return 'Stoppage'

        team_players = (state.home_team_players if self.team_side == 'home' 
                    else state.away_team_players)


        ball_pos = state['ball_position']
        player = next(p for p in team_players if p.id == player_id)
        
        if self.team_tactics[self.team.name].defense_strategy == 'Man Mark':
            nearest_opponent = self._find_nearest_opponent(player.current_position, state)
            if nearest_opponent:
                return {
                    'type': 'defensive_run',
                    'target': nearest_opponent.current_position,
                    'style': 'man_mark'
                }
        
        # Zone defense positioning
        zone_pos = self._calculate_zone_position(defensive_line, ball_pos, role)
        return {
            'type': 'defensive_run',
            'target': zone_pos,
            'style': 'zone'
        }

    def _find_nearest_opponent(self, position, state):
        """Find nearest opponent to a position"""
        opponents = [p for p in state.all_players() 
                    if p.team_side != self.team_side]
        
        if not opponents:
            return None
            
        return min(opponents, 
                key=lambda p: self.ground.get_distance(position, p.current_position))

    def _calculate_zone_position(self, defensive_line, ball_pos, role):
        """Calculate zonal defensive position"""
        # Base position on defensive line
        base_x = defensive_line * self.ground.rows
        
        # Adjust based on role and ball position
        if role == 'defender':
            x_offset = -10  # Deeper
        elif role == 'midfielder':
            x_offset = 0   # Middle
        else:
            x_offset = 10  # Higher
            
        # Adjust y position based on ball position
        y_offset = (ball_pos[1] - (self.ground.cols / 2)) * 0.5
        
        return self._ensure_valid_position((
            base_x + x_offset,
            (self.ground.cols / 2) + y_offset
        ))

    def get_team_instructions(self, state):
        """Get team-level tactical instructions based on current tactics and game state"""
        tactics = self.team_tactics[self.team.name]
        score_diff = state.get_score_difference(self.team_side)
        
        # Base instructions from current tactics
        instructions = {
            'mentality': tactics.mentality,
            'push_factor': tactics.push_factor,
            'defense_strategy': tactics.defense_strategy,
            'offense_strategy': tactics.offense_strategy
        }
        
        # Adjust based on score and time
        if score_diff < -24:  # Down by 4 goals
            instructions['mentality'] = 'attacking'  # Push forward more
            instructions['push_factor'] = f"push_factor{min(int(instructions['push_factor'][-1]) + 1, 2)}"
        elif score_diff > 24:  # Up by 4 goals
            instructions['mentality'] = 'defensive'  # Play more conservatively
            instructions['push_factor'] = 'push_factor0'
            
        return instructions




  # Not used code below!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!  
    

        
    def _calculate_press_line(self, state):
        """Calculate where team should set up their press"""
        ball_pos = state['ball_position']
        if state.ball_carrier and state.ball_carrier.team_side != self.team.name:
            # Defensive press
            return self._get_defensive_press_line(ball_pos)
        else:
            # Offensive setup
            return self._get_offensive_press_line(ball_pos)
            
    def _get_defensive_setup(self, state):
        """Get defensive structure based on game situation"""
        return {
            'style': 'zone' if self._should_play_zone(state) else 'man_on_man',
            'matchups': self._get_defensive_matchups(state),
            'zones': self._get_defensive_zones(state)
        }
        
    def _get_offensive_structure(self, state):
        """Get offensive structure based on game situation"""
        return {
            'style': self._determine_offensive_style(state),
            'key_positions': self._calculate_key_positions(state),
            'running_lanes': self._identify_running_lanes(state)
        }
    

    """
    def _calculate_support_positions(self, ball_pos, nearby_teammates, phase):
        #Calculate optimal support positions
        support_spots = []
        
        # Define base support distances based on phase
        if phase == 'attack':
            distances = [15, 25]  # Closer support in attack
        else:
            distances = [20, 30]  # Wider spread in defense
            
        # Calculate potential support angles
        angles = [45, 90, 135]  # Degrees
        
        for distance in distances:
            for angle in angles:
                # Convert angle to radians
                rad_angle = math.radians(angle)
                
                # Calculate position
                x = ball_pos[0] + distance * math.cos(rad_angle)
                y = ball_pos[1] + distance * math.sin(rad_angle)
                
                # Check if position is already occupied
                occupied = any(
                    MovementPattern._calculate_distance((x, y), t.current_position) < 5 
                    for t in nearby_teammates
                )
                
                if not occupied:
                    support_spots.append({
                        'position': (x, y),
                        'distance': distance,
                        'angle': angle
                    })
        
        return support_spots    
    """

    """
    def _calculate_attack_position(self, ball_pos):
        #Calculate optimal attacking position relative to ball
        # Position slightly goal-side and ahead of ball
        return (ball_pos[0] - 1, ball_pos[1] - 2)
    """
                 

                                           

class PositionManager:
    """Manages player positioning and formations"""
    def __init__(self, ground):
        self.ground = ground
        self.player_positions = {}  # Current player positions
        self.game_state = None
        print(f"self.game_state: {self.game_state}")
        self.scoring_engine = ScoringEngine(self.ground)
        self.position_patterns = self._initialize_position_patterns()        
        
    def update_player_position(self, player, new_position):
        """Update player's position and maintain formation structure"""
        old_position = self.player_positions.get(player.name)  # Use player.name instead of id

        # Ensure coordinates are within ground boundaries
        x = min(max(0, new_position[0]), self.ground.length - 1)
        y = min(max(0, new_position[1]), self.ground.width - 1)
        #print(f"ground length: {self.ground.length} ground width: {self.ground.width}")
        bounded_position = (x, y)
        #print(f"X: {x} Y: {y}")

        #print(f"Updating player {player.name} from {old_position} to {new_position}")
        
        if old_position:
            old_cell = self.ground.grid[old_position[0]][old_position[1]]
            old_cell.remove_player(player)
            
        new_cell = self.ground.grid[x][y]
        new_cell.add_player(player)
        #print(f"Player {player.name} moved to {new_cell}")
        self.player_positions[player.name] = bounded_position
        player.current_position = bounded_position
        #print(f"Player {player.name} moved to {bounded_position}")
        
    def get_nearby_players(self, position, radius=2):
        """Get all players within a certain radius of a position"""
        nearby_players = []
        x, y = position
        
        for dx in range(-radius, radius + 1):
            for dy in range(-radius, radius + 1):
                new_x, new_y = x + dx, y + dy
                if (0 <= new_x < self.ground.rows and 
                    0 <= new_y < self.ground.cols):
                    cell = self.ground.grid[new_x][new_y]
                    nearby_players.extend(cell.players)
        
        return nearby_players
    
    def get_ideal_position(self, player, play_phase, state):
        """Get ideal position based on play phase and game state"""
        position = player.position
        pattern = self.position_patterns.get(position, {}).get(play_phase)
        return pattern(player, state) if pattern else player.current_position
        
    def _initialize_position_patterns(self):
        """Initialize position-specific movement patterns"""
        return {
            # Forward line (6 positions)
            'LF': {  # Left Forward Pocket
                'stoppage_contest': self._forward_pocket_stoppage_pattern,
                'marking_contest': self._forward_pocket_marking_pattern,
                'ground_contest': self._forward_pocket_ground_pattern,
                'lead_pattern': self._forward_pocket_lead_pattern,
                'crumb': self._forward_pocket_crumb_pattern,
                'defensive_press': self._forward_pocket_defensive_press_pattern,
                'goal_square': self._forward_pocket_goal_square_pattern
            },
            'LHF': {  # Left Half Forward
                'stoppage_contest': self._half_forward_stoppage_pattern,
                'marking_contest': self._half_forward_marking_pattern,
                'ground_contest': self._half_forward_ground_pattern,
                'lead_pattern': self._half_forward_lead_pattern,
                'link_up': self._half_forward_link_pattern,
                'spread': self._half_forward_spread_pattern,
                'defensive_press': self._half_forward_defensive_press_pattern
            },
            'CHF': {  # Center Half Forward
                'stoppage_contest': self._chf_stoppage_pattern,
                'marking_contest': self._chf_marking_pattern,
                'ground_contest': self._chf_ground_pattern,
                'lead_pattern': self._chf_lead_pattern,
                'link_up': self._chf_link_pattern,
                'spread': self._chf_spread_pattern,
                'defensive_press': self._chf_defensive_press_pattern
            },
            'RHF': {  # Right Half Forward - mirror of LHF
                'stoppage_contest': self._half_forward_stoppage_pattern,
                'marking_contest': self._half_forward_marking_pattern,
                'ground_contest': self._half_forward_ground_pattern,
                'lead_pattern': self._half_forward_lead_pattern,
                'link_up': self._half_forward_link_pattern,
                'spread': self._half_forward_spread_pattern,
                'defensive_press': self._half_forward_defensive_press_pattern
            },
            'RF': {  # Right Forward Pocket - mirror of LF
                'stoppage_contest': self._forward_pocket_stoppage_pattern,
                'marking_contest': self._forward_pocket_marking_pattern,
                'ground_contest': self._forward_pocket_ground_pattern,
                'lead_pattern': self._forward_pocket_lead_pattern,
                'crumb': self._forward_pocket_crumb_pattern,
                'defensive_press': self._forward_pocket_defensive_press_pattern,
                'goal_square': self._forward_pocket_goal_square_pattern
            },
            'FF': {  # Full Forward
                'stoppage_contest': self._full_forward_stoppage_pattern,
                'marking_contest': self._full_forward_marking_pattern,
                'ground_contest': self._full_forward_ground_pattern,
                'lead_pattern': self._full_forward_lead_pattern,
                'goal_square': self._full_forward_goal_square_pattern,
                'defensive_press': self._full_forward_defensive_press_pattern
            },
            
            # Midfield (6 positions)
            'Rover': {
                'stoppage_contest': self._midfielder_stoppage_pattern,
                'marking_contest': self._midfielder_marking_pattern,
                'ground_contest': self._midfielder_ground_pattern,
                'spread': self._midfielder_spread_pattern,
                'follow': self._midfielder_follow_pattern,
                'link_up': self._midfielder_link_pattern,
                'defensive_press': self._midfielder_defensive_press_pattern
            },
            'Centre': {
                'stoppage_contest': self._midfielder_stoppage_pattern,
                'marking_contest': self._midfielder_marking_pattern,
                'ground_contest': self._midfielder_ground_pattern,
                'spread': self._midfielder_spread_pattern,
                'follow': self._midfielder_follow_pattern,
                'link_up': self._midfielder_link_pattern,
                'defensive_press': self._midfielder_defensive_press_pattern
            },
            'RuckRover': {
                'stoppage_contest': self._midfielder_stoppage_pattern,
                'marking_contest': self._midfielder_marking_pattern,
                'ground_contest': self._midfielder_ground_pattern,
                'spread': self._midfielder_spread_pattern,
                'follow': self._midfielder_follow_pattern,
                'link_up': self._midfielder_link_pattern,
                'defensive_press': self._midfielder_defensive_press_pattern
            },
            'Ruck': {
                'stoppage_contest': self._ruck_stoppage_pattern,
                'marking_contest': self._ruck_marking_pattern,
                'ground_contest': self._ruck_ground_pattern,
                'follow': self._ruck_follow_pattern,
                'spread': self._ruck_spread_pattern,
                'defensive_press': self._ruck_defensive_press_pattern,
                'goal_square': self._ruck_goal_square_pattern
            },
            'LWing': {  # Left Wing
                'stoppage_contest': self._wing_stoppage_pattern,
                'marking_contest': self._wing_marking_pattern,
                'ground_contest': self._wing_ground_pattern,
                'spread': self._wing_spread_pattern,
                'link_up': self._wing_link_pattern,
                'defensive_press': self._wing_defensive_press_pattern,
                'corridor_run': self._wing_corridor_pattern
            },
            'RWing': {  # Right Wing - mirror of LW
                'stoppage_contest': self._wing_stoppage_pattern,
                'marking_contest': self._wing_marking_pattern,
                'ground_contest': self._wing_ground_pattern,
                'spread': self._wing_spread_pattern,
                'link_up': self._wing_link_pattern,
                'defensive_press': self._wing_defensive_press_pattern,
                'corridor_run': self._wing_corridor_pattern
            },

            # Back line (6 positions)
            'LB': {  # Left Back Pocket
                'stoppage_contest': self._back_pocket_stoppage_pattern,
                'marking_contest': self._back_pocket_marking_pattern,
                'ground_contest': self._back_pocket_ground_pattern,
                'defensive_cover': self._back_pocket_cover_pattern,
                'intercept': self._back_pocket_intercept_pattern,
                'rebound': self._back_pocket_rebound_pattern
            },
            'LHB': {  # Left Half Back
                'stoppage_contest': self._half_back_stoppage_pattern,
                'marking_contest': self._half_back_marking_pattern,
                'ground_contest': self._half_back_ground_pattern,
                'defensive_cover': self._half_back_cover_pattern,
                'intercept': self._half_back_intercept_pattern,
                'rebound': self._half_back_rebound_pattern,
                'link_up': self._half_back_link_pattern
            },
            'CHB': {  # Center Half Back
                'stoppage_contest': self._chb_stoppage_pattern,
                'marking_contest': self._chb_marking_pattern,
                'ground_contest': self._chb_ground_pattern,
                'defensive_cover': self._chb_cover_pattern,
                'intercept': self._chb_intercept_pattern,
                'rebound': self._chb_rebound_pattern,
                'link_up': self._chb_link_pattern
            },
            'RHB': {  # Right Half Back - mirror of LHB
                'stoppage_contest': self._half_back_stoppage_pattern,
                'marking_contest': self._half_back_marking_pattern,
                'ground_contest': self._half_back_ground_pattern,
                'defensive_cover': self._half_back_cover_pattern,
                'intercept': self._half_back_intercept_pattern,
                'rebound': self._half_back_rebound_pattern,
                'link_up': self._half_back_link_pattern
            },
            'RB': {  # Right Back Pocket - mirror of LB
                'stoppage_contest': self._back_pocket_stoppage_pattern,
                'marking_contest': self._back_pocket_marking_pattern,
                'ground_contest': self._back_pocket_ground_pattern,
                'defensive_cover': self._back_pocket_cover_pattern,
                'intercept': self._back_pocket_intercept_pattern,
                'rebound': self._back_pocket_rebound_pattern
            },
            'FB': {  # Full Back
                'stoppage_contest': self._full_back_stoppage_pattern,
                'marking_contest': self._full_back_marking_pattern,
                'ground_contest': self._full_back_ground_pattern,
                'defensive_cover': self._full_back_cover_pattern,
                'intercept': self._full_back_intercept_pattern,
                'rebound': self._full_back_rebound_pattern
            }
        }

    def _forward_pocket_stoppage_pattern(self, state, player):
        """
        Generate movement pattern for forward pocket during stoppages (ball ups).
        Key responsibilities:
        - Position for crumb gathering
        - Create space/options for midfielders at stoppage
        - Maintain goal-scoring threat position
        """
        ball_pos = state['ball_position']
        player_pos = state['player_position']
        print(f"Player position: {player_pos}")
        
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Determine which pocket
        is_left_pocket = player_pos == 'LF'
        is_right_pocket = player_pos == 'RF'
        
        # Calculate base position relative to stoppage
        if is_left_pocket:
            pocket_offset = (-4, -2)  # Wider offset from ball
        elif is_right_pocket:
            pocket_offset = (4, -2)   # Wider offset from ball

        
        # Adjust based on attributes
        agility_factor = physical.agility / 20
        tactical_factor = ability.tactical / 20
        
        # Age consideration for reaction time
        age_penalty = max(0, (physical.age - 30) * 0.05)
        reaction_modifier = 1 - age_penalty
        
        target_pos = (
            ball_pos[0] + (pocket_offset[0] * (1 + tactical_factor)),
            ball_pos[1] + (pocket_offset[1] * (1 + agility_factor))
        )
        
        return {
            'type': 'stoppage_contest',
            'phases': [
                {
                    'action': 'position',
                    'target': target_pos,
                    'timing': 'pre_contest',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'read_play',
                    'focus': 'midfielder_movement',
                    'timing': 'during_contest',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'crumb',
                    'direction': 'goal' if self.scoring_engine._is_scoring_position(player) else 'space',
                    'timing': 'on_ground_ball',
                    'speed': physical.speed / 20 * reaction_modifier,
                    'agility': physical.agility / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'ground_conditions': self._adjust_for_conditions,
                'teammate_positions': self._adjust_for_teammates,
                'scoring_opportunity': self.scoring_engine._adjust_for_scoring_chance
            }
        }

    def _forward_pocket_marking_pattern(self, state, player):
        """
        Generate movement pattern for forward pocket during marking contests.
        Forward pockets use their speed/agility to either:
        - Get separation for marks
        - Crumb if contested
        """
        ball_pos = state['ball_position']
        player_pos = state['player_position']
        
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Determine which pocket
        is_left_pocket = player_pos == 'LF'
        is_right_pocket = player_pos == 'RF'
        
        # Calculate position based on pocket
        if is_left_pocket:
            base_offset = (-2, -1)
        elif is_right_pocket:
            base_offset = (2, -1)

            
        # Use speed/agility to try get separation
        separation_factor = (physical.speed + physical.agility) / 40  # Average of speed and agility
        
        target_pos = (
            ball_pos[0] + (base_offset[0] * (1 + separation_factor)),
            ball_pos[1] + (base_offset[1] * (1 + separation_factor))
        )
        
        return {
            'type': 'marking_contest',
            'phases': [
                {
                    'action': 'position',
                    'target': target_pos,
                    'timing': 'pre_contest',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'read_play',
                    'focus': 'space_and_opponent',  # Looking for separation
                    'timing': 'during_contest',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'contest',
                    'primary': 'get_separation',  # Try to get free first
                    'secondary': 'mark',         
                    'tertiary': 'crumb', # Crumb if contested
                    'timing': 'on_contest',
                    'marking_ability': ability.marking / 20,
                    'ground_ball': ability.tackling / 20,
                    'separation_ability': separation_factor
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'weather_conditions': self._adjust_for_conditions,
                'teammate_positions': self._adjust_for_teammates
            }
        }
    
    def _forward_pocket_ground_pattern(self, state, player):
        """
        Generate movement pattern for forward pocket during ground ball contests.
        Key focus on using speed and agility to get to the ball first.
        """
        ball_pos = state['ball_position']
        player_pos = state['player_position']
        
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Determine which pocket
        is_left_pocket = player_pos == 'LF'
        is_right_pocket = player_pos == 'RF'
        
        # Calculate approach angle based on pocket
        if is_left_pocket:
            approach_offset = (-2, -1)  # Approach from left
        elif is_right_pocket:
            approach_offset = (2, -1)   # Approach from right

            
        # Adjust based on attributes
        agility_factor = physical.agility / 20
        speed_factor = physical.speed / 20
        
        # Age consideration for reaction time
        age_penalty = max(0, (physical.age - 30) * 0.05)
        reaction_modifier = 1 - age_penalty
        
        target_pos = (
            ball_pos[0] + (approach_offset[0] * (1 + speed_factor)),
            ball_pos[1] + (approach_offset[1] * (1 + agility_factor))
        )
        
        return {
            'type': 'ground_contest',
            'phases': [
                {
                    'action': 'position',
                    'target': target_pos,
                    'timing': 'pre_contest',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'read_bounce',
                    'focus': 'ball_trajectory',
                    'timing': 'during_contest',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'contest',
                    'primary': 'gather',     # Try to gather cleanly first
                    'secondary': 'knock_on',  # Knock ball into space if can't gather
                    'timing': 'on_contest',
                    'ground_ball': ability.tackling / 20,
                    'speed': physical.speed / 20 * reaction_modifier,
                    'agility': physical.agility / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'ground_conditions': self._adjust_for_conditions,
                'teammate_positions': self._adjust_for_teammates,
                'scoring_opportunity': self.scoring_engine._adjust_for_scoring_chance
            }
        }

    def _forward_pocket_lead_pattern(self, state, player):
        """
        Generate movement pattern for forward pocket leading patterns.
        Key focus on creating space, timing the lead, and providing options.
        """
        ball_pos = state['ball_position']
        player_pos = state['player_position']
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Determine which pocket
        is_left_pocket = player_pos == 'LF'
        is_right_pocket = player_pos == 'RF'
        
        # Calculate lead direction and starting position
        if is_left_pocket:
            lead_offset = (-3, -4)  # Lead towards left boundary
            starting_offset = (-1, -2)  # Start slightly wider
        elif is_right_pocket:
            lead_offset = (3, -4)   # Lead towards right boundary
            starting_offset = (1, -2)  # Start slightly wider
            
        # Adjust based on attributes
        speed_factor = physical.speed / 20
        tactical_factor = ability.tactical / 20
        
        # Age consideration for endurance
        age_penalty = max(0, (physical.age - 30) * 0.05)
        stamina_modifier = 1 - age_penalty
        
        lead_pos = (
            ball_pos[0] + (lead_offset[0] * (1 + speed_factor)),
            ball_pos[1] + (lead_offset[1] * (1 + tactical_factor))
        )
        
        starting_pos = (
            ball_pos[0] + starting_offset[0],
            ball_pos[1] + starting_offset[1]
        )
        
        return {
            'type': 'lead_pattern',
            'phases': [
                {
                    'action': 'position',
                    'target': starting_pos,
                    'timing': 'pre_lead',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'read_play',
                    'focus': 'ball_carrier',
                    'timing': 'pre_lead',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'lead',
                    'primary': 'sprint_to_space',    # Sprint to space first
                    'secondary': 'hold_position',    # Hold position if marked
                    'timing': 'on_lead',
                    'speed': physical.speed / 20 * stamina_modifier,
                    'target': lead_pos,
                    'lead_timing': ability.tactical / 20,
                    'marking_ability': ability.marking / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'teammate_positions': self._adjust_for_teammates,
                'scoring_opportunity': self.scoring_engine._adjust_for_scoring_chance,
                'fatigue': physical.stamina / 20 * stamina_modifier
            }
        }

    def _forward_pocket_crumb_pattern(self, state, player):
        """
        Generate movement pattern for forward pocket crumbing.
        Key focus on reading the drop of the ball and quick reaction.
        """
        ball_pos = state['ball_position']
        player_pos = state['player_position']
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Determine which pocket
        is_left_pocket = player_pos == 'LF'
        is_right_pocket = player_pos == 'RF'
        
        # Calculate crumbing position
        if is_left_pocket:
            crumb_offset = (-2, -1)  # Position on left side of contest
        elif is_right_pocket:
            crumb_offset = (2, -1)   # Position on right side of contest
            
        # Adjust based on attributes
        agility_factor = physical.agility / 20
        tactical_factor = ability.tactical / 20
        
        # Age consideration for reaction time
        age_penalty = max(0, (physical.age - 30) * 0.05)
        reaction_modifier = 1 - age_penalty
        
        target_pos = (
            ball_pos[0] + (crumb_offset[0] * (1 + agility_factor)),
            ball_pos[1] + (crumb_offset[1] * (1 + tactical_factor))
        )
        
        return {
            'type': 'crumb_pattern',
            'phases': [
                {
                    'action': 'position',
                    'target': target_pos,
                    'timing': 'pre_contest',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'read_contest',
                    'focus': 'ball_drop',
                    'timing': 'during_contest',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'crumb',
                    'primary': 'gather',      # Clean pickup
                    'secondary': 'soccer',    # Soccer ball forward if can't gather
                    'timing': 'on_drop',
                    'speed': physical.speed / 20 * reaction_modifier,
                    'agility': physical.agility / 20,
                    'ground_ball': ability.tackling / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'ground_conditions': self._adjust_for_conditions,
                'teammate_positions': self._adjust_for_teammates,
                'scoring_opportunity': self.scoring_engine._adjust_for_scoring_chance
            }
        }

    def _forward_pocket_defensive_press_pattern(self, state, player):
        """
        Generate movement pattern for forward pocket defensive pressing.
        Key focus on applying pressure and cutting off exit lanes.
        """
        ball_pos = state['ball_position']
        player_pos = state['player_position']
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Determine which pocket
        is_left_pocket = player_pos == 'LF'
        is_right_pocket = player_pos == 'RF'
        
        # Calculate pressing position to cut off exits
        if is_left_pocket:
            press_offset = (-3, 0)  # Cut off left exit
        elif is_right_pocket:
            press_offset = (3, 0)   # Cut off right exit
            
        # Adjust based on attributes
        speed_factor = physical.speed / 20
        tactical_factor = ability.tactical / 20
        
        # Age consideration for pressing intensity
        age_penalty = max(0, (physical.age - 30) * 0.05)
        stamina_modifier = 1 - age_penalty
        
        target_pos = (
            ball_pos[0] + (press_offset[0] * (1 + tactical_factor)),
            ball_pos[1] + (press_offset[1] * (1 + speed_factor))
        )
        
        return {
            'type': 'defensive_press',
            'phases': [
                {
                    'action': 'position',
                    'target': target_pos,
                    'timing': 'immediate',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'press',
                    'primary': 'channel_inside',    # Force opponent inside
                    'secondary': 'tackle',          # Tackle if opportunity
                    'timing': 'on_opponent_possession',
                    'speed': physical.speed / 20 * stamina_modifier,
                    'tackling': ability.tackling / 20,
                    'pressure_intensity': physical.stamina / 20 * stamina_modifier
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'teammate_positions': self._adjust_for_teammates,
                'fatigue': physical.stamina / 20 * stamina_modifier
            }
        }

    def _forward_pocket_goal_square_pattern(self, state, player):
        """
        Generate movement pattern for forward pocket in goal square situations.
        Key focus on creating space and being ready for crumbs.
        """
        ball_pos = state['ball_position']
        player_pos = state['player_position']

        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Determine which pocket
        is_left_pocket = player_pos == 'LF'
        is_right_pocket = player_pos == 'RF'
        
        # Calculate goal square position
        if is_left_pocket:
            square_offset = (-1, 0)  # Left side of square
        elif is_right_pocket:
            square_offset = (1, 0)   # Right side of square
            
        # Adjust based on attributes
        agility_factor = physical.agility / 20
        tactical_factor = ability.tactical / 20
        
        target_pos = (
            ball_pos[0] + (square_offset[0] * (1 + tactical_factor)),
            ball_pos[1] + (square_offset[1] * (1 + agility_factor))
        )
        
        return {
            'type': 'goal_square',
            'phases': [
                {
                    'action': 'position',
                    'target': target_pos,
                    'timing': 'pre_contest',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'read_play',
                    'focus': 'contest_zone',
                    'timing': 'during_contest',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'contest',
                    'primary': 'crumb',       # Ready for crumbs
                    'secondary': 'block',     # Block opponent if needed
                    'timing': 'on_contest',
                    'speed': physical.speed / 20,
                    'agility': physical.agility / 20,
                    'ground_ball': ability.tackling / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'teammate_positions': self._adjust_for_teammates,
                'scoring_opportunity': self.scoring_engine._adjust_for_scoring_chance
            }
        }

    def _half_forward_stoppage_pattern(self, state, player):
        """
        Generate movement pattern for half forward during stoppages (ball ups).
        Key responsibilities:
        - Link between midfield and forward line
        - Provide outlet option for clearance
        - Ready for secondary contests
        """
        ball_pos = state['ball_position']
        player_pos = state['player_position']

        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Determine which half forward
        is_left_half = player_pos == 'LHF'
        is_right_half = player_pos == 'RHF'
        
        # Calculate base position relative to stoppage
        if is_left_half:
            position_offset = (-5, -3)  # Wider on left
        elif is_right_half:
            position_offset = (5, -3)   # Wider on right
            
        # Adjust based on attributes
        height_factor = physical.height / 20
        tactical_factor = ability.tactical / 20
        
        # Age consideration
        age_penalty = max(0, (physical.age - 30) * 0.05)
        stamina_modifier = 1 - age_penalty
        
        target_pos = (
            ball_pos[0] + (position_offset[0] * (1 + tactical_factor)),
            ball_pos[1] + (position_offset[1] * (1 + height_factor))
        )
        
        return {
            'type': 'stoppage_contest',
            'phases': [
                {
                    'action': 'position',
                    'target': target_pos,
                    'timing': 'pre_contest',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'read_play',
                    'focus': 'clearance_direction',
                    'timing': 'during_contest',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'contest',
                    'primary': 'provide_outlet',    # Be available for clearance
                    'secondary': 'contest_ground',  # Compete if ball hits ground
                    'timing': 'on_clearance',
                    'marking_ability': ability.marking / 20,
                    'ground_ball': ability.tackling / 20,
                    'stamina': physical.stamina / 20 * stamina_modifier
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'ground_conditions': self._adjust_for_conditions,
                'teammate_positions': self._adjust_for_teammates,
                'scoring_opportunity': self.scoring_engine._adjust_for_scoring_chance
            }
        }

    def _half_forward_marking_pattern(self, state, player):
        """
        Generate movement pattern for half forward during marking contests.
        Key focus on using height/strength for marking contests while 
        maintaining link between midfield and forward line.
        """
        ball_pos = state['ball_position']
        player_pos = state['player_position']

        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Determine which half forward
        is_left_half = player_pos == 'LHF'
        is_right_half = player_pos == 'RHF'
        
        # Calculate contest position
        if is_left_half:
            contest_offset = (-3, -2)  # Left side approach
        elif is_right_half:
            contest_offset = (3, -2)   # Right side approach
            
        # Adjust based on attributes
        height_factor = physical.height / 20
        strength_factor = physical.strength / 20
        
        # Age consideration for jumping/strength
        age_penalty = max(0, (physical.age - 30) * 0.05)
        power_modifier = 1 - age_penalty
        
        target_pos = (
            ball_pos[0] + (contest_offset[0] * (1 + height_factor)),
            ball_pos[1] + (contest_offset[1] * (1 + strength_factor))
        )
        
        return {
            'type': 'marking_contest',
            'phases': [
                {
                    'action': 'position',
                    'target': target_pos,
                    'timing': 'pre_contest',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'read_play',
                    'focus': 'ball_flight',
                    'timing': 'during_contest',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'contest',
                    'primary': 'mark',           # Attempt to mark
                    'secondary': 'bring_to_ground', # Bring ball to ground if can't mark
                    'timing': 'on_contest',
                    'marking_ability': ability.marking / 20,
                    'strength': physical.strength / 20 * power_modifier,
                    'height_advantage': height_factor
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'weather_conditions': self._adjust_for_conditions,
                'teammate_positions': self._adjust_for_teammates
            }
        }

    def _half_forward_ground_pattern(self, state, player):
        """
        Generate movement pattern for half forward during ground ball contests.
        Key focus on using speed/agility while maintaining structure.
        """
        ball_pos = state['ball_position']
        player_pos = state['player_position']

        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Determine which half forward
        is_left_half = player_pos == 'LHF'
        is_right_half = player_pos == 'RHF'
        
        # Calculate approach angle
        if is_left_half:
            approach_offset = (-3, -2)  # Approach from left
        elif is_right_half:
            approach_offset = (3, -2)   # Approach from right
            
        # Adjust based on attributes
        agility_factor = physical.agility / 20
        speed_factor = physical.speed / 20
        
        # Age consideration for reaction
        age_penalty = max(0, (physical.age - 30) * 0.05)
        reaction_modifier = 1 - age_penalty
        
        target_pos = (
            ball_pos[0] + (approach_offset[0] * (1 + speed_factor)),
            ball_pos[1] + (approach_offset[1] * (1 + agility_factor))
        )
        
        return {
            'type': 'ground_contest',
            'phases': [
                {
                    'action': 'position',
                    'target': target_pos,
                    'timing': 'pre_contest',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'read_bounce',
                    'focus': 'ball_trajectory',
                    'timing': 'during_contest',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'contest',
                    'primary': 'gather',      # Clean pickup
                    'secondary': 'kick',   # Kick if opportunity
                    'tertiary': 'handball',         # Quick handball if pressured
                    'timing': 'on_contest',
                    'ground_ball': ability.tackling / 20,
                    'speed': physical.speed / 20 * reaction_modifier,
                    'agility': physical.agility / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'ground_conditions': self._adjust_for_conditions,
                'teammate_positions': self._adjust_for_teammates,
                'scoring_opportunity': self.scoring_engine._adjust_for_scoring_chance
            }
        }

    def _half_forward_lead_pattern(self, state, player):
        """
        Generate movement pattern for half forward leads.
        Key focus on creating space and providing link options.
        """
        ball_pos = state['ball_position']
        player_pos = state['player_position']

        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Determine which half forward
        is_left_half = player_pos == 'LHF'
        is_right_half = player_pos == 'RHF'
        
        # Calculate lead direction and starting position
        if is_left_half:
            lead_offset = (-4, -5)      # Lead towards boundary
            starting_offset = (-2, -3)   # Start slightly inside
        elif is_right_half:
            lead_offset = (4, -5)       # Lead towards boundary
            starting_offset = (2, -3)    # Start slightly inside
            
        # Adjust based on attributes
        speed_factor = physical.speed / 20
        tactical_factor = ability.tactical / 20
        
        # Age consideration for endurance
        age_penalty = max(0, (physical.age - 30) * 0.05)
        stamina_modifier = 1 - age_penalty
        
        lead_pos = (
            ball_pos[0] + (lead_offset[0] * (1 + speed_factor)),
            ball_pos[1] + (lead_offset[1] * (1 + tactical_factor))
        )
        
        starting_pos = (
            ball_pos[0] + starting_offset[0],
            ball_pos[1] + starting_offset[1]
        )
        
        return {
            'type': 'lead_pattern',
            'phases': [
                {
                    'action': 'position',
                    'target': starting_pos,
                    'timing': 'pre_lead',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'read_play',
                    'focus': 'midfield_possession',
                    'timing': 'pre_lead',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'lead',
                    'primary': 'lead_to_space',     # Lead to space
                    'secondary': 'block_defender',   # Block for teammates if marked
                    'timing': 'on_lead',
                    'speed': physical.speed / 20 * stamina_modifier,
                    'target': lead_pos,
                    'lead_timing': ability.tactical / 20,
                    'strength': physical.strength / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'teammate_positions': self._adjust_for_teammates,
                'scoring_opportunity': self.scoring_engine._adjust_for_scoring_chance,
                'fatigue': physical.stamina / 20 * stamina_modifier
            }
        }

    def _half_forward_link_pattern(self, state, player):
        """
        Generate movement pattern for half forward linking play.
        Key focus on providing connection between midfield and forward line.
        """
        ball_pos = state['ball_position']
        player_pos = state['player_position']

        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Determine which half forward
        is_left_half = player_pos == 'LHF'
        is_right_half = player_pos == 'RHF'
        
        # Calculate linking position
        if is_left_half:
            link_offset = (-4, -3)  # Left side corridor
        elif is_right_half:
            link_offset = (4, -3)   # Right side corridor
            
        # Adjust based on attributes
        speed_factor = physical.speed / 20
        tactical_factor = ability.tactical / 20
        
        target_pos = (
            ball_pos[0] + (link_offset[0] * (1 + tactical_factor)),
            ball_pos[1] + (link_offset[1] * (1 + speed_factor))
        )
        
        return {
            'type': 'link_pattern',
            'phases': [
                {
                    'action': 'position',
                    'target': target_pos,
                    'timing': 'immediate',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'read_play',
                    'focus': 'midfield_movement',
                    'timing': 'continuous',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'link',
                    'primary': 'provide_option',    # Provide handball/kick option
                    'secondary': 'spread',          # Spread if no direct option
                    'timing': 'on_midfield_possession',
                    'speed': physical.speed / 20,
                    'tactical': ability.tactical / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'teammate_positions': self._adjust_for_teammates,
                'scoring_opportunity': self.scoring_engine._adjust_for_scoring_chance
            }
        }

    def _half_forward_spread_pattern(self, state, player):
        """
        Generate movement pattern for half forward spreading play.
        Key focus on creating space and width in attack.
        """
        ball_pos = state['ball_position']
        player_pos = state['player_position']

        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Determine which half forward
        is_left_half = player_pos == 'LHF'
        is_right_half = player_pos == 'RHF'
        
        # Calculate spread position
        if is_left_half:
            spread_offset = (-6, -4)  # Wide left
        elif is_right_half:
            spread_offset = (6, -4)   # Wide right
            
        # Adjust based on attributes
        speed_factor = physical.speed / 20
        tactical_factor = ability.tactical / 20
        
        target_pos = (
            ball_pos[0] + (spread_offset[0] * (1 + tactical_factor)),
            ball_pos[1] + (spread_offset[1] * (1 + speed_factor))
        )
        
        return {
            'type': 'spread_pattern',
            'phases': [
                {
                    'action': 'position',
                    'target': target_pos,
                    'timing': 'immediate',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'read_play',
                    'focus': 'defensive_setup',
                    'timing': 'continuous',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'spread',
                    'primary': 'maintain_width',     # Keep width in attack
                    'secondary': 'attack_space',     # Attack space if available
                    'timing': 'on_team_possession',
                    'speed': physical.speed / 20,
                    'tactical': ability.tactical / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'teammate_positions': self._adjust_for_teammates,
                'scoring_opportunity': self.scoring_engine._adjust_for_scoring_chance
            }
        }

    def _half_forward_defensive_press_pattern(self, state, player):
        """
        Generate movement pattern for half forward defensive pressing.
        Key focus on preventing defensive rebound and maintaining forward pressure.
        """
        ball_pos = state['ball_position']
        player_pos = state['player_position']

        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Determine which half forward
        is_left_half = player_pos == 'LHF'
        is_right_half = player_pos == 'RHF'
        
        # Calculate press position
        if is_left_half:
            press_offset = (-4, -2)  # Press left side
        elif is_right_half:
            press_offset = (4, -2)   # Press right side
            
        # Adjust based on attributes
        speed_factor = physical.speed / 20
        stamina_factor = physical.stamina / 20
        
        # Age consideration for pressing intensity
        age_penalty = max(0, (physical.age - 30) * 0.05)
        stamina_modifier = 1 - age_penalty
        
        target_pos = (
            ball_pos[0] + (press_offset[0] * (1 + speed_factor)),
            ball_pos[1] + (press_offset[1] * (1 + stamina_factor))
        )
        
        return {
            'type': 'defensive_press',
            'phases': [
                {
                    'action': 'position',
                    'target': target_pos,
                    'timing': 'immediate',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'press',
                    'primary': 'prevent_rebound',    # Stop defensive rebound
                    'secondary': 'force_turnover',   # Create turnover opportunity
                    'timing': 'on_opposition_possession',
                    'speed': physical.speed / 20 * stamina_modifier,
                    'pressure': physical.stamina / 20 * stamina_modifier,
                    'tackling': ability.tackling / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'teammate_positions': self._adjust_for_teammates,
                'fatigue': physical.stamina / 20 * stamina_modifier
            }
        }

    def _chf_stoppage_pattern(self, state, player):
        """
        Generate movement pattern for CHF during stoppages (ball ups).
        Key responsibilities:
        - Primary marking target
        - Contest if in forward zone
        - Link between midfield and forward line
        """
        ball_pos = state['ball_position']
        
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Calculate base position relative to stoppage
        # CHF typically positions centrally but slightly forward
        position_offset = (0, -4)  # Central position, forward of stoppage
            
        # Adjust based on attributes
        height_factor = physical.height / 20
        strength_factor = physical.strength / 20
        
        # Age consideration
        age_penalty = max(0, (physical.age - 30) * 0.05)
        power_modifier = 1 - age_penalty
        
        target_pos = (
            ball_pos[0] + (position_offset[0] * (1 + height_factor)),
            ball_pos[1] + (position_offset[1] * (1 + strength_factor))
        )
        
        return {
            'type': 'stoppage_contest',
            'phases': [
                {
                    'action': 'position',
                    'target': target_pos,
                    'timing': 'pre_contest',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'read_play',
                    'focus': 'ruck_contest',
                    'timing': 'during_contest',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'contest',
                    'primary': 'mark_space',      # Position for marking
                    'secondary': 'contest_ground', # Compete at ground level
                    'timing': 'on_clearance',
                    'marking_ability': ability.marking / 20,
                    'strength': physical.strength / 20 * power_modifier,
                    'ground_ball': ability.tackling / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'ground_conditions': self._adjust_for_conditions,
                'teammate_positions': self._adjust_for_teammates,
                'scoring_opportunity': self.scoring_engine._adjust_for_scoring_chance
            }
        }

    def _chf_marking_pattern(self, state, player):
        """
        Generate movement pattern for CHF during marking contests.
        Key focus on being primary marking target and creating contests.
        """
        ball_pos = state['ball_position']
        
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Calculate contest position - CHF typically central
        contest_offset = (0, -2)  # Central approach
            
        # Adjust based on attributes
        height_factor = physical.height / 20
        strength_factor = physical.strength / 20
        
        # Age consideration for jumping/strength
        age_penalty = max(0, (physical.age - 30) * 0.05)
        power_modifier = 1 - age_penalty
        
        target_pos = (
            ball_pos[0] + (contest_offset[0] * (1 + height_factor)),
            ball_pos[1] + (contest_offset[1] * (1 + strength_factor))
        )
        
        return {
            'type': 'marking_contest',
            'phases': [
                {
                    'action': 'position',
                    'target': target_pos,
                    'timing': 'pre_contest',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'read_play',
                    'focus': 'ball_flight',
                    'timing': 'during_contest',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'contest',
                    'primary': 'mark',              # Strong marking attempt
                    'secondary': 'bring_to_ground', # Bring ball to ground if can't mark
                    'tertiary': 'protect_drop_zone', # Protect where ball might drop
                    'timing': 'on_contest',
                    'marking_ability': ability.marking / 20,
                    'strength': physical.strength / 20 * power_modifier,
                    'height_advantage': height_factor
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'weather_conditions': self._adjust_for_conditions,
                'teammate_positions': self._adjust_for_teammates
            }
        }
    
    def _chf_spread_pattern(self, state, player):
        """
        Generate movement pattern for CHF spreading play.
        Key focus on creating space centrally and providing structure.
        """
        ball_pos = state['ball_position']
        
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Calculate spread position - CHF spreads centrally
        spread_offset = (0, -5)  # Central spread
            
        # Adjust based on attributes
        tactical_factor = ability.tactical / 20
        stamina_factor = physical.stamina / 20
        
        target_pos = (
            ball_pos[0] + (spread_offset[0] * (1 + tactical_factor)),
            ball_pos[1] + (spread_offset[1] * (1 + stamina_factor))
        )
        
        return {
            'type': 'spread_pattern',
            'phases': [
                {
                    'action': 'position',
                    'target': target_pos,
                    'timing': 'immediate',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'read_play',
                    'focus': 'defensive_setup',
                    'timing': 'continuous',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'spread',
                    'primary': 'hold_central',      # Maintain central structure
                    'secondary': 'create_space',    # Create space for teammates
                    'timing': 'on_team_possession',
                    'tactical': ability.tactical / 20,
                    'strength': physical.strength / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'teammate_positions': self._adjust_for_teammates,
                'scoring_opportunity': self.scoring_engine._adjust_for_scoring_chance
            }
        }

    def _chf_defensive_press_pattern(self, state, player):
        """
        Generate movement pattern for CHF defensive pressing.
        Key focus on central pressing and preventing easy clearances.
        """
        ball_pos = state['ball_position']
        
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Calculate press position - central pressing
        press_offset = (0, -3)  # Central press
            
        # Adjust based on attributes
        speed_factor = physical.speed / 20
        stamina_factor = physical.stamina / 20
        
        # Age consideration for pressing intensity
        age_penalty = max(0, (physical.age - 30) * 0.05)
        stamina_modifier = 1 - age_penalty
        
        target_pos = (
            ball_pos[0] + (press_offset[0] * (1 + speed_factor)),
            ball_pos[1] + (press_offset[1] * (1 + stamina_factor))
        )
        
        return {
            'type': 'defensive_press',
            'phases': [
                {
                    'action': 'position',
                    'target': target_pos,
                    'timing': 'immediate',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'read_play',
                    'focus': 'opposition_setup',
                    'timing': 'continuous',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'press',
                    'primary': 'block_corridor',     # Block central corridor
                    'secondary': 'force_wide',       # Force play wide
                    'timing': 'on_opposition_possession',
                    'speed': physical.speed / 20 * stamina_modifier,
                    'pressure': physical.stamina / 20 * stamina_modifier,
                    'tackling': ability.tackling / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'teammate_positions': self._adjust_for_teammates,
                'fatigue': physical.stamina / 20 * stamina_modifier
            }
        }    

    def _chf_ground_pattern(self, state, player):
        """
        Generate movement pattern for CHF during ground ball contests.
        Key focus on using size/strength while maintaining central position.
        """
        ball_pos = state['ball_position']
        
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Calculate approach angle - CHF approaches centrally
        approach_offset = (0, -2)  # Central approach
            
        # Adjust based on attributes
        agility_factor = physical.agility / 20
        strength_factor = physical.strength / 20
        
        # Age consideration for reaction
        age_penalty = max(0, (physical.age - 30) * 0.05)
        reaction_modifier = 1 - age_penalty
        
        target_pos = (
            ball_pos[0] + (approach_offset[0] * (1 + strength_factor)),
            ball_pos[1] + (approach_offset[1] * (1 + agility_factor))
        )
        
        return {
            'type': 'ground_contest',
            'phases': [
                {
                    'action': 'position',
                    'target': target_pos,
                    'timing': 'pre_contest',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'read_bounce',
                    'focus': 'ball_trajectory',
                    'timing': 'during_contest',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'contest',
                    'primary': 'gather',      # Clean pickup
                    'secondary': 'kick',      # Kick if opportunity
                    'tertiary': 'handball',   # Quick handball if pressured
                    'timing': 'on_contest',
                    'ground_ball': ability.tackling / 20,
                    'strength': physical.strength / 20 * reaction_modifier,
                    'agility': physical.agility / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'ground_conditions': self._adjust_for_conditions,
                'teammate_positions': self._adjust_for_teammates,
                'scoring_opportunity': self.scoring_engine._adjust_for_scoring_chance
            }
        }

    def _chf_lead_pattern(self, state, player):
        """
        Generate movement pattern for CHF leads.
        Key focus on being primary leading target and creating space.
        """
        ball_pos = state['ball_position']
        
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Calculate lead direction and starting position
        lead_offset = (0, -6)       # Lead straight ahead
        starting_offset = (0, -3)    # Start slightly forward
            
        # Adjust based on attributes
        speed_factor = physical.speed / 20
        tactical_factor = ability.tactical / 20
        
        # Age consideration for endurance
        age_penalty = max(0, (physical.age - 30) * 0.05)
        stamina_modifier = 1 - age_penalty
        
        lead_pos = (
            ball_pos[0] + (lead_offset[0] * (1 + speed_factor)),
            ball_pos[1] + (lead_offset[1] * (1 + tactical_factor))
        )
        
        starting_pos = (
            ball_pos[0] + starting_offset[0],
            ball_pos[1] + starting_offset[1]
        )
        
        return {
            'type': 'lead_pattern',
            'phases': [
                {
                    'action': 'position',
                    'target': starting_pos,
                    'timing': 'pre_lead',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'read_play',
                    'focus': 'midfield_possession',
                    'timing': 'pre_lead',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'lead',
                    'primary': 'lead_to_space',     # Strong lead to space
                    'secondary': 'draw_defender',    # Draw defender to create space
                    'timing': 'on_lead',
                    'speed': physical.speed / 20 * stamina_modifier,
                    'target': lead_pos,
                    'lead_timing': ability.tactical / 20,
                    'strength': physical.strength / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'teammate_positions': self._adjust_for_teammates,
                'scoring_opportunity': self.scoring_engine._adjust_for_scoring_chance,
                'fatigue': physical.stamina / 20 * stamina_modifier
            }
        }

    def _chf_link_pattern(self, state, player):
        """
        Generate movement pattern for CHF linking play.
        Key focus on being central link between midfield and forwards.
        """
        ball_pos = state['ball_position']
        
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Calculate linking position - central corridor
        link_offset = (0, -4)  # Central corridor
            
        # Adjust based on attributes
        tactical_factor = ability.tactical / 20
        stamina_factor = physical.stamina / 20
        
        target_pos = (
            ball_pos[0] + (link_offset[0] * (1 + tactical_factor)),
            ball_pos[1] + (link_offset[1] * (1 + stamina_factor))
        )
        
        return {
            'type': 'link_pattern',
            'phases': [
                {
                    'action': 'position',
                    'target': target_pos,
                    'timing': 'immediate',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'read_play',
                    'focus': 'midfield_movement',
                    'timing': 'continuous',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'link',
                    'primary': 'central_option',    # Primary central option
                    'secondary': 'distribute',      # Distribute to forwards
                    'timing': 'on_possession',
                    'tactical': ability.tactical / 20,
                    'strength': physical.strength / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'teammate_positions': self._adjust_for_teammates,
                'scoring_opportunity': self.scoring_engine._adjust_for_scoring_chance
            }
        }

    def _full_forward_stoppage_pattern(self, state, player):
        """
        Generate movement pattern for FF during stoppages (ball ups).
        Key responsibilities:
        - Primary deep forward target
        - Maintain goal-front position
        - Create space for other forwards
        """
        ball_pos = state['ball_position']
        
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Calculate base position relative to stoppage
        # FF typically positions centrally and deep
        position_offset = (0, -6)  # Deep central position
            
        # Adjust based on attributes
        height_factor = physical.height / 20
        strength_factor = physical.strength / 20
        
        # Age consideration
        age_penalty = max(0, (physical.age - 30) * 0.05)
        power_modifier = 1 - age_penalty
        
        target_pos = (
            ball_pos[0] + (position_offset[0] * (1 + height_factor)),
            ball_pos[1] + (position_offset[1] * (1 + strength_factor))
        )
        
        return {
            'type': 'stoppage_contest',
            'phases': [
                {
                    'action': 'position',
                    'target': target_pos,
                    'timing': 'pre_contest',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'read_play',
                    'focus': 'long_clearance',
                    'timing': 'during_contest',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'contest',
                    'primary': 'hold_position',     # Maintain deep position
                    'secondary': 'protect_space',   # Protect landing zone
                    'timing': 'on_clearance',
                    'strength': physical.strength / 20 * power_modifier,
                    'marking_ability': ability.marking / 20,
                    'positioning': ability.tactical / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'ground_conditions': self._adjust_for_conditions,
                'teammate_positions': self._adjust_for_teammates,
                'scoring_opportunity': self.scoring_engine._adjust_for_scoring_chance
            }
        }

    def _full_forward_marking_pattern(self, state, player):
        """
        Generate movement pattern for FF during marking contests.
        Key focus on being primary marking target in scoring position.
        """
        ball_pos = state['ball_position']
        
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Calculate contest position - FF contests centrally and deep
        contest_offset = (0, -5)  # Deep central position
            
        # Adjust based on attributes
        height_factor = physical.height / 20
        strength_factor = physical.strength / 20
        
        # Age consideration for jumping/strength
        age_penalty = max(0, (physical.age - 30) * 0.05)
        power_modifier = 1 - age_penalty
        
        target_pos = (
            ball_pos[0] + (contest_offset[0] * (1 + height_factor)),
            ball_pos[1] + (contest_offset[1] * (1 + strength_factor))
        )
        
        return {
            'type': 'marking_contest',
            'phases': [
                {
                    'action': 'position',
                    'target': target_pos,
                    'timing': 'pre_contest',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'read_play',
                    'focus': 'ball_flight',
                    'timing': 'during_contest',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'contest',
                    'primary': 'mark',              # Strong marking attempt
                    'secondary': 'front_position',   # Front position if can't mark
                    'tertiary': 'protect_drop_zone', # Protect where ball might drop
                    'timing': 'on_contest',
                    'marking_ability': ability.marking / 20,
                    'strength': physical.strength / 20 * power_modifier,
                    'height_advantage': height_factor
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'weather_conditions': self._adjust_for_conditions,
                'teammate_positions': self._adjust_for_teammates,
                'scoring_opportunity': self.scoring_engine._adjust_for_scoring_chance
            }
        }

    def _full_forward_ground_pattern(self, state, player):
        """
        Generate movement pattern for FF during ground ball contests.
        Key focus on using strength in tight spaces and goal sense.
        """
        ball_pos = state['ball_position']
        
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Calculate approach angle - FF approaches centrally
        approach_offset = (0, -3)  # Central approach
            
        # Adjust based on attributes
        agility_factor = physical.agility / 20
        strength_factor = physical.strength / 20
        
        # Age consideration for reaction
        age_penalty = max(0, (physical.age - 30) * 0.05)
        reaction_modifier = 1 - age_penalty
        
        target_pos = (
            ball_pos[0] + (approach_offset[0] * (1 + strength_factor)),
            ball_pos[1] + (approach_offset[1] * (1 + agility_factor))
        )
        
        return {
            'type': 'ground_contest',
            'phases': [
                {
                    'action': 'position',
                    'target': target_pos,
                    'timing': 'pre_contest',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'read_bounce',
                    'focus': 'ball_trajectory',
                    'timing': 'during_contest',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'contest',
                    'primary': 'gather',      # Clean pickup
                    'secondary': 'snap_shot',  # Quick shot at goal
                    'tertiary': 'handball',   # Handball if pressured
                    'timing': 'on_contest',
                    'ground_ball': ability.tackling / 20,
                    'strength': physical.strength / 20 * reaction_modifier,
                    'agility': physical.agility / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'ground_conditions': self._adjust_for_conditions,
                'teammate_positions': self._adjust_for_teammates,
                'scoring_opportunity': self.scoring_engine._adjust_for_scoring_chance
            }
        }

    def _full_forward_lead_pattern(self, state, player):
        """
        Generate movement pattern for FF leads.
        Key focus on leading into scoring position and creating space.
        """
        ball_pos = state['ball_position']
        
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Calculate lead direction and starting position
        lead_offset = (0, -7)       # Deep lead straight ahead
        starting_offset = (0, -5)    # Start deep
            
        # Adjust based on attributes
        speed_factor = physical.speed / 20
        tactical_factor = ability.tactical / 20
        
        # Age consideration for endurance
        age_penalty = max(0, (physical.age - 30) * 0.05)
        stamina_modifier = 1 - age_penalty
        
        lead_pos = (
            ball_pos[0] + (lead_offset[0] * (1 + speed_factor)),
            ball_pos[1] + (lead_offset[1] * (1 + tactical_factor))
        )
        
        starting_pos = (
            ball_pos[0] + starting_offset[0],
            ball_pos[1] + starting_offset[1]
        )
        
        return {
            'type': 'lead_pattern',
            'phases': [
                {
                    'action': 'position',
                    'target': starting_pos,
                    'timing': 'pre_lead',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'read_play',
                    'focus': 'ball_carrier',
                    'timing': 'pre_lead',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'lead',
                    'primary': 'lead_to_goal',     # Lead towards goal
                    'secondary': 'create_space',    # Create space if marked
                    'timing': 'on_lead',
                    'speed': physical.speed / 20 * stamina_modifier,
                    'target': lead_pos,
                    'lead_timing': ability.tactical / 20,
                    'strength': physical.strength / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'teammate_positions': self._adjust_for_teammates,
                'scoring_opportunity': self.scoring_engine._adjust_for_scoring_chance,
                'fatigue': physical.stamina / 20 * stamina_modifier
            }
        }

    def _full_forward_goal_square_pattern(self, state, player):
        """
        Generate movement pattern for FF in goal square situations.
        Key focus on positioning for scoring opportunities.
        """
        ball_pos = state['ball_position']
        
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Calculate goal square position - central in square
        square_offset = (0, -1)  # Central in square
            
        # Adjust based on attributes
        strength_factor = physical.strength / 20
        tactical_factor = ability.tactical / 20
        
        target_pos = (
            ball_pos[0] + (square_offset[0] * (1 + tactical_factor)),
            ball_pos[1] + (square_offset[1] * (1 + strength_factor))
        )
        
        return {
            'type': 'goal_square',
            'phases': [
                {
                    'action': 'position',
                    'target': target_pos,
                    'timing': 'immediate',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'read_play',
                    'focus': 'ball_movement',
                    'timing': 'continuous',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'contest',
                    'primary': 'front_position',    # Get front position
                    'secondary': 'body_block',      # Block defender
                    'timing': 'on_contest',
                    'strength': physical.strength / 20,
                    'positioning': ability.tactical / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'teammate_positions': self._adjust_for_teammates,
                'scoring_opportunity': self.scoring_engine._adjust_for_scoring_chance
            }
        }

    def _full_forward_defensive_press_pattern(self, state, player):
        """
        Generate movement pattern for FF defensive pressing.
        Key focus on preventing easy exits and maintaining deep position.
        """
        ball_pos = state['ball_position']
        
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Calculate press position - maintain deep central position
        press_offset = (0, -4)  # Deep central press
            
        # Adjust based on attributes
        speed_factor = physical.speed / 20
        stamina_factor = physical.stamina / 20
        
        # Age consideration for pressing intensity
        age_penalty = max(0, (physical.age - 30) * 0.05)
        stamina_modifier = 1 - age_penalty
        
        target_pos = (
            ball_pos[0] + (press_offset[0] * (1 + speed_factor)),
            ball_pos[1] + (press_offset[1] * (1 + stamina_factor))
        )
        
        return {
            'type': 'defensive_press',
            'phases': [
                {
                    'action': 'position',
                    'target': target_pos,
                    'timing': 'immediate',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'press',
                    'primary': 'block_long',       # Prevent long kicks
                    'secondary': 'force_error',    # Force rushed disposal
                    'timing': 'on_opposition_possession',
                    'speed': physical.speed / 20 * stamina_modifier,
                    'pressure': physical.stamina / 20 * stamina_modifier,
                    'tackling': ability.tackling / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'teammate_positions': self._adjust_for_teammates,
                'fatigue': physical.stamina / 20 * stamina_modifier
            }
        }

    def _midfielder_stoppage_pattern(self, state, player):
        """
        Generate movement pattern for midfielders during stoppages.
        Key focus on winning clearances and executing role-specific actions.
        """
        ball_pos = state['ball_position']
        
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Get role-specific patterns and determine best role for situation
        role_patterns = self._define_midfielder_roles('stoppage', ball_pos, state, physical, ability)
        selected_role = self._select_midfielder_role(state, role_patterns, player)
        role_data = role_patterns[selected_role]
        
        # Calculate position based on role offset
        target_pos = (
            ball_pos[0] + role_data['offset'][0],
            ball_pos[1] + role_data['offset'][1]
        ) if role_data['offset'] != 'dynamic' else self._calculate_dynamic_position(state)
        
        return {
            'type': 'stoppage_contest',
            'phases': [
                {
                    'action': 'position',
                    'target': target_pos,
                    'timing': 'pre_contest',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'read_play',
                    'focus': role_data['focus'],
                    'timing': 'during_contest',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'contest',
                    'primary': role_data['primary_action'],
                    'secondary': role_data['secondary_action'],
                    'timing': 'on_contest',
                    'strength': physical.strength / 20,
                    'agility': physical.agility / 20,
                    'stamina': physical.stamina / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'teammate_positions': self._adjust_for_teammates,
                'role_coordination': role_data['coordination']
            }
        }

    def _select_midfielder_role(self, state, role_patterns, player):
        """
        Select the most appropriate midfielder role based on current situation.
        """
        # Check for Daicos role eligibility first
        daicos_attempt = self._can_attempt_daicos_role(player, state, role_patterns['TheDaicos'])
        if daicos_attempt['eligible'] or daicos_attempt['attempt_anyway']:
            return 'TheDaicos'
        
        # Score each role based on:
        # 1. Player attributes matching role weights
        # 2. Game situation suitability
        # 3. Team strategy alignment
        role_scores = {}
        for role, pattern in role_patterns.items():
            score = self._calculate_role_suitability(
                player,
                pattern,
                state
            )
            role_scores[role] = score
        
        # Return highest scoring role
        return max(role_scores.items(), key=lambda x: x[1])[0]    
    
    def _define_midfielder_roles(self, role, ball_pos, state, physical, ability):
        """
        Calculate optimal position and actions based on assigned role.
        Each pattern named after iconic Collingwood midfielders.
        """
        role_patterns = {
            'TFrancis': {  # Tony Francis - The in-and-under specialist
                'offset': (1, 0),  # Slightly offset for run in
                'focus': 'ball_drop',
                'primary_action': 'hard_ball_get',
                'secondary_action': 'quick_clearance',
                'coordination': {'type': 'space_creation', 'priority': 'high'},
                'attribute_weight': {
                    'strength': 1.5,      # Brave in-and-under player
                    'agility': 1.3,     # Strong in the contest
                    'tackling': 1.2       # Quick in tight spaces
                }
            },
            'Pendlebury': {  # Scott Pendlebury - The master reader
                'offset': (-2, -1),  # Defensive side
                'focus': 'ruck_tap',
                'primary_action': 'receive_tap',
                'secondary_action': 'distribute_possession',
                'coordination': {'type': 'support_line', 'priority': 'high'},
                'attribute_weight': {
                    'tactical': 1.5,     # Elite decision maker
                    'mental': 1.4,        # Precise disposal
                    'vision': 1.3        # Excellent awareness
                }
            },
            'Buckley': {  # Nathan Buckley - The defensive midfielder
                'offset': (-1, -2),  # Defensive side positioning
                'focus': 'opposition_mids',
                'primary_action': 'block_run',
                'secondary_action': 'pressure_disposal',
                'coordination': {'type': 'defensive_screen', 'priority': 'high'},
                'attribute_weight': {
                    'strength': 1.4,     # Strong defensive presence
                    'tackling': 1.3,     # Elite tackler
                    'stamina': 1.2       # High work rate
                }
            },
            'DaneSwann': {  # Dane Swan - The complete midfielder
                'offset': (0, -1),  # Balanced positioning
                'focus': 'all_around',
                'primary_action': 'read_play',
                'secondary_action': 'support_teammates',
                'coordination': {'type': 'dynamic_support', 'priority': 'high'},
                'attribute_weight': {
                    'endurance': 1.4,    # Incredible work rate
                    'tactical': 1.3,     # Excellent game sense
                    'versatility': 1.3   # Can play any midfield role
                }
            },
            'TheDaicos': {  # Nick Daicos - The elite talent
                'offset': 'dynamic',     # Position changes based on game reading
                'focus': 'opportunity',   # Looking for game-breaking moments
                'primary_action': 'find_ball',
                'secondary_action': 'elite_disposal',
                'coordination': {'type': 'instinctive', 'priority': 'exceptional'},
                'attribute_weight': {
                    'tactical': 1.8,        # Elite disposal
                    'vision': 1.7,       # Exceptional reading of play
                    'mental': 1.6,     # Brilliant decision making
                    'agility': 1.5,       # Outstanding evasiveness
                    'kicking': 1.4
                },
                'special_conditions': {
                    'threshold': 85,      # Minimum combined attribute score
                    'risk_factor': 0.2,   # Chance to attempt role anyway
                    'confidence_boost': {  # Random confidence factors
                        'trigger': 'momentum',
                        'boost_amount': random.uniform(1.1, 1.3)
                    },
                    'x_factor': {
                        'chance': 0.15,    # Chance for magical moment
                        'impact': 1.5      # Impact multiplier if successful
                    }
                }
            }
        }
        return role_patterns  # Add this return statement
    
    def _can_attempt_daicos_role(self, player, state, role_patterns):
        """
        Determines if a player can/should attempt TheDaicos role
        """
        print(f"Player {player.name} is attempting TheDaicos role")
        
        # Calculate base eligibility score using attribute weights from pattern
        weights = role_patterns['attribute_weight']
        base_score = (
            player.ability_stats.tactical * weights['tactical'] +
            player.ability_stats.kicking * weights['kicking']
            # Add other attributes if available
        ) / len(weights)

        special_conditions = role_patterns['special_conditions']
        
        # Check if player meets the threshold
        meets_threshold = base_score >= special_conditions['threshold']
        
        # Random chance to attempt anyway
        feeling_lucky = random.random() < special_conditions['risk_factor']
        
        # Check for momentum/confidence triggers
        has_momentum = self._check_player_momentum(player, state)
        
        # Special game situations that might encourage attempting the role
        game_situation = self._evaluate_game_situation(state, player, has_momentum)
        attempt_anyway = feeling_lucky or has_momentum or game_situation > 1.2
        
        return {
            'eligible': meets_threshold,
            'attempt_anyway': attempt_anyway,
            'confidence_multiplier': self._calculate_confidence_boost(
                player, 
                state, 
                special_conditions['confidence_boost'],
                has_momentum
            ),
            'x_factor_active': random.random() < special_conditions['x_factor']['chance']
        }    


    """
    def _can_attempt_daicos_role(self, player, state, role_patterns):
       
        #Determines if a player can/should attempt TheDaicos role
       
        print(f"Player {player.name} is attempting TheDaicos role {role_patterns}")
        pattern = role_patterns
        
        # Calculate base eligibility score
        base_score = (
            player.ability_stats.tactical * 1.5 +
            player.ability_stats.kicking * 1.3 
            #player.ability_stats.vision * 1.2
        ) / 2

        # Check if player meets the threshold
        meets_threshold = base_score >= pattern['special_conditions']['threshold']
        
        # Random chance to attempt anyway
        feeling_lucky = random.random() < pattern['special_conditions']['risk_factor']
        
        # Check for momentum/confidence triggers
        has_momentum = self._check_player_momentum(player, state)
        
        # Special game situations that might encourage attempting the role
        game_situation = self._evaluate_game_situation(state)
        attempt_anyway = feeling_lucky or has_momentum or game_situation > 1.2  # Add game situation impact
        
        return {
            'eligible': meets_threshold,
            'attempt_anyway': attempt_anyway,
            'confidence_multiplier': self._calculate_confidence_boost(
                player, 
                state, 
                pattern['special_conditions']['confidence_boost']
            ),
            'x_factor_active': random.random() < pattern['special_conditions']['x_factor']['chance']
        }
    """

    def _calculate_daicos_impact(self, player, state, attempt_data):
        """
        Calculate the impact of attempting TheDaicos role
        """
        if attempt_data['eligible']:
            # Player has the skills - normal positive impact
            base_impact = 1.0
            if attempt_data['x_factor_active']:
                # Potential for magical moment
                base_impact *= self.role_patterns['TheDaicos']['special_conditions']['x_factor']['impact']
        else:
            # Player attempting beyond their skill level
            base_impact = 0.6  # Reduced effectiveness
            if attempt_data['x_factor_active']:
                # Could still come off...
                base_impact = random.uniform(0.4, 1.3)  # High risk, high reward

        return base_impact * attempt_data['confidence_multiplier']
    
    def _calculate_confidence_boost(self, player, state, confidence_settings, has_momentum):
        """
        Calculate confidence boost based on momentum and settings
        Now accepts has_momentum parameter
        """
        if confidence_settings['trigger'] == 'momentum' and has_momentum:
            return confidence_settings['boost_amount']
        return 1.0  # No boost

    def _calculate_role_suitability(self, player, pattern, state):
        """
        Calculate how suitable a role is for a player in current situation.
        """
        score = 0
        
        # Weight player attributes according to role requirements
        for attr, weight in pattern['attribute_weight'].items():
            if hasattr(player.ability_stats, attr):
                score += getattr(player.ability_stats, attr) * weight
            elif hasattr(player.physical_stats, attr):
                score += getattr(player.physical_stats, attr) * weight
        
        # Adjust for game situation
        situation_modifier = self._evaluate_game_situation(state, player, has_momentum=None)
        score *= situation_modifier
        
        # Consider team strategy alignment
        #strategy_alignment = self._check_strategy_alignment(pattern, state)
        #score *= strategy_alignment
        
        return score

    def _evaluate_game_situation(self, state, player, has_momentum):
        """
        Evaluate current game situation to determine role suitability modifiers.
        Returns a multiplier between 0.5 and 1.5.
        """
        score = 1.0  # Base multiplier
        print(f"Player {player.name} is in game situation: {state}")
        team_side = state['team_side']

        # Debug print to check game_state
        if self.game_state is None:
            print("Game state is not set.")
            return score
        else:
            print("Game state is set:", self.game_state)
        
        
        # Score difference impact

        score_diff = self.game_state.get_score_difference(team_side)
        print(f"Score Diff {score_diff}")

        if score_diff < 0:  # Behind
            if abs(score_diff) > 24:  # More than 4 goals down
                score *= 1.3  # Encourage more aggressive roles
            else:
                score *= 1.1  # Slightly more aggressive
        elif score_diff > 24:  # More than 4 goals up
            score *= 0.8  # More conservative roles
            
        # Time remaining impact

        time_remaining = self.game_state.time_remaining
        if time_remaining < 300:  # Last 5 minutes
            if score_diff < 0:  # Behind
                score *= 1.4  # Very aggressive
            elif 0 <= score_diff <= 12:  # Up by 2 goals or less
                score *= 0.7  # Very conservative
                
        # Field position impact
        if self.game_state.ball_position in self.ground.zones['forward']:
            score *= 1.2  # Attacking opportunity
        elif self.game_state.ball_position in self.game_state.ground.zones['defensive']:
            score *= 0.8  # Defensive situation

        print(f"Has Momentum {has_momentum}")    
        # Momentum consideration
        if has_momentum:
            score *= 1.2
            
        return max(0.5, min(1.5, score))  # Clamp between 0.5 and 1.5

    def _check_player_momentum(self, player, state):
        """
        Check if player has momentum based on recent performance
        Returns True if player has momentum, False otherwise
        """
        # Basic momentum checks
        momentum_factors = {
            'recent_disposals': 0,
            'recent_marks': 0,
            'recent_tackles': 0,
            'recent_goals': 0,
            'recent_score_involvements': 0
        }
        
        # Could be expanded to check:
        # - Recent possessions/disposals
        # - Recent score involvements
        # - Recent contested marks
        # - Team momentum
        # - Ground position/territory
        
        # For now, simplified version:
        momentum_score = (
            momentum_factors['recent_disposals'] * 0.3 +
            momentum_factors['recent_marks'] * 0.2 +
            momentum_factors['recent_tackles'] * 0.2 +
            momentum_factors['recent_goals'] * 0.2 +
            momentum_factors['recent_score_involvements'] * 0.1
        )
        
        # Default to random chance if we don't have momentum data yet
        return random.random() < 0.3  # 30% chance of having "momentum" 

    def _calculate_dynamic_position(self, state):
        """
        Calculate dynamic position for elite midfielders (like Daicos).
        Takes into account:
        - Ball position
        - Space availability
        - Scoring opportunity
        - Opposition positions
        """
        ball_pos = state['ball_position']
        
        # For now, implement a basic dynamic position
        # Slightly forward and to the side of the ball
        dynamic_x = ball_pos[0] + random.randint(-3, 3)  # Random offset for unpredictability
        dynamic_y = ball_pos[1] + random.randint(-2, 2)  # Smaller Y variation
        
        # Ensure position stays within ground boundaries
        dynamic_x = max(0, min(dynamic_x, self.ground.width))
        dynamic_y = max(0, min(dynamic_y, self.ground.length))
        
        return (dynamic_x, dynamic_y)
          
    """
    def _check_strategy_alignment(self, pattern, state):
        
        #Check how well a role pattern aligns with current team strategy.
        #Returns a multiplier between 0.7 and 1.3.
        
        team_strategy = state.get_team_strategy(self.player.team)
        alignment_score = 1.0
        
        # Check role type alignment
        if pattern.get('type') in team_strategy.get('preferred_roles', []):
            alignment_score *= 1.2
        elif pattern.get('type') in team_strategy.get('avoided_roles', []):
            alignment_score *= 0.8
            
        # Check style alignment
        if pattern.get('style') == team_strategy.get('play_style'):
            alignment_score *= 1.1
            
        # Check risk alignment
        pattern_risk = pattern.get('risk_level', 'medium')
        strategy_risk = team_strategy.get('risk_preference', 'medium')
        
        if pattern_risk == strategy_risk:
            alignment_score *= 1.1
        elif (pattern_risk == 'high' and strategy_risk == 'low') or \
            (pattern_risk == 'low' and strategy_risk == 'high'):
            alignment_score *= 0.8
            
        return max(0.7, min(1.3, alignment_score))  # Clamp between 0.7 and 1.3       
    """
    def _midfielder_spread_pattern(self, state, player):
        """
        Generate movement pattern for midfielders during spread play.
        Key focus on creating optimal spacing and maintaining structure.
        """
        ball_pos = state['ball_position']
        
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Get all midfielders for coordination
        midfielders = self._get_team_midfielders(state)
        
        # Calculate optimal spread position based on other midfielder positions
        spread_data = self._calculate_spread_position(
            self.player,
            midfielders,
            state
        )
        
        # Age consideration for running capacity
        age_penalty = max(0, (physical.age - 30) * 0.05)
        stamina_modifier = 1 - age_penalty
        
        return {
            'type': 'spread_pattern',
            'phases': [
                {
                    'action': 'position',
                    'target': spread_data['target'],
                    'timing': 'immediate',
                    'confidence_factor': ability.consistency / 20,
                    'coordination': spread_data['coordination']
                },
                {
                    'action': 'read_play',
                    'focus': 'space_utilization',
                    'timing': 'continuous',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'maintain_structure',
                    'primary': 'hold_width',      # Maintain optimal width
                    'secondary': 'link_play',     # Provide connection option
                    'timing': 'during_possession',
                    'tactical': ability.tactical / 20,
                    'stamina': physical.stamina / 20 * stamina_modifier
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'teammate_positions': self._adjust_for_teammates,
                'fatigue': physical.stamina / 20 * stamina_modifier
            }
        }

    def _midfielder_defensive_press_pattern(self, state, player):
        """
        Generate movement pattern for midfielders during pressing.
        Key focus on coordinated pressure and cutting off options.
        """
        ball_pos = state['ball_position']

        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats        
        
        # Calculate optimal pressing position and coordination
        press_data = self._calculate_press_position(
            self.player,
            state
        )
        
        return {
            'type': 'press_pattern',
            'phases': [
                {
                    'action': 'position',
                    'target': press_data['target'],
                    'timing': 'immediate',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'press',
                    'primary': 'cut_options',    # Cut off passing lanes
                    'secondary': 'force_error',  # Force turnover
                    'timing': 'on_opposition_possession',
                    'pressure': physical.stamina / 20,
                    'speed': physical.speed / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'teammate_positions': self._adjust_for_teammates,
                'press_intensity': self._calculate_press_intensity(state)
            }
        }
    
    def _midfielder_follow_pattern(self, state, player):
        """
        Generate movement pattern for midfielders following the play.
        Key focus on maintaining optimal position relative to the ball and teammates.
        """
        ball_pos = state['ball_position']
        
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Calculate optimal following position
        follow_data = self._calculate_follow_position(
            self.player,
            state
        )
        
        # Age consideration for running capacity
        age_penalty = max(0, (physical.age - 30) * 0.05)
        stamina_modifier = 1 - age_penalty
        
        return {
            'type': 'follow_pattern',
            'phases': [
                {
                    'action': 'position',
                    'target': follow_data['target'],
                    'timing': 'continuous',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'read_play',
                    'focus': 'ball_movement',
                    'timing': 'continuous',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'maintain_position',
                    'primary': 'support_play',     # Provide support option
                    'secondary': 'cover_space',    # Cover defensive space
                    'timing': 'during_play',
                    'stamina': physical.stamina / 20 * stamina_modifier,
                    'tactical': ability.tactical / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'teammate_positions': self._adjust_for_teammates,
                'fatigue': physical.stamina / 20 * stamina_modifier
            }
        }

    def _midfielder_link_pattern(self, state, player):
        """
        Generate movement pattern for midfielders linking play.
        Key focus on creating connections between defense and attack.
        """
        ball_pos = state['ball_position']
        
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Calculate optimal linking position
        link_data = self._calculate_link_position(
            self.player,
            state
        )
        
        return {
            'type': 'link_pattern',
            'phases': [
                {
                    'action': 'position',
                    'target': link_data['target'],
                    'timing': 'immediate',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'create_option',
                    'primary': 'provide_outlet',    # Provide passing option
                    'secondary': 'switch_play',     # Option to switch play
                    'timing': 'during_possession',
                    'tactical': ability.tactical / 20,
                    'skill': ability.vision / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'teammate_positions': self._adjust_for_teammates,
                'play_style': self._get_team_play_style(state)
            }
        }

    def _midfielder_marking_pattern(self, state, player):
        """
        Generate movement pattern for midfielders during marking contests.
        Key focus on reading ball flight, positioning, and either marking or spoiling.
        """
        ball_pos = state['ball_position']
        
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Calculate optimal marking position
        marking_data = self._calculate_marking_position(
            self.player,
            state
        )
        
        # Age consideration for jumping/timing
        age_penalty = max(0, (physical.age - 30) * 0.05)
        power_modifier = 1 - age_penalty
        
        return {
            'type': 'marking_contest',
            'phases': [
                {
                    'action': 'position',
                    'target': marking_data['target'],
                    'timing': 'pre_contest',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'read_play',
                    'focus': 'ball_flight',
                    'timing': 'during_flight',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'contest',
                    'primary': marking_data['primary_action'],    # Mark or spoil based on position
                    'secondary': marking_data['secondary_action'], # Front position or protect drop
                    'timing': 'on_contest',
                    'marking': ability.marking / 20 * power_modifier,
                    #'leap': physical.leap / 20 * power_modifier,
                    'strength': physical.strength / 20,
                    'timing': ability.timing / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'weather_conditions': self._adjust_for_conditions,
                'teammate_positions': self._adjust_for_teammates,
                'contest_type': marking_data['contest_type']
            }
        }

    def _midfielder_ground_pattern(self, state, player):
        """
        Generate movement pattern for midfielders during ground ball contests.
        Key focus on winning first possession and quick disposal.
        """
        ball_pos = state['ball_position']
        
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Calculate optimal ground ball position
        ground_data = self._calculate_ground_position(
            self.player,
            state
        )
        
        # Age consideration for reaction time
        age_penalty = max(0, (physical.age - 30) * 0.05)
        reaction_modifier = 1 - age_penalty
        
        return {
            'type': 'ground_contest',
            'phases': [
                {
                    'action': 'position',
                    'target': ground_data['target'],
                    'timing': 'pre_contest',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'read_bounce',
                    'focus': 'ball_trajectory',
                    'timing': 'during_bounce',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'contest',
                    'primary': 'clean_pickup',        # Clean possession
                    'secondary': 'knock_forward',     # Knock to advantage
                    'tertiary': 'lock_ball_in',      # Lock it in if outnumbered
                    'timing': 'on_contest',
                    #'ground_ball': ability.ground_ball / 20 * reaction_modifier,
                    'agility': physical.agility / 20 * reaction_modifier,
                    'strength': physical.strength / 20,
                    'balance': physical.balance / 20
                },
                {
                    'action': 'disposal',
                    'primary': 'quick_hands',         # Quick handball
                    'secondary': 'snap_kick',         # Quick kick
                    'timing': 'on_possession',
                    'disposal': ability.disposal / 20,
                    'vision': ability.vision / 20,
                    'pressure_tolerance': ability.composure / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'ground_conditions': self._adjust_for_conditions,
                'teammate_positions': self._adjust_for_teammates,
                'contest_intensity': ground_data['intensity']
            }
        }        

    def _ruck_stoppage_pattern(self, state, player):
        """
        Generate movement pattern for ruck during stoppages.
        Primary focus on winning ruck contests and providing advantage to midfielders.
        """
        ball_pos = state['ball_position']
        
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Analyze opposition ruck
        #opp_ruck = self._get_opposition_ruck(state)
        #ruck_matchup = self._analyze_ruck_matchup(self.player, opp_ruck)
        
        # Get midfielder positions and identify best tap targets
        #tap_options = self._analyze_tap_options(state)
        
        # Determine optimal ruck strategy based on matchup
        #strategy = self._determine_ruck_strategy(ruck_matchup, tap_options)
        strategy = [None]
        
        return {
            'type': 'ruck_contest',
            'strategy': strategy, #strategy['type'],   'tap_to_advantage', 'contest_neutralize', or 'bring_to_ground'
            'phases': [
                {
                    'action': 'position',
                    'target': ball_pos,
                    'timing': 'pre_contest',
                    'confidence_factor': ability.consistency / 20,
                    'positioning': None
                },
                {
                    'action': 'read_play',
                    'focus': 'midfielder_positions',
                    'timing': 'pre_contest',
                    'tactical': ability.tactical / 20,
                    'preferred_target': None
                },
                {
                    'action': 'contest',
                    'primary': strategy,#strategy['primary_action'],     Tap, contest, or neutralize
                    'secondary': strategy,#strategy['secondary_action'],  Followup action
                    'timing': 'on_contest',
                    'tactical': ability.tactical / 20,
                    'height': physical.height / 20,
                    'strength': physical.strength / 20,
                    'tap_target': strategy, #strategy['tap_target']
                }
            ],
            'adjustments': {
                #'opposition_setup': self._adjust_for_opposition_ruck,
                'ground_conditions': self._adjust_for_conditions,
                'teammate_positions': self._adjust_for_teammates
            }
        }

    def _determine_ruck_strategy(self, matchup, tap_options):
        """
        Determine optimal ruck strategy based on matchup and options.
        """
        if matchup['advantage'] > 0.2:  # Clear advantage
            # Look for precision tap to advantage
            if any(o['role'] == 'TheDaicos' for o in tap_options):
                return {
                    'type': 'tap_to_advantage',
                    'primary_action': 'precise_tap',
                    'secondary_action': 'follow_up',
                    'tap_target': self._get_daicos_position(tap_options)
                }
            return {
                'type': 'tap_to_advantage',
                'primary_action': 'tap_to_space',
                'secondary_action': 'follow_up',
                'tap_target': self._get_best_tap_option(tap_options)
            }
        elif matchup['advantage'] < -0.2:  # At disadvantage
            return {
                'type': 'bring_to_ground',
                'primary_action': 'disrupt_tap',
                'secondary_action': 'ground_contest',
                'tap_target': None
            }
        else:  # Even contest
            return {
                'type': 'contest_neutralize',
                'primary_action': 'physical_contest',
                'secondary_action': 'protect_drop',
                'tap_target': self._get_safe_tap_option(tap_options)
            }
        

    def _ruck_marking_pattern(self, state, player):
        """
        Generate movement pattern for ruck during marking contests.
        Focus on providing aerial support and creating contests.
        Uses standard marking calculations with ruck's height/strength advantage.
        """
        ball_pos = state['ball_position']
        
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Use standard marking position calculation
        marking_data = self._calculate_marking_position(
            self.player,
            state
        )
        
        return {
            'type': 'marking_contest',
            'phases': [
                {
                    'action': 'position',
                    'target': marking_data['target'],
                    'timing': 'pre_contest',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'read_play',
                    'focus': 'ball_flight',
                    'timing': 'during_flight',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'contest',
                    'primary': marking_data['primary_action'],
                    'secondary': marking_data['secondary_action'],
                    'timing': 'on_contest',
                    'marking': ability.marking / 20,
                    'height': physical.height / 20,
                    'strength': physical.strength / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'weather_conditions': self._adjust_for_conditions,
                'teammate_positions': self._adjust_for_teammates
            }
        }

    def _ruck_defensive_press_pattern(self, state, player):
        """
        Generate movement pattern for ruck defensive press.
        Focus on following opposition ruck and providing aerial support.
        Uses standard press calculations.
        """       
        
         # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Use standard press position calculation
        press_data = self._calculate_press_position(
            self.player,
            state
        )
        
        return {
            'type': 'defensive_press',
            'phases': [
                {
                    'action': 'position',
                    'target': press_data['target'],
                    'timing': 'immediate',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'press',
                    'primary': 'cut_options',
                    'secondary': 'force_error',
                    'timing': 'on_opposition_possession',
                    'pressure': physical.stamina / 20,
                    'versatility': physical.versatility / 20,
                    'speed': physical.speed / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'teammate_positions': self._adjust_for_teammates,
                'press_intensity': self._calculate_press_intensity(state)
            }
        }

    def _ruck_goal_square_pattern(self, state, player):
        """
        Generate movement pattern for ruck in goal square contests.
        Focus on creating marking contests or bringing ball to ground.
        Uses standard marking contest calculations.
        """
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats

        # Use standard marking contest calculations
        contest_data = self._calculate_marking_position(
            self.player,
            state
        )
        
        return {
            'type': 'goal_square_contest',
            'phases': [
                {
                    'action': 'position',
                    'target': contest_data['target'],
                    'timing': 'pre_contest',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'contest',
                    'primary': contest_data['primary_action'],
                    'secondary': contest_data['secondary_action'],
                    'timing': 'on_contest',
                    'marking': ability.marking / 20,
                    'strength': physical.strength / 20,
                    'versatility': physical.versatility / 20,
                    'height': physical.height / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'teammate_positions': self._adjust_for_teammates,
                'weather_conditions': self._adjust_for_conditions
            }
        }
    
    def _ruck_ground_pattern(self, state, player):
        """
        Generate movement pattern for ruck during ground ball contests.
        Being tall makes ground balls more challenging, but strength can be an advantage.
        """
        ball_pos = state['ball_position']
        
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Calculate optimal ground ball position
        ground_data = self._calculate_ground_position(
            self.player,
            state
        )
        
        # Height penalty for ground balls
        height_penalty = (physical.height - 180) * 0.01  # Taller = harder to get low
        #sys.exit()
        
        return {
            'type': 'ground_contest',
            'phases': [
                {
                    'action': 'position',
                    'target': ground_data['target'],
                    'timing': 'pre_contest',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'read_bounce',
                    'focus': 'ball_trajectory',
                    'timing': 'during_bounce',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'contest',
                    'primary': 'body_protection',     # Use size to protect ball
                    'secondary': 'knock_forward',     # Knock to advantage if can't gather
                    'timing': 'on_contest',
                    'ground_ball': (ability.ground_ball / 20) - height_penalty,
                    'agility': physical.agility / 20,
                    'strength': physical.strength / 20,
                    'versatility': physical.versatility / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'ground_conditions': self._adjust_for_conditions,
                'teammate_positions': self._adjust_for_teammates,
                'contest_intensity': ground_data['intensity']
            }
        }

    def _ruck_follow_pattern(self, state, player):
        """
        Generate movement pattern for ruck following the play.
        Focus on providing aerial support and getting to next stoppage.
        """
        ball_pos = state['ball_position']
        
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Calculate optimal following position
        follow_data = self._calculate_follow_position(
            self.player,
            state
        )
        
        # Age consideration for running capacity
        age_penalty = max(0, (physical.age - 30) * 0.05)
        stamina_modifier = 1 - age_penalty
        
        return {
            'type': 'follow_pattern',
            'phases': [
                {
                    'action': 'position',
                    'target': follow_data['target'],
                    'timing': 'continuous',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'read_play',
                    'focus': 'potential_stoppages',  # Look for where next stoppage might be
                    'timing': 'continuous',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'maintain_position',
                    'primary': 'aerial_support',     # Provide marking target
                    'secondary': 'stoppage_ready',   # Be ready for next ruck contest
                    'timing': 'during_play',
                    'stamina': physical.stamina / 20 * stamina_modifier,
                    'tactical': ability.tactical / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'teammate_positions': self._adjust_for_teammates,
                'fatigue': physical.stamina / 20 * stamina_modifier
            }
        }

    def _ruck_spread_pattern(self, state, player):
        """
        Generate movement pattern for ruck during spread play.
        Focus on providing a tall target while maintaining stoppage readiness.
        """
        ball_pos = state['ball_position']
        
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Calculate optimal spread position
        spread_data = self._calculate_spread_position(
            self.player,
            state
        )
        
        return {
            'type': 'spread_pattern',
            'phases': [
                {
                    'action': 'position',
                    'target': spread_data['target'],
                    'timing': 'immediate',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'read_play',
                    'focus': 'space_utilization',
                    'timing': 'continuous',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'maintain_structure',
                    'primary': 'tall_target',       # Provide tall marking option
                    'secondary': 'central_position', # Stay central for quick access to stoppages
                    'timing': 'during_possession',
                    'tactical': ability.tactical / 20,
                    'versatility': physical.versatility / 20,
                    'stamina': physical.stamina / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'teammate_positions': self._adjust_for_teammates,
                'field_position': self._adjust_for_field_position(state)
            }
        }
    
    def _wing_stoppage_pattern(self, state, player):
        """
        Generate movement pattern for wings during stoppages.
        Key focus on providing width and outlet options from stoppages.
        """
        ball_pos = state['ball_position']
        player_pos = state['player_position']
        
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Determine which wing
        is_left_wing = player_pos == 'LWing'
        is_right_wing = player_pos == 'RWing'
        
        # Calculate base position relative to stoppage
        if is_left_wing:
            position_offset = (-8, 0)  # Wide left
        elif is_right_wing:
            position_offset = (8, 0)   # Wide right
            
        # Adjust based on attributes
        speed_factor = physical.speed / 20
        tactical_factor = ability.tactical / 20
        
        target_pos = (
            ball_pos[0] + (position_offset[0] * (1 + tactical_factor)),
            ball_pos[1] + (position_offset[1] * (1 + speed_factor))
        )
        
        return {
            'type': 'stoppage_contest',
            'phases': [
                {
                    'action': 'position',
                    'target': target_pos,
                    'timing': 'pre_contest',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'read_play',
                    'focus': 'clearance_options',
                    'timing': 'during_contest',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'provide_option',
                    'primary': 'width_outlet',     # Provide wide option
                    'secondary': 'run_receive',    # Run to receive if clear
                    'timing': 'on_clearance',
                    'speed': physical.speed / 20,
                    'stamina': physical.stamina / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'teammate_positions': self._adjust_for_teammates,
                'field_position': self._adjust_for_field_position(state)
            }
        }

    def _wing_corridor_pattern(self, state, player):
        """
        Generate movement pattern for wings running corridors.
        Key focus on using pace and width to break lines.
        """
        ball_pos = state['ball_position']
        player_pos = state['player_position']

        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Determine which wing
        is_left_wing = player_pos == 'LW'
        is_right_wing = player_pos == 'RW'
        
        # Calculate corridor run path
        if is_left_wing:
            corridor_offset = (-10, -5)  # Left corridor
        elif is_right_wing:
            corridor_offset = (10, -5)   # Right corridor
            
        # Age consideration for running capacity
        age_penalty = max(0, (physical.age - 30) * 0.05)
        stamina_modifier = 1 - age_penalty
        
        target_pos = (
            ball_pos[0] + corridor_offset[0],
            ball_pos[1] + corridor_offset[1]
        )
        
        return {
            'type': 'corridor_run',
            'phases': [
                {
                    'action': 'position',
                    'target': target_pos,
                    'timing': 'immediate',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'run_pattern',
                    'primary': 'break_lines',      # Run to break defensive lines
                    'secondary': 'link_play',      # Link with forwards
                    'timing': 'on_possession',
                    'speed': physical.speed / 20 * stamina_modifier,
                    'stamina': physical.stamina / 20 * stamina_modifier,
                    'agility': physical.agility / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'teammate_positions': self._adjust_for_teammates,
                'fatigue': physical.stamina / 20 * stamina_modifier
            }
        }

    def _wing_marking_pattern(self, state, player):
        """
        Generate movement pattern for wings during marking contests.
        Focus on providing wide marking options and crumbing.
        """
        ball_pos = state['ball_position']
        player_pos = state['player_position']

        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Determine which wing
        is_left_wing = player_pos == 'LW'
        is_right_wing = player_pos == 'RW'
        
        # Calculate marking position with wing bias
        marking_data = self._calculate_marking_position(
            self.player,
            state,
            side_bias='left' if is_left_wing else 'right'
        )
        
        return {
            'type': 'marking_contest',
            'phases': [
                {
                    'action': 'position',
                    'target': marking_data['target'],
                    'timing': 'pre_contest',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'read_play',
                    'focus': 'crumb_opportunity',  # Look for crumbing chances
                    'timing': 'during_contest',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'contest',
                    'primary': marking_data['primary_action'],
                    'secondary': 'crumb_position',  # Get ready to crumb
                    'timing': 'on_contest',
                    'marking': ability.marking / 20,
                    'agility': physical.agility / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'weather_conditions': self._adjust_for_conditions,
                'teammate_positions': self._adjust_for_teammates
            }
        }

    def _wing_ground_pattern(self, state, player):
        """
        Generate movement pattern for wings during ground ball contests.
        Focus on using pace and agility to win ground balls.
        """
        ball_pos = state['ball_position']
        
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Calculate optimal ground ball position
        ground_data = self._calculate_ground_position(
            self.player,
            state
        )
        
        return {
            'type': 'ground_contest',
            'phases': [
                {
                    'action': 'position',
                    'target': ground_data['target'],
                    'timing': 'pre_contest',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'read_bounce',
                    'focus': 'ball_trajectory',
                    'timing': 'during_bounce',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'contest',
                    'primary': 'gather_pace',      # Gather ball at speed
                    'secondary': 'burst_away',     # Use pace to break away
                    'timing': 'on_contest',
                    'agility': physical.agility / 20,
                    'speed': physical.speed / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'ground_conditions': self._adjust_for_conditions,
                'teammate_positions': self._adjust_for_teammates
            }
        }

    def _wing_spread_pattern(self, state, player):
        """
        Generate movement pattern for wings during spread play.
        Focus on maintaining width and providing switch options.
        """
        ball_pos = state['ball_position']
        player_pos = state['player_position']

        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Determine which wing
        is_left_wing = player_pos == 'LWing'
        is_right_wing = player_pos == 'RWing'
        
        # Calculate spread position with maximum width
        spread_offset = (-15, 0) if is_left_wing else (15, 0)
        
        target_pos = (
            ball_pos[0] + spread_offset[0],
            ball_pos[1] + spread_offset[1]
        )
        
        return {
            'type': 'spread_pattern',
            'phases': [
                {
                    'action': 'position',
                    'target': target_pos,
                    'timing': 'immediate',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'maintain_width',
                    'primary': 'switch_option',    # Provide switch of play option
                    'secondary': 'forward_link',   # Link with forwards
                    'timing': 'continuous',
                    'tactical': ability.tactical / 20,
                    'stamina': physical.stamina / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'teammate_positions': self._adjust_for_teammates,
                'field_position': self._adjust_for_field_position(state)
            }
        }

    def _wing_link_pattern(self, state, player):
        """
        Generate movement pattern for wings linking play.
        Focus on providing connection between defense and attack.
        """
        ball_pos = state['ball_position']
        
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Calculate optimal linking position
        link_data = self._calculate_link_position(
            self.player,
            state
        )
        
        return {
            'type': 'link_pattern',
            'phases': [
                {
                    'action': 'position',
                    'target': link_data['target'],
                    'timing': 'immediate',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'create_option',
                    'primary': 'overlap_run',     # Create overlap run
                    'secondary': 'hold_width',    # Maintain width
                    'timing': 'during_possession',
                    'speed': physical.speed / 20,
                    'stamina': physical.stamina / 20,
                    'tactical': ability.tactical / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'teammate_positions': self._adjust_for_teammates,
                'field_position': self._adjust_for_field_position(state)
            }
        }

    def _wing_defensive_press_pattern(self, state, player):
        """
        Generate movement pattern for wings during defensive press.
        Focus on cutting off switch of play options and forcing play inside.
        """
        ball_pos = state['ball_position']
        
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Calculate optimal pressing position
        press_data = self._calculate_press_position(
            self.player,
            state
        )
        
        return {
            'type': 'defensive_press',
            'phases': [
                {
                    'action': 'position',
                    'target': press_data['target'],
                    'timing': 'immediate',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'press',
                    'primary': 'cut_switch',      # Prevent switch of play
                    'secondary': 'force_inside',  # Force play inside
                    'timing': 'on_opposition_possession',
                    'pressure': physical.stamina / 20,
                    'speed': physical.speed / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'teammate_positions': self._adjust_for_teammates,
                'press_intensity': self._calculate_press_intensity(state)
            }
        }

    def _back_pocket_stoppage_pattern(self, state, player):
        """
        Generate movement pattern for back pockets during stoppages.
        Focus on defensive positioning and protecting dangerous space.
        """
        ball_pos = state['ball_position']
        player_pos = state['player_position']

        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Determine which pocket
        is_left_pocket = player_pos == 'LB'
        is_right_pocket = player_pos == 'RB'
        
        # Calculate base position relative to stoppage
        if is_left_pocket:
            pocket_offset = (-4, 4)  # Defensive left pocket
        elif is_right_pocket:
            pocket_offset = (4, 4)   # Defensive right pocket
            
        target_pos = (
            ball_pos[0] + pocket_offset[0],
            ball_pos[1] + pocket_offset[1]
        )
        
        return {
            'type': 'stoppage_contest',
            'phases': [
                {
                    'action': 'position',
                    'target': target_pos,
                    'timing': 'pre_contest',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'read_play',
                    'focus': 'dangerous_space',
                    'timing': 'during_contest',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'protect',
                    'primary': 'goal_side',       # Stay goal side
                    'secondary': 'block_opponent',     # Block opponent
                    'tertiary': 'block_run',   # Block dangerous runs
                    'timing': 'continuous',
                    'strength': physical.strength / 20,
                    'mental': ability.mental / 20,
                    'tactical': ability.tactical / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'teammate_positions': self._adjust_for_teammates,
                'field_position': self._adjust_for_field_position(state)
            }
        }

    def _back_pocket_marking_pattern(self, state, player):
        """
        Generate movement pattern for back pockets during marking contests.
        Focus on spoiling and protecting the drop zone.
        """
        #ball_pos = state['ball_position']
        
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Calculate marking position
        marking_data = self._calculate_marking_position(
            self.player,
            state
        )
        
        return {
            'type': 'marking_contest',
            'phases': [
                {
                    'action': 'position',
                    'target': marking_data['target'],
                    'timing': 'pre_contest',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'read_play',
                    'focus': 'opposition_forward',
                    'timing': 'during_flight',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'contest',
                    'primary': 'spoil',           # Focus on spoiling
                    'secondary': 'crumb',  
                    'tertiary': 'protect_drop', # Protect the drop zone
                    'timing': 'on_contest',
                    'marking': ability.marking / 20,
                    'agility': physical.agility / 20,
                    'height': physical.height / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'weather_conditions': self._adjust_for_conditions,
                'teammate_positions': self._adjust_for_teammates
            }
        }

    def _back_pocket_ground_pattern(self, state, player):
        """
        Generate movement pattern for back pockets during ground contests.
        Focus on clearing the danger and protecting space.
        """
        ball_pos = state['ball_position']
        
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Calculate ground contest position
        ground_data = self._calculate_ground_position(
            self.player,
            state
        )
        
        return {
            'type': 'ground_contest',
            'phases': [
                {
                    'action': 'position',
                    'target': ground_data['target'],
                    'timing': 'pre_contest',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'contest',
                    'primary': 'clear_danger',    # Clear ball from danger
                    'secondary': 'lock_in',       # Lock ball in if outnumbered
                    'timing': 'on_contest',
                    'agility': physical.agility / 20,
                    'speed': physical.speed / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'ground_conditions': self._adjust_for_conditions,
                'teammate_positions': self._adjust_for_teammates
            }
        }

    def _back_pocket_cover_pattern(self, state, player):
        """
        Generate movement pattern for back pockets providing defensive cover.
        Focus on protecting dangerous space and supporting teammates.
        """
        ball_pos = state['ball_position']
        
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Calculate cover position
        cover_pos = self._calculate_defensive_cover_position(
            self.player,
            state
        )
        
        return {
            'type': 'defensive_cover',
            'phases': [
                {
                    'action': 'position',
                    'target': cover_pos,
                    'timing': 'continuous',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'protect',
                    'primary': 'dangerous_space',  # Protect dangerous space
                    'secondary': 'support_def',    # Support other defenders
                    'timing': 'continuous',
                    'speed': physical.speed / 20,
                    'stamina': physical.stamina / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'teammate_positions': self._adjust_for_teammates,
                'field_position': self._adjust_for_field_position(state)
            }
        } 

    def _back_pocket_intercept_pattern(self, state, player):
        """
        Generate movement pattern for back pockets looking to intercept.
        Focus on reading play and cutting off forward entries.
        """
        ball_pos = state['ball_position']
        player_pos = state['player_position']
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Consider both position and opponent
        is_left_pocket = player_pos == 'LB'
        is_right_pocket = player_pos == 'RB'
        
        opponent = self._get_direct_opponent(state)
        is_marking_left_forward = opponent and opponent.position == 'LF'
        is_marking_right_forward = opponent and opponent.position == 'RF'
        
        # Calculate intercept position considering both factors
        intercept_data = self._calculate_intercept_position(
            self.player,
            state,
            side='left' if (is_left_pocket or is_marking_left_forward) else 'right'
        )
        
        return {
            'type': 'intercept_pattern',
            'phases': [
                {
                    'action': 'position',
                    'target': intercept_data['target'],
                    'timing': 'pre_entry',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'read_play',
                    'focus': 'forward_entry',
                    'timing': 'continuous',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'move',
                    'primary': 'cut_off_lane',
                    'secondary': 'maintain_opponent',  # Don't lose opponent
                    'timing': 'on_forward_entry',
                    'speed': physical.speed / 20,
                    'agility': physical.agility / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'teammate_positions': self._adjust_for_teammates,
                'field_position': self._adjust_for_field_position(state)
            }
        }

    def _back_pocket_rebound_pattern(self, state, player):
        """
        Generate movement pattern for back pockets during rebound situations.
        Focus on getting to drop zone while maintaining defensive responsibility.
        """
        ball_pos = state['ball_position']
        player_pos = state['player_position']

        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Consider both position and opponent
        is_left_pocket = player_pos == 'LB'
        is_right_pocket = player_pos == 'RB'
        
        opponent = self._get_direct_opponent(state)
        is_marking_left_forward = opponent and opponent.position == 'LF'
        is_marking_right_forward = opponent and opponent.position == 'RF'
        
        # Calculate rebound position considering both factors
        rebound_data = self._calculate_rebound_position(
            self.player,
            state,
            side='left' if (is_left_pocket or is_marking_left_forward) else 'right'
        )
        
        return {
            'type': 'rebound_pattern',
            'phases': [
                {
                    'action': 'position',
                    'target': rebound_data['target'],
                    'timing': 'pre_contest',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'read_play',
                    'focus': 'drop_zone',
                    'timing': 'during_contest',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'move',
                    'primary': 'get_to_drop',
                    'secondary': 'track_opponent',  # Keep awareness of opponent
                    'timing': 'on_contest',
                    'speed': physical.speed / 20,
                    'agility': physical.agility / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'teammate_positions': self._adjust_for_teammates,
                'field_position': self._adjust_for_field_position(state)
            }
        }

    def _half_back_stoppage_pattern(self, state, player):
        """
        Generate movement pattern for half backs during stoppages.
        Focus on defensive positioning while being ready to provide rebound/link option.
        """
        ball_pos = state['ball_position']
        player_pos = state['player_position']

        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Consider both position and opponent
        is_left_half_back = player_pos == 'LHB'
        is_right_half_back = player_pos == 'RHB'
        
        opponent = self._get_direct_opponent(state)
        is_marking_left_half_forward = opponent and opponent.position == 'LHF'
        is_marking_right_half_forward = opponent and opponent.position == 'RHF'
        
        # Calculate base position relative to stoppage
        if is_left_half_back or is_marking_left_half_forward:
            flank_offset = (-8, 4)  # Wider than back pocket
        else:
            flank_offset = (8, 4)   # Wider than back pocket
            
        target_pos = (
            ball_pos[0] + flank_offset[0],
            ball_pos[1] + flank_offset[1]
        )
        
        return {
            'type': 'stoppage_contest',
            'phases': [
                {
                    'action': 'position',
                    'target': target_pos,
                    'timing': 'pre_contest',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'read_play',
                    'focus': 'clearance_options',
                    'timing': 'during_contest',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'provide_option',
                    'primary': 'defensive_outlet',  # Provide defensive outlet
                    'secondary': 'rebound_option',  # Ready for rebound
                    'timing': 'continuous',
                    'speed': physical.speed / 20,
                    'stamina': physical.stamina / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'teammate_positions': self._adjust_for_teammates,
                'field_position': self._adjust_for_field_position(state)
            }
        }

    def _half_back_marking_pattern(self, state, player):
        """
        Generate movement pattern for half backs during marking contests.
        Focus on spoiling and protecting the drop zone.
        """
        #ball_pos = state['ball_position']
        
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Calculate marking position
        marking_data = self._calculate_marking_position(
            self.player,
            state
        )
        
        return {
            'type': 'marking_contest',
            'phases': [
                {
                    'action': 'position',
                    'target': marking_data['target'],
                    'timing': 'pre_contest',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'read_play',
                    'focus': 'opposition_forward',
                    'timing': 'during_flight',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'contest',
                    'primary': 'spoil',           # Focus on spoiling
                    'secondary': 'protect_drop',  # Protect the drop zone
                    'timing': 'on_contest',
                    'height': physical.height / 20,
                    'marking': ability.marking / 20,
                    'strength': physical.strength / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'weather_conditions': self._adjust_for_conditions,
                'teammate_positions': self._adjust_for_teammates
            }
        }

    def _half_back_ground_pattern(self, state, player):
        """
        Generate movement pattern for half backs during ground contests.
        Focus on clean possession and quick disposal.
        """
        ball_pos = state['ball_position']
        
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Calculate ground contest position
        ground_data = self._calculate_ground_position(
            self.player,
            state
        )
        
        return {
            'type': 'ground_contest',
            'phases': [
                {
                    'action': 'position',
                    'target': ground_data['target'],
                    'timing': 'pre_contest',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'contest',
                    'primary': 'clean_gather',    # Clean possession
                    'secondary': 'quick_clear',   # Quick disposal
                    'timing': 'on_contest',
                    'agility': physical.agility / 20,
                    'strength': physical.strength / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'ground_conditions': self._adjust_for_conditions,
                'teammate_positions': self._adjust_for_teammates
            }
        }

    def _half_back_cover_pattern(self, state, player):
        """
        Generate movement pattern for half backs providing defensive cover.
        Focus on zoning space and supporting back pockets.
        """
        ball_pos = state['ball_position']
        player_pos = state['player_position']

        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Consider both position and opponent
        is_left_half_back = player_pos == 'LHB'
        is_right_half_back = player_pos == 'RHB'
        
        opponent = self._get_direct_opponent(state)
        is_marking_left_half_forward = opponent and opponent.position == 'LHF'
        is_marking_right_half_forward = opponent and opponent.position == 'RHF'
        
        # Calculate cover position
        cover_pos = self._calculate_defensive_cover_position(
            self.player,
            state,
            side='left' if (is_left_half_back or is_marking_left_half_forward) else 'right'
        )
        
        return {
            'type': 'defensive_cover',
            'phases': [
                {
                    'action': 'position',
                    'target': cover_pos,
                    'timing': 'continuous',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'protect',
                    'primary': 'zone_space',      # Zone defensive space
                    'secondary': 'support_backs',  # Support back pockets
                    'timing': 'continuous',
                    'speed': physical.speed / 20,
                    'stamina': physical.stamina / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'teammate_positions': self._adjust_for_teammates,
                'field_position': self._adjust_for_field_position(state)
            }
        }

    def _half_back_intercept_pattern(self, state, player):
        """
        Generate movement pattern for half backs looking to intercept.
        Focus on reading play and cutting off forward entries.
        """
        ball_pos = state['ball_position']
        player_pos = state['player_position']

        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Consider both position and opponent
        is_left_half_back = player_pos == 'LHB'
        is_right_half_back = player_pos == 'RHB'
        
        opponent = self._get_direct_opponent(state)
        is_marking_left_half_forward = opponent and opponent.position == 'LHF'
        is_marking_right_half_forward = opponent and opponent.position == 'RHF'
        
        # Calculate intercept position
        intercept_data = self._calculate_intercept_position(
            self.player,
            state,
            side='left' if (is_left_half_back or is_marking_left_half_forward) else 'right'
        )
        
        return {
            'type': 'intercept_pattern',
            'phases': [
                {
                    'action': 'position',
                    'target': intercept_data['target'],
                    'timing': 'pre_entry',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'read_play',
                    'focus': 'forward_entry',
                    'timing': 'continuous',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'move',
                    'primary': 'cut_off_lane',     # Cut off forward entry
                    'secondary': 'ready_rebound',   # Ready for rebound
                    'timing': 'on_forward_entry',
                    'speed': physical.speed / 20,
                    'agility': physical.agility / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'teammate_positions': self._adjust_for_teammates,
                'field_position': self._adjust_for_field_position(state)
            }
        }

    def _half_back_rebound_pattern(self, state, player):
        """
        Generate movement pattern for half backs during rebound situations.
        Focus on reading play and providing an offensive outlet.
        """
        ball_pos = state['ball_position']
        player_pos = state['player_position']

        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Consider both position and opponent
        is_left_half_back = player_pos == 'LHB'
        is_right_half_back = player_pos == 'RHB'
        
        opponent = self._get_direct_opponent(state)
        is_marking_left_half_forward = opponent and opponent.position == 'LHF'
        is_marking_right_half_forward = opponent and opponent.position == 'RHF'
        
        # Calculate rebound position considering both factors
        rebound_data = self._calculate_rebound_position(
            self.player,
            state,
            side='left' if (is_left_half_back or is_marking_left_half_forward) else 'right'
        )
        
        return {
            'type': 'rebound_pattern',
            'phases': [
                {
                    'action': 'position',
                    'target': rebound_data['target'],
                    'timing': 'pre_contest',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'read_play',
                    'focus': 'drop_zone',
                    'timing': 'during_contest',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'move',
                    'primary': 'attacking_rebound',  # Look for offensive rebound
                    'secondary': 'maintain_balance', # Keep defensive balance
                    'timing': 'on_contest',
                    'speed': physical.speed / 20,
                    'agility': physical.agility / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'teammate_positions': self._adjust_for_teammates,
                'field_position': self._adjust_for_field_position(state)
            }
        }

    def _half_back_link_pattern(self, state, player):
        """
        Generate movement pattern for half backs linking play.
        Focus on providing offensive transition while maintaining defensive responsibility.
        """
        ball_pos = state['ball_position']
        player_pos = state['player_position']

        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Consider both position and opponent
        is_left_half_back = player_pos == 'LHB'
        is_right_half_back = player_pos == 'RHB'
        
        opponent = self._get_direct_opponent(state)
        is_marking_left_half_forward = opponent and opponent.position == 'LHF'
        is_marking_right_half_forward = opponent and opponent.position == 'RHF'
        
        # Calculate link position considering both factors
        link_data = self._calculate_link_position(
            self.player,
            state,
            side='left' if (is_left_half_back or is_marking_left_half_forward) else 'right'
        )
        
        return {
            'type': 'link_pattern',
            'phases': [
                {
                    'action': 'position',
                    'target': link_data['target'],
                    'timing': 'immediate',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'read_play',
                    'focus': 'transition_options',
                    'timing': 'continuous',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'move',
                    'primary': 'provide_outlet',    # Offensive outlet
                    'secondary': 'recovery_run',    # Ready to recover
                    'timing': 'on_possession',
                    'speed': physical.speed / 20,
                    'stamina': physical.stamina / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'teammate_positions': self._adjust_for_teammates,
                'field_position': self._adjust_for_field_position(state)
            }
        }

    def _chb_stoppage_pattern(self, state, player):
        """
        Generate movement pattern for CHB during stoppages.
        Focus on central defensive positioning and organizing backline.
        """
        ball_pos = state['ball_position']
        
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # CHB is central, but consider opponent
        opponent = self._get_direct_opponent(state)
        is_marking_chf = opponent and opponent.position == 'CHF'
        
        # Calculate base position relative to stoppage - more central than half-backs
        central_offset = (0, 5)  # Straight back from contest
        
        target_pos = (
            ball_pos[0] + central_offset[0],
            ball_pos[1] + central_offset[1]
        )
        
        return {
            'type': 'stoppage_contest',
            'phases': [
                {
                    'action': 'position',
                    'target': target_pos,
                    'timing': 'pre_contest',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'read_play',
                    'focus': 'central_corridor',
                    'timing': 'during_contest',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'organize',
                    'primary': 'protect_corridor',   # Guard central corridor
                    'secondary': 'direct_defense',   # Organize other defenders
                    'timing': 'continuous',
                    'speed': physical.speed / 20,
                    'stamina': physical.stamina / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'teammate_positions': self._adjust_for_teammates,
                'field_position': self._adjust_for_field_position(state)
            }
        }

    def _chb_marking_pattern(self, state, player):
        """
        Generate movement pattern for CHB during marking contests.
        Focus on spoiling and protecting the central corridor.
        """
        #ball_pos = state['ball_position']
        
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Calculate marking position
        marking_data = self._calculate_marking_position(
            self.player,
            state
        )
        
        return {
            'type': 'marking_contest',
            'phases': [
                {
                    'action': 'position',
                    'target': marking_data['target'],
                    'timing': 'pre_contest',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'read_play',
                    'focus': 'key_forward',
                    'timing': 'during_flight',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'contest',
                    'primary': 'spoil',           # Focus on spoiling
                    'secondary': 'body_work',     # Strong body position
                    'timing': 'on_contest',
                    'height': physical.height / 20,
                    'strength': physical.strength / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'weather_conditions': self._adjust_for_conditions,
                'teammate_positions': self._adjust_for_teammates
            }
        }

    def _chb_ground_pattern(self, state, player):
        """
        Generate movement pattern for CHB during ground contests.
        Focus on securing possession and clearing danger.
        """
        ball_pos = state['ball_position']
        
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Calculate ground contest position
        ground_data = self._calculate_ground_position(
            self.player,
            state
        )
        
        return {
            'type': 'ground_contest',
            'phases': [
                {
                    'action': 'position',
                    'target': ground_data['target'],
                    'timing': 'pre_contest',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'contest',
                    'primary': 'secure_ball',     # Secure possession
                    'secondary': 'clear_danger',  # Clear from danger zone
                    'timing': 'on_contest',
                    'agility': physical.agility / 20,
                    'strength': physical.strength / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'ground_conditions': self._adjust_for_conditions,
                'teammate_positions': self._adjust_for_teammates
            }
        }

    def _chb_cover_pattern(self, state, player):
        """
        Generate movement pattern for CHB providing defensive cover.
        Focus on protecting central corridor and organizing defense.
        """
        ball_pos = state['ball_position']
        
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Calculate cover position - central focus
        cover_pos = self._calculate_defensive_cover_position(
            self.player,
            state,
            side='central'  # CHB stays central
        )
        
        return {
            'type': 'defensive_cover',
            'phases': [
                {
                    'action': 'position',
                    'target': cover_pos,
                    'timing': 'continuous',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'protect',
                    'primary': 'central_space',    # Protect central corridor
                    'secondary': 'direct_defense', # Organize defense
                    'timing': 'continuous',
                    'speed': physical.speed / 20,
                    'stamina': physical.stamina / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'teammate_positions': self._adjust_for_teammates,
                'field_position': self._adjust_for_field_position(state)
            }
        }

    def _chb_intercept_pattern(self, state, player):
        """
        Generate movement pattern for CHB looking to intercept.
        Focus on reading central entries and protecting corridor.
        """
        ball_pos = state['ball_position']
        
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Calculate intercept position - central focus
        intercept_data = self._calculate_intercept_position(
            self.player,
            state,
            side='central'  # CHB stays central
        )
        
        return {
            'type': 'intercept_pattern',
            'phases': [
                {
                    'action': 'position',
                    'target': intercept_data['target'],
                    'timing': 'pre_entry',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'read_play',
                    'focus': 'central_entry',
                    'timing': 'continuous',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'move',
                    'primary': 'protect_corridor',  # Protect central corridor
                    'secondary': 'intercept_ball',  # Look to intercept
                    'timing': 'on_forward_entry',
                    'speed': physical.speed / 20,
                    'agility': physical.agility / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'teammate_positions': self._adjust_for_teammates,
                'field_position': self._adjust_for_field_position(state)
            }
        }

    def _chb_rebound_pattern(self, state, player):
        """
        Generate movement pattern for CHB during rebound situations.
        Focus on central positioning and organizing defensive rebound setup.
        """
        ball_pos = state['ball_position']
        
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Calculate rebound position - central focus
        rebound_data = self._calculate_rebound_position(
            self.player,
            state,
            side='central'  # CHB stays central
        )
        
        return {
            'type': 'rebound_pattern',
            'phases': [
                {
                    'action': 'position',
                    'target': rebound_data['target'],
                    'timing': 'pre_contest',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'read_play',
                    'focus': 'central_drop',
                    'timing': 'during_contest',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'move',
                    'primary': 'protect_drop',     # Protect drop zone
                    'secondary': 'direct_rebound',  # Organize rebound setup
                    'timing': 'on_contest',
                    'speed': physical.speed / 20,
                    'agility': physical.agility / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'teammate_positions': self._adjust_for_teammates,
                'field_position': self._adjust_for_field_position(state)
            }
        }

    def _chb_link_pattern(self, state, player):
        """
        Generate movement pattern for CHB during linking play.
        Focus on central distribution while maintaining defensive structure.
        """
        ball_pos = state['ball_position']
        
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Calculate link position - central focus
        link_data = self._calculate_link_position(
            self.player,
            state,
            side='central'  # CHB stays central
        )
        
        return {
            'type': 'link_pattern',
            'phases': [
                {
                    'action': 'position',
                    'target': link_data['target'],
                    'timing': 'immediate',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'read_play',
                    'focus': 'distribution_options',
                    'timing': 'continuous',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'move',
                    'primary': 'central_link',      # Central distribution
                    'secondary': 'maintain_shape',   # Keep defensive structure
                    'timing': 'on_possession',
                    'speed': physical.speed / 20,
                    'stamina': physical.stamina / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'teammate_positions': self._adjust_for_teammates,
                'field_position': self._adjust_for_field_position(state)
            }
        }

    def _full_back_stoppage_pattern(self, state, player):
        """
        Generate movement pattern for FB during stoppages.
        Focus on goal square protection and key forward coverage.
        """
        ball_pos = state['ball_position']
        
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # FB is central, but consider opponent
        opponent = self._get_direct_opponent(state)
        is_marking_ff = opponent and opponent.position == 'FF'
        
        # Calculate base position relative to stoppage - deepest defender
        deep_offset = (0, 6)  # Straight back, deepest position
        
        target_pos = (
            ball_pos[0] + deep_offset[0],
            ball_pos[1] + deep_offset[1]
        )
        
        return {
            'type': 'stoppage_contest',
            'phases': [
                {
                    'action': 'position',
                    'target': target_pos,
                    'timing': 'pre_contest',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'read_play',
                    'focus': 'goal_square',
                    'timing': 'during_contest',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'protect',
                    'primary': 'goal_area',      # Protect goal square
                    'secondary': 'track_ff',     # Track full forward
                    'timing': 'continuous',
                    'speed': physical.speed / 20,
                    'stamina': physical.stamina / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'teammate_positions': self._adjust_for_teammates,
                'field_position': self._adjust_for_field_position(state)
            }
        }

    def _full_back_marking_pattern(self, state, player):
        """
        Generate movement pattern for FB during marking contests.
        Focus on spoiling and protecting goal square.
        """
        #ball_pos = state['ball_position']
        
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Calculate marking position
        marking_data = self._calculate_marking_position(
            self.player,
            state
        )
        
        return {
            'type': 'marking_contest',
            'phases': [
                {
                    'action': 'position',
                    'target': marking_data['target'],
                    'timing': 'pre_contest',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'read_play',
                    'focus': 'full_forward',
                    'timing': 'during_flight',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'contest',
                    'primary': 'spoil',           # Focus on spoiling
                    'secondary': 'goal_side',     # Stay goal side
                    'timing': 'on_contest',
                    'height': physical.height / 20,
                    'marking': ability.marking / 20,
                    'strength': physical.strength / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'weather_conditions': self._adjust_for_conditions,
                'teammate_positions': self._adjust_for_teammates
            }
        }

    def _full_back_ground_pattern(self, state, player):
        """
        Generate movement pattern for FB during ground contests.
        Focus on clearing danger and protecting goal.
        """
        ball_pos = state['ball_position']
        
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Calculate ground contest position
        ground_data = self._calculate_ground_position(
            self.player,
            state
        )
        
        return {
            'type': 'ground_contest',
            'phases': [
                {
                    'action': 'position',
                    'target': ground_data['target'],
                    'timing': 'pre_contest',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'contest',
                    'primary': 'clear_danger',    # Clear from danger zone
                    'secondary': 'protect_goal',  # Protect goal area
                    'timing': 'on_contest',
                    'agility': physical.agility / 20,
                    'strength': physical.strength / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'ground_conditions': self._adjust_for_conditions,
                'teammate_positions': self._adjust_for_teammates
            }
        }

    def _full_back_cover_pattern(self, state, player):
        """
        Generate movement pattern for FB providing defensive cover.
        Focus on goal protection and organizing last line.
        """
        ball_pos = state['ball_position']
        
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Calculate cover position - deepest defender
        cover_pos = self._calculate_defensive_cover_position(
            self.player,
            state,
            side='central'  # FB stays central
        )
        
        return {
            'type': 'defensive_cover',
            'phases': [
                {
                    'action': 'position',
                    'target': cover_pos,
                    'timing': 'continuous',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'protect',
                    'primary': 'goal_area',       # Protect goal area
                    'secondary': 'direct_backs',  # Direct back line
                    'timing': 'continuous',
                    'speed': physical.speed / 20,
                    'stamina': physical.stamina / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'teammate_positions': self._adjust_for_teammates,
                'field_position': self._adjust_for_field_position(state)
            }
        }

    def _full_back_intercept_pattern(self, state, player):
        """
        Generate movement pattern for FB looking to intercept.
        Focus on protecting goal while looking for intercept opportunities.
        """
        ball_pos = state['ball_position']
        
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Calculate intercept position - goal area focus
        intercept_data = self._calculate_intercept_position(
            self.player,
            state,
            side='central'  # FB stays central
        )
        
        return {
            'type': 'intercept_pattern',
            'phases': [
                {
                    'action': 'position',
                    'target': intercept_data['target'],
                    'timing': 'pre_entry',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'read_play',
                    'focus': 'deep_entry',
                    'timing': 'continuous',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'move',
                    'primary': 'protect_goal',    # Protect goal first
                    'secondary': 'intercept',     # Look to intercept if safe
                    'timing': 'on_forward_entry',
                    'speed': physical.speed / 20,
                    'agility': physical.agility / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'teammate_positions': self._adjust_for_teammates,
                'field_position': self._adjust_for_field_position(state)
            }
        }

    def _full_back_rebound_pattern(self, state, player):
        """
        Generate movement pattern for FB during rebound situations.
        Focus on protecting goal and organizing deep defense.
        """
        ball_pos = state['ball_position']
        
        # Get player's relevant attributes
        physical = player.physical_stats
        ability = player.ability_stats
        
        # Calculate rebound position - goal area focus
        rebound_data = self._calculate_rebound_position(
            self.player,
            state,
            side='central'  # FB stays central
        )
        
        return {
            'type': 'rebound_pattern',
            'phases': [
                {
                    'action': 'position',
                    'target': rebound_data['target'],
                    'timing': 'pre_contest',
                    'confidence_factor': ability.consistency / 20
                },
                {
                    'action': 'read_play',
                    'focus': 'goal_area_drop',
                    'timing': 'during_contest',
                    'tactical': ability.tactical / 20
                },
                {
                    'action': 'move',
                    'primary': 'protect_goal',     # Protect goal area
                    'secondary': 'organize_deep',   # Organize deep defense
                    'timing': 'on_contest',
                    'speed': physical.speed / 20,
                    'agility': physical.agility / 20
                }
            ],
            'adjustments': {
                'opposition_setup': self._adjust_for_opposition_setup,
                'teammate_positions': self._adjust_for_teammates,
                'field_position': self._adjust_for_field_position(state)
            }
        }
    
    def _adjust_for_teammates(self, state):
        """Adjust based on teammate positions"""
        # Get nearby teammates
        nearby_teammates = state.get_nearby_players(
            self.player.current_position, 
            team=self.player.team
        )
        
        # Calculate optimal spacing
        space_x, space_y = self._calculate_teammate_spacing(nearby_teammates)
        
        # Identify blocked paths
        blocked = self._identify_blocked_paths(
            self.player.current_position,
            nearby_teammates,
            state.ball_position
        )
        
        # Find support positions
        support_spots = self._calculate_support_positions(
            state.ball_position,
            nearby_teammates,
            state.phase
        )
        
        return {
            'space_modifier': (space_x, space_y),  # Real spacing adjustments
            'blocking_paths': blocked,             # Actually blocked paths
            'support_positions': support_spots     # Real support positions
        }

    def _calculate_teammate_spacing(self, nearby_teammates):
        """Calculate optimal spacing adjustments for teammates"""
        if not nearby_teammates:
            return (0, 0)
            
        # Calculate average teammate position
        avg_x = sum(t.current_position[0] for t in nearby_teammates) / len(nearby_teammates)
        avg_y = sum(t.current_position[1] for t in nearby_teammates) / len(nearby_teammates)
        
        # Calculate spread factor - move away from clusters
        optimal_spacing = 8  # meters
        x_adjust = self.player.current_position[0] - avg_x
        y_adjust = self.player.current_position[1] - avg_y
        
        # Normalize adjustments
        magnitude = math.sqrt(x_adjust**2 + y_adjust**2)
        if magnitude > 0:
            x_adjust = (x_adjust / magnitude) * optimal_spacing
            y_adjust = (y_adjust / magnitude) * optimal_spacing
        
        return (x_adjust, y_adjust)

    def _calculate_support_positions(self, ball_pos, nearby_teammates, phase):
        """Calculate optimal support positions"""
        support_spots = []
        
        # Define base support distances based on phase
        if phase == 'attack':
            distances = [15, 25]  # Closer support in attack
        else:
            distances = [20, 30]  # Wider spread in defense
            
        # Calculate potential support angles
        angles = [45, 90, 135]  # Degrees
        
        for distance in distances:
            for angle in angles:
                # Convert angle to radians
                rad_angle = math.radians(angle)
                
                # Calculate position
                x = ball_pos[0] + distance * math.cos(rad_angle)
                y = ball_pos[1] + distance * math.sin(rad_angle)
                
                # Check if position is already occupied
                occupied = any(
                    MovementPattern._calculate_distance((x, y), t.current_position) < 5 
                    for t in nearby_teammates
                )
                
                if not occupied:
                    support_spots.append({
                        'position': (x, y),
                        'distance': distance,
                        'angle': angle
                    })
        
        return support_spots

    def _adjust_for_opposition_setup(self, state):
        """Adjust positioning based on opposition setup"""
        # Get nearby opposition players
        nearby_opponents = state.get_nearby_players(
            self.player.current_position, 
            team=state.get_opposition_team(self.player.team)
        )
        
        # Calculate space and risk based on opposition positions
        space_level = self._calculate_available_space(nearby_opponents)
        risk_level = self._calculate_risk_factor(nearby_opponents)
        
        # Calculate position modification needed
        position_mod = self._calculate_position_adjustment(
            nearby_opponents,
            state.ball_position
        )
        
        return {
            'position_modifier': position_mod,
            'risk_factor': risk_level,
            'space_available': space_level
        }
    
    def _adjust_for_field_position(self, state):
        """
        Adjusts pattern based on field position.
        Returns adjustment factors for different zones of the ground.
        """
        ball_pos = state['ball_position']
        
        # Define ground zones (assuming standard AFL ground dimensions)
        defensive_50 = {'x': (0, 25), 'y': (0, 100)}
        defensive_mid = {'x': (25, 45), 'y': (0, 100)}
        center = {'x': (45, 55), 'y': (0, 100)}
        forward_mid = {'x': (55, 75), 'y': (0, 100)}
        forward_50 = {'x': (75, 100), 'y': (0, 100)}
        
        # Determine which zone the ball is in
        x, y = ball_pos
        
        # Base adjustments for each zone
        adjustments = {
            'defensive_press': 1.0,    # How tight to press
            'attacking_spread': 1.0,   # How wide to spread
            'vertical_position': 1.0,  # How far up/back to play
            'risk_factor': 1.0        # How much risk to take
        }
        
        # Adjust based on ball position
        if x <= defensive_50['x'][1]:  # In defensive 50
            adjustments.update({
                'defensive_press': 1.3,    # Tighter press
                'attacking_spread': 0.8,   # Stay compact
                'vertical_position': 0.7,  # Play deeper
                'risk_factor': 0.7        # Lower risk
            })
        elif x <= defensive_mid['x'][1]:  # In defensive midfield
            adjustments.update({
                'defensive_press': 1.2,
                'attacking_spread': 0.9,
                'vertical_position': 0.8,
                'risk_factor': 0.8
            })
        elif x <= center['x'][1]:  # In center
            adjustments.update({
                'defensive_press': 1.0,
                'attacking_spread': 1.0,
                'vertical_position': 1.0,
                'risk_factor': 1.0
            })
        elif x <= forward_mid['x'][1]:  # In forward midfield
            adjustments.update({
                'defensive_press': 0.9,
                'attacking_spread': 1.1,
                'vertical_position': 1.2,
                'risk_factor': 1.2
            })
        else:  # In forward 50
            adjustments.update({
                'defensive_press': 0.8,    # Looser press
                'attacking_spread': 1.2,   # Wider spread
                'vertical_position': 1.3,  # Play higher
                'risk_factor': 1.3        # Higher risk
            })
            
        # Consider corridor vs boundary
        if 30 <= y <= 70:  # In corridor
            adjustments['attacking_spread'] *= 1.2  # Encourage wider spread in corridor
        else:  # On boundary
            adjustments['attacking_spread'] *= 0.8  # Stay more compact on boundary
            
        return adjustments    
    
    def _calculate_available_space(self, nearby_opponents):
        """Calculate available space based on opponent positions"""
        if not nearby_opponents:
            return 'high'
            
        # Calculate density of opponents in area
        space_radius = 10  # meters
        opponents_in_space = len(nearby_opponents)
        density = opponents_in_space / (math.pi * space_radius ** 2)
        
        # Define space thresholds
        if density < 0.01:  # Less than 1 opponent per 100m²
            return 'high'
        elif density < 0.02:  # Less than 1 opponent per 50m²
            return 'medium'
        else:
            return 'low'

    def _calculate_risk_factor(self, nearby_opponents):
        """Calculate risk factor based on opponent pressure"""
        if not nearby_opponents:
            return 1.0
            
        # Calculate pressure from each opponent
        total_pressure = 0
        for opponent in nearby_opponents:
            distance = MovementPattern._calculate_distance(self.player.current_position, opponent.current_position)
            pressure = 1.0 / max(distance, 1)  # Avoid division by zero
            total_pressure += pressure
        
        # Normalize pressure to risk factor (0.5 to 1.5)
        return max(0.5, min(1.5, 1.0 - (total_pressure * 0.1)))

    def _calculate_position_adjustment(self, nearby_opponents, ball_pos):
        """Calculate position adjustment based on opponents and ball"""
        if not nearby_opponents:
            return (0, 0)
            
        # Calculate repulsion from opponents
        x_adjust = 0
        y_adjust = 0
        for opponent in nearby_opponents:
            dx = self.player.current_position[0] - opponent.current_position[0]
            dy = self.player.current_position[1] - opponent.current_position[1]
            distance = math.sqrt(dx**2 + dy**2)
            
            if distance < 5:  # Only adjust if opponent within 5 meters
                # Repulsion strength inversely proportional to distance
                strength = 5 - distance
                x_adjust += (dx / distance) * strength
                y_adjust += (dy / distance) * strength
        
        # Factor in ball position - slight attraction
        ball_dx = ball_pos[0] - self.player.current_position[0]
        ball_dy = ball_pos[1] - self.player.current_position[1]
        ball_distance = math.sqrt(ball_dx**2 + ball_dy**2)
        
        ball_factor = 0.2  # Ball position influence
        x_adjust += (ball_dx / ball_distance) * ball_factor
        y_adjust += (ball_dy / ball_distance) * ball_factor
        
        return (x_adjust, y_adjust)



    def _identify_blocked_paths(self, current_pos, nearby_teammates, ball_pos):
        """Identify paths blocked by teammates"""
        blocked_paths = []
        path_angles = []
        
        # Check paths to ball
        ball_angle = math.atan2(
            ball_pos[1] - current_pos[1],
            ball_pos[0] - current_pos[0]
        )
        
        for teammate in nearby_teammates:
            # Calculate angle to teammate
            teammate_angle = math.atan2(
                teammate.current_position[1] - current_pos[1],
                teammate.current_position[0] - current_pos[0]
            )
            
            # If teammate is within 15 degrees of path to ball
            if abs(ball_angle - teammate_angle) < math.pi/12:  # 15 degrees in radians
                blocked_paths.append({
                    'angle': teammate_angle,
                    'distance': MovementPattern._calculate_distance(current_pos, teammate.current_position),
                    'blocking_player': teammate
                })
                
        return blocked_paths

    def _adjust_for_conditions(self, state):
        """Adjust for ground/weather conditions"""
        # Use existing weather system
        weather_effects = state.weather_system.get_ground_movement_modifier()
        visibility = state.weather_system.get_visibility_for_position(
            self.player.current_position,
            state.ground
        )
        
        return {
            'movement_modifier': weather_effects,
            'positioning_safety': 2.0 - visibility  # More conservative in poor visibility
        }


    
    def _calculate_press_intensity(self, state):
        """Calculate press intensity based on game state"""
        intensity = 1.0  # Base intensity
        
        # Score impact
        if state.score_difference < 0:  # Behind
            intensity *= 1.2  # Press harder when behind
        elif state.score_difference > 24:  # Well ahead
            intensity *= 0.8  # Ease off when well ahead
            
        # Time impact
        if state.time_remaining < 300:  # Last 5 minutes
            if state.score_difference <= 12:  # Close game
                intensity *= 1.3  # Strong press in close finish
                
        # Field position impact
        if state.ball_position in state.ground.zones['forward_50']:
            intensity *= 1.2  # Strong forward press
            
        return max(0.5, min(1.5, intensity))  # Clamp between 0.5 and 1.5

    def _get_team_play_style(self, state):
        """Get current team tactical approach"""
        team_tactics = state.get_team_tactics(self.player.team)
        return team_tactics.current_tactic  # This will return one of our defined tactics

    def _calculate_marking_position(self, player, state):
        """Calculate optimal marking position for marking contests"""
        ball_pos = state['ball_position']
        
        # Get direct opponent if any
        opponent = self._get_direct_opponent(state)
        
        # Calculate optimal position relative to ball and opposition
        target_pos = self._calculate_contest_position(ball_pos, 'marking')
        
        return {
            'target': target_pos,
            'contest_type': 'marking',
            'primary_action': 'mark',
            'secondary_action': 'protect_goal'  # Default to protecting goal
        }

    def _calculate_contest_position(self, ball_pos, contest_type):
        """Calculate optimal position for different contest types"""
        if contest_type == 'marking_contest':
            # Position slightly goal-side for marking
            return (ball_pos[0] - 1, ball_pos[1] - 1)
        elif contest_type == 'spoil_opportunity':
            # Position for spoil
            return (ball_pos[0] + 1, ball_pos[1])
        else:  # ground_ball
            # Position for crumb
            return (ball_pos[0], ball_pos[1] + 2)

    def _get_direct_opponent(self, state):
        """
        Determines the direct opponent based on position matchups.
        Returns opponent data or None if no direct matchup.
        """
        player_pos = state['player_position']
        
        # Define standard position matchups
        position_matchups = {
            'FF': 'FB',
            'LF': 'RB',    # Left Forward matches up on Right Back
            'RF': 'LB',    # Right Forward on Left Back
            'LHF': 'RHB',  # Left Half Forward on Right Half Back
            'RHF': 'LHB',  # Right Half Forward on Left Half Back
            'CHF': 'CHB',  # Centre Half Forward on Centre Half Back
            'LWing': 'RWing',    # Left Wing on Right Wing
            'RWing': 'LWing',    # Right Wing on Left Wing
            'Centre': 'Centre',      # Centre on Centre
            'RuckRover': 'RuckRover',    #Ruck Rover on Ruck Rover
            'Rover': 'Rover',    # Rover on Rover
            'Ruck': 'Ruck',      # Ruck on Ruck
            'LB': 'RF',    # Left Back on Right Forward
            'RB': 'LF',    # Right Back on Left Forward
            'FB': 'FF',    # Full Back on Full Forward
            'LHB': 'RHF',  # Left Half Back on Right Half Forward
            'RHB': 'LHF',  # Right Half Back on Left Half Forward
            'CHB': 'CHF'   # Centre Half Back on Centre Half Forward
        }
        
        # Get the matching position for the current player
        matching_position = position_matchups.get(player_pos)

            
        # Find opponent in that position from state
        # Assuming state has opposition_positions or similar
        if 'opposition_positions' in state:
            for opp in state['opposition_positions']:
                if opp['position'] == matching_position:
                    return opp
                    
        return None                              

        
    
class SetPlayPattern:
    """Manages structured movements for set plays"""
    def __init__(self, team, ground):
        self.team = team
        self.ground = ground
        self.current_setup = None
        self.player_assignments = {}
        
    def generate_setup(self, play_type, state):
        """Generate set play setup based on type and state"""
        setups = {
            'ball_up': self._setup_ball_up,
            'kick_in': self._setup_kick_in,
            'set_shot': self._setup_set_shot,
            'free_kick': self._setup_free_kick,
            'boundary_throw': self._setup_boundary_throw
        }
        
        setup_func = setups.get(play_type)
        if setup_func:
            self.current_setup = setup_func(state)
            self._assign_players(state)
            return self.current_setup
            
    def _assign_players(self, state):
        """Assign players to roles in the set play"""
        self.player_assignments = {}
        required_roles = self.current_setup['required_roles']
        
        # Sort players by suitability for each role
        for role, requirements in required_roles.items():
            best_player = self._find_best_player_for_role(
                role,
                requirements,
                state
            )
            if best_player:
                self.player_assignments[best_player.id] = role
                
    def _setup_ball_up(self, state):
        """Setup for ball up"""
        ball_pos = state['ball_position']
        return {
            'type': 'ball_up',
            'required_roles': {
                'ruckman': {'position': ball_pos, 'priority': 'high'},
                'rover': {'position': self._calculate_rover_position(ball_pos), 'priority': 'high'},
                'support_left': {'position': self._calculate_support_position(ball_pos, 'left'), 'priority': 'medium'},
                'support_right': {'position': self._calculate_support_position(ball_pos, 'right'), 'priority': 'medium'},
                'defensive_cover': {'position': self._calculate_defensive_cover(ball_pos), 'priority': 'medium'}
            },
            'movement_patterns': {
                'ruckman': self._generate_ruck_pattern(),
                'rover': self._generate_rover_pattern(),
                'support_left': self._generate_support_pattern('left'),
                'support_right': self._generate_support_pattern('right'),
                'defensive_cover': self._generate_defensive_pattern()
            }
        }
        
    def _setup_set_shot(self, state):
        """Setup for set shot"""
        kicker_pos = state.ball_position
        return {
            'type': 'set_shot',
            'required_roles': {
                'kicker': {'position': kicker_pos, 'priority': 'high'},
                'lead_left': {'position': self._calculate_lead_position(kicker_pos, 'left'), 'priority': 'high'},
                'lead_right': {'position': self._calculate_lead_position(kicker_pos, 'right'), 'priority': 'high'},
                'crumber_left': {'position': self._calculate_crumb_position(kicker_pos, 'left'), 'priority': 'medium'},
                'crumber_right': {'position': self._calculate_crumb_position(kicker_pos, 'right'), 'priority': 'medium'}
            },
            'movement_patterns': {
                'kicker': self._generate_set_shot_pattern(),
                'lead_left': self._generate_lead_pattern('left'),
                'lead_right': self._generate_lead_pattern('right'),
                'crumber_left': self._generate_crumb_pattern('left'),
                'crumber_right': self._generate_crumb_pattern('right')
            }
        }

class ActionCombination:
    """Handles sequences of connected actions"""
    def __init__(self, primary_action, player, state):
        self.player = player
        self.primary_action = primary_action
        self.setup_actions = []
        self.follow_up_actions = []
        self.current_step = 0
        self.state = state
        self.status = 'pending'  # pending, in_progress, completed, failed
        
    def add_setup(self, action):
        """Add setup action to be performed before primary action"""
        self.setup_actions.append(action)
        return self
        
    def add_follow_up(self, action):
        """Add follow-up action to be performed after primary action"""
        self.follow_up_actions.append(action)
        return self
        
    def get_next_action(self):
        """Get the next action in the sequence"""
        if self.status == 'failed':
            return None
            
        all_actions = self.setup_actions + [self.primary_action] + self.follow_up_actions
        if self.current_step < len(all_actions):
            return all_actions[self.current_step]
        return None
        
    def advance(self, result):
        """Advance to next action based on previous result"""
        if result['outcome'] == 'success':
            self.current_step += 1
            if self.current_step >= len(self.setup_actions) + len(self.follow_up_actions) + 1:
                self.status = 'completed'
        else:
            self.status = 'failed'    

    
class TeamTactics:
    """Defines team's tactical approach and decision-making"""
    def __init__(self, tactics_config, aggression=50):
        # New tactical options from config
        push_factor_map = {
            '0': 'push_factor0',
            '1': 'push_factor1',
            '2': 'push_factor2'
        }

        # New tactical options from config
        self.mentality = tactics_config['mentality'] 
        self.push_factor = push_factor_map.get(tactics_config['push_factor'])
        self.defense_strategy = tactics_config['defense_strategy']
        self.offense_strategy = tactics_config['offense_strategy']
        
        print(f"Team Push Factor: {self.push_factor}")
        # Existing attributes
        self.style = self.mentality  # Map mentality to style
        self.aggression = aggression
        self.pressure_points = self._initialize_pressure_points()
        self.risk_profile = self._initialize_risk_profile()
        
    def _initialize_pressure_points(self):
        """Define where team applies pressure based on style and push factor"""
        base_pressure = {
            'attacking': {
                'forward_50': 90,
                'midfield': 70,
                'defensive_50': 40,
                'contest': 60
            },
            'defensive': {
                'forward_50': 40,
                'midfield': 60,
                'defensive_50': 90,
                'contest': 80
            },
            'balanced': {
                'forward_50': 60,
                'midfield': 60,
                'defensive_50': 60,
                'contest': 70
            }
        }[self.style]
        
        # Modify based on push factor
        push_modifiers = {
            'push_factor0': 0.8,
            'push_factor1': 1.0,
            'push_factor2': 1.2
        }
        
        modifier = push_modifiers.get(self.push_factor, 1.0)
        return {k: v * modifier for k, v in base_pressure.items()}
    
    def _initialize_risk_profile(self):
        """Define risk tolerance for different actions"""
        base_risk = self.aggression / 100  # Convert to 0-1 scale
        
        base_profile = {
            'attacking': {
                'kick': 0.7 * base_risk,
                'handball': 0.8 * base_risk,
                'run': 0.9 * base_risk,
                'contest': 0.6 * base_risk
            },
            'defensive': {
                'kick': 0.4 * base_risk,
                'handball': 0.5 * base_risk,
                'run': 0.3 * base_risk,
                'contest': 0.8 * base_risk
            },
            'balanced': {
                'kick': 0.6 * base_risk,
                'handball': 0.6 * base_risk,
                'run': 0.6 * base_risk,
                'contest': 0.7 * base_risk
            }
        }[self.style]
        
        # Modify based on offensive strategy
        if self.offense_strategy == 'Direct':
            base_profile['kick'] *= 1.2
        elif self.offense_strategy == 'stay_wide':
            base_profile['handball'] *= 1.1
            base_profile['run'] *= 1.1
            
        return base_profile
    
    def get_action_weighting(self, action_type, context, state):  # Add state parameter
        """Get tactical weighting for an action in current context"""
        zone = state.ground.get_zone(context.position)  # Use the method
        pressure = self.pressure_points[zone]
        risk = self.risk_profile[action_type]
        
        # Base weighting from existing logic
        weight = pressure * risk
        
        # Additional weighting based on defense strategy
        if self.defense_strategy == 'Man Mark':
            if action_type in ['mark', 'tackle']:
                weight *= 1.2
        elif self.defense_strategy == 'Zone Mark':
            if action_type == 'mark':
                weight *= 0.9
                
        # Adjust based on score and time
        score_diff = state.get_score_difference(self.team.side)  # Use the method
        if score_diff < -24:  # Two goals down
            weight *= 1.2  # Increase risk-taking when behind
        elif score_diff > 24:
            weight *= 0.8  # More conservative when ahead
            
        return weight
        
    def get_defensive_line_height(self):
        """Get defensive line height based on push factor"""
        return {
            'push_factor0': 0.3,
            'push_factor1': 0.5,
            'push_factor2': 0.7
        }.get(self.push_factor, 0.5)
        
    def should_man_mark(self, player, opponent):
        """Determine if player should man mark based on defense strategy"""
        return self.defense_strategy == 'Man Mark'
        
    def get_offensive_width(self):
        """Get preferred offensive width based on strategy"""
        return 'wide' if self.offense_strategy == 'stay_wide' else 'narrow'
    

    
class ActionInterrupt:
    """Handles interruption of actions and appropriate responses"""
    def __init__(self, type, source, priority='medium'):
        self.type = type
        self.source = source
        self.priority = priority
        self.timestamp = time.time()

class RecoveryAction:
    """Manages recovery actions after interrupts or failures"""
    def __init__(self, player, state, cause):
        self.player = player
        self.state = state
        self.cause = cause  # What triggered the need for recovery
        self.recovery_type = self._determine_recovery_type()
        self.priority = self._calculate_priority()
        self.steps = self._generate_recovery_steps()
        self.current_step = 0
        
    def _determine_recovery_type(self):
        """Determine type of recovery needed"""
        if isinstance(self.cause, ActionInterrupt):
            return {
                'tackle': 'physical_recovery',
                'pressure': 'positional_recovery',
                'whistle': 'play_reset',
                'collision': 'physical_recovery'
            }.get(self.cause.type, 'default_recovery')
        elif isinstance(self.cause, dict) and 'type' in self.cause:
            return {
                'failed_disposal': 'ball_recovery',
                'missed_mark': 'positional_recovery',
                'fumble': 'ball_recovery',
                'poor_bounce': 'ball_recovery'
            }.get(self.cause['type'], 'default_recovery')
            
    def _calculate_priority(self):
        """Calculate priority of recovery action"""
        base_priority = {
            'physical_recovery': 'high',
            'ball_recovery': 'high',
            'positional_recovery': 'medium',
            'play_reset': 'critical',
            'default_recovery': 'medium'
        }.get(self.recovery_type, 'medium')
        
        # Adjust based on game context
        if self.state.ball_carrier == self.player:
            return 'critical'  # Highest priority if we have the ball
        return base_priority
        
    def _generate_recovery_steps(self):
        """Generate sequence of recovery steps"""
        generators = {
            'physical_recovery': self._generate_physical_recovery,
            'ball_recovery': self._generate_ball_recovery,
            'positional_recovery': self._generate_positional_recovery,
            'play_reset': self._generate_play_reset,
            'default_recovery': self._generate_default_recovery
        }
        
        generator = generators.get(self.recovery_type, self._generate_default_recovery)
        return generator()
        
    def _generate_physical_recovery(self):
        """Generate steps for physical recovery"""
        return [
            {
                'type': 'brace',
                'duration': 0.5,
                'priority': 'immediate'
            },
            {
                'type': 'regain_balance',
                'target_position': self._calculate_recovery_position(),
                'priority': 'high'
            },
            {
                'type': 'ready_position',
                'orientation': self._determine_best_orientation(),
                'priority': 'medium'
            }
        ]
        
    def _generate_ball_recovery(self):
        """Generate steps for ball recovery"""
        return [
            {
                'type': 'track_ball',
                'target': self.state.ball_position,
                'priority': 'high'
            },
            {
                'type': 'approach_ball',
                'path': self._calculate_approach_path(),
                'priority': 'high'
            },
            {
                'type': 'gather_ball',
                'technique': self._determine_gather_technique(),
                'priority': 'critical'
            }
        ]
        
    def get_next_step(self):
        """Get next recovery step"""
        if self.current_step < len(self.steps):
            return self.steps[self.current_step]
        return None
        
    def advance(self):
        """Advance to next recovery step"""
        self.current_step += 1
        return self.current_step < len(self.steps)
    
class GameState:
    """Manages the current state of the game"""
    def __init__(self, home_team, home_team_players, away_team, away_team_players, ground, weather_conditions=None):
        self.ground = ground
        self.home_team = home_team
        self.away_team = away_team
        self.home_team_players = home_team_players
        self.away_team_players = away_team_players
        self.weather_system = WeatherSystem(weather_conditions)
        
        # Game status
        self.quarter = 1
        self.time_remaining = 20 * 60  # 20 minutes in seconds
        self.play_phase = 'pre_game'
        self.score = {'home': 0, 'away': 0}
        self.ball_position = None
        self.ball_carrier = None
        self.last_possession = None
        
        # Phase-specific data
        self.phase_data = {
            'contest': {
                'type': None,  # 'ball_up', 'boundary_throw', 'free_kick'
                'position': None,
                'primary_players': []
            },
            'set_shot': {
                'player': None,
                'position': None,
                'angle': None,
                'distance': None
            },
            'open_play': {
                'congestion_level': 0,
                'pressure_level': 0
            }
        }
        
        # Player states
        self.player_states = {}  # player_id -> PlayerState
        self._initialize_player_states()
        
    def _initialize_player_states(self):
        """Initialize state tracking for all players"""
        # Use home_team_players and away_team_players instead of team.players
        for pos, player in self.home_team_players.items():
            self.player_states[player.id] = PlayerState(player, starting_position=pos)
        for pos, player in self.away_team_players.items():
            self.player_states[player.id] = PlayerState(player, starting_position=pos)
                
    def update(self, delta_time):
        """Update game state for the given time delta"""
        # Update time
        self.time_remaining -= delta_time
        
        # Update player states with weather effects
        fatigue_modifier = self.weather_system.get_fatigue_modifier()
        for player_state in self.player_states.values():
            player_state.update(delta_time, fatigue_modifier)
            
        # Update phase-specific data
        self._update_phase_data()
        
    def transition_phase(self, new_phase, data=None):
        """Handle transition to a new play phase"""
        old_phase = self.play_phase
        self.play_phase = new_phase
        
        # Reset phase data
        if new_phase in self.phase_data:
            self.phase_data[new_phase] = {}
            
        # Set new phase data
        if data:
            self.phase_data[new_phase].update(data)
            
        return {
            'old_phase': old_phase,
            'new_phase': new_phase,
            'data': data
        }
        
    def _update_phase_data(self):
        """Update current phase specific data"""
        if self.play_phase == 'open_play':
            self._update_open_play_data()
        elif self.play_phase == 'contest':
            self._update_contest_data()
        elif self.play_phase == 'set_shot':
            self._update_set_shot_data()
            
    def get_score_difference(self, team_side):
        """Get score difference from team's perspective"""
        #print(f"Score difference: {self.score['home']} - {self.score['away']}")
        #print(f"Team side: {team_side}")
        if team_side == 'home':
            return self.score['home'] - self.score['away']
        return self.score['away'] - self.score['home']
"""        
    def get_field_position_zone(self, position):
        #Get zone name for a field position
        x, y = position
        field_third = x / self.ground.length
        
        if field_third < 0.3:
            return 'defensive_50'
        elif field_third > 0.7:
            return 'forward_50'
        return 'midfield'
"""        

class PlayerState:
    """Tracks individual player state"""
    def __init__(self, player, starting_position):
        self.player = player
        #print(f"Initializing player state for {self.player}")
        self.current_position = starting_position
        self.fatigue = 0  # 0-100
        self.status = 'active'  # active, injured, suspended
        self.recent_actions = []  # List of recent actions
        self.possession_count = 0
        self.current_role =  starting_position  # Can change during game
        self.marking_target = None
        
    def update(self, delta_time, fatigue_modifier):
        """Update player state"""
        # Update fatigue
        self._update_fatigue(delta_time, fatigue_modifier)
        
        # Clear old actions
        self._clean_recent_actions()
        
    def _update_fatigue(self, delta_time, fatigue_modifier):
        """Update player fatigue level"""
        # Base fatigue increase
        fatigue_increase = delta_time * fatigue_modifier * 0.1  # Basic rate
        
        # Modify based on recent actions
        for action in self.recent_actions[-5:]:  # Last 5 actions
            if action['type'] in ['sprint', 'contest', 'tackle']:
                fatigue_increase *= 1.2
                
        # Apply fatigue increase
        self.fatigue = min(100, self.fatigue + fatigue_increase)
        
        # Natural recovery when inactive
        if not self.recent_actions:
            self.fatigue = max(0, self.fatigue - (delta_time * 0.05))
            
    def _clean_recent_actions(self):
        """Remove old actions from history"""
        current_time = time.time()
        self.recent_actions = [
            action for action in self.recent_actions
            if current_time - action['timestamp'] < 30  # Keep last 30 seconds
        ]
        
    def add_action(self, action):
        """Record a new action"""
        self.recent_actions.append({
            'type': action['type'],
            'timestamp': time.time(),
            **action
        })
        
        if action['type'] in ['mark', 'gather', 'receive']:
            self.possession_count += 1

class RoleManager:
    """Manages role-specific behaviors and decision making"""
    def __init__(self):
        self.role_behaviors = {
            'forward': {
                'primary': self._forward_primary_behavior,
                'secondary': self._forward_secondary_behavior,
                'defensive': self._forward_defensive_behavior
            },
            'midfielder': {
                'primary': self._midfielder_primary_behavior,
                'secondary': self._midfielder_secondary_behavior,
                'defensive': self._midfielder_defensive_behavior
            },
            'defender': {
                'primary': self._defender_primary_behavior,
                'secondary': self._defender_secondary_behavior,
                'offensive': self._defender_offensive_behavior
            },
            'ruck': {
                'primary': self._ruck_primary_behavior,
                'secondary': self._ruck_secondary_behavior,
                'roaming': self._ruck_roaming_behavior
            }
        }
        
    def get_role_behavior(self, role, situation):
        """Get appropriate behavior for role and situation"""
        return self.role_behaviors.get(role, {}).get(situation)
        
    def _forward_primary_behavior(self, player, state):
        """Primary forward behavior - scoring opportunities"""
        return {
            'priority': 'score',
            'actions': ['lead', 'mark', 'shoot'],
            'positioning': 'attacking',
            'risk_level': 'high'
        }
        
    def _midfielder_primary_behavior(self, player, state):
        """Primary midfielder behavior - ball movement"""
        return {
            'priority': 'possession',
            'actions': ['gather', 'handball', 'kick'],
            'positioning': 'dynamic',
            'risk_level': 'medium'
        } 

class WeatherSystem:
    """Manages weather conditions and their effects on gameplay"""
    
    WEATHER_TYPES = {
        'clear': {
            'visibility': 1.0,
            'ball_handling': 1.0,
            'fatigue_rate': 1.0,
            'ground_condition': 1.0
        },
        'light_rain': {
            'visibility': 0.9,
            'ball_handling': 0.85,
            'fatigue_rate': 1.1,
            'ground_condition': 0.9
        },
        'heavy_rain': {
            'visibility': 0.7,
            'ball_handling': 0.6,
            'fatigue_rate': 1.25,
            'ground_condition': 0.7
        },
        'windy': {
            'visibility': 0.95,
            'ball_handling': 0.8,
            'fatigue_rate': 1.15,
            'ground_condition': 1.0,
            'wind_direction': None,  # Set during initialization
            'wind_strength': None    # Set during initialization
        },
        'stormy': {
            'visibility': 0.6,
            'ball_handling': 0.5,
            'fatigue_rate': 1.3,
            'ground_condition': 0.6,
            'wind_direction': None,
            'wind_strength': None
        },
        'hot': {
            'visibility': 1.0,
            'ball_handling': 0.95,
            'fatigue_rate': 1.4,
            'ground_condition': 1.0
        },
        'overcast': {
            'visibility': 0.9,
            'ball_handling': 0.95,
            'fatigue_rate': 1.0,
            'ground_condition': 1.0
        }
    }
    
    def __init__(self, conditions=None):
        self.current_conditions = conditions or 'clear'
        self.effects = self.WEATHER_TYPES[self.current_conditions].copy()
        
        # Initialize wind if applicable
        if 'wind_direction' in self.effects:
            self.effects['wind_direction'] = random.choice(['N', 'S', 'E', 'W', 'NE', 'NW', 'SE', 'SW'])
            self.effects['wind_strength'] = random.uniform(0.5, 1.0)
    
    def get_kick_modifier(self, kick_direction, distance):
        """Calculate weather effect on kick accuracy and distance"""
        base_modifier = self.effects['ball_handling']
        
        if 'wind_direction' in self.effects:
            wind_impact = self._calculate_wind_impact(kick_direction)
            # Longer kicks are more affected by wind
            distance_factor = min(1.0, distance / 50.0)  # Max effect at 50m
            base_modifier *= (1.0 + (wind_impact * distance_factor))
            
        return base_modifier
    
    def get_marking_modifier(self):
        """Calculate weather effect on marking ability"""
        return self.effects['visibility'] * self.effects['ball_handling']
    
    def get_ground_movement_modifier(self):
        """Calculate weather effect on player movement"""
        return self.effects['ground_condition']
    
    def get_fatigue_modifier(self):
        """Calculate weather effect on player fatigue"""
        return self.effects['fatigue_rate']
    
    def _calculate_wind_impact(self, kick_direction):
        """Calculate wind impact on kick direction"""
        if 'wind_direction' not in self.effects:
            return 0.0
            
        # Convert directions to angles
        wind_angle = self._direction_to_angle(self.effects['wind_direction'])
        kick_angle = self._direction_to_angle(kick_direction)
        
        # Calculate angle difference
        angle_diff = abs(wind_angle - kick_angle)
        if angle_diff > 180:
            angle_diff = 360 - angle_diff
            
        # Calculate impact
        impact = math.cos(math.radians(angle_diff))
        return impact * self.effects['wind_strength']
    
    def _direction_to_angle(self, direction):
        """Convert cardinal direction to angle"""
        angles = {
            'N': 0, 'NE': 45, 'E': 90, 'SE': 135,
            'S': 180, 'SW': 225, 'W': 270, 'NW': 315
        }
        return angles.get(direction, 0)
    
    def get_visibility_for_position(self, position, ground):
        """Calculate visibility at specific position considering weather"""
        base_visibility = self.effects['visibility']
        
        # Adjust for position-specific factors
        if 'wind_direction' in self.effects:
            # Positions facing into wind have reduced visibility
            position_direction = ground.get_position_direction(position)
            wind_impact = self._calculate_wind_impact(position_direction)
            base_visibility *= (1.0 - (abs(wind_impact) * 0.2))
            
        return max(0.4, min(1.0, base_visibility))

class ScoringEngine:
    def __init__(self, ground):
        """Initialize scoring engine with ground reference"""
        self.ground = ground

    def evaluate_scoring_chance(self, player, state):
        """Main method to evaluate scoring opportunity"""
        goal_pos = self.ground.get_goal_position(player.team_side)
        
        # Calculate key factors
        angle = self._calculate_goal_angle(player.current_position, goal_pos)
        distance = self.ground.get_distance(player.current_position, goal_pos)
        pressure = self._calculate_pressure_factor(player, state)
        
        # Get player ability factors
        skill_factor = self._calculate_skill_factor(player)
        
        # Get environmental factors
        weather_factor = self._calculate_weather_impact(state)
        
        return {
            'probability': self._calculate_shot_probability(
                angle, distance, pressure, skill_factor, weather_factor
            ),
            'difficulty': self._calculate_shot_difficulty(
                angle, distance, pressure
            ),
            'recommendation': self._get_shot_recommendation(
                angle, distance, pressure, state
            )
        }
    
    def _is_scoring_position(self, player):
        #Check if player is in scoring position
        forward_50 = self.ground.zones['forward']
        player_pos = player.current_position
        
        # Check if player is in forward 50
        if player_pos in forward_50:
            # Calculate angle to goal
            angle = self._calculate_goal_angle(player_pos)
            return angle < 45  # 45 degree angle or less is considered scoring position
        return False  

    def _calculate_goal_angle(self, player_pos, goal_pos):
        """Calculate angle to goal in degrees"""
        dx = goal_pos[0] - player_pos[0]
        dy = goal_pos[1] - player_pos[1]
        angle = math.degrees(math.atan2(abs(dy), abs(dx)))
        return angle

    def _calculate_distance_impact(self, distance):
        """Calculate impact of distance on scoring chance"""
        # Scoring probability decreases with distance
        # Max range ~65m
        if distance > 65:
            return 0.1  # Very low probability
        elif distance < 15:
            return 1.0  # Point blank range
        else:
            # Linear decrease between 15-65m
            return 1.0 - ((distance - 15) / 50)

    def _adjust_for_scoring_chance(self, state):
        """Evaluate scoring opportunity factors"""
        # Get goal position and calculate angle
        goal_pos = self.ground.get_goal_position(self.player.team_side)
        goal_angle = self._calculate_goal_angle(self.player.current_position, goal_pos)
        
        # Calculate distance using existing ground method
        distance = self.ground.get_distance(
            self.player.current_position,
            goal_pos
        )
        distance_factor = self._calculate_distance_impact(distance)
        
        # Calculate risk/reward based on game situation
        risk_reward = self._calculate_risk_reward(
            state.score_difference,
            state.time_remaining,
            goal_angle,
            distance
        )
        
        return {
            'risk_reward': risk_reward,
            'goal_angle': goal_angle,
            'distance_factor': distance_factor
        }

    def _calculate_pressure_factor(self, player, state):
        """Calculate pressure impact on scoring chance"""
        # Stub: Implement pressure calculation
        pass

    def _calculate_skill_factor(self, player):
        """Calculate player skill impact on scoring chance"""
        # Stub: Implement skill factor calculation
        pass

    def _calculate_weather_impact(self, state):
        """Calculate weather impact on scoring chance"""
        # Stub: Implement weather impact
        pass

    def _calculate_shot_probability(self, angle, distance, pressure, skill, weather):
        """Calculate overall shot probability"""
        # Stub: Implement probability calculation
        pass

    def _calculate_shot_difficulty(self, angle, distance, pressure):
        """Calculate shot difficulty rating"""
        # Stub: Implement difficulty calculation
        pass

    def _get_shot_recommendation(self, angle, distance, pressure, state):
        """Get recommendation for shot attempt"""
        # Stub: Implement shot recommendation logic
        pass

    def _evaluate_game_context(self, state):
        """Evaluate game context for shot decision"""
        # Stub: Implement game context evaluation
        pass

    def _check_better_options(self, player, state):
        """Check if better options exist than shooting"""
        # Stub: Implement options evaluation
        pass
    
        
class PlayPhases:
    """Defines and manages all possible play phases"""
    
    # Game status phases
    GAME_STATUS = [
        'pre_game',
        'quarter_break',
        'post_game'
    ]
    
    # Set play phases
    SET_PLAY = [
        'ball_up',
        'kick_in',
        'set_shot',
        'free_kick',
        'bounce',
        'boundary_throw'
    ]
    
    # Contest phases
    CONTEST = [
        'stoppage_contest',
        'aerial_contest',
        'ground_contest',
        'marking_contest'
    ]
    
    # Open play phases
    OPEN_PLAY = [
        'open_play',
        'transition'
    ]
    
    @classmethod
    def all_phases(cls):
        """Return all possible play phases"""
        return (cls.GAME_STATUS + cls.SET_PLAY + 
                cls.CONTEST + cls.OPEN_PLAY)
    
    @classmethod
    def is_set_play(cls, phase):
        return phase in cls.SET_PLAY
    
    @classmethod
    def is_contest(cls, phase):
        return phase in cls.CONTEST
    
    @classmethod
    def is_open_play(cls, phase):
        return phase in cls.OPEN_PLAY