{% extends "admin/base_site.html" %}
{% load static %}

{% block content %}
  <h1>Simulate Match</h1>
  <form method="post" id="simulate-match-form">
    {% csrf_token %}
    <div>
      <label for="id_team1">Team 1:</label>
      <select id="id_team1" name="team1">
        <option value="">Select a team</option>
        {% for team in teams %}
          <option value="{{ team.id }}">{{ team.name }}</option>
        {% endfor %}
      </select>
    </div>
    <div>
      <label for="id_team1_ruck">Ruck 1:</label>
      <select id="id_team1_ruck" name="team1_ruck">
        <option value="">Select a player</option>
      </select>
    </div>
    <div>
      <label for="id_team2">Team 2:</label>
      <select id="id_team2" name="team2">
        <option value="">Select a team</option>
        {% for team in teams %}
          <option value="{{ team.id }}">{{ team.name }}</option>
        {% endfor %}
      </select>
    </div>
    <div>
      <label for="id_team2_ruck">Ruck 2:</label>
      <select id="id_team2_ruck" name="team2_ruck">
        <option value="">Select a player</option>
      </select>
    </div>
    <button type="submit">Simulate Match</button>
  </form>

  <div id="results"></div>

  <script src="{% static 'admin/js/match_simulation.js' %}"></script>
  <script>
document.getElementById('simulate-match-form').onsubmit = function(e) {
  e.preventDefault();

  // Get selected values
  var team1Id = document.getElementById('id_team1').value;
  var team2Id = document.getElementById('id_team2').value;
  var team1RuckId = document.getElementById('id_team1_ruck').value;
  var team2RuckId = document.getElementById('id_team2_ruck').value;

  // Create WebSocket connection
  var ws = new WebSocket('ws://' + window.location.host.replace(':8000', ':8001') + '/ws/simulate_match/');

  ws.onopen = function() {
    console.log("WebSocket connection opened");

    // Send data to the WebSocket server
    ws.send(JSON.stringify({
      'command': 'start_simulation',
      'team1_id': team1Id,
      'team2_id': team2Id,
      'team1_ruck_id': team1RuckId,
      'team2_ruck_id': team2RuckId
    }));
  };

  ws.onmessage = function(event) {
    console.log("WebSocket message received");
    var data = JSON.parse(event.data);

    if (data.error) {
      console.error("WebSocket Error:", data.error);
      return;
    }
    if (data.event) {
      var resultsDiv = document.getElementById('results');
      resultsDiv.innerHTML += '<p>' + data.event + '</p>';
    }
    if (data.quarter_result) {
      var resultsDiv = document.getElementById('results');
      resultsDiv.innerHTML += '<h3>Quarter ' + data.quarter_result.quarter + ' Results:</h3>';
      data.quarter_result.events.forEach(function(event) {
        resultsDiv.innerHTML += '<p>' + event + '</p>';
      });
    }
    if (data.final_result) {
      var resultsDiv = document.getElementById('results');
      resultsDiv.innerHTML += '<h2>' + data.final_result + '</h2>';
    }
  };

  ws.onclose = function(e) {
    console.log("WebSocket connection closed");
  };

  ws.onerror = function(e) {
    console.log("WebSocket error:", e);
  };
};
  </script>
{% endblock %}
