# Generated by Django 5.0.7 on 2024-11-12 00:35

from django.db import migrations

def set_empty_league_to_null(apps, schema_editor):
    Team = apps.get_model('teams', 'Team')
    #Team.objects.filter(league="").update(league=None)  # Set empty strings to NULL

class Migration(migrations.Migration):
    dependencies = [
        ('teams', '0021_alter_team_league'),  # Replace with the actual last migration name in 'teams'
    ]

    operations = [
        migrations.RunPython(set_empty_league_to_null),
    ]
