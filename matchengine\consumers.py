import json
from collections import deque
from channels.generic.websocket import AsyncWebsocketConsumer
from matchengine.models import simulate_match
import asyncio, traceback

MAX_QUEUE_SIZE = 50

class MatchConsumer(AsyncWebsocketConsumer):
    def __init__(self, *args, **kwargs):
       super().__init__(*args, **kwargs)
       self.message_queue = deque()
       self.is_processing = False

    async def connect(self):
        self.match_id = self.scope['url_route']['kwargs']['match_id']
        self.match_group_name = f'match_{self.match_id}'
        
        print(f"Consumer attempting to connect to {self.match_group_name}")
        
        try:
            # Accept the connection first
            await self.accept()
            print(f"WebSocket connection accepted")
            
            # Try to join the group
            await self.channel_layer.group_add(
                self.match_group_name,
                self.channel_name
            )
            print(f"Added {self.channel_name} to group {self.match_group_name}")
            
            # Verify group membership directly with Redis
            connection = await self.channel_layer.connection(0)
            group_key = f"{self.channel_layer.prefix}:groups:{self.match_group_name}"
            
            # Try to add member directly if needed
            await connection.sadd(group_key, self.channel_name)
            
            # Verify membership
            members = await connection.smembers(group_key)
            print(f"Redis group members: {members}")
            
            # Send test message to verify connection
            await self.send(text_data=json.dumps({
                'type': 'connection_status',
                'message': 'Connected successfully'
            }))
            
        except Exception as e:
            print(f"Error in consumer connect: {e}")
            print(traceback.format_exc())

    async def disconnect(self, close_code):
        # Remove the channel from the group
        await self.channel_layer.group_discard(self.match_group_name, self.channel_name)
        print(f"WebSocket disconnected from match group: {self.match_group_name}")  # Debugging

    async def receive(self, text_data):
        """Handle incoming messages from WebSocket"""
        data = json.loads(text_data)
        command = data.get('command')
        
        if command == 'start_simulation':
            print(f"Starting match simulation for match_id: {self.match_id}")
            
            # Start simulation in a separate task
            asyncio.create_task(self.run_simulation())
            
            # Acknowledge start
            await self.send(text_data=json.dumps({
                'type': 'simulation_started',
                'message': 'Match simulation started'
            }))
        
    async def run_simulation(self):
        """Run the simulation in a separate task"""
        try:
            async for event in simulate_match(match_id=self.match_id):
                # Each event from simulate_match will be processed here
                if event:
                    #print(f"Processing event in real-time: {event}")
                    await self.send(text_data=json.dumps({
                        'type': event['type'],
                        'data': event
                    }))
                    # Give the event loop a chance to process
                    await asyncio.sleep(0.1)
                    
        except Exception as e:
            print(f"Error in simulation: {e}")
            print(traceback.format_exc())
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': str(e)
            }))
    """
    async def send_match_event(self, event):
        #Handle match events from group_send
        try:
            print(f"Consumer received event: {event}")
            
            # Extract the actual event data
            data = event.get('data', {})
            
            # Send to WebSocket
            await self.send(text_data=json.dumps({
                'type': 'match_event',
                'data': data
            }))
            print(f"Sent event to WebSocket: {data}")
            
        except Exception as e:
    """