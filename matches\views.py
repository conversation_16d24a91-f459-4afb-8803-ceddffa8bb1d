from django.shortcuts import render, get_object_or_404
from django.http import JsonResponse
from django.contrib.auth.decorators import login_required
from teams.models import Team, Player, PlayerStatsLvls
from .models import Match

@login_required
def set_orders_view(request, match_id):
    match = get_object_or_404(Match, id=match_id)
    team = get_object_or_404(Team, user=request.user)
    if team != match.home_team and team != match.away_team:
        return render(request, 'teams/access_denied.html')  # Or raise an error
        
    players = Player.objects.filter(team=team)  # Get the players for the team
    player_stats_lvls = PlayerStatsLvls.objects.all().first()
    
    return render(request, 'teams/match_orders/orders.html', {'match': match, 'team': team, 'players': players, 'player_stats_lvls': player_stats_lvls})


def set_lineup_and_tactics_view(request, match_id):
    if request.method == "POST":
        match = get_object_or_404(Match, id=match_id)

        # Parse JSON payload from request body
        import json
        try:
            data = json.loads(request.body)
            team = data.get("team")  # 'home' or 'away'
            lineup = data.get("lineup")
            tactics = data.get("tactics")
        except json.JSONDecodeError:
            return JsonResponse({"error": "Invalid JSON format"}, status=400)

        # Validate team
        try:
            team_id = int(team)  # Ensure team is cast to an integer for comparison
        except ValueError:
            return JsonResponse({"error": "Invalid team ID"}, status=400)

        if match.home_team.id == team_id:
            team_instance = match.home_team
        elif match.away_team.id == team_id:
            team_instance = match.away_team
        else:
            team_instance = None

        # Debugging output
        #print(f"Linup: {lineup}")
        #print(f"Team: {team_id}")
        #print(f"Home: {match.home_team.id} Away: {match.away_team.id}")
        #print(f"Team instance: {team_instance}")

        if not team_instance:
            return JsonResponse({"error": "Invalid team selection"}, status=400)

        # Save lineup and tactics
        try:
            match.set_lineup_and_tactics(team=team_instance, lineup=lineup, tactics=tactics)
            return JsonResponse({"success": True})
        except ValueError as e:
            return JsonResponse({"error": str(e)}, status=400)
    else:
        return JsonResponse({"error": "POST request required"}, status=405)


def get_lineup_and_tactics_view(request, match_id):
    match = get_object_or_404(Match, id=match_id)
    data = {
        "lineup": match.home_team_lineup if match.home_team.id == request.user.team.id else match.away_team_lineup,
        "tactics": match.home_team_tactics if match.home_team.id == request.user.team.id else match.away_team_tactics,
    }
    return JsonResponse(data)