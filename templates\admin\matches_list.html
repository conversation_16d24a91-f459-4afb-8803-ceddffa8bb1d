<h1>Matches</h1>
<table>
    <thead>
        <tr>
            <th>Home Team</th>
            <th>Away Team</th>
            <th>Start Time</th>
            <th>Status</th>
            <th>Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for match in matches %}
        <tr>
            <td>{{ match.home_team.name }}</td>
            <td>{{ match.away_team.name }}</td>
            <td>{{ match.date }}</td>
            <td>{{ match.played }}</td>
            <td>
                {% if match.played != True %}
                <form method="POST" action="{% url 'admin:force_simulate_match' match.id %}">
                    {% csrf_token %}
                    <button type="submit">Force Simulate</button>
                </form>
                {% else %}
                Completed
                {% endif %}
            </td>
        </tr>
        {% endfor %}
	
    </tbody>
</table>
