# Generated by Django 5.0.7 on 2024-11-06 04:24

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('teams', '0011_delete_playerstatslvls'),
    ]

    operations = [
        migrations.CreateModel(
            name='PlayerStatsAttributes',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('experience', models.IntegerField()),
                ('leadership', models.IntegerField()),
                ('loyalty', models.IntegerField(default=20)),
                ('wage', models.DecimalField(decimal_places=2, max_digits=10)),
            ],
        ),
        migrations.AlterField(
            model_name='playerstatsability',
            name='marking',
            field=models.IntegerField(),
        ),
        migrations.AddField(
            model_name='player',
            name='attributes_stats',
            field=models.OneToOneField(default=5, on_delete=django.db.models.deletion.CASCADE, to='teams.playerstatsattributes'),
            preserve_default=False,
        ),
    ]
