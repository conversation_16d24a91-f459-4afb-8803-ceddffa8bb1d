from django.urls import path
from .views import team_view, dashboard_view, club_view, stadium_view, staff_view, supporters_view, finances_view, transfers_view, senior_team_view, league_view, matches_view#, set_orders_view
from matches.views import set_lineup_and_tactics_view, get_lineup_and_tactics_view, set_orders_view

urlpatterns = [
    path('team/<int:team_id>/', team_view, name='team'),
    path('dashboard/', dashboard_view, name='dashboard'),  # New dashboard URL
    path('club/', club_view, name='club'),
    path('stadium/', stadium_view, name='stadium'),
    path('staff/', staff_view, name='staff'),
    path('supporters/', supporters_view, name='supporters'),
    path('finances/', finances_view, name='finances'),
    path('transfers/', transfers_view, name='transfers'),
    path('league/<int:team_id>/', league_view, name='league'), 
    path('matches/<int:team_id>/', matches_view, name='matches'),
    path('matches/set_orders/<int:match_id>', set_orders_view, name='set_orders'),
    path('senior_team/<int:team_id>/', senior_team_view, name='senior_team'),
    path('matches/set_orders/set_lineup_and_tactics/<int:match_id>', set_lineup_and_tactics_view, name='set_lineup_and_tactics'),
    path('matches/set_orders/get_lineup_and_tactics/<int:match_id>', get_lineup_and_tactics_view, name='get_lineup_and_tactics')
]

print("Teams URLs loaded")
