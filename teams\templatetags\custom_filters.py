from django import template

register = template.Library()


@register.filter
def divisibleby(value, arg):
    value = float(value)
    arg = float(arg)
    result = value / arg
    # Return the result formatted with commas
    return '{:,.2f}'.format(result)
    
@register.filter
def multiply(value, arg):
    return value * arg

@register.filter
def get_stat_label(player_stats_lvls, stat_level):
    """
    Given the `player_stats_lvls` and a stat level (e.g., 4),
    this filter retrieves the corresponding label from the appropriate column.
    """
    column_name = f"level_{stat_level}" 
    return getattr(player_stats_lvls, column_name, 'Unknown')  # Return the label or 'Unknown' if not found
