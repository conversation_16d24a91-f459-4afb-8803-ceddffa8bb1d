import json
from channels.generic.websocket import AsyncWebsocketConsumer
from matchengine.models import simulate_match

class MatchConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        self.match_id = self.scope['url_route']['kwargs']['match_id']
        self.match_group_name = f"match_{self.match_id}"
        #print(f"match_id {self.match_id}")
        
        await self.channel_layer.group_add(self.match_group_name, self.channel_name)
        await self.accept()

    async def disconnect(self, close_code):
        await self.channel_layer.group_discard(self.match_group_name, self.channel_name)

    async def receive(self, text_data):
        data = json.loads(text_data)
        if data.get('command') == 'start_simulation':
            # Trigger match simulation
            await simulate_match(self.match_id)

    async def send_match_event(self, event):
        print(f"Received event: {event}")  # Debugging

        await self.send(text_data=json.dumps({
            'event_type': event['event_type'],  # Use 'event_type' directly
            'data': event  # Include the entire event data as the payload
        }))
