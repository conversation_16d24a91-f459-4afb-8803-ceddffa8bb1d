from django.db import models
from django.conf import settings
from users.models import CustomUser

class League(models.Model):
    name = models.CharField(max_length=255)
    tier = models.PositiveIntegerField()  # Tier 1 for top league, increasing downwards
    division_number = models.PositiveIntegerField()  # Multiple divisions per tier
    country = models.CharField(max_length=255)  # New country field
    
    class Meta:
        unique_together = ('tier', 'division_number')
        
    def __str__(self):
        return f"{self.name}, - Tier {self.tier}, Division {self.division_number}, Country:({self.country})"
        
    #def get_higher_league(self):
    #return League.objects.filter(tier=self.tier - 1).first()

    #def get_lower_league(self):
    #return League.objects.filter(tier=self.tier + 1).first()            
        
    #def get_standings(self):
    # This should be a method to calculate team standings
    # You could implement this based on match results in the current season
    #teams = self.teams.all()
    #standings = sorted(teams, key=lambda team: team.get_points(), reverse=True)
    #ladder = Ladder.objects.filter(league_id=team.league_id).order_by('-points', 'games_played', '-percentage')
    #return standings    


