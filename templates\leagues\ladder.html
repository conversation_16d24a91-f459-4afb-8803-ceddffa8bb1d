{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>League Ladder</title>
	<link rel="stylesheet" href="{% static 'teams/css/ladder.css' %}?<?php echo date('l jS \of F Y h:i:s A'); ?>">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
<div class="ladder-container">
    <div class="ladder-header">
        {{ league.name }} - Tier: {{ league.tier }}, Division: {{ league.division_number }}, Country: {{ league.country }}
</div>
	<div class="toggle-buttons">
    <button onclick="showSummary()">Summary</button>
    <button onclick="showExtended()">Extended</button>
</div>
    <table class="ladder-table">
        <thead>
            <tr>
                <th>Position</th>
                <th>Team</th>
                <th>Games</th>
                <th>Wins</th>
                <th>Losses</th>
                <th>Draws</th>
                <th>Points</th>
				<th class="pf-pa-column hidden">PF</th> <!-- New Points For Column -->
				<th class="pf-pa-column hidden">PA</th>
                <th>Percentage</th>
            </tr>
        </thead>
        <tbody>
            {% for team in ladder %}
            <tr
				class="
					{% if forloop.counter <= 2 %} auto-promotion
					{% elif forloop.counter <= 4 %} playoff
					{% elif forloop.counter >= 5 and forloop.counter <= 6 %} midtable
					{% elif forloop.counter >= 7 and forloop.counter <= 8 %} relegation-playoff
					{% elif forloop.counter >= 9 and forloop.counter <= 10 %} relegation
					{% endif %}
				"
			>
                <td class="ladder-position">{{ forloop.counter }}</td>
                <td class="ladder-team">{{ team.team.name }}</td>
                <td>{{ team.games_played }}</td>
                <td>{{ team.wins }}</td>
                <td>{{ team.losses }}</td>
                <td>{{ team.draws }}</td>
                <td class="ladder-points">{{ team.points }}</td>
				<td class="pf-pa-column hidden">{{ team.points_for }}</td> <!-- PF Data -->
				<td class="pf-pa-column hidden">{{ team.points_against }}</td>
                <td>{{ team.percentage }}%</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
</body>
</html>

