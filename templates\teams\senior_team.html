{% load static %}
{% load custom_filters %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Senior Team</title>
	<link rel="stylesheet" href="{% static 'teams/css/senior_team.css' %}?<?php echo date('l jS \of F Y h:i:s A'); ?>">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>

    <h1>Senior Team</h1>

    {% for player in players %}
    <div class="player-card">
        <img src="{{ player.avatar_url }}" alt="{{ player.name }}" class="player-avatar">
        
        <div class="player-info">
            <!-- Left Side: Basic Info -->
            <div class="player-details">
                <h2>{{ player.name }}</h2>
				<div class="stat-row">Age: {{ player.physical_stats.age }}</div>
                <div class="stat-row">Height: {{ player.physical_stats.height }}</div>
                <div class="stat-row">Experience: {{ player.attributes_stats.experience }}</div>
                <div class="stat-row">Leadership: {{ player.attributes_stats.leadership }}</div>
                <div class="stat-row">Loyalty: {{ player.attributes_stats.loyalty }}</div>
                <div class="stat-row">Weekly Wage: ${{ player.attributes_stats.wage|divisibleby:52 }}</div>

                <h3>Physical Stats</h3>
 				<div class="progress-bar-container">
					<div class="stat-row">
						<label>Agility:</label>
						<span>{{ player.physical_stats.agility }}</span>
					</div>
					
					<!-- Label centered across the full width of the container -->
					<span class="progress-label">{{ player_stats_lvls|get_stat_label:player.physical_stats.agility }}</span>

					<!-- Progress bar fill -->
					<div class="progress-bar">
						<div class="progress-bar-fill" style="width: {{ player.physical_stats.agility|multiply:5 }}%;"></div>
					</div>
				</div>
				<div class="progress-bar-container">
					<div class="stat-row">
						<label>Speed:</label>
						<span>{{ player.physical_stats.speed }}</span>
					</div>
					
					<!-- Label centered across the full width of the container -->
					<span class="progress-label">{{ player_stats_lvls|get_stat_label:player.physical_stats.speed }}</span>

					<!-- Progress bar fill -->
					<div class="progress-bar">
						<div class="progress-bar-fill" style="width: {{ player.physical_stats.speed|multiply:5 }}%;"></div>
					</div>
				</div>
				<div class="progress-bar-container">
					<div class="stat-row">
						<label>Strength:</label>
						<span>{{ player.physical_stats.strength }}</span>
					</div>
					
					<!-- Label centered across the full width of the container -->
					<span class="progress-label">{{ player_stats_lvls|get_stat_label:player.physical_stats.strength }}</span>

					<!-- Progress bar fill -->
					<div class="progress-bar">
						<div class="progress-bar-fill" style="width: {{ player.physical_stats.strength|multiply:5 }}%;"></div>
					</div>
				</div>
				<div class="progress-bar-container">
					<div class="stat-row">
						<label>Stamina:</label>
						<span>{{ player.physical_stats.stamina }}</span>
					</div>
					
					<!-- Label centered across the full width of the container -->
					<span class="progress-label">{{ player_stats_lvls|get_stat_label:player.physical_stats.stamina }}</span>

					<!-- Progress bar fill -->
					<div class="progress-bar">
						<div class="progress-bar-fill" style="width: {{ player.physical_stats.stamina|multiply:5 }}%;"></div>
					</div>
				</div>
            </div>

            <!-- Right Side: Ability Stats -->
            <div class="ability-stats">
                <h3>Ability Stats</h3>

                <!-- Kicking Stat -->
				<div class="progress-bar-container">
					<div class="stat-row">
						<label>Kicking:</label>
						<span>{{ player.ability_stats.kicking }}</span>
					</div>
					
					<!-- Label centered across the full width of the container -->
					<span class="progress-label">{{ player_stats_lvls|get_stat_label:player.ability_stats.kicking }}</span>

					<!-- Progress bar fill -->
					<div class="progress-bar">
						<div class="progress-bar-fill" style="width: {{ player.ability_stats.kicking|multiply:5 }}%;"></div>
					</div>
				</div>

				<!-- Goal Kicking Stat -->
				<div class="progress-bar-container">
					<div class="stat-row">
						<label>Goal Kicking:</label>
						<span>{{ player.ability_stats.goal_kicking }}</span>
					</div>
					
					<!-- Label centered across the full width of the container -->
					<span class="progress-label">{{ player_stats_lvls|get_stat_label:player.ability_stats.goal_kicking }}</span>

					<!-- Progress bar fill -->
					<div class="progress-bar">
						<div class="progress-bar-fill" style="width: {{ player.ability_stats.goal_kicking|multiply:5 }}%;"></div>
					</div>
				</div>

                <!-- Handball Stat -->
                <div class="progress-bar-container">
                    <div class="stat-row">
                        <label>Handball:</label>
                        <span>{{ player.ability_stats.handball }}</span>
                    </div>
					<!-- Label centered across the full width of the container -->
					<span class="progress-label">{{ player_stats_lvls|get_stat_label:player.ability_stats.handball }}</span>

					<!-- Progress bar fill -->
					<div class="progress-bar">
						<div class="progress-bar-fill" style="width: {{ player.ability_stats.handball|multiply:5 }}%;"></div>
					</div>
                </div>

                <!-- Marking Stat -->
                <div class="progress-bar-container">
                    <div class="stat-row">
                        <label>Marking:</label>
                        <span>{{ player.ability_stats.marking }}</span>
                    </div>
					<!-- Label centered across the full width of the container -->
					<span class="progress-label">{{ player_stats_lvls|get_stat_label:player.ability_stats.marking }}</span>

					<!-- Progress bar fill -->
					<div class="progress-bar">
						<div class="progress-bar-fill" style="width: {{ player.ability_stats.marking|multiply:5 }}%;"></div>
					</div>
                </div>

                <!-- Tackling Stat -->
                <div class="progress-bar-container">
                    <div class="stat-row">
                        <label>Tackling:</label>
                        <span>{{ player.ability_stats.tackling }}</span>
                    </div>
					<!-- Label centered across the full width of the container -->
					<span class="progress-label">{{ player_stats_lvls|get_stat_label:player.ability_stats.tackling }}</span>

					<!-- Progress bar fill -->
					<div class="progress-bar">
						<div class="progress-bar-fill" style="width: {{ player.ability_stats.tackling|multiply:5 }}%;"></div>
					</div>
                </div>

                <!-- Mental Stat -->
                <div class="progress-bar-container">
                    <div class="stat-row">
                        <label>Mental:</label>
                        <span>{{ player.ability_stats.mental }}</span>
                    </div>
					<!-- Label centered across the full width of the container -->
					<span class="progress-label">{{ player_stats_lvls|get_stat_label:player.ability_stats.mental }}</span>

					<!-- Progress bar fill -->
					<div class="progress-bar">
						<div class="progress-bar-fill" style="width: {{ player.ability_stats.mental|multiply:5 }}%;"></div>
					</div>
                </div>

                <!-- Tactical Stat -->
                <div class="progress-bar-container">
                    <div class="stat-row">
                        <label>Tactical:</label>
                        <span>{{ player.ability_stats.tactical }}</span>
                    </div>
					<!-- Label centered across the full width of the container -->
					<span class="progress-label">{{ player_stats_lvls|get_stat_label:player.ability_stats.tactical }}</span>

					<!-- Progress bar fill -->
					<div class="progress-bar">
						<div class="progress-bar-fill" style="width: {{ player.ability_stats.tactical|multiply:5 }}%;"></div>
					</div>
                </div>

                <!-- Versatility Stat -->
                <div class="progress-bar-container">
                    <div class="stat-row">
                        <label>Versatility:</label>
                        <span>{{ player.ability_stats.versatility }}</span>
                    </div>
					<!-- Label centered across the full width of the container -->
					<span class="progress-label">{{ player_stats_lvls|get_stat_label:player.ability_stats.versatility }}</span>

					<!-- Progress bar fill -->
					<div class="progress-bar">
						<div class="progress-bar-fill" style="width: {{ player.ability_stats.versatility|multiply:5 }}%;"></div>
					</div>
                </div>

                <!-- Consistency Stat -->
                <div class="progress-bar-container">
                    <div class="stat-row">
                        <label>Consistency:</label>
                        <span>{{ player.ability_stats.consistency }}</span>
                    </div>
					<!-- Label centered across the full width of the container -->
					<span class="progress-label">{{ player_stats_lvls|get_stat_label:player.ability_stats.consistency }}</span>

					<!-- Progress bar fill -->
					<div class="progress-bar">
						<div class="progress-bar-fill" style="width: {{ player.ability_stats.consistency|multiply:5 }}%;"></div>
					</div>
                </div>

            </div>
        </div>
    </div>
    {% endfor %}

</body>
</html>
