function initializeOrdersScript() {
    //console.log("Orders.js script initialized!");

    const matchId = document.getElementById('match-id').value;
  const players = document.querySelectorAll('.player');
  const field = document.querySelector('.field-container');
  const followers = document.querySelector('.followers-container');
  const Interchange = document.querySelector('.Interchange-container');
  let lineup = {}; // Tracks the current lineup, e.g., { FB: 1, HB: 2 }
	const requiredPositions = ["FB", "RBP", "LBP", "CHB", "LHB", "RHB", "Center", "Lwing", "Rwing", "CHF", "LHF", "RHF", "FF", "RFP", "LFP"];
  const requiredFollowersPositions = ["Ruck", "Ruck_Rover", "Rover"];
  const requiredIntPositions = ["1", "2", "3", "4", "Sub"];
  const positionFullNames = {
    FB: "Full Back",
    LBP: "Left Back Pocket",
    RBP: "Right Back Pocket",
    CHB: "Centre Half Back",
    LHB: "Left Half Back",
    RHB: "Right Half Back",
    Center: "Center",
    Lwing: "Left Wing",
    Rwing: "Right Wing",
    CHF: "Center Half Forward",
    LHF: "Left Half Forward",
    RHF: "Right Half Forward",
    FF: "Full Forward",
    RFP: "Right Forward Pocket",
    LFP: "Left Forward Pocket",
    Ruck: "Ruck",
    Ruck_Rover: "Ruck Rover",
    Rover: "Rover",
    1: "Interchange 1",
    2: "interchange 2",
    3: "interchange 3",
    4: "interchange 4",
    Sub: "Sub"
};
	let tactics = {}; // Tracks tactics data
  const sendGamePlanButton = document.getElementById("send-game-plan");

  const reviewContent = document.getElementById("review-content");
	const missingInfo = document.getElementById("missing-info");

  
  fetch(`../matches/set_orders/get_lineup_and_tactics/${matchId}`)
  .then(response => {
      if (!response.ok) {
          throw new Error('Failed to fetch lineup and tactics');
      }
      return response.json();
  })
  .then(data => {
      // Populate lineup
      const importedLineup = JSON.parse(data.lineup || '{}');
      //console.log(`lineup: ${lineup}`);
      Object.entries(importedLineup).forEach(([position, playerId]) => {
        lineup[position] = importedLineup[position]; // Update lineup
        // Use a wildcard selector to find the position element
        const positionElement = Array.from(document.querySelectorAll('[class^="player-rect"]'))
            .find(el => el.getAttribute('position') === position);
            const players = Array.from(document.querySelectorAll('.player')); // Convert NodeList to an Array
            const playerMap = Object.fromEntries(
              players.map(player => [
                  player.dataset.id,
                  player.querySelector('.player-name')?.textContent.trim() || player.textContent.trim()
              ])
          );
        if (positionElement) {
            // Set player ID and add styling
            positionElement.setAttribute('data-player-id', playerId);
            positionElement.style.backgroundImage = "url('../../../static/player/img/guinea.png')";
            positionElement.style.backgroundSize = "contain"; // Ensure the image fits within the rectangle
            //positionElement.style.backgroundColor = "#fff"; // Clear any previous color
            positionElement.style.backgroundRepeat = "no-repeat"; // Prevent repeating of the image
            positionElement.style.backgroundPosition = "center 6px"; // Adjust the vertical position
            positionElement.textContent = ""; // Clear previous text content if any
    
            // Find the player in the player list
            const playerElement = document.querySelector(`.player[data-id="${playerId}"]`);
            //console.log(`Player Element: ${playerElement}`);
            const playerName = playerMap[playerId];
            //console.log(`Player name: ${playerName}`);
            if (playerElement) {
                // Add the player name below the position label
                const playerNameElement = document.createElement('div');
                playerNameElement.textContent = playerName;
                playerNameElement.style.fontSize = "10px";
                if (positionElement.classList.contains("player-rect")) { 
                playerNameElement.style.color = "#fff";
                }
                else {
                  playerNameElement.style.color = "#000";
                }
                playerNameElement.style.marginTop = "200%";
                positionElement.appendChild(playerNameElement);
    
                // Add a faded class to the player in the list to indicate it has been placed
                playerElement.classList.add('faded');
    
                // Update the lineup object
                //lineup[position] = playerId;
                //console.log(`lineup: ${lineup[position]}`);
               
            };
          };
        })

      // Populate tactics
      const tactics = JSON.parse(data.tactics || '{}');
      if (tactics.mentality) {
        document.getElementById('mentality').value= tactics.mentality;
      }
      if (tactics.push_factor) {
        document.getElementById('push_factor').value= tactics.push_factor;
      }
      if (tactics.defense_strategy) {
          document.getElementById('defense_strategy').value= tactics.defense_strategy;
      }
      if (tactics.offense_strategy) {
        document.getElementById('offense_strategy').value= tactics.offense_strategy;
      }

      // Re-validate inputs
      checkAndUpdateSendButton();
  })
  .catch(error => {
      console.error('Error loading lineup and tactics:', error);
  });

  document.querySelectorAll('.player').forEach(card => {
    card.addEventListener('mouseenter', function () {
        const statsDiv = this.querySelector('.player-stats');
        const playerName = this.querySelector('.player-name').innerText;

        // Check if stats already populated
        if (statsDiv.innerHTML.trim() === '') {
            const kicking = this.getAttribute('data-kicking');
            const marking = this.getAttribute('data-marking');
            const stamina = this.getAttribute('data-stamina');
            const speed = this.getAttribute('data-speed');

            statsDiv.innerHTML = `
                <strong>${playerName}</strong>
                <p>Kicking: ${kicking}</p>
                <p>Marking: ${marking}</p>
                <p>Stamina: ${stamina}</p>
                <p>Speed: ${speed}</p>
            `;
        }

        statsDiv.style.display = 'block';
    });

    card.addEventListener('mouseleave', function () {
        const statsDiv = this.querySelector('.player-stats');
        statsDiv.style.display = 'none';
    });
});  

  players.forEach((player) => {
    player.draggable = true;
    player.addEventListener('dragstart', (e) => {
      const statsCard = player.querySelector('.player-stats');
      statsCard.style.display = 'none'; // Hide stats card during drag
  
      e.dataTransfer.setData('player-id', player.dataset.id);
      e.dataTransfer.setData('player-name', player.querySelector('.player-name').textContent);
  
      // Add a subtle drag image if needed
      const dragImage = document.createElement('div');
      dragImage.textContent = player.querySelector('.player-name').textContent;
      dragImage.style.position = 'absolute';
      dragImage.style.color = '#fff';
      dragImage.style.backgroundColor = '#007bff';
      dragImage.style.padding = '5px 10px';
      dragImage.style.borderRadius = '5px';
      document.body.appendChild(dragImage);
      e.dataTransfer.setDragImage(dragImage, 0, 0);
  
      // Cleanup drag image after dragging ends
      player.addEventListener('dragend', () => {
        statsCard.style.display = 'none'; // Ensure it's hidden after drag
        document.body.removeChild(dragImage);
      });
    });
  
    // Prevent floating `player-stats` interference
    player.querySelector('.player-stats').addEventListener('dragstart', (e) => {
      e.stopPropagation(); // Block drag from starting here
    });
  });

  field.addEventListener('dragover', (e) => {
    e.preventDefault(); // Allow dropping
  });
  
    followers.addEventListener('dragover', (e) => {
    e.preventDefault(); // Allow dropping
  });
  
  Interchange.addEventListener('dragover', (e) => {
    e.preventDefault(); // Allow dropping
  });

  field.addEventListener('drop', (e) => {
    e.preventDefault();
    const playerId = e.dataTransfer.getData('player-id');
    const playerName = e.dataTransfer.getData('player-name');
    const playerElement = document.querySelector(`.player[data-id="${playerId}"]`);
	//console.log(playerName);
    // Check if a player is already in this spot
    if (e.target.classList.contains('player-rect') && !e.target.hasAttribute('data-player-id')) {
      // Assign the player to this spot
      //e.target.textContent = playerName;
      e.target.setAttribute('data-player-id', playerId);
      //e.target.style.backgroundColor = playerElement.style.backgroundColor; // Match color
	  
      //rect.style.backgroundColor = "#ffffff"; // Indicate it's filled
	  e.target.style.backgroundImage = "url('../../../static/player/img/guinea.png')";
	  e.target.style.backgroundSize = "contain"; // Ensure the image fits within the rectangle
	  e.target.style.backgroundColor = "fff"; // Clear any previous color (optional)
	  e.target.style.backgroundRepeat = "no-repeat"; // Prevent repeating of the image
	  //e.target.style.opacity = "0.9";
	  e.target.style.backgroundPosition = "center 6px"; // Adjusts the vertical position to "2px" down
	  e.target.textContent = "";
	  
	  	const playerElement = document.querySelector(`.player[data-id="${playerId}"]`);
		if (playerElement) {
		
	  	  // Find the nearest player-rect (drop target)
		//rect.textContent = "FB"; // Keep the position label
		const playerNameElement = document.createElement('div');
		playerNameElement.textContent = playerName;
		playerNameElement.style.fontSize = "10px";
		playerNameElement.style.color = "#fff";
		playerNameElement.style.marginTop = "200%";

		// Append the player name below the position label
		e.target.appendChild(playerNameElement);

    const positionName = e.target.getAttribute("position");

    // Update the lineup object
    lineup[positionName] = playerId;

    // Re-validate inputs
    checkAndUpdateSendButton();

		};
	  
      // Fade the player in the list
      playerElement.classList.add('faded');
    };
  });
  
  followers.addEventListener('drop', (e) => {
    e.preventDefault();
    const playerId = e.dataTransfer.getData('player-id');
    const playerName = e.dataTransfer.getData('player-name');
    const playerElement = document.querySelector(`.player[data-id="${playerId}"]`);

    // Check if a player is already in this spot
    if (e.target.classList.contains('player-rectf') && !e.target.hasAttribute('data-player-id')) {
      // Assign the player to this spot
      //e.target.textContent = playerName;
      e.target.setAttribute('data-player-id', playerId);
      //e.target.style.backgroundColor = playerElement.style.backgroundColor; // Match color
	  
      //rect.style.backgroundColor = "#ffffff"; // Indicate it's filled
	  e.target.style.backgroundImage = "url('../../../static/player/img/guinea.png')";
	  e.target.style.backgroundSize = "contain"; // Ensure the image fits within the rectangle
	  e.target.style.backgroundColor = "fff"; // Clear any previous color (optional)
	  e.target.style.backgroundRepeat = "no-repeat"; // Prevent repeating of the image
	  //e.target.style.opacity = "0.9";
	  e.target.style.backgroundPosition = "center 6px"; // Adjusts the vertical position to "2px" down
	  e.target.textContent = "";
	  
	  	const playerElement = document.querySelector(`.player[data-id="${playerId}"]`);
		if (playerElement) {
		
	  	  // Find the nearest player-rect (drop target)
		//rect.textContent = "FB"; // Keep the position label
		const playerNameElement = document.createElement('div');
		playerNameElement.textContent = playerName;
		playerNameElement.style.fontSize = "10px";
		playerNameElement.style.color = "#000";
		playerNameElement.style.marginTop = "370%";

		// Append the player name below the position label
		e.target.appendChild(playerNameElement);

    const positionName = e.target.getAttribute("position");

    // Update the lineup object
    lineup[positionName] = playerId;

    //console.log(`Linup: ${lineup}`);

    // Re-validate inputs
    checkAndUpdateSendButton();

		};
	  
      // Fade the player in the list
      playerElement.classList.add('faded');
    };
  });
  
    Interchange.addEventListener('drop', (e) => {
    e.preventDefault();
    const playerId = e.dataTransfer.getData('player-id');
    const playerName = e.dataTransfer.getData('player-name');
    const playerElement = document.querySelector(`.player[data-id="${playerId}"]`);

    // Check if a player is already in this spot
    if ((e.target.classList.contains('player-recti') || e.target.classList.contains('player-rects')) && !e.target.hasAttribute('data-player-id')) {

      e.target.setAttribute('data-player-id', playerId);

	  e.target.style.backgroundImage = "url('../../../static/player/img/guinea.png')";
	  e.target.style.backgroundSize = "contain"; // Ensure the image fits within the rectangle
	  e.target.style.backgroundColor = "fff"; // Clear any previous color (optional)
	  e.target.style.backgroundRepeat = "no-repeat"; // Prevent repeating of the image
	  e.target.style.backgroundPosition = "center 6px"; // Adjusts the vertical position to "2px" down
	  e.target.textContent = "";
	  
	  	const playerElement = document.querySelector(`.player[data-id="${playerId}"]`);
		if (playerElement) {
		
	  	  // Find the nearest player-rect (drop target)
		//rect.textContent = "FB"; // Keep the position label
		const playerNameElement = document.createElement('div');
		playerNameElement.textContent = playerName;
		playerNameElement.style.fontSize = "10px";
		playerNameElement.style.color = "#000";
		playerNameElement.style.marginTop = "280%";

		// Append the player name below the position label
		e.target.appendChild(playerNameElement);

    const positionName = e.target.getAttribute("position");

    // Update the lineup object
    lineup[positionName] = playerId;

    //console.log(`Linup: ${lineup}`);

    // Re-validate inputs
    checkAndUpdateSendButton();

		};
	  
      // Fade the player in the list
      playerElement.classList.add('faded');
    };
  });

  // Add right-click or double-click functionality
  document.addEventListener('contextmenu', (e) => {
    e.preventDefault(); // Disable default context menu

          //Array.from(document.querySelectorAll('[class^="player-rect"]'))
    if (e.target.classList.contains('player-rect') || e.target.classList.contains('player-rectf') || e.target.classList.contains('player-recti') || e.target.classList.contains('player-rects') && e.target.hasAttribute('data-player-id')) {
      // Remove player from the field
      console.log(`e.target: ${e.target.hasAttribute('data-player-id')}`);
      const playerId = e.target.getAttribute('data-player-id');
      const playerElement = document.querySelector(`.player[data-id="${playerId}"]`);
	  const originalState = e.target.getAttribute('position');
      e.target.textContent = originalState;
      e.target.removeAttribute('data-player-id');
	  e.target.style.backgroundImage = "";
	  e.target.style.backgroundSize = ""; // Ensure the image fits within the rectangle
	  e.target.style.backgroundColor = "rgba(255, 255, 255, 0.3)"; // Clear any previous color (optional)
	  e.target.style.backgroundRepeat = ""; // Prevent repeating of the image
	  //e.target.style.opacity = "0.3";

    const positionName = e.target.getAttribute("position");
    lineup[positionName] = "";

      // Restore the player in the list
      playerElement.classList.remove('faded');
    }
	
	/*if (
	(e.target.classList.contains('player-rectf') || e.target.classList.contains('player-recti') | e.target.classList.contains('player-rects')) &&
	e.target.hasAttribute('data-player-id')) {
      console.log(`e.target: ${e.target.hasAttribute('data-player-id')}`);
      // Remove player from the field
      const playerId = e.target.getAttribute('data-player-id');
      const playerElement = document.querySelector(`.player[data-id="${playerId}"]`);
	  const originalState = e.target.getAttribute('position');
      e.target.textContent = originalState;
      e.target.removeAttribute('data-player-id');
	  e.target.style.backgroundImage = "";
	  e.target.style.backgroundSize = ""; // Ensure the image fits within the rectangle
	  e.target.style.backgroundColor = "rgba(243, 244, 246, 0.8)"; // Clear any previous color (optional)
	  e.target.style.backgroundRepeat = ""; // Prevent repeating of the image
	  //e.target.style.opacity = "0.3";

    const positionName = e.target.getAttribute("position");
    lineup[positionName] = "";

      // Restore the player in the list
      playerElement.classList.remove('faded');
    } */
  });
  


  // Optional: Add double-click functionality

  //Array.from(document.querySelectorAll('[class^="player-rect"]'))
  field.addEventListener('dblclick', (e) => {
    if (e.target.classList.contains('player-rect') && e.target.hasAttribute('data-player-id')) {
      // Remove player from the field (same logic as above)
      const playerId = e.target.getAttribute('data-player-id');
      const playerElement = document.querySelector(`.player[data-id="${playerId}"]`);
	  const originalState = e.target.getAttribute('position');
      e.target.textContent = originalState;
      e.target.removeAttribute('data-player-id');
	  e.target.style.backgroundImage = "";
	  e.target.style.backgroundSize = ""; // Ensure the image fits within the rectangle
	  e.target.style.backgroundColor = "rgba(255, 255, 255, 0.3)"; // Clear any previous color (optional)
	  e.target.style.backgroundRepeat = ""; // Prevent repeating of the image
	  //e.target.style.opacity = "0.3";

      // Restore the player in the list
      playerElement.classList.remove('faded');
      
      const positionName = e.target.getAttribute("position");
      lineup[positionName] = "";
      console.log(`e.target: ${e.target.hasAttribute('data-player-id')}`);
    }

  });


  document.getElementById('sort-rules').addEventListener('change', (e) => {
    const sortRule = e.target.value; // Get the selected sorting rule
    const playersList = document.querySelector('.players-list'); // The container holding player cards
    const playerCards = Array.from(document.querySelectorAll('.player')); // All player elements
  
    // Define sorting criteria for each rule using stat IDs
    const criteria = {
      forward: ['Goal_Kicking', 'Marking', 'Speed'],
      defender: ['Tackling', 'Marking', 'Strength'],
      midfielder: ['Tactical', 'Mental', 'Stamina'],
      ruck:  ['Height', 'Tactical', 'Consistency'],
    };
  
    // Sort player cards based on the selected rule
    playerCards.sort((a, b) => {
      const aScore = calculateScore(a, criteria[sortRule]);
      const bScore = calculateScore(b, criteria[sortRule]);
      return bScore - aScore; // Descending order
    });
  
    // Reorder the player cards in the DOM
    playersList.innerHTML = ''; // Clear the container
    playerCards.forEach(card => playersList.appendChild(card));
  });
  
  // Function to calculate a player's score based on criteria
  function calculateScore(playerCard, criteria) {
    return criteria.reduce((score, stat) => {
      const playerId = playerCard.dataset.id; // Get the player's ID
      const statElement = document.getElementById(`${stat}-${playerId}`); // Find the stat element by ID
      if (statElement) {
        const statValue = parseInt(statElement.textContent.split(': ')[1], 10); // Extract stat value
        return score + statValue;
      }
      return score;
    }, 0);
  }

  document.querySelectorAll('.player').forEach((player) => {
    const statsCard = player.querySelector('.player-stats');

    // Show stats card on hover
    player.addEventListener('mouseenter', (e) => {
      statsCard.style.display = 'block';
      
      // Get player card and stats card dimensions
      const playerRect = player.getBoundingClientRect();
      const statsRect = statsCard.getBoundingClientRect();
      const viewportHeight = window.innerHeight;
  
      // Adjust position if the stats card would overflow the bottom
      if (playerRect.bottom + statsRect.height > viewportHeight) {
        statsCard.style.top = `${viewportHeight - statsRect.height - 10}px`; // Stay on screen
      } else {
        statsCard.style.top = `${playerRect.top}px`; // Default position
      }
  
      statsCard.style.left = `${playerRect.left - statsRect.width - 10}px`; // Display to the left
    });
  
    // Hide stats card on mouse leave
    player.addEventListener('mouseleave', () => {
      statsCard.style.display = 'none';
      statsCard.style.top = ''; // Reset to default
      statsCard.style.left = ''; // Reset to default
    });
  });

  document.querySelectorAll('.tab-btn').forEach((button) => {
    button.addEventListener('click', () => {
      const targetTab = button.getAttribute('data-tab');
      
      // Check if the targetTab exists in the DOM
      const targetContent = document.getElementById(targetTab);
      if (!targetContent) {
        //console.error(`No content found for tab: ${targetTab}`);
        return;
      }
      
      // Remove 'active' class from all buttons and content
      document.querySelectorAll('.tab-btn').forEach((btn) => btn.classList.remove('active'));
      document.querySelectorAll('.tab-content').forEach((content) => content.classList.remove('active'));
      populateReview();
      // Add 'active' class to the clicked button and corresponding content
      button.classList.add('active');
      targetContent.classList.add('active');
    });
  });


	// Validate lineup and tactics
	function validateInputs() {
    //let isLineupComplete = Object.keys(lineup).length === 18;
		const isFieldLineupComplete = requiredPositions.every(pos => lineup[pos]);
    const isFollowersLineupComplete = requiredFollowersPositions.every(pos => lineup[pos]);
    const isIntLineupComplete = requiredIntPositions.every(pos => lineup[pos]);
    let areTacticsComplete = (
      document.getElementById('mentality').value !== "" &&
      document.getElementById('push_factor').value !== "" &&
      document.getElementById('defense_strategy').value !== "" &&
      document.getElementById('offense_strategy').value !== ""
  );

    //console.log(`lineup length: ${isLineupComplete}`);

		return isFieldLineupComplete && isFollowersLineupComplete && isIntLineupComplete && areTacticsComplete;
	}

	// Update the "Send Game Plan" button based on validation
	function checkAndUpdateSendButton() {
		const sendGamePlanButton = document.getElementById("send-game-plan");
		sendGamePlanButton.disabled = !validateInputs();
    //console.log(`Send Game Plan Btn: ${sendGamePlanButton}`);
	}

  sendGamePlanButton.addEventListener("click", (e) => {
		if (!validateInputs()) {
			e.preventDefault();
			navigateToTab("review");
			alert("Please complete all information before sending the game plan.");
		}
	});

	// Attach event listener for tactics form
	document.getElementById("tactics").addEventListener("input", (e) => {
		tactics[e.target.name] = e.target.value;
		checkAndUpdateSendButton();
	});


	// Function to populate the review tab
  function populateReview() {
    console.log(`PopulateReview called`);
    reviewContent.innerHTML = ""; // Clear old content

    // Validate lineup completeness
    const isFieldLineupComplete = requiredPositions.every(pos => lineup[pos]);
    const isFollowersLineupComplete = requiredFollowersPositions.every(pos => lineup[pos]);
    const isIntLineupComplete = requiredIntPositions.every(pos => lineup[pos]);
    

    //console.log(`Field: ${isFieldLineupComplete}`);
    
    // Validate tactics completeness
    const areTacticsComplete = (
        document.getElementById('mentality').value !== "" &&
        document.getElementById('push_factor').value !== "" &&
        document.getElementById('defense_strategy').value !== "" &&
        document.getElementById('offense_strategy').value !== ""
    );


    // Add lineup review
    let lineupReview = `
        <div class="review-card">
            <h3>Lineup Review</h3>
    `;
    if (!isFieldLineupComplete) {
        lineupReview += `
            <p class="error-text">Lineup is incomplete. Missing positions:</p>
            <ul class="missing-positions">
        `;
        for (let position of requiredPositions) {
          if (!lineup[position]) {
              const fullName = positionFullNames[position];
              lineupReview += `<li><span class="position-name">${fullName} (${position})</span>: No player assigned</li>`;
          }
      }
        lineupReview += "</ul>";
    } else {
        lineupReview += `
            <p class="success-text">Lineup is complete!</p>
            <ul class="lineup-positions">
        `;
        requiredPositions.forEach(position => {
          const playerId = lineup[position]; // Get the player ID for this position
          let playerName = "Not Assigned"; // Default if no match is found
      
          if (playerId) {
              // Look through the DOM for the player with the matching ID
              const playerElement = Array.from(document.querySelectorAll('.player'))
                  .find(player => player.dataset.id === String(playerId));
      
              if (playerElement) {
                  playerName = playerElement.querySelector('.player-name').textContent;
              }
          }
      
          //console.log(`Position: ${position}, PlayerID: ${playerId}, Player Name: ${playerName}`);
  
          lineupReview += `
              <li class="lineup-position">
                  <span class="position-name">${position}:</span>
                  <span class="player-name">${playerName}</span>
              </li>
          `;
      });
        lineupReview += "</ul>";
    }
    lineupReview += `</div>`;

   // Add Followers review
   let FollowersReview = `
   <div class="review-card">
       <h3>Followers Review</h3>
`;
//console.log(`Followers: ${isFollowersLineupComplete}`);
if (!isFollowersLineupComplete) {
  FollowersReview += `
       <p class="error-text">Followers is incomplete. Missing positions:</p>
       <ul class="missing-positions">
   `;
   for (let position of requiredFollowersPositions) {
     if (!lineup[position]) {
         const fullName = positionFullNames[position];
         FollowersReview += `<li><span class="position-name">${fullName}</span>: No player assigned</li>`;
     }
 }
 FollowersReview += "</ul>";
} else {
  FollowersReview += `
       <p class="success-text">Followers is complete!</p>
       <ul class="followers-positions">
   `;
   requiredFollowersPositions.forEach(position => {
    const playerId = lineup[position]; // Get the player ID for this position
    let playerName = "Not Assigned"; // Default if no match is found

    if (playerId) {
        // Look through the DOM for the player with the matching ID
        const playerElement = Array.from(document.querySelectorAll('.player'))
            .find(player => player.dataset.id === String(playerId));

        if (playerElement) {
            playerName = playerElement.querySelector('.player-name').textContent;
        }
    }

    //console.log(`Position: ${position}, PlayerID: ${playerId}, Player Name: ${playerName}`);

          FollowersReview += `
          <li class="followers-position">
              <span class="position-name">${position}:</span>
              <span class="player-name">${playerName}</span>
          </li>
      `;
      });

   FollowersReview += "</ul>";
}
FollowersReview += `</div>`;

   // Add Interchange review
   let InterchangeReview = `
   <div class="review-card">
       <h3>Interchange Review</h3>
`;
//console.log(`Interchange: ${isIntLineupComplete}`);
if (!isIntLineupComplete) {
  InterchangeReview += `
       <p class="error-text">Interchange is incomplete. Missing positions:</p>
       <ul class="missing-positions">
   `;
   for (let position of requiredIntPositions) {
     if (!lineup[position]) {
         const fullName = positionFullNames[position];
         InterchangeReview += `<li><span class="position-name">${fullName}</span>: No player assigned</li>`;
     }
 }
 InterchangeReview += "</ul>";
} else {
  InterchangeReview += `
       <p class="success-text">Interchange is complete!</p>
       <ul class="interchange-positions">
   `;
   requiredIntPositions.forEach(position => {
    const playerId = lineup[position]; // Get the player ID for this position
    let playerName = "Not Assigned"; // Default if no match is found

    if (playerId) {
        // Look through the DOM for the player with the matching ID
        const playerElement = Array.from(document.querySelectorAll('.player'))
            .find(player => player.dataset.id === String(playerId));

        if (playerElement) {
            playerName = playerElement.querySelector('.player-name').textContent;
        }
    }

    //console.log(`Position: ${position}, PlayerID: ${playerId}, Player Name: ${playerName}`);

          InterchangeReview += `
           <li class="interchange-position">
               <span class="position-name">${position}:</span>
               <span class="player-name">${playerName}</span>
           </li>
      `;
      });
   InterchangeReview += "</ul>";
}
InterchangeReview += `</div>`;

    // Add tactics review
    let tacticsReview = `
        <div class="review-card">
            <h3>Tactics Review</h3>
    `;
    if (!areTacticsComplete) {
      tacticsReview += `<p class="error-text">Tactics are incomplete. Please fill all fields.</p>`;
  } else {
      const aggressionLabel = tactics.mentality === "attacking" ? "Attack aggression level" : "Defense aggression level";

      const pushFactorLabels = {
        "push_factor0": "Low",
        "push_factor1": "Moderate",
        "push_factor2": "High"
    };
    const pushFactorText = pushFactorLabels[document.getElementById('push_factor').value] || "Not Set";

    const defense_strategyLabels = {
      "zone_mark": "Zone Mark",
      "man_mark": "Man Mark"
  };
  const defense_strategyText = defense_strategyLabels[document.getElementById('defense_strategy').value];

  const offense_strategyLabels = {
    "direct": "Direct",
    "stay_wide": "Stay Wide"
};
const offense_strategyText =offense_strategyLabels[document.getElementById('offense_strategy').value];
  
      tacticsReview += `
          <p class="success-text">Tactics are set!</p>
          <ul class="tactics-details">
              <li><strong>Mentality:</strong> ${document.getElementById('mentality').value.charAt(0).toUpperCase() + document.getElementById('mentality').value.slice(1)}</li>
              <li><strong>${aggressionLabel}:</strong> ${pushFactorText}</li>
              <li><strong>Defense Strategy:</strong> ${defense_strategyText}</li>
              <li><strong>Offense Strategy:</strong> ${offense_strategyText}</li>
          </ul>
      `;
  }
    tacticsReview += `</div>`;

    // Append reviews
    reviewContent.innerHTML = lineupReview + FollowersReview + InterchangeReview + tacticsReview;

    // Display missing info warning if either validation fails
    missingInfo.style.display = isFieldLineupComplete && areTacticsComplete ? "none" : "block";
};



document.getElementById('send-game-plan').addEventListener('click', async () => {
  // Gather lineup data
  const lineup = {};
  document.querySelectorAll('[class^="player-rect"]').forEach(positionElement => {
      const position = positionElement.getAttribute('position'); // Retrieve the position attribute
      const playerId = positionElement.getAttribute('data-player-id'); // Retrieve the player-id attribute (if present)
      //console.log(position);
      //console.log(playerId);
      if (position && playerId) {
          lineup[position] = playerId; // Map position to player ID
      }
  });
  
  //console.log(lineup); // Debugging to confirm the lineup is built correctly

  // Gather tactics data
  const tactics = {
      mentality: document.getElementById('mentality').value,
      push_factor: document.getElementById('push_factor').value,
      defense_strategy: document.getElementById('defense_strategy').value,
      offense_strategy: document.getElementById('offense_strategy').value
  };

  // Get match ID and team information from data attributes or context
  const matchId = document.getElementById('match-id').value; // Ensure this is included in your template as a hidden input
  const teamId = document.getElementById('team-id').value;   // Also include team ID in the template context

  //console.log(`matchId: ${matchId}`);
  //console.log(`teamId: ${teamId}`);

  try {
      const response = await fetch(`../matches/set_orders/set_lineup_and_tactics/${matchId}`, {
          method: 'POST',
          headers: {
              'Content-Type': 'application/json',
              'X-CSRFToken': getCookie('csrftoken') // Ensure CSRF token is included
          },
          body: JSON.stringify({
              team: teamId, // Pass the team ID derived from the context
              lineup: JSON.stringify(lineup), // Convert to string as expected by the backend
              tactics: JSON.stringify(tactics) // Convert to string as expected by the backend
          })
      });

      if (response.ok) {
          const result = await response.json();
          if (result.success) {
              alert('Game plan submitted successfully!');
              // Optionally update the UI or redirect
          } else {
              alert(`Error: ${result.error}`);
          }
      } else {
          const error = await response.json();
          alert(`Error: ${error.error}`);
      }
  } catch (error) {
      console.error('Submission failed:', error);
      alert('Submission failed. Please try again.');
  }
});

function getCookie(name) {
  let cookieValue = null;
  if (document.cookie && document.cookie !== '') {
      const cookies = document.cookie.split(';');
      for (let i = 0; i < cookies.length; i++) {
          const cookie = cookies[i].trim();
          if (cookie.startsWith(name + '=')) {
              cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
              break;
          }
      }
  }
  return cookieValue;
};


}

// Check if DOM is already loaded
if (document.readyState === "loading") {
    // DOM is still loading, attach the event listener
    document.addEventListener("DOMContentLoaded", () => {
        //console.log("DOMContentLoaded triggered in orders.js");
        initializeOrdersScript();
    });
} else {
    // DOM is already loaded
    //console.log("DOM already loaded, executing directly");
    initializeOrdersScript();
}