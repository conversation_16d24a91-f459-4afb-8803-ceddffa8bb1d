from django.db import models
from teams.models import Team  # Adjust according to your Team model location
from leagues.models import League

class Ladder(models.Model):
    league = models.ForeignKey(League, on_delete=models.CASCADE, related_name="ladders", null=True, blank=True)
    team = models.OneToOneField(Team, on_delete=models.CASCADE, related_name='ladder')
    division = models.IntegerField()  # Use the division number to segment the ladder standings
    games_played = models.IntegerField(default=0)
    wins = models.IntegerField(default=0)
    losses = models.IntegerField(default=0)
    draws = models.IntegerField(default=0)
    points = models.IntegerField(default=0)  # 4 points for a win, 2 for a draw
    points_for = models.IntegerField(default=0)  # Cumulative points scored by the team
    points_against = models.IntegerField(default=0)
    percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0.00)  # (Points For / Points Against) * 100

    def update_percentage(self):
        if self.points_against > 0:
            self.percentage = (self.points_for / self.points_against) * 100
        else:
            self.percentage = 100  # Default to max if no points conceded


    def update_ladder(self, result, points_for, points_against):
        self.games_played += 1
        self.points_for += points_for
        self.points_against += points_against
        print(f"points for {self.points_for}")
        print(f"points against {self.points_against}")

        if result == "win":
            self.wins += 1
            self.points += 4
        elif result == "draw":
            self.draws += 1
            self.points += 2
        else:
            self.losses += 1

        self.update_percentage()
        self.save()
