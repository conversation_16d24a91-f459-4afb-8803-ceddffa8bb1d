# teams/signals.py
from django.db.models.signals import post_save
from django.dispatch import receiver
from .models import Team
from ladder.models import Ladder

@receiver(post_save, sender=Team)
def create_team_ladder(sender, instance, created, **kwargs):
    print("Hit Teams signals")
    if created:
        print(f"Team created: {instance.name}")
        if instance.league:
            print(f"Hit created {instance.league}")
            Ladder.objects.get_or_create(
                team=instance,
                division=instance.league.division_number,
            )
        else:
            print("Warning: League is None on the created Team instance")
