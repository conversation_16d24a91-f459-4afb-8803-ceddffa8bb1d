{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Matches</title>
    <link rel="stylesheet" href="{% static 'teams/css/matches.css' %}?<?php echo date('l jS \of F Y h:i:s A'); ?>">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script src="{% static 'teams/js/matches.js' %}"></script>
</head>
<body>
    <div class="matches-container">
	<div class="matches-header">
    <h1>Matches</h1>
	</div>

    {% for match in matches %}
    <a href="{% url 'set_orders' match.id %}" class="dynamic-link">
    <div class="match-card">
        <div class="match-date">
            <span class="match-day">{{ match.date|date:"D" }}</span>
            <span class="match-full-date">{{ match.date|date:"d M Y" }}</span>
        </div>

        <div class="match-info">
            <div class="team">
                <!-- <img src="{{ match.home_team.logo_url }}" class="team-logo"> -->
				<img src="../../static/teams/images/Team_logos/AFL_Manager100.png" class="team-logo">
                <span class="team-name">{{ match.home_team.name }}</span>
            </div>
            <span class="vs">vs</span>
            <div class="team">
                <!-- <img src="{{ match.home_team.logo_url }}" class="team-logo"> -->
				<img src="../../static/teams/images/Team_logos/AFL_Manager100.png" class="team-logo">
                <span class="team-name">{{ match.away_team.name }}</span>
            </div>
        </div>

        <div class="match-extra">
			<!--<a href="{% url 'set_orders' match.id %}" class="btn btn-primary">Set Orders</a>-->
            <span class="match-location"><i class="fas fa-map-marker-alt"></i> {{ match.location }}</span>
            <span class="match-score">
                <strong>{{ match.home_score }}</strong> - <strong>{{ match.away_score }}</strong>
            </span>
        </div>
        <div class="orders">
        <span>
            {% if match.orders_submitted %}
              <span class="submitted">Orders Submitted</span>
            {% else %}
              <span class="not-submitted">Pending</span>
            {% endif %}
        </span>
    </div>
    </div>
</a>
    {% endfor %}
	</div>
</body>
</html>