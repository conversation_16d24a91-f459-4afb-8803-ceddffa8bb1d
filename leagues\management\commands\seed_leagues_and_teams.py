from django.core.management.base import BaseCommand
from leagues.models import League
from teams.models import Team
from teams.signals import create_team_ladder
from users.models import CustomUser
from faker import Faker
import sys
import random

class Command(BaseCommand):
    help = "Seed the database with initial leagues, divisions, and bot teams"

    def handle(self, *args, **kwargs):
        fake = Faker()
        bot_name_num = 0
        # Define the league structure for tiers and divisions
        league_structure = {
            1: {"divisions": 1, "name": "AFL Premier League"},
            2: {"divisions": 2, "name": "AFL Championship"},
            3: {"divisions": 4, "name": "AFL League One"},
            4: {"divisions": 8, "name": "AFL League Two"},
            5: {"divisions": 16, "name": "AFL League Three"},
            6: {"divisions": 32, "name": "AFL League Four"}
        }

        for tier, details in league_structure.items():
            for division_number in range(1, details["divisions"] + 1):
                league_name = f"{details['name']}"
                league, league_created = League.objects.get_or_create(
                    name=league_name,
                    tier=tier,
                    division_number=division_number,
                    country="Australia"
                )
                
                if league_created:
                    self.stdout.write(self.style.SUCCESS(f"Created league: {league_name}"))
                else:
                    self.stdout.write(self.style.SUCCESS(f"Using existing league: {league_name}"))

                
                # Generate bot teams with realistic names
                for bot_num in range(1, 11):  # 10 bot teams per division
                    city = fake.city()
                    mascot = random.choice([
                    "Lions", "Tigers", "Hawks", "Eagles", "Sharks", "Wolves", "Panthers", "Dragons",
                    "Falcons", "Dogs", "Crocodiles", "Rhinos", "Bears", "Spartans", "Pirates", "Knights",
                    "Vikings", "Gladiators", "Warriors", "Raiders", "Pumas", "Raptors", "Scorpions", "Cobras",
                    "Thunder", "Pies", "Titans", "Giants", "Phoenix", "Stallions", "Wildcats", "Grizzlies",
                    "Cats", "Owls", "Mavericks", "Dons", "Jets", "Blues", "Stingers", "Bees",
                    "Saints", "Crows", "Foxes", "Swans", "Dockers", "Wolverines", "Storm", "Bobcats"
                    ])
                    team_name = f"{city} {mascot}"
                    print(f" {team_name}")
                    
                    bot_user, created = CustomUser.objects.get_or_create(
                        username="Bot "+str(bot_name_num),
                        defaults={'team_name': team_name, 'is_bot': True}
                    )
                    
                    print("Hit created for bot user:", bot_user.username)
                
                    # Link bot user to league with a team
                    bot_team, created = Team.objects.update_or_create(
                        user=bot_user,
                        defaults={'name': team_name, 'league': league}
                    )
                    bot_name_num += 1
                    bot_name_num = bot_name_num + 1
                    #if bot_team.league != league:
                    #    print(f"No Bot team")
                    #    bot_team.league = league
                    #    bot_team.save()
                    create_team_ladder(sender=Team, instance=bot_team, created=True)
 
                    
                    self.stdout.write(self.style.SUCCESS(f"Created bot team: {team_name} in {league.name}"))
                    
                    #sys.exit()  
        
