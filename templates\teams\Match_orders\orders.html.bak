
{% load static %}
{% block content %}
<div class="match-orders-container">
    <h1>Match Orders</h1>

    <div class="field-container">
        <h2>AFL Field</h2>
        <div id="field" class="field">
            <!-- AFL field positions -->
            <div class="position" data-position="full_forward">Full Forward</div>
            <div class="position" data-position="ruck">Ruck</div>
            <div class="position" data-position="centre">Centre</div>
            <!-- Add other positions -->
        </div>
    </div>

    <div class="players-container">
        <h2>Players</h2>
        <div id="players" class="players">
            <!-- Dynamically rendered players from the backend -->
            {% for player in players %}
            <div class="player" data-player-id="{{ player.id }}">{{ player.name }}</div>
            {% endfor %}
        </div>
    </div>

    <div class="tactics-container">
        <h2>Tactics</h2>
        <form id="tacticsForm">
            <label for="mentality">Mentality:</label>
            <select id="mentality" name="mentality">
                <option value="attacking">Attacking</option>
                <option value="defensive">Defensive</option>
            </select>

            <label for="push_factor">Push Factor:</label>
            <input type="number" id="push_factor" name="push_factor" min="1" max="3" value="2">

            <label for="defense_strategy">Defense Strategy:</label>
            <select id="defense_strategy" name="defense_strategy">
                <option value="zone_mark">Zone Mark</option>
                <option value="man_mark">Man Mark</option>
            </select>

            <label for="offense_strategy">Offense Strategy:</label>
            <select id="offense_strategy" name="offense_strategy">
                <option value="direct">Direct</option>
                <option value="stay_wide">Stay Wide</option>
            </select>

            <button type="button" id="submitTactics">Submit</button>
        </form>
    </div>
</div>

<!-- Include CSS and JavaScript -->
<link rel="stylesheet" href="{% static 'teams/css/match_orders/orders.css' %}?<?php echo date('l jS \of F Y h:i:s A'); ?>">
<script src="{% static 'teams/js/match_orders/orders.js' %}"></script>
{% endblock %}
