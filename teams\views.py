from django.shortcuts import render, get_object_or_404
from django.contrib.auth.decorators import login_required
from .models import Team, Player, PlayerStatsLvls
from ladder.models import Ladder
from leagues.models import League
from matches.models import Match
from django.db.models import Q
from django.http import JsonResponse

@login_required
def team_view(request, team_id):
    print("team_view called")
    team = get_object_or_404(Team, id=team_id, user=request.user)
    players = Player.objects.filter(team=team)
    return render(request, 'teams/team.html', {'team': team, 'players': players})
    
@login_required
def dashboard_view(request):
    print("dashboard_view called")
    return render(request, 'teams/dashboard.html')  # New dashboard rendering
    
@login_required
def club_view(request):
    return render(request, 'teams/club.html')    
    
@login_required
def stadium_view(request):
    return render(request, 'teams/stadium.html')

@login_required
def staff_view(request):
    return render(request, 'teams/staff.html')

@login_required
def supporters_view(request):
    return render(request, 'teams/supporters.html')

@login_required
def finances_view(request):
    return render(request, 'teams/finances.html')

@login_required
def transfers_view(request):
    return render(request, 'teams/transfers.html')
     
@login_required
def league_view(request, team_id):
    team = get_object_or_404(Team, id=team_id, user=request.user)
    print(f"team league {team.league_id}")
    ladder = Ladder.objects.filter(league_id=team.league_id).order_by('-points', 'games_played', '-percentage')
    print(f"Ladder {ladder}")
    league = get_object_or_404(League, id=team.league_id)
    print(f"league {league}")
    return render(request, 'leagues/ladder.html', {'ladder': ladder, 'league': league})
    
@login_required
def matches_view(request, team_id):
    team = get_object_or_404(Team, id=team_id, user=request.user)
    print(f"Team ID: {team.id}")
    
    matches = Match.objects.filter(
        Q(home_team_id=team.id) | Q(away_team_id=team.id)
    ).order_by('-date')

    for match in matches:
        match.orders_submitted = (
            match.home_team_lineup if match.home_team_id == team.id else match.away_team_lineup
        )
    
    print(f"Matches: {matches}")
    return render(request, 'teams/matches.html', {'matches': matches, 'team': team})

#@login_required
#def set_orders_view(request, match_id):
    match = get_object_or_404(Match, id=match_id)
    team = get_object_or_404(Team, user=request.user)
    if team != match.home_team and team != match.away_team:
        return render(request, 'teams/access_denied.html')  # Or raise an error
        
    players = Player.objects.filter(team=team)  # Get the players for the team
    player_stats_lvls = PlayerStatsLvls.objects.all().first()
    
    return render(request, 'teams/match_orders/orders.html', {'match': match, 'team': team, 'players': players, 'player_stats_lvls': player_stats_lvls})

@login_required    
def senior_team_view(request, team_id):
    team = get_object_or_404(Team, id=team_id, user=request.user)
    players = Player.objects.filter(team=team)
    #print({team})
    player_stats_lvls = PlayerStatsLvls.objects.all().first()  # Adjust this if necessary for your data structure
    #print(player_stats_lvls.level_2)
    return render(request, 'teams/senior_team.html',  {'team': team, 'players': players, 'player_stats_lvls': player_stats_lvls})
      