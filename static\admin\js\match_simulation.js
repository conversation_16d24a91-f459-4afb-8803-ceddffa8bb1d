document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM fully loaded and parsed');

    const team1Select = document.getElementById('id_team1');
    const team2Select = document.getElementById('id_team2');
    const team1Positions = {
        Ruck: document.getElementById('id_team1_ruck'),
        Rover: document.getElementById('id_team1_rover'),
        RuckRover: document.getElementById('id_team1_ruckrover'),
        LB: document.getElementById('id_team1_LBackPocket'),
        RB: document.getElementById('id_team1_RBackPocket'),
        FB: document.getElementById('id_team1_fullback'),
        LHB: document.getElementById('id_team1_Lhalfback'),
        CHB: document.getElementById('id_team1_Chalfback'),
        RHB: document.getElementById('id_team1_Rhalfback'),
        LWing: document.getElementById('id_team1_Lwing'),
        Centre: document.getElementById('id_team1_centre'),
        RWing: document.getElementById('id_team1_Rwing'),
        LHF: document.getElementById('id_team1_Lhalfforward'),
        CHF: document.getElementById('id_team1_halfforward'),
        RHF: document.getElementById('id_team1_Rhalfforward'),
        LF: document.getElementById('id_team1_Lforwardpocket'),
        FF: document.getElementById('id_team1_fullforward'),
        RF: document.getElementById('id_team1_Rforwardpocket')
    };

    const team2Positions = {
        Ruck: document.getElementById('id_team2_ruck'),
        Rover: document.getElementById('id_team2_rover'),
        RuckRover: document.getElementById('id_team2_ruckrover'),
        LB: document.getElementById('id_team2_LBackPocket'),
        RB: document.getElementById('id_team2_RBackPocket'),
        FB: document.getElementById('id_team2_fullback'),
        LHB: document.getElementById('id_team2_Lhalfback'),
        CHB: document.getElementById('id_team2_Chalfback'),
        RHB: document.getElementById('id_team2_Rhalfback'),
        LWing: document.getElementById('id_team2_Lwing'),
        Centre: document.getElementById('id_team2_centre'),
        RWing: document.getElementById('id_team2_Rwing'),
        LHF: document.getElementById('id_team2_Lhalfforward'),
        CHF: document.getElementById('id_team2_halfforward'),
        RHF: document.getElementById('id_team2_Rhalfforward'),
        LF: document.getElementById('id_team2_Lforwardpocket'),
        FF: document.getElementById('id_team2_fullforward'),
        RF: document.getElementById('id_team2_Rforwardpocket')
    };

    function fetchPlayers(teamId, positions) {
        if (teamId) {
            fetch(`/admin/ajax/load-players/?team_id=${teamId}`)
                .then(response => response.json())
                .then(players => {
                    Object.values(positions).forEach(select => {
                        select.options.length = 0;
                        players.forEach(player => {
                            const option = document.createElement('option');
                            option.value = player.id;
                            option.textContent = player.name;
                            select.appendChild(option);
                        });
                    });
                })
                .catch(error => console.error('Error fetching players:', error));
        }
    }

    team1Select.addEventListener('change', function() {
        fetchPlayers(this.value, team1Positions);
    });

    team2Select.addEventListener('change', function() {
        fetchPlayers(this.value, team2Positions);
    });
});
