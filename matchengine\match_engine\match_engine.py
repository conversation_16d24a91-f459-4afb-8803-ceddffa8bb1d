import random
import math
import time
import asyncio
import sys
from typing import Dict, List, Tuple, Optional, Any, Generator

DEFAULT_GROUND_CONFIG = {
    'length': 160,  # Length in meters
    'width': 120,   # Width in meters
}

class WeatherSystem:
    """Handles weather conditions and their effects on gameplay"""
    def __init__(self, condition: str = 'clear'):
        self.conditions = {
            'clear': {'handling': 1.0, 'kicking': 1.0, 'visibility': 1.0},
            'light_rain': {'handling': 0.85, 'kicking': 0.9, 'visibility': 0.95},
            'heavy_rain': {'handling': 0.7, 'kicking': 0.8, 'visibility': 0.8},
            'windy': {'handling': 0.95, 'kicking': 0.75, 'visibility': 1.0},
            'wet_ground': {'handling': 0.8, 'kicking': 0.85, 'visibility': 1.0}
        }
        self.condition = condition
        self.wind_direction = random.randint(0, 359)  # degrees
        self.wind_strength = random.uniform(0, 1)  # 0 to 1
        
    def get_modifiers(self) -> Dict[str, float]:
        """Get current weather modifiers"""
        mods = self.conditions[self.condition].copy()
        mods['wind_direction'] = self.wind_direction
        mods['wind_strength'] = self.wind_strength
        return mods
        
    def update(self) -> None:
        """Update weather conditions"""
        # Small chance of weather change each quarter
        if random.random() < 0.1:
            self.condition = random.choice(list(self.conditions.keys()))
        # Wind can shift slightly
        self.wind_direction = (self.wind_direction + random.randint(-20, 20)) % 360
        self.wind_strength = max(0, min(1, self.wind_strength + random.uniform(-0.1, 0.1)))

class Ground:
    """Represents the AFL ground and its zones"""
    def __init__(self, config: Dict[str, Any]):
        self.length = config.get('length', 160)  # Length in meters
        self.width = config.get('width', 120)   # Width in meters
        self.center_circle_radius = 5  # Center circle radius in meters
        self.fifty_arc_radius = 50    # 50m arc radius
        
        # Define zones for tactical positioning
        self.zones = {
            'forward_50': {'x': (110, 160), 'y': (0, 120)},
            'forward_flank': {'x': (80, 110), 'y': (0, 120)},
            'center': {'x': (60, 100), 'y': (0, 120)},
            'back_flank': {'x': (50, 80), 'y': (0, 120)},
            'back_50': {'x': (0, 50), 'y': (0, 120)}
        }
        
    def get_zone(self, position: Tuple[int, int]) -> str:
        """Get zone name for a position"""
        x, y = position
        for zone_name, zone in self.zones.items():
            if zone['x'][0] <= x <= zone['x'][1] and zone['y'][0] <= y <= zone['y'][1]:
                return zone_name
        return 'out_of_bounds'
        
    def is_in_bounds(self, position: Tuple[int, int]) -> bool:
        """Check if position is in bounds"""
        x, y = position
        return 0 <= x <= self.length and 0 <= y <= self.width
        
    def is_in_fifty(self, position: Tuple[int, int], end: str) -> bool:
        """Check if position is inside 50m arc"""
        x, y = position
        if end == 'home':
            goal_x, goal_y = (self.length, self.width/2)
        else:
            goal_x, goal_y = (0, self.width/2)
            
        distance = math.sqrt((x - goal_x)**2 + (y - goal_y)**2)
        return distance <= self.fifty_arc_radius
        
    def get_distance_to_goal(self, position: Tuple[int, int], end: str) -> float:
        """Get distance to goal"""
        x, y = position
        if end == 'home':
            goal_x, goal_y = (self.length, self.width/2)
        else:
            goal_x, goal_y = (0, self.width/2)
            
        return math.sqrt((x - goal_x)**2 + (y - goal_y)**2)

class Player:
    """Represents a player in the match simulation"""
    def __init__(self, id: int, name: str, team: Any, ability_stats: Any, physical_stats: Any):
        self.id = id
        self.name = name
        self.team = team
        self.team_side = None  # Will be set when added to team
        self.ability_stats = ability_stats
        self.physical_stats = physical_stats
        self.current_position = None
        self.fatigue = 0
        self.last_action_time = 0
        self.recent_actions = []
        self._position = None
        
    def can_act(self) -> bool:
        """Check if player can perform an action"""
        return self.fatigue < 90
        
    def update(self, delta_time: float) -> None:
        """Update player state"""
        # Recover fatigue slowly
        self.fatigue = max(0, self.fatigue - (delta_time * 0.1))
        
        # Clean up old actions
        self.recent_actions = [
            action for action in self.recent_actions
            if time.time() - action['time'] < 60
        ]
        
    @property
    def position(self) -> str:
        """Get player's current position"""
        return self._position
        
    @position.setter
    def position(self, value: str) -> None:
        """Set player's position"""
        self._position = value

class Team:
    """Represents a team in the match simulation"""
    def __init__(self, id: int, name: str, side: str, tactics: Dict[str, Any]):
        self.id = id
        self.name = name
        self.side = side  # 'home' or 'away'
        self.players = []
        self.score = {'goals': 0, 'behinds': 0, 'total': 0}
        self.tactics = tactics
        
    def add_player(self, player: Player) -> None:
        """Add a player to the team"""
        self.players.append(player)
        player.team = self
        player.team_side = self.side
        
    def get_players_by_position(self, position: str) -> List[Player]:
        """Get all players in a specific position"""
        return [p for p in self.players if p.position == position]
        
    def update_score(self, score_type: str) -> None:
        """Update team score"""
        if score_type == 'goal':
            self.score['goals'] += 1
            self.score['total'] += 6
        elif score_type == 'behind':
            self.score['behinds'] += 1
            self.score['total'] += 1

class StatsManager:
    """Manages game statistics"""
    def __init__(self):
        self.player_stats = {}
        self.team_stats = {
            'home': self._initialize_team_stats(),
            'away': self._initialize_team_stats()
        }
        self.quarter_stats = {
            1: {'home': self._initialize_team_stats(), 'away': self._initialize_team_stats()},
            2: {'home': self._initialize_team_stats(), 'away': self._initialize_team_stats()},
            3: {'home': self._initialize_team_stats(), 'away': self._initialize_team_stats()},
            4: {'home': self._initialize_team_stats(), 'away': self._initialize_team_stats()}
        }
        
    def _initialize_team_stats(self) -> Dict[str, int]:
        """Initialize team statistics"""
        return {
            'kick': 0,
            'handball': 0,
            'mark': 0,
            'tackle': 0,
            'hitout': 0,
            'clearance': 0,
            'inside_50': 0,
            'rebound_50': 0,
            'contested_possession': 0,
            'uncontested_possession': 0,
            'goal': 0,
            'behind': 0,
            'disposal': 0
        }
        
    def _initialize_player_stats(self, player: Any) -> Dict[str, Any]:
        """Initialize individual player statistics"""
        return {
            'player': player,
            'kick': 0,
            'handball': 0,
            'mark': 0,
            'tackle': 0,
            'hitout': 0,
            'clearance': 0,
            'inside_50': 0,
            'rebound_50': 0,
            'contested_possession': 0,
            'uncontested_possession': 0,
            'goal': 0,
            'behind': 0,
            'disposal': 0,
            'effective_disposal': 0,
            'clanger': 0,
            'free_kick_for': 0,
            'free_kick_against': 0
        }
        
    def update_stats(self, event_type: str, player: Any, team_side: str, quarter: int) -> None:
        """Update statistics based on event"""
        # Initialize player stats if needed
        if player.id not in self.player_stats:
            self.player_stats[player.id] = self._initialize_player_stats(player)
            
        # Update player stats
        player_stats = self.player_stats[player.id]
        if event_type in player_stats:
            player_stats[event_type] += 1
            
        # Update team stats
        self.team_stats[team_side][event_type] += 1
        self.quarter_stats[quarter][team_side][event_type] += 1
        
        # Update derived stats
        if event_type in ['kick', 'handball']:
            player_stats['disposal'] += 1
            self.team_stats[team_side]['disposal'] = (
                self.team_stats[team_side]['kick'] +
                self.team_stats[team_side]['handball']
            )


class CommentaryEngine:
    """Generates match commentary"""
    def __init__(self):
        self.phrases = {
            'mark': [
                "{player} takes a strong grab!",
                "What a mark by {player}!",
                "{player} climbs high and takes a beauty!",
                "Specky from {player}! That'll be on the highlights reel!",
                "{player} shows great hands with that mark."
            ],
            'goal': [
                "{player} slots it through for a major!",
                "GOAL! {player} makes no mistake!",
                "That's six points! Beautiful finish from {player}!",
                "{player} threads the needle for a goal!",
                "The crowd goes wild as {player} kicks truly!"
            ],
            'behind': [
                "Just a minor score for {player}",
                "{player} pushes it a bit wide for a behind",
                "One point as {player}'s shot drifts to the right",
                "Not quite straight enough from {player}",
                "A rushed behind as the defense scrambles"
            ],
            'center_bounce': [
                "We're back in the middle with {ruck1} and {ruck2} going at it",
                "The big men {ruck1} and {ruck2} face off in the center",
                "Back to the center we go, {ruck1} versus {ruck2}",
                "{ruck1} and {ruck2} ready for another ruck contest",
                "The umpire holds the ball aloft as {ruck1} and {ruck2} prepare to do battle"
            ],
            'tackle': [
                "Brilliant tackle from {player}!",
                "{player} wraps them up perfectly!",
                "No getting out of that one! Great tackle by {player}",
                "{player} shows perfect technique in that tackle",
                "Bone-crunching tackle from {player}!"
            ],
            'handball': [
                "Quick hands from {player}",
                "{player} dishes it off nicely",
                "Clever handball from {player}",
                "{player} gets it away under pressure",
                "Slick handball delivery from {player}"
            ],
            'kick': [
                "{player} sends it long",
                "Beautiful kick from {player}",
                "{player} puts it into space",
                "Great field kick by {player}",
                "{player} launches it forward"
            ],
            'hitout': [
                "{player} taps it down perfectly",
                "Great tap work from {player}",
                "{player} gives first use to the rovers",
                "Textbook ruck work from {player}",
                "{player} dominates the hitout"
            ],
            'clearance': [
                "{player} bursts clear from the stoppage",
                "Brilliant clearance work from {player}",
                "{player} extracts it from the congestion",
                "Clean hands in traffic from {player}",
                "{player} breaks through the pack"
            ],
            'quarter_start': [
                "And we're underway in the {quarter} quarter!",
                "The {quarter} quarter begins!",
                "We're back for the {quarter} quarter of this contest",
                "The umpire bounces the ball to start the {quarter} quarter",
                "Here we go for the {quarter} quarter!"
            ],
            'quarter_end': [
                "That's the end of the {quarter} quarter!",
                "The siren sounds to end the {quarter} quarter",
                "{quarter} quarter complete!",
                "And there's the siren! End of the {quarter} quarter",
                "The {quarter} quarter comes to a close"
            ]
        }
        
        self.jokes = [
            "That kick was so bad it might get nominated for the Brownlow!",
            "He's been quieter than a Collingwood supporter after a Grand Final loss!",
            "That was a bigger flop than the Gold Coast Suns' premiership aspirations!",
            "He's got more space than a Carlton defender in September!",
            "That was more confusing than the AFL's rule changes!",
            "He's been more elusive than a Richmond player at a nightclub!",
            "That was more dramatic than an Eddie McGuire press conference!",
            "He's got more moves than a Dusty Martin don't argue!",
            "That was shakier than Essendon's supplement program!",
            "He's got more pressure than a Fremantle player in front of goal!"
        ]
        
    def generate_commentary(self, event_type: str, **data: Any) -> str:
        """Generate commentary for an event"""
        if event_type not in self.phrases:
            return ""
            
        # Get random phrase for this event type
        phrase = random.choice(self.phrases[event_type])
        
        # Add a joke occasionally
        if random.random() < 0.05:  # 5% chance of a joke
            phrase += " " + random.choice(self.jokes)
            
        # Format with event data
        try:
            return phrase.format(**data)
        except KeyError:
            return phrase

class GameState:
    """Manages the current state of the game"""
    def __init__(self, home_team: Any, away_team: Any, home_players: Dict[str, Any], away_players: Dict[str, Any], ground: Ground, weather_system: Any):
        self.quarter: int = 1
        self.time: float = 0
        self.time_remaining: float = 2 * 60  # 20 minutes in seconds
        
        self.home_team = home_team
        self.away_team = away_team
        self.home_players = home_players
        self.away_players = away_players
        self.weather_system = weather_system
        
        self.score: Dict[str, Dict[str, int]] = {
            'home': {'goals': 0, 'behinds': 0, 'total': 0},
            'away': {'goals': 0, 'behinds': 0, 'total': 0}
        }
        
        # Initialize ball position to center of ground
        self.ball_position: Tuple[int, int] = (ground.length // 2, ground.width // 2)
        self.ball_carrier: Optional[Any] = None
        self.phase: str = 'pre_game'
        self.last_possession: Optional[Any] = None
        
        # Player states
        self.player_states: Dict[str, 'PlayerState'] = {}
        self._initialize_player_states()
        
    def _initialize_player_states(self) -> None:
        """Initialize state tracking for all players"""
        for player in list(self.home_players.values()) + list(self.away_players.values()):
            self.player_states[str(player.id)] = PlayerState(player)
            
    def update(self, delta_time: float) -> None:
        """Update game state"""
        self.time += delta_time
        self.time_remaining -= delta_time
        
        # Update player states
        for state in self.player_states.values():
            state.update(delta_time)
            
        # Update weather
        self.weather_system.update()

class PlayerState:
    """Tracks individual player state during the match"""
    def __init__(self, player: Any):
        self.player = player
        self.fatigue = 0
        self.last_action_time = 0
        self.current_position: Optional[Tuple[int, int]] = None
        self.target_position: Optional[Tuple[int, int]] = None
        self.is_leading = False
        self.is_marking_contest = False
        self.marking_target: Optional[Any] = None
        self.recent_possessions = []
        
    def update(self, delta_time: float) -> None:
        """Update player state"""
        # Recover fatigue
        self.fatigue = max(0, self.fatigue - (delta_time * 0.1))
        
        # Clean up old possessions
        self.recent_possessions = [
            p for p in self.recent_possessions
            if time.time() - p['time'] < 60
        ]
        
    def add_possession(self) -> None:
        """Record a possession"""
        self.recent_possessions.append({
            'time': time.time(),
            'position': self.current_position
        })
        
    def can_act(self) -> bool:
        """Check if player can perform actions"""
        return self.fatigue < 90

class PositionManager:
    """Manages player positions and formations"""
    def __init__(self, ground: Ground):
        self.ground = ground
        self.formations = self._initialize_formations()
        
    def _initialize_formations(self) -> Dict[str, Dict[str, Tuple[float, float]]]:
        """Initialize formation templates"""
        return {
            'center_bounce': {
                'Ruck': (0.5, 0.5),    # Center
                'RuckRover': (0.48, 0.45),    # Slightly back and left
                'Rover': (0.48, 0.55),   # Slightly back and right
                'Centre': (0.45, 0.5),      # Behind center
                'LWing': (0.5, 0.2),      # Left wing
                'RWing': (0.5, 0.8),      # Right wing
                'LHF': (0.7, 0.3),     # Left half forward
                'RHF': (0.7, 0.7),     # Right half forward
                'CHF': (0.75, 0.5),    # Center half forward
                'LF': (0.9, 0.3),     # Left full forward
                'RF': (0.9, 0.7),     # Right full forward
                'LHB': (0.3, 0.3),     # Left half back
                'RHB': (0.3, 0.7),     # Right half back
                'CHB': (0.25, 0.5),    # Center half back
                'LB': (0.1, 0.3),     # Left full back
                'RB': (0.1, 0.7),     # Right full back
            },
            'kickout': {
                'Ruck': (0.3, 0.5),    # Up the ground
                'RuckRover': (0.4, 0.4),      # Wing
                'Rover': (0.4, 0.6),     # Wing
                'Centre': (0.5, 0.5),       # Center
                'LWing': (0.4, 0.2),      # Wide
                'RWing': (0.4, 0.8),      # Wide
                'LHF': (0.6, 0.3),     # Forward
                'RHF': (0.6, 0.7),     # Forward
                'CHF': (0.7, 0.5),     # Forward
                'LF': (0.8, 0.3),     # Deep forward
                'RF': (0.8, 0.7),     # Deep forward
                'LHB': (0.15, 0.3),    # Defense
                'RHB': (0.15, 0.7),    # Defense
                'CHB': (0.2, 0.5),     # Defense
                'LB': (0.1, 0.3),     # Deep defense
                'RB': (0.1, 0.7),     # Deep defense
            }
        }
        
    def get_base_position(self, position: str, team_side: str) -> Tuple[int, int]:
        """Get base position for a player"""
        # Get relative coordinates from formations
        formation = self.formations['center_bounce']  # Default formation
        
        # Get base position for this position type
        if position not in formation:
            # Default to center if position not found
            rel_x, rel_y = (0.5, 0.5)
        else:
            rel_x, rel_y = formation[position]
        
        # Flip x-coordinate for away team
        if team_side == 'away':
            rel_x = 1 - rel_x
            
        # Convert to absolute coordinates
        x = int(rel_x * self.ground.length)
        y = int(rel_y * self.ground.width)
        
        return (x, y)
        
    def get_formation_position(self, position: str, formation: str, team_side: str) -> Tuple[int, int]:
        """Get position in specific formation"""
        if formation not in self.formations:
            return self.get_base_position(position, team_side)
            
        if position not in self.formations[formation]:
            # Default to base position if not found in formation
            return self.get_base_position(position, team_side)
            
        rel_x, rel_y = self.formations[formation][position]
        
        if team_side == 'away':
            rel_x = 1 - rel_x
            
        x = int(rel_x * self.ground.length)
        y = int(rel_y * self.ground.width)
        
        return (x, y)
        
    def get_valid_position(self, position: Tuple[int, int]) -> Tuple[int, int]:
        """Ensure position is within ground boundaries"""
        x, y = position
        
        x = max(0, min(x, self.ground.length))
        y = max(0, min(y, self.ground.width))
        
        return (x, y)

class MovementEngine:
    """Handles player movement and positioning"""
    def __init__(self, ground: Ground, position_manager: PositionManager):
        self.ground = ground
        self.position_manager = position_manager
        self.movement_speeds = {
            'sprint': 3.0,  # Meters per update
            'run': 2.0,
            'jog': 1.0,
            'walk': 0.5
        }
        
    def calculate_new_position(
        self,
        player: Any,
        target_pos: Optional[Tuple[int, int]] = None,
        phase: str = 'open_play',
        ball_pos: Optional[Tuple[int, int]] = None
    ) -> Tuple[int, int]:
        """Calculate new position for player"""
        if not player.current_position:
            return self.position_manager.get_base_position(player.position, player.team_side)
            
        current_x, current_y = player.current_position
        
        # Get player's role-based positioning
        role_pos = self._get_role_based_position(player, phase, ball_pos)
        
        # Determine movement target based on game situation
        target_x, target_y = self._determine_target_position(player, role_pos, phase, ball_pos)
        
        # Calculate movement vector
        dx = target_x - current_x
        dy = target_y - current_y
        distance = math.sqrt(dx*dx + dy*dy)
        
        if distance < 0.1:  # If very close to target, make minor adjustment
            return self._make_minor_movement(current_x, current_y)
            
        # Normalize direction
        if distance > 0:
            dx = dx / distance
            dy = dy / distance
            
        # Calculate movement speed based on situation
        speed = self._calculate_movement_speed(player, phase, distance)
        
        # Apply player attributes
        speed_factor = player.physical_stats.speed / 20  # Convert to 0-1 range
        agility_factor = player.physical_stats.agility / 20
        endurance_factor = max(0.5, 1 - (player.fatigue / 100))
        
        # Combine factors
        actual_speed = speed * speed_factor * (1 + agility_factor * 0.3) * endurance_factor
        
        # Calculate new position
        new_x = current_x + dx * actual_speed
        new_y = current_y + dy * actual_speed
        
        # Ensure position is valid and apply tactical adjustments
        new_pos = self.position_manager.get_valid_position((new_x, new_y))
        return self._apply_tactical_adjustments(new_pos, player.team.tactics, phase)
        
    def _get_role_based_position(
        self,
        player: Any,
        phase: str,
        ball_pos: Optional[Tuple[int, int]]
    ) -> Tuple[int, int]:
        """Get ideal position based on player's role"""
        base_pos = self.position_manager.get_base_position(player.position, player.team_side)
        
        if not ball_pos:
            return base_pos
            
        ball_x, ball_y = ball_pos
        base_x, base_y = base_pos
        
        # Role-specific positioning
        if player.position in ['Ruck', 'RuckRover', 'Rover']:  # Midfielders
            # Stay close to ball but maintain structure
            return (
                ball_x + (base_x - ball_x) * 0.3,
                ball_y + (base_y - ball_y) * 0.3
            )
        elif player.position in ['LF', 'RF', 'CHF']:  # Forwards
            if player.team_side == 'home':
                # Push up when ball is forward, drop back when defensive
                forward_bias = 0.7 if ball_x > self.ground.length * 0.6 else 0.4
                return (base_x * forward_bias, base_y)
            else:
                forward_bias = 0.7 if ball_x < self.ground.length * 0.4 else 0.4
                return (base_x * forward_bias, base_y)
        elif player.position in ['LB', 'RB', 'CHB']:  # Defenders
            if player.team_side == 'home':
                # Hold position when ball is forward, push up when defensive
                back_bias = 0.3 if ball_x < self.ground.length * 0.4 else 0.6
                return (base_x * back_bias, base_y)
            else:
                back_bias = 0.3 if ball_x > self.ground.length * 0.6 else 0.6
                return (base_x * back_bias, base_y)
                
        return base_pos
        
    def _determine_target_position(
        self,
        player: Any,
        role_pos: Tuple[int, int],
        phase: str,
        ball_pos: Optional[Tuple[int, int]]
    ) -> Tuple[int, int]:
        """Determine where the player should move to"""
        if not ball_pos:
            return role_pos
            
        ball_x, ball_y = ball_pos
        role_x, role_y = role_pos
        
        if phase == 'contest':
            if self._should_contest(player, ball_pos):
                # Move to contest but maintain some structure
                return (
                    ball_x + (role_x - ball_x) * 0.2,
                    ball_y + (role_y - ball_y) * 0.2
                )
        elif phase == 'open_play':
            if player.team.tactics['mentality'] == 'attacking':
                # More aggressive positioning
                return (
                    role_x + (ball_x - role_x) * 0.3,
                    role_y + (ball_y - role_y) * 0.3
                )
            elif player.team.tactics['mentality'] == 'defensive':
                # More conservative positioning
                return (
                    role_x + (ball_x - role_x) * 0.1,
                    role_y + (ball_y - role_y) * 0.1
                )
                
        return role_pos
        
    def _should_contest(self, player: Any, ball_pos: Tuple[int, int]) -> bool:
        """Determine if player should contest for the ball"""
        if not player.current_position:
            return False
            
        # Calculate distance to ball
        dx = player.current_position[0] - ball_pos[0]
        dy = player.current_position[1] - ball_pos[1]
        distance = math.sqrt(dx*dx + dy*dy)
        
        # Base contest radius on player attributes
        contest_radius = 10 + (player.physical_stats.speed / 20) * 5
        
        # Midfielders more likely to contest
        if player.position in ['Ruck', 'RuckRover', 'Rover', 'Centre']:
            contest_radius *= 1.5
            
        return distance <= contest_radius
        
    def _calculate_movement_speed(
        self,
        player: Any,
        phase: str,
        distance_to_target: float
    ) -> float:
        """Calculate how fast the player should move"""
        if phase == 'contest':
            return self.movement_speeds['sprint']
        elif phase == 'open_play':
            if distance_to_target > 30:
                return self.movement_speeds['run']
            elif distance_to_target > 15:
                return self.movement_speeds['jog']
            else:
                return self.movement_speeds['walk']
        else:
            return self.movement_speeds['jog']
            
    def _make_minor_movement(
        self,
        current_x: float,
        current_y: float
    ) -> Tuple[int, int]:
        """Make small random movement to prevent stagnation"""
        angle = random.uniform(0, 2 * math.pi)
        distance = random.uniform(0.1, 0.5)
        
        new_x = current_x + math.cos(angle) * distance
        new_y = current_y + math.sin(angle) * distance
        
        return self.position_manager.get_valid_position((new_x, new_y))
        
    def _apply_tactical_adjustments(
        self,
        position: Tuple[int, int],
        tactics: Dict[str, Any],
        phase: str
    ) -> Tuple[int, int]:
        """Apply team tactical adjustments to position"""
        x, y = position
        
        # Apply tactical adjustments based on team strategy
        if tactics['mentality'] == 'attacking':
            if phase == 'open_play':
                x += random.uniform(1, 3)  # Push up more aggressively
        elif tactics['mentality'] == 'defensive':
            if phase == 'open_play':
                x -= random.uniform(1, 2)  # Sit back but maintain some attack
                
        # Apply width adjustments
        if tactics.get('offense_strategy') == 'stay_wide':
            if y < self.ground.width / 2:
                y -= random.uniform(0.5, 1.5)  # Wider on the left
            else:
                y += random.uniform(0.5, 1.5)  # Wider on the right
                
        return self.position_manager.get_valid_position((x, y))

class ActionEngine:
    """Handles player actions and their outcomes"""
    def __init__(self, ground: Ground, position_manager: PositionManager):
        self.ground = ground
        self.position_manager = position_manager
        
    def process_action(
        self,
        player: Any,
        action_type: str,
        target: Optional[Any] = None,
        state: Optional[GameState] = None
    ) -> Dict[str, Any]:
        """Process a player action"""
        # Get base success chance
        base_chance = self._get_base_chance(player, action_type)
        
        # Apply modifiers
        modified_chance = self._apply_modifiers(
            base_chance,
            player,
            action_type,
            target,
            state
        )
        
        # Add some skill-based randomness
        skill_variance = random.uniform(-0.1, 0.1)
        modified_chance = max(0.1, min(0.9, modified_chance + skill_variance))
        
        # Critical moments - players can rise to the occasion or crack under pressure
        if random.random() < 0.05:  # 5% chance of a critical moment
            if player.ability_stats.tactical > 15:  # High tactical players handle pressure better
                modified_chance *= 1.2
            else:
                modified_chance *= 0.8
        
        modified_chance += (random.random() * 0.3) # Base chance (30%) even for low-skilled players
        print(f"Modified chance: {modified_chance}")
        # Determine outcome
        success = random.random() < modified_chance
        
        # Generate appropriate commentary based on outcome
        commentary = self._get_outcome_commentary(action_type, success, player, modified_chance)
        print(f"Returning action: {action_type} for {player.name}, and target: {target.name if target else 'None'}")
        return {
            'type': action_type,
            'player': player,
            'target': target,
            'success': success,
            'position': player.current_position,
            'commentary': commentary
        }
        
    def _get_base_chance(self, player: Any, action_type: str) -> float:
        """Get base success chance for action"""
        if action_type == 'kick':
            return player.ability_stats.kicking /  20
        elif action_type == 'handball':
            return player.ability_stats.handball /  20
        elif action_type == 'mark':
            return player.ability_stats.marking /  20
        elif action_type == 'tackle':
            return player.ability_stats.tackling /  20
        elif action_type == 'ruck_contest':
            # For ruck contest, use height and marking ability
            height_factor = player.physical_stats.height /  20
            marking_factor = player.ability_stats.tactical /  20
            return (height_factor + marking_factor) / 2
        elif action_type == 'gather':
            # For ground ball gets, use agility and tackling
            agility_factor = player.physical_stats.agility /  20
            speed_factor = player.physical_stats.speed /  20
            return (agility_factor + speed_factor) / 2
        return 0.5
        
    def _apply_modifiers(
        self,
        base_chance: float,
        player: Any,
        action_type: str,
        target: Optional[Any],
        state: Optional[GameState]
    ) -> float:
        """Apply modifiers to base success chance"""
        modified_chance = base_chance
        
        # Apply fatigue modifier
        fatigue_mod = max(0.5, 1 - (player.fatigue / 100))
        modified_chance *= fatigue_mod
        
        # Apply weather modifiers if state available
        if state and state.weather_system:
            weather_mods = state.weather_system.get_modifiers()
            if action_type in ['kick', 'mark']:
                modified_chance *= weather_mods['kicking']
            elif action_type in ['handball', 'gather']:
                modified_chance *= weather_mods['handling']
                
        # Apply pressure modifier if target exists
        if target:
            pressure = self._calculate_pressure(player, target)
            modified_chance *= (1 - pressure * 0.5)
            
        return min(modified_chance, 1.0)  # Cap at 100% chance
        
    def _calculate_pressure(self, player: Any, opponent: Any) -> float:
        """Calculate pressure from opponent"""
        if not player.current_position or not opponent.current_position:
            return 0
            
        # Calculate distance
        dx = player.current_position[0] - opponent.current_position[0]
        dy = player.current_position[1] - opponent.current_position[1]
        distance = math.sqrt(dx*dx + dy*dy)
        
        # More pressure when closer
        base_pressure = max(0, 1 - distance/10)
        
        # Modify based on opponent's tackling and agility
        tackle_factor = opponent.ability_stats.tackling / 100
        agility_factor = opponent.physical_stats.agility / 100
        
        pressure = base_pressure * (tackle_factor + agility_factor) / 2
        
        return min(pressure, 1.0)  # Cap at 100% pressure

    def _get_outcome_commentary(self, action_type: str, success: bool, player: Any, chance: float) -> str:
        """Generate commentary based on action outcome"""
        if not success:
            if chance < 0.3:  # It was a difficult attempt
                return random.choice([
                    f"Tough ask for {player.name}, couldn't quite pull it off",
                    f"{player.name} had a go, but that was always going to be difficult",
                    f"The degree of difficulty was just too high for {player.name} there"
                ])
            else:  # Should have done better
                return random.choice([
                    f"That's not like {player.name}, should have done better",
                    f"{player.name} will want that one back",
                    f"Uncharacteristic error from {player.name}",
                    f"The coach won't be happy with that one from {player.name}"
                ])
        else:
            if chance < 0.3:  # Impressive success
                return random.choice([
                    f"Brilliant from {player.name}! That's a highlight reel moment!",
                    f"What a play by {player.name}! That's pure class!",
                    f"That's why {player.name} gets paid the big bucks!"
                ])
            else:  # Standard success
                return random.choice([
                    f"Clean execution from {player.name}",
                    f"Well played {player.name}",
                    f"Good skills shown by {player.name}"
                ])

class EventManager:
    """Handles event creation and yielding"""
    def __init__(self):
        self.commentary = CommentaryEngine()
        
    def create_event(self, event_type: str, quarter: int, match_event="match_event", **data: Any) -> Dict[str, Any]:
        """Create a game event"""
        event = {
            'type': match_event,
            'quarter': quarter,
            'timestamp': time.time(),
            'event_type': event_type,
            'data': data
        }
        
        # Add commentary
        commentary = self.commentary.generate_commentary(event_type, **data)
        if commentary:
            event['commentary'] = commentary
            
        return event

class ScoringEngine:
    """Handles scoring calculations and outcomes"""
    def __init__(self, ground: Ground):
        self.ground = ground
        
    def process_shot(self, player: Any, position: Tuple[int, int], state: GameState) -> Dict[str, Any]:
        """Process a shot at goal"""
        # Calculate base accuracy
        base_accuracy = 0.3 + (player.ability_stats.goal_kicking /  20)
        
        # Apply distance modifier
        distance = self.ground.get_distance_to_goal(position, player.team_side)
        distance_mod = max(0.2, 1 - (distance / 60))  # Harder from further out
        
        # Apply angle modifier
        angle = self._calculate_angle(position, player.team_side)
        angle_mod = max(0.2, 1 - (angle / 45))  # Harder from wider angles
        
        # Apply weather effects
        weather_mod = 1.0
        if state.weather_system:
            mods = state.weather_system.get_modifiers()
            weather_mod = mods['kicking']
            
        # Calculate final accuracy
        accuracy = base_accuracy * distance_mod * angle_mod * weather_mod
        print(f"Accuracy: {accuracy}")
        time.sleep(3)
        # Determine outcome
        if random.random() < accuracy:
            return {'result': 'goal', 'player': player}
        elif random.random() < 0.7:  # 70% chance of behind if not goal
            return {'result': 'behind', 'player': player}
        else:
            return {'result': 'miss', 'player': player}
            
    def _calculate_angle(self, position: Tuple[int, int], team_side: str) -> float:
        """Calculate angle to goal"""
        x, y = position
        if team_side == 'home':
            goal_x = self.ground.length
        else:
            goal_x = 0
        goal_y = self.ground.width / 2
        
        # Calculate angle using arctangent
        dx = abs(goal_x - x)
        dy = abs(goal_y - y)
        
        if dx == 0:
            return 90
        return math.degrees(math.atan(dy/dx))

class TeamCoordinator:
    """Manages team-level decisions and coordination"""
    def __init__(self, team: Any, players: Dict[str, Any], tactics: Dict[str, Any], side: str):
        self.team = team
        self.players = players
        self.tactics = tactics
        self.side = side
        
    def get_target_player(self, kicker: Any, phase: str) -> Optional[Any]:
        """Find best target player for a kick"""
        if not kicker.current_position:
            return None
            
        best_target = None
        best_score = -1
        
        for player in self.players.values():
            if player.id == kicker.id:
                continue
                
            if not player.current_position:
                continue
                
            # Calculate score for this target
            score = self._calculate_target_score(kicker, player, phase)
            
            if score > best_score:
                best_score = score
                best_target = player
                
        return best_target
        
    def _calculate_target_score(self, kicker: Any, target: Any, phase: str) -> float:
        """Calculate how good a target player is"""
        if not kicker.current_position or not target.current_position:
            return 0
            
        # Base score from marking ability and mental stats
        score = (target.ability_stats.marking + target.ability_stats.tactical) / 40
        
        # Distance factor - minimum 10m for a mark
        dx = target.current_position[0] - kicker.current_position[0]
        dy = target.current_position[1] - kicker.current_position[1]
        distance = math.sqrt(dx*dx + dy*dy)
        
        # Distance scoring
        if distance < 10:
            score *= 0.3  # Too close for a mark
        elif distance > 50:
            score *= 0.5  # Very long kick, higher risk
            
        # Factor in kicker's vision/decision making under pressure
        pressure = self._calculate_pressure(kicker)
        vision_factor = max(0.2, (kicker.ability_stats.tactical / 20) * (1 - pressure * 0.7))
        score *= vision_factor
        
        # Add some randomness - players don't always see the best option
        score *= random.uniform(0.7, 1.0)
        
        # Bias towards forward movement
        if kicker.team_side == 'home' and dx > 0:
            score *= 1.2
        elif kicker.team_side == 'away' and dx < 0:
            score *= 1.2
            
        return score
        
    def update_tactics(self, score_difference: int) -> None:
        """Update tactics based on game situation"""
        if score_difference < -12:  # Two goals down
            self.tactics['mentality'] = 'attacking'
            self.tactics['push_factor'] = '3'
        elif score_difference > 12:  # Two goals up
            self.tactics['mentality'] = 'defensive'
            self.tactics['push_factor'] = '1'
        else:
            # Reset to default
            self.tactics['mentality'] = 'balanced'
            self.tactics['push_factor'] = '2'

class MatchEngine:
    """Main match simulation engine"""
    def __init__(self, home_team: Any, away_team: Any, home_team_players: Dict[str, Any], away_team_players: Dict[str, Any], home_team_tactics: Dict[str, Any], away_team_tactics: Dict[str, Any], match_group_name: str, channel_layer: Any, ground_config: Optional[Dict] = None):
        # Initialize teams
        self.home_team = Team(home_team.id, home_team.name, 'home', home_team_tactics)
        self.away_team = Team(away_team.id, away_team.name, 'away', away_team_tactics)
        
        # Initialize players
        self.home_team_players = {}
        self.away_team_players = {}
        
        # Add home team players
        for position, player_data in home_team_players.items():
            player = Player(
                id=player_data.id,
                name=player_data.name,
                team=self.home_team,
                ability_stats=player_data.ability_stats,
                physical_stats=player_data.physical_stats
            )
            player.position = position
            self.home_team_players[position] = player
            self.home_team.add_player(player)
            
        # Add away team players
        for position, player_data in away_team_players.items():
            player = Player(
                id=player_data.id,
                name=player_data.name,
                team=self.away_team,
                ability_stats=player_data.ability_stats,
                physical_stats=player_data.physical_stats
            )
            player.position = position
            self.away_team_players[position] = player
            self.away_team.add_player(player)
        
        # Initialize other components
        self.ground = Ground(ground_config or DEFAULT_GROUND_CONFIG)
        self.weather_system = WeatherSystem()
        self.position_manager = PositionManager(self.ground)
        self.movement_engine = MovementEngine(self.ground, self.position_manager)
        self.action_engine = ActionEngine(self.ground, self.position_manager)
        self.scoring_engine = ScoringEngine(self.ground)
        self.stats_manager = StatsManager()
        
        # Initialize team coordinators
        self.home_coordinator = TeamCoordinator(
            self.home_team,
            self.home_team_players,
            home_team_tactics,
            'home'
        )
        self.away_coordinator = TeamCoordinator(
            self.away_team,
            self.away_team_players,
            away_team_tactics,
            'away'
        )
        
        # Initialize game state
        self.state = GameState(
            self.home_team,
            self.away_team,
            self.home_team_players,
            self.away_team_players,
            self.ground,
            self.weather_system
        )
        
        # Initialize event manager
        self.event_manager = EventManager()
        
        self.quarter_length = 2 * 60  # 20 minutes in seconds
        self.time_elapsed = 0
        self.is_paused = False

    async def simulate_match(self) -> Generator[Dict[str, Any], None, None]:
        """Simulate entire match"""
        # Pre-match setup
        self._initialize_positions()
        
        # Simulate all quarters
        for quarter in range(1, 5):
            self.state.quarter = quarter
            self.state.time_remaining = self.quarter_length
            
            # Start quarter
            yield self.event_manager.create_event(
                'quarter_start',
                self.state.quarter,
                name=self._get_quarter_name(quarter)
            )
            
            # Simulate quarter
            async for event in self._simulate_quarter():
                yield event
                
            # End quarter
            yield self.event_manager.create_event(
                'quarter_end',
                self.state.quarter,
                name=self._get_quarter_name(quarter),
                home_score=self.state.score['home'],
                away_score=self.state.score['away']
            )

            player_stats_formatted = []
            for player_id, stats in self.stats_manager.player_stats.items():
                player = stats['player']
                player_stats_formatted.append({
                    'player_name': player.name,
                    'team': player.team.name,
                    'kick': stats['kick'],
                    'handball': stats['handball'],
                    'mark': stats['mark'],
                    'tackle': stats['tackle'],
                    'hitout': stats['hitout'],
                    'clearance': stats['clearance'],
                    'inside_50': stats['inside_50'],
                    'rebound_50': stats['rebound_50'],
                    'contested_possession': stats['contested_possession'],
                    'uncontested_possession': stats['uncontested_possession'],
                    'goal': stats['goal'],
                    'behind': stats['behind'],
                    'disposal': stats['disposal'],
                    'effective_disposal': stats['effective_disposal'],
                    'clanger': stats['clanger'],
                    'free_kick_for': stats['free_kick_for'],
                    'free_kick_against': stats['free_kick_against']
                })

            yield self.event_manager.create_event(
                'player_stats',
                self.state.quarter,
                'player_stats',
                player_stats=player_stats_formatted
            )

            
            # Process quarter break
            if quarter < 4:
                await self._process_quarter_break()
                
        # End match
        yield self.event_manager.create_event(
            'match_end',
            4,
            home_score=self.state.score['home'],
            away_score=self.state.score['away'],
            stats=self.stats_manager.team_stats
        )
        
    async def _simulate_quarter(self) -> Generator[Dict[str, Any], None, None]:
        """Simulate a quarter of play"""
        # Start with center bounce
        self._setup_center_bounce()
        
        # Initial center bounce
        result = await self._process_center_bounce()
        if result:
            yield result
        print(f"Phase after center bounce: {self.state.phase}")
        print(f"Result after center bounce: {result}")
        # Main simulation loop
        while self.state.time_remaining > 0 and not self.is_paused:
            # Update player positions and get position event
            position_event = self._update_positions()
            if position_event:
                yield position_event
            
            # Process current phase
            if self.state.phase == 'contest':
                result = await self._process_contest()
                print(f"Phase after contest: {self.state.phase}")
                print(f"Result after contest: {result}")
            elif self.state.phase == 'loose_ball':
                result = await self._process_loose_ball()    
            elif self.state.phase == 'open_play':
                result = await self._process_open_play()
                print(f"Phase after open play: {self.state.phase}")
                print(f"Result after open play: {result}")
            elif self.state.phase == 'set_shot':
                result = await self._process_set_shot()
                print(f"Phase after set shot: {self.state.phase}")
                print(f"Result after set shot: {result}")
            #else:
                # Reset to center bounce if unknown phase
                #self._setup_center_bounce()
                #result = await self._process_center_bounce()
            print(f"Phase: {self.state.phase}")
            print(f"Result: {result}")
            if result:
                yield result

                event = self.event_manager.create_event(
                    'score_update',
                    self.state.quarter,
                    'score_update',
                    score=self.state.score
                )
                if event:
                    yield event
                
            # Update game state - use larger time increment
            self.state.update(1.0)  # Update every second instead of 0.1
            await asyncio.sleep(0.1)  # Keep real-time simulation smooth
            
    async def _process_center_bounce(self) -> Optional[Dict[str, Any]]:
        """Process center bounce phase"""
        # Get ruckmen
        home_ruck = self._get_primary_ruck('home')
        away_ruck = self._get_primary_ruck('away')
        
        if not home_ruck or not away_ruck:
            return None

        # Calculate contest
        contest_result = self.action_engine.process_action(
            home_ruck,
            'ruck_contest',
            target=away_ruck,
            state=self.state
        )
        
        # Determine winner based on success
        winner = home_ruck if contest_result['success'] else away_ruck
        
        # Update stats
        self.stats_manager.update_stats(
            'hitout',
            winner,
            winner.team_side,
            self.state.quarter
        )
        
        # Transition to next phase and set ball carrier
        self.state.phase = 'contest'
        self.state.ball_carrier = winner
        self.state.ball_position = winner.current_position
        
        event = self.event_manager.create_event(
            'center_bounce',
            self.state.quarter,
            ruck1=home_ruck.name,
            ruck2=away_ruck.name,
            winner=winner.name
        )
        return event
        
    async def _process_contest(self) -> Optional[Dict[str, Any]]:
        """Process contest phase"""
        # Get players in contest
        players = self._get_players_in_contest()
        
           
        # Filter out the last ball carrier to prevent immediate repossession
        if self.state.last_possession:
            players = [p for p in players if p.id != self.state.last_possession.id]
            
        if not players:  # If no eligible players after filtering
            # Still create loose ball event if all nearby players are filtered out
            self.state.phase = 'loose_ball'
            return self.event_manager.create_event(
                'loose_ball',
                self.state.quarter,
                commentary="Ball spills free!"
            )
            
        # Determine winner
        contest_result = self.action_engine.process_action(
            players[0],
            'gather',
            target=players[1] if len(players) > 1 else None,
            state=self.state
        )

        winner = contest_result['player'] if contest_result['success'] else (
            contest_result['target'] if contest_result['target'] else None
        )
        print(f"Winner: {contest_result['player'].name} and contest_result {contest_result['success']}")
        if winner:
            # Update stats
            self.stats_manager.update_stats(
                'contested_possession',
                winner,
                winner.team_side,
                self.state.quarter
            )
            
            # Transition to open play
            self.state.phase = 'open_play'
            self.state.ball_carrier = winner
            
            event = self.event_manager.create_event(
                'contest_won',
                self.state.quarter,
                player=winner.name,
                commentary=f"{winner.name} emerges with the ball!"
            )
            return event
        print(f"No winner")
        print(f"Current phase: {self.state.phase}")
        """Not sure this should be None, but it is causing the game to crash"""
        #sys.exit()    
        return None
        
    async def _process_open_play(self) -> Optional[Dict[str, Any]]:
        """Process open play phase"""
        if not self.state.ball_carrier:
            self.state.phase = 'contest'
            return None
            
        # Store the current ball carrier for event creation
        current_carrier = self.state.ball_carrier
            
        # Decide player action
        action = self._decide_player_action(current_carrier)
        print(f"Action: {action}")
        
        if action['action'] == 'kick_for_goal':
            # Transition to set shot
            self.state.phase = 'set_shot'
            return self.event_manager.create_event(
                'lining_up',
                self.state.quarter,
                player=current_carrier.name,
                commentary=action['commentary']
            )
            
        elif action['action'] == 'kick_to_player':
            # Find kick target
            target_info = self._find_kick_target(current_carrier)
            
            if not target_info:
                # If no target found, kick to space
                target_pos = self._calculate_kick_destination(current_carrier, 'kick_clear')
                self._move_ball_to_position(target_pos, speed=2.0)  # Faster for kicks
                
                self.state.ball_carrier = None
                self.state.phase = 'contest'
                return self.event_manager.create_event(
                    'kick',
                    self.state.quarter,
                    player=current_carrier.name,
                    commentary="Kicks it into space, it'll be a contest!"
                )

            print(f"Target info: {{" + 
                f"'player': {target_info['player'].name}, " +
                f"'distance': {target_info['distance']:.2f}, " +
                f"'accuracy': {target_info['accuracy']:.2f}, " +
                f"'landing_pos': {target_info['landing_pos']}" +
                "}")    
            # Process kick
            kick_result = self.action_engine.process_action(
                current_carrier,
                'kick',
                target=target_info['player'],
                state=self.state
            )
            
            # Update stats
            self.stats_manager.update_stats(
                'kick',
                current_carrier,
                current_carrier.team_side,
                self.state.quarter
            )
            
            # Move ball to landing position
            self._move_ball_to_position(target_info['landing_pos'], speed=2.0)
            
            if kick_result['success']:
                # Calculate base marking chance using multiple factors
                base_marking = target_info['player'].ability_stats.marking / 20  # 0-1 range
                
                # Calculate modifiers
                # Distance modifier - marks are easier at optimal distance (20-35m)
                distance = target_info['distance']
                if distance < 15:
                    distance_mod = 0.6  # Too close, harder to mark
                elif 20 <= distance <= 35:
                    distance_mod = 1.2  # Optimal marking range
                else:
                    distance_mod = max(0.7, 1 - ((distance - 35) / 40))  # Gradually harder at longer distances
                    
                # Pressure modifier - easier to mark when in space
                pressure = self._calculate_pressure(target_info['player'])
                pressure_mod = 1 - (pressure * 0.7)  # Pressure reduces effectiveness by up to 70%
                
                # Kick quality modifier - good kicks are easier to mark
                kick_mod = 0.7 + (target_info['accuracy'] * 0.6)  # Range 0.7-1.3
                
                # Height advantage modifier (if we track player height)
                # height_mod = 1.0 + ((player.height - avg_height) * 0.1)
                
                # Fatigue modifier (if we track fatigue)
                # fatigue_mod = 1.0 - (player.fatigue * 0.3)
                
                # Calculate final marking chance
                mark_chance = (
                    0.3 +  # Base chance (30%) even for low-skilled players
                    (base_marking * 0.4) +  # Skill contribution (up to 40%)
                    (random.random() * 0.3)  # Random factor (up to 30%)
                )
                
                # Apply modifiers
                mark_chance *= distance_mod * pressure_mod * kick_mod
                
                # Cap the final chance
                mark_chance = min(0.95, max(0.15, mark_chance))  # Always at least 15% chance, max 95%
                
                print(f"Mark chance breakdown for {target_info['player'].name}:")
                print(f"  Base marking ability: {base_marking:.2f}")
                print(f"  Distance modifier ({distance:.1f}m): {distance_mod:.2f}")
                print(f"  Pressure modifier: {pressure_mod:.2f}")
                print(f"  Kick quality modifier: {kick_mod:.2f}")
                print(f"  Final mark chance: {mark_chance:.2f}")
                
                mark_taken = random.random() < mark_chance
                
                if mark_taken:
                    # Mark taken
                    self.state.ball_carrier = target_info['player']
                    self.state.phase = 'set_shot'
                    self.state.last_possession = current_carrier
                    
                    # Update stats
                    self.stats_manager.update_stats(
                        'mark',
                        target_info['player'],
                        target_info['player'].team_side,
                        self.state.quarter
                    )
                    
                    return self.event_manager.create_event(
                        'mark',
                        self.state.quarter,
                        player=target_info['player'].name,
                        commentary=action['commentary']
                    )
                else:
                    # No mark, contest
                    self.state.ball_carrier = None
                    self.state.phase = 'contest'
                    self.state.last_possession = current_carrier
                
            return self.event_manager.create_event(
                'kick',
                self.state.quarter,
                player=current_carrier.name,
                target=target_info['player'].name,
                success=kick_result['success'],
                commentary=action['commentary']
            )
            
        elif action['action'] in ['kick_clear', 'panic_kick']:
            # Calculate kick destination based on type
            target_pos = self._calculate_kick_destination(current_carrier, action['action'])
            
            # Move ball to target position
            self._move_ball_to_position(target_pos, speed=2.0)
            
            # Check if kick goes out of bounds
            if not self.ground.is_in_bounds(target_pos):
                self.state.phase = 'throw_in'
                self.state.ball_carrier = None
                self.state.last_possession = current_carrier
                
                return self.event_manager.create_event(
                    'out_of_bounds',
                    self.state.quarter,
                    player=current_carrier.name,
                    commentary=action['commentary']
                )
            else:
                # Ball in play - contest
                self.state.ball_carrier = None
                self.state.phase = 'contest'
                self.state.last_possession = current_carrier
                
                return self.event_manager.create_event(
                    'clearing_kick',
                    self.state.quarter,
                    player=current_carrier.name,
                    commentary=action['commentary']
                )
            
        elif action['action'] == 'handball':
            # Find handball target
            target_info = self._find_handball_target(current_carrier)
            if not target_info:
                # If no target, ball spills to contest
                spill_pos = (
                    current_carrier.current_position[0] + random.uniform(-2, 2),
                    current_carrier.current_position[1] + random.uniform(-2, 2)
                )
                self._move_ball_to_position(spill_pos, speed=1.0)
                
                self.state.ball_carrier = None
                self.state.phase = 'contest'
                self.state.last_possession = current_carrier
                
                return self.event_manager.create_event(
                    'handball',
                    self.state.quarter,
                    player=current_carrier.name,
                    commentary="Handball goes astray, it'll be a contest!"
                )
                
            # Process handball
            handball_result = self.action_engine.process_action(
                current_carrier,
                'handball',
                target=target_info['player'],
                state=self.state
            )
            
            # Update stats
            self.stats_manager.update_stats(
                'handball',
                current_carrier,
                current_carrier.team_side,
                self.state.quarter
            )
            
            # Move ball to landing position
            self._move_ball_to_position(target_info['landing_pos'], speed=1.5)
            
            if handball_result['success']:
                self.state.ball_carrier = target_info['player']
                self.state.last_possession = current_carrier
            else:
                self.state.ball_carrier = None
                self.state.phase = 'contest'
                self.state.last_possession = current_carrier
                
            return self.event_manager.create_event(
                'handball',
                self.state.quarter,
                player=current_carrier.name,
                target=target_info['player'].name,
                success=handball_result['success'],
                commentary=action['commentary']
            )
            
        elif action['action'] == 'bounce':
            # Process bounce
            bounce_result = self.action_engine.process_action(
                current_carrier,
                'bounce',
                state=self.state
            )
            
            if not bounce_result['success']:
                # Ball spills on failed bounce
                spill_pos = (
                    current_carrier.current_position[0] + random.uniform(-2, 2),
                    current_carrier.current_position[1] + random.uniform(-2, 2)
                )
                self._move_ball_to_position(spill_pos, speed=1.0)
                
                self.state.ball_carrier = None
                self.state.phase = 'contest'
                self.state.last_possession = current_carrier
            else:
                # Successful bounce - ball stays with carrier
                self.state.ball_position = current_carrier.current_position
                
            return self.event_manager.create_event(
                'bounce',
                self.state.quarter,
                player=current_carrier.name,
                success=bounce_result['success'],
                commentary=action['commentary']
            )
            
        # If we somehow get here (shouldn't happen), transition to contest
        spill_pos = (
            current_carrier.current_position[0] + random.uniform(-2, 2),
            current_carrier.current_position[1] + random.uniform(-2, 2)
        )
        self._move_ball_to_position(spill_pos, speed=1.0)
        
        self.state.ball_carrier = None
        self.state.phase = 'contest'
        self.state.last_possession = current_carrier
        
        return self.event_manager.create_event(
            'turnover',
            self.state.quarter,
            player=current_carrier.name,
            commentary="Ball spills free, it'll be a contest!"
        )
        
    async def _process_set_shot(self) -> Optional[Dict[str, Any]]:
        """Process set shot at goal"""
        if not self.state.ball_carrier:
            self.state.phase = 'contest'
            print(f"No ball carrier, should not happen")
            sys.exit()
            return None
            
        # Check if in range
        distance = self._calculate_distance_to_goal(
            self.state.ball_carrier.current_position,
            self.state.ball_carrier.team_side
        )
        
        if distance > 60:  # Too far out
            # Switch to open play
            self.state.phase = 'open_play'
            return None
            
        # Process shot
        shot_result = self.scoring_engine.process_shot(
            self.state.ball_carrier,
            self.state.ball_carrier.current_position,
            self.state
        )
        print(f"Shot result: {shot_result}")
        time.sleep(3)
        # Update score and stats
        if shot_result['result'] == 'goal':
            self.state.score[self.state.ball_carrier.team_side]['goals'] += 1
            self.state.score[self.state.ball_carrier.team_side]['total'] += 6
            # Reset for next center bounce
            self._setup_center_bounce()
            self.stats_manager.update_stats(
                'goal',
                self.state.ball_carrier,
                self.state.ball_carrier.team_side,
                self.state.quarter
            )
        elif shot_result['result'] == 'behind':
            self.state.score[self.state.ball_carrier.team_side]['behinds'] += 1
            self.state.score[self.state.ball_carrier.team_side]['total'] += 1
            self.stats_manager.update_stats(
                'behind',
                self.state.ball_carrier,
                self.state.ball_carrier.team_side,
                self.state.quarter
            )
            # Setup for kick-in after behind
            self._setup_kick_in(self.state.ball_carrier.team_side)
        else:  # Miss
            # Ball spills into play near the goals
            if self.state.ball_carrier.team_side == 'home':
                spill_x = self.ground.length - random.uniform(5, 10)  # 5-10m from goal line
            else:
                spill_x = random.uniform(5, 10)  # 5-10m from goal line
            
            spill_y = self.ground.width / 2 + random.uniform(-10, 10)  # Random deviation from center
            
            # Move ball to spill position
            self._move_ball_to_position((spill_x, spill_y))
            
            # Clear ball carrier and transition to contest
            
            self.state.phase = 'loose_ball'

        # Create event for the shot
        event = self.event_manager.create_event(
            shot_result['result'],
            self.state.quarter,
            player=self.state.ball_carrier.name,
            distance=int(distance)
        )
        if shot_result['result'] == 'miss':
            self.state.ball_carrier = None

        return event

    def _setup_kick_in(self, scoring_team_side: str) -> None:
        """Setup for kick-in after a behind"""
        # Determine defending team (team taking the kick-in)
        defending_team_side = 'away' if scoring_team_side == 'home' else 'home'
        defending_team_players = self.away_team_players if scoring_team_side == 'home' else self.home_team_players
        
        # Find the fullback to take the kick-in
        kick_in_player = None
        for position, player in defending_team_players.items():
            if position in ['FB']:  # Prioritize backline players
                kick_in_player = player
                break
        
        
        # Set ball position at defending team's goal line, centered
        if defending_team_side == 'home':
            ball_x = 5  # 5 meters out from goal line
            ball_y = self.ground.width / 2
        else:
            ball_x = self.ground.length - 5
            ball_y = self.ground.width / 2
            
        # Position the kick-in player
        kick_in_player.current_position = (ball_x, ball_y)
        self.state.ball_position = (ball_x, ball_y)
        self.state.ball_carrier = kick_in_player
        #self.state.phase = 'kick_in'
        self.state.phase = 'open_play'
        
        # Move other players to appropriate positions
        self._position_players_for_kick_in(defending_team_side)
        
    def _position_players_for_kick_in(self, defending_team_side: str) -> None:
        """Position players appropriately for a kick-in"""
        # Get attacking and defending teams
        defending_players = self.home_team_players if defending_team_side == 'home' else self.away_team_players
        attacking_players = self.away_team_players if defending_team_side == 'home' else self.home_team_players
        
        # Set base positions based on which end the kick-in is from
        if defending_team_side == 'home':
            def_base_x = 15  # 15 meters out
            atk_base_x = 25  # 25 meters out
        else:
            def_base_x = self.ground.length - 15
            atk_base_x = self.ground.length - 25
            
        # Position defending team (taking kick-in)
        for position, player in defending_players.items():
            if player == self.state.ball_carrier:
                continue  # Skip kick-in taker, already positioned
                
            # Spread players across the defensive 50
            if position in ['LB', 'RB']:
                y_pos = self.ground.width * (0.3 if 'L' in position else 0.7)
                player.current_position = (def_base_x, y_pos)
            else:
                # Other players spread wider
                y_pos = random.uniform(0.2, 0.8) * self.ground.width
                x_pos = def_base_x + random.uniform(-5, 5)
                player.current_position = (x_pos, y_pos)
                
        # Position attacking team (defending kick-in)
        for position, player in attacking_players.items():
            # Position forwards on defensive 50 arc
            if position in ['LF', 'RF', 'CHF']:
                y_pos = self.ground.width * (0.3 if 'L' in position else 0.7)
                player.current_position = (atk_base_x, y_pos)
            else:
                # Other players spread across 50m arc
                y_pos = random.uniform(0.2, 0.8) * self.ground.width
                x_pos = atk_base_x + random.uniform(-5, 5)
                player.current_position = (x_pos, y_pos)

    def _initialize_positions(self) -> None:
        """Initialize player positions"""
        # Position home team players
        for position, player in self.home_team_players.items():
            base_pos = self.position_manager.get_base_position(
                position,
                'home'
            )
            player.current_position = base_pos
            
        # Position away team players
        for position, player in self.away_team_players.items():
            base_pos = self.position_manager.get_base_position(
                position,
                'away'
            )
            player.current_position = base_pos
            
    def _setup_center_bounce(self) -> None:
        """Setup for center bounce"""
        self.state.phase = 'center_bounce'
        self.state.ball_carrier = None
        self.state.ball_position = (self.ground.length // 2, self.ground.width // 2)
        
        # Position players in center bounce formation
        self._position_players_in_formation('center_bounce')
        
    def _position_players_in_formation(self, formation: str) -> None:
        """Position all players according to formation"""
        # Position home team players
        for position, player in self.home_team_players.items():
            formation_pos = self.position_manager.get_formation_position(
                position,
                formation,
                'home'
            )
            player.current_position = formation_pos
            
        # Position away team players
        for position, player in self.away_team_players.items():
            formation_pos = self.position_manager.get_formation_position(
                position,
                formation,
                'away'
            )
            player.current_position = formation_pos
            
    def _update_positions(self) -> Optional[Dict[str, Any]]:
        """Update all player positions and ball position"""
        #print("\n=== Starting Position Update ===")
        #print(f"Current Phase: {self.state.phase}")
        #print(f"Ball Carrier: {self.state.ball_carrier.name if self.state.ball_carrier else 'None'}")
        position_updates = []

        # Update ball position first
        if self.state.ball_carrier:
            # Ball should always be exactly where the carrier is
            print(f"Ball carrier position: {self.state.ball_carrier.current_position}")
            self.state.ball_position = self.state.ball_carrier.current_position
            
            # Check if ball carrier has run out of bounds
            if not self.ground.is_in_bounds(self.state.ball_position):
                # Ball carrier has run out - throw in
                print("Ball carrier out of bounds - transitioning to throw in")
                self.state.phase = 'throw_in'
                # Move ball position to boundary where it went out
                x, y = self.state.ball_position
                x = max(0, min(x, self.ground.length))
                y = max(0, min(y, self.ground.width))
                self.state.ball_position = (x, y)
                self.state.ball_carrier = None
            
        elif self.state.phase == 'contest':
            # During contest, ball stays at contest location
            # If somehow we lost the ball position during contest (shouldn't happen),
            # use the last known position of the last possession
            if not self.state.ball_position and self.state.last_possession:
                print("Contest phase - using last possession position")
                self.state.ball_position = self.state.last_possession.current_position
        elif self.state.phase == 'loose_ball':
            if self.state.ball_position:
                # Ball bobbles around more realistically
                momentum_x = random.uniform(-0.8, 0.8)
                momentum_y = random.uniform(-0.8, 0.8)
                self.state.ball_position = (
                    self.state.ball_position[0] + momentum_x,
                    self.state.ball_position[1] + momentum_y
                )
        """        
        elif self.state.phase == 'throw_in':
            print("Throw in phase - using boundary line")
            # Ball position should already be set to boundary line
            pass
            
        elif self.state.phase == 'kick_in':
            print("Kick in phase - using boundary line")
            # Ball position should be at point of going out on the full
            pass
            
        # Ball must always have a position - if we somehow got here without one,
        # this is a bug that needs to be fixed in the calling code
        print(f"Current ball position: {self.state.ball_position}")
        assert self.state.ball_position is not None, "Ball position should never be None"
        """
        # Add ball position to updates
        position_updates.append({
            'player_name': 'ball',
            'team': None,
            'position': {
                'x': self.state.ball_position[0],
                'y': self.state.ball_position[1]
            }
        })


        # Update player positions based on roles and game state
        for player in list(self.home_team_players.values()) + list(self.away_team_players.values()):
            if not player.current_position:
                #print(f"Player {player.name} has no current position")
                continue

            new_pos = self._calculate_player_movement(player)
            player.current_position = new_pos
            
            position_updates.append({
                'player_name': player.name,
                'team': player.team_side,
                'position': {'x': new_pos[0], 'y': new_pos[1]}
            })

        # Create position update event
        if position_updates:
            return self.event_manager.create_event(
                'position_update',
                self.state.quarter,
                'position_update',
                positions=position_updates
            )
        return None

    def _calculate_player_movement(self, player: Any) -> Tuple[float, float]:
        """Calculate new position for a player based on role and game state"""
        if not player.current_position:
            return self.position_manager.get_base_position(player.position, player.team_side)
            
        current_x, current_y = player.current_position
        ball_x, ball_y = self.state.ball_position if self.state.ball_position else (self.ground.length/2, self.ground.width/2)
        
        # Get base position for structure
        base_x, base_y = self.position_manager.get_base_position(player.position, player.team_side)
        
        # Check for tackle opportunities first
        if self.state.ball_carrier and self.state.ball_carrier.team_side != player.team_side:
            # Only attempt tackles if we're close enough and it makes sense
            if self._should_attempt_tackle(player, self.state.ball_carrier):
                tackle_result = self._process_tackle_attempt(player, self.state.ball_carrier)
                if tackle_result:
                    # Tackle successful, stay in current position
                    return player.current_position
                    
        # Ball carrier movement
        if player == self.state.ball_carrier:
            return self._calculate_ball_carrier_movement(player)
            
        # Role-specific movement patterns
        movement_vector = self._get_role_based_movement(
            player, 
            (ball_x, ball_y), 
            (base_x, base_y)
        )
        
        # Apply team tactics
        movement_vector = self._apply_tactical_movement(
            movement_vector,
            player
        )
        
        # Apply player attributes
        movement_vector = self._apply_player_attributes(
            movement_vector,
            player
        )
        
        # Calculate final position
        new_x = current_x + movement_vector[0]
        new_y = current_y + movement_vector[1]
        
        # Ensure position is valid
        return self.position_manager.get_valid_position((new_x, new_y))

    def _get_role_based_movement(
        self,
        player: Any,
        ball_pos: Tuple[float, float],
        base_pos: Tuple[float, float]
    ) -> Tuple[float, float]:
        """Get movement vector based on player's role and game context"""
        ball_x, ball_y = ball_pos
        base_x, base_y = base_pos
        current_x, current_y = player.current_position
        
        # Get opponent team's players
        opponents = self.away_team_players.values() if player.team_side == 'home' else self.home_team_players.values()
        
        # Find nearest opponent
        nearest_opponent = None
        nearest_dist = float('inf')
        for opp in opponents:
            if not opp.current_position:
                continue
            dist = math.sqrt(
                (current_x - opp.current_position[0])**2 + 
                (current_y - opp.current_position[1])**2
            )
            if dist < nearest_dist:
                nearest_dist = dist
                nearest_opponent = opp

        # Calculate which third of the ground the ball is in
        ball_third = 'forward' if (
            (player.team_side == 'home' and ball_x > self.ground.length * 0.66) or
            (player.team_side == 'away' and ball_x < self.ground.length * 0.33)
        ) else 'defensive' if (
            (player.team_side == 'home' and ball_x < self.ground.length * 0.33) or
            (player.team_side == 'away' and ball_x > self.ground.length * 0.66)
        ) else 'midfield'

        # Initialize movement weights
        ball_weight = 0.0
        structure_weight = 0.0
        opponent_weight = 0.0
        
        # Midfield positions
        if player.position in ['Ruck', 'RuckRover', 'Rover', 'Centre']:
            if ball_third == 'midfield':
                ball_weight = 0.8
                structure_weight = 0.1
                opponent_weight = 0.1
            else:
                ball_weight = 0.6
                structure_weight = 0.3
                opponent_weight = 0.1
                
        # Forward positions
        elif player.position in ['FF', 'CHF', 'LF', 'RF', 'LHF', 'RHF']:
            if ball_third == 'forward':
                ball_weight = 0.5
                structure_weight = 0.3
                opponent_weight = 0.2
            elif ball_third == 'midfield':
                ball_weight = 0.3
                structure_weight = 0.5
                opponent_weight = 0.2
            else:  # Ball in defensive third
                ball_weight = 0.2
                structure_weight = 0.7
                opponent_weight = 0.1
                
        # Defensive positions
        elif player.position in ['FB', 'CHB', 'LB', 'RB', 'LHB', 'RHB']:
            if ball_third == 'defensive':
                ball_weight = 0.5
                structure_weight = 0.2
                opponent_weight = 0.3
            elif ball_third == 'midfield':
                ball_weight = 0.3
                structure_weight = 0.4
                opponent_weight = 0.3
            else:  # Ball in forward third
                ball_weight = 0.2
                structure_weight = 0.5
                opponent_weight = 0.3
                
        # Wing positions
        else:  # LWing, RWing
            if ball_third == 'midfield':
                ball_weight = 0.5
                structure_weight = 0.4
                opponent_weight = 0.1
            else:
                ball_weight = 0.4
                structure_weight = 0.5
                opponent_weight = 0.1

        # Calculate vectors
        dx_ball = ball_x - current_x
        dy_ball = ball_y - current_y
        
        dx_structure = base_x - current_x
        dy_structure = base_y - current_y
        
        # Calculate opponent vector if there is a nearby opponent
        dx_opponent = 0
        dy_opponent = 0
        if nearest_opponent and nearest_opponent.current_position:
            if nearest_dist < 5:  # Only consider very close opponents
                dx_opponent = nearest_opponent.current_position[0] - current_x
                dy_opponent = nearest_opponent.current_position[1] - current_y
                # Reverse direction to move away from opponent
                dx_opponent = -dx_opponent
                dy_opponent = -dy_opponent

        # Normalize vectors
        ball_dist = math.sqrt(dx_ball**2 + dy_ball**2)
        if ball_dist > 0:
            dx_ball /= ball_dist
            dy_ball /= ball_dist
            
        structure_dist = math.sqrt(dx_structure**2 + dy_structure**2)
        if structure_dist > 0:
            dx_structure /= structure_dist
            dy_structure /= structure_dist
            
        opponent_dist = math.sqrt(dx_opponent**2 + dy_opponent**2)
        if opponent_dist > 0:
            dx_opponent /= opponent_dist
            dy_opponent /= opponent_dist

        # Combine vectors with weights
        dx = (dx_ball * ball_weight + 
              dx_structure * structure_weight + 
              dx_opponent * opponent_weight)
        dy = (dy_ball * ball_weight + 
              dy_structure * structure_weight + 
              dy_opponent * opponent_weight)

        # Get player speed
        speed = self._get_player_speed(player)
        
        # Normalize final vector
        final_dist = math.sqrt(dx**2 + dy**2)
        if final_dist > 0:
            dx = (dx / final_dist) * speed
            dy = (dy / final_dist) * speed
            
        return (dx, dy)

    def _apply_tactical_movement(
        self,
        movement_vector: Tuple[float, float],
        player: Any
    ) -> Tuple[float, float]:
        """Apply team tactical modifications to movement"""
        dx, dy = movement_vector
        tactics = player.team.tactics
        
        # Get current field position
        if not player.current_position:
            return movement_vector
            
        current_x, current_y = player.current_position
        
        # Determine field position context
        field_third = current_x / self.ground.length
        if player.team_side == 'away':
            field_third = 1 - field_third
            
        # Determine if we're attacking or defending based on ball position
        if not self.state.ball_position:
            return movement_vector
            
        ball_x = self.state.ball_position[0]
        attacking = (
            (player.team_side == 'home' and ball_x > self.ground.length * 0.6) or
            (player.team_side == 'away' and ball_x < self.ground.length * 0.4)
        )
        
        # Apply mentality-based modifications
        if tactics['mentality'] == 'attacking':
            if attacking:
                # More aggressive forward movement when attacking
                dx *= 1.3
                # Wider spread in attack
                if current_y < self.ground.width/2:
                    dy -= 0.3  # Drift wider on the left
                else:
                    dy += 0.3  # Drift wider on the right
            else:
                # Still maintain some attacking intent when defending
                dx *= 1.1
        elif tactics['mentality'] == 'defensive':
            if not attacking:
                # More conservative when defending
                dx *= 0.8
                # Stay more compact
                if current_y < self.ground.width/2:
                    dy += 0.2  # Move inward on the left
                else:
                    dy -= 0.2  # Move inward on the right
                    
        # Apply role-specific tactical adjustments
        if player.position in ['Ruck', 'RuckRover', 'Rover', 'Centre']:
            # Midfielders maintain central positions more
            dy *= 0.8  # Reduce lateral movement
        elif player.position in ['FF', 'CHF', 'LF', 'RF']:
            if attacking:
                # Forwards make more dynamic runs in attack
                dx *= random.uniform(0.8, 1.2)
                dy *= random.uniform(0.8, 1.2)
        elif player.position in ['FB', 'CHB', 'LB', 'RB']:
            if not attacking:
                # Defenders hold position more when defending
                dx *= 0.7
                dy *= 0.7
                
        # Apply pressure-based modifications
        pressure = self._calculate_pressure(player)
        if pressure > 0.7:
            # Under high pressure, movement becomes more erratic
            dx *= random.uniform(0.7, 1.3)
            dy *= random.uniform(0.7, 1.3)
            
        # Ensure movement stays within reasonable bounds
        max_movement = 3.0  # Maximum movement per update
        movement_magnitude = math.sqrt(dx*dx + dy*dy)
        if movement_magnitude > max_movement:
            dx = (dx / movement_magnitude) * max_movement
            dy = (dy / movement_magnitude) * max_movement
            
        return (dx, dy)

    def _apply_player_attributes(
        self,
        movement_vector: Tuple[float, float],
        player: Any
    ) -> Tuple[float, float]:
        """Modify movement based on player attributes"""
        dx, dy = movement_vector
        
        # Apply stamina/fatigue effects
        if hasattr(player, 'stamina'):
            stamina_factor = max(0.5, player.stamina / 100)
            dx *= stamina_factor
            dy *= stamina_factor
            
        # Apply agility for direction changes
        agility_factor = player.physical_stats.agility / 20
        random_movement = random.uniform(-0.5, 0.5) * agility_factor
        dx += random_movement
        dy += random_movement
        
        # Apply tactical awareness for positioning
        if hasattr(player, 'mental_stats'):
            tactical_factor = player.mental_stats.tactical / 20
            # Better tactical awareness = better positioning
            dx *= (1 + tactical_factor * 0.2)
            dy *= (1 + tactical_factor * 0.2)
            
        return (dx, dy)

    def _get_player_speed(self, player: Any) -> float:
        """Calculate player's current movement speed"""
        base_speed = player.physical_stats.speed / 10
        
        # Modify speed based on game state
        if self.state.phase == 'contest' and self._is_near_ball(player):
            base_speed *= 1.2  # Faster when contesting
        elif self.state.phase == 'loose_ball' and self._is_near_ball(player):
            base_speed *= 1.3  # Even faster when ball is loose
        
        # Apply fatigue
        if hasattr(player, 'stamina'):
            stamina_factor = max(0.6, player.stamina / 100)
            base_speed *= stamina_factor
            
        return min(base_speed, 3.0)  # Cap maximum speed

    def _calculate_ball_carrier_movement(self, player: Any) -> Tuple[float, float]:
        """Calculate movement for ball carrier"""
        current_x, current_y = player.current_position
        goal_x = self.ground.length if player.team_side == 'home' else 0
        goal_y = self.ground.width / 2
        
        # Calculate distance to goal
        distance_to_goal = math.sqrt((goal_x - current_x)**2 + (goal_y - current_y)**2)
        
        # If should make a play, don't move
        if self._should_make_play_decision(player):
            self.state.phase = 'open_play'
            return current_x, current_y
            
        # Calculate movement vector
        dx = (goal_x - current_x)
        dy = (goal_y - current_y)
        
        # Normalize and apply speed
        distance = math.sqrt(dx*dx + dy*dy)
        if distance > 0:
            speed = self._get_player_speed(player)
            dx = (dx / distance) * speed
            dy = (dy / distance) * speed
            
        # Add evasive movement based on pressure and agility
        pressure = self._calculate_pressure(player)
        if pressure > 0.3:
            evasion = player.physical_stats.agility / 20
            dx += random.uniform(-evasion, evasion)
            dy += random.uniform(-evasion, evasion)
            
        # Slow down when near goal
        if distance_to_goal < 30:
            dx *= 0.7
            dy *= 0.7
            
        # Avoid boundaries
        if current_y < 5:
            dy = max(dy, 1)  # Force movement away from boundary
        elif current_y > self.ground.width - 5:
            dy = min(dy, -1)  # Force movement away from boundary
            
        return self.position_manager.get_valid_position((current_x + dx, current_y + dy))

    def _should_make_play_decision(self, player: Any) -> bool:
        """Determine if ball carrier should make a play based on various factors"""
        if not player.current_position:
            return False
            
        # Calculate base decision threshold based on mental stats
        decision_threshold = 0.5 - (player.ability_stats.mental / 20)  # Better decision making = more likely to make good choices
        
        # Factor in player fatigue
        if hasattr(player, 'stamina'):
            decision_threshold += (100 - player.physical_stats.stamina) / 200  # More fatigue = more likely to dispose
        
        # Calculate pressure and modify threshold
        pressure = self._calculate_pressure(player)
        if pressure > 0.7:  # High pressure
            decision_threshold *= 0.5  # Much more likely to make a decision under pressure
        
        # Check scoring opportunity based on position and role
        distance_to_goal = self._calculate_distance_to_goal(player.current_position, player.team_side)
        if player.position in ['FF', 'CHF', "RF", 'LF', 'LHF', 'RHF']:  # Forward positions
            if distance_to_goal < 55:  # Longer range for forwards
                decision_threshold *= 0.6  # More likely to take the shot
        else:  # Non-forwards
            if distance_to_goal < 40:  # Shorter range for non-forwards
                decision_threshold *= 0.8  # Less likely to take the shot than forwards
        
        # Consider tactical awareness for boundary situations
        x, y = player.current_position
        if y < 10 or y > self.ground.width - 10:  # Near boundary
            tactical_modifier = player.ability_stats.tactical / 20
            decision_threshold *= (1 - tactical_modifier)  # Better tactical awareness = better boundary decisions
        
        # Factor in game context
        score_difference = abs(self.state.score['home']['total'] - self.state.score['away']['total'])
        if score_difference > 24:  # If more than 2 goals difference
            if player.team_side == self._get_leading_team():
                decision_threshold *= 1.2  # Leading team more likely to make conservative decisions
            else:
                decision_threshold *= 0.8  # Trailing team more likely to take risks
        
        # Random element influenced by player's composure
        random_factor = random.random() * (1 - (player.ability_stats.consistency / 20))
        
        return random_factor < decision_threshold

    def _get_leading_team(self) -> str:
        """Helper to determine which team is leading"""
        return 'home' if self.state.score['home']['total'] > self.state.score['away']['total'] else 'away'

    def _is_near_ball(self, player: Any) -> bool:
        """Simple check if player is near the ball"""

        if not player.current_position or not self.state.ball_position:
            return False
            
        dx = player.current_position[0] - self.state.ball_position[0]
        dy = player.current_position[1] - self.state.ball_position[1]
        distance = math.sqrt(dx*dx + dy*dy)
        print(f"Distance to ball: {distance}")
        return distance < 10  # Contest radius in meters

    def _get_primary_ruck(self, team_side: str) -> Any:
        """Get team's primary ruckman"""
        players = self.home_team_players if team_side == 'home' else self.away_team_players
        
        # Find all players in the RUCK position
        rucks = [(pos, player) for pos, player in players.items() if pos == 'Ruck']
        
        if not rucks:
            # If no ruck found, use tallest player
            return max(players.values(), key=lambda p: p.physical_stats.height)
            
        # If multiple rucks, use the one with best marking stat
        return max(rucks, key=lambda r: r[1].ability_stats.marking)[1]
        
    def _decide_player_action(self, player: Any) -> Dict[str, Any]:
        """Decide what action a player should take"""
        if not player.current_position:
            print(f"Player {player.name} has no current position")
            return {'action': 'none'}
            
        # Calculate base kicking distance based on goal_kicking stat
        # Even poor kickers can kick 20m, great kickers up to 60m
        base_kicking_distance = 20 + (player.ability_stats.goal_kicking / 20) * 40
        
        # Calculate distance to goal
        distance = self._calculate_distance_to_goal(
            player.current_position,
            player.team_side
        )
        print(f"Distance to goal: {distance}")
        # Calculate angle
        x, y = player.current_position
        goal_y = self.ground.width / 2
        angle = abs(y - goal_y) / distance if distance > 0 else float('inf')
        
        # Get pressure
        pressure = self._calculate_pressure(player)
        
        # Decision making influenced by mental stats and pressure
        mental_clarity = max(0.2, (player.ability_stats.mental / 20) * (1 - pressure * 0.5))
        print(f"Mental clarity: {mental_clarity}")
        
        # Possible actions with their base probabilities
        actions = {
            'kick_for_goal': 0.0,
            'kick_to_player': 0.0,
            'kick_clear': 0.0,
            'handball': 0.0,
            'panic_kick': 0.0,
            'bounce': 0.0
        }
        
        # Shooting for goal logic
        if distance <= base_kicking_distance and angle < 0.5:
            actions['kick_for_goal'] = 0.8 * (1 - angle) * (1 - distance/base_kicking_distance)
            print(f"Kick for goal: {actions['kick_for_goal']}")
        elif distance <= base_kicking_distance * 1.2:  # Allow for "miracle" shots
            actions['kick_for_goal'] = 0.2 * (1 - angle) * (1 - distance/base_kicking_distance)
            print(f"Kick for goal (miracle): {actions['kick_for_goal']}")
            if random.random() < 0.1:  # 10% chance of attempting a miracle
                return {
                    'action': 'kick_for_goal',
                    'commentary': random.choice([
                        f"{player.name} goes for glory from downtown!",
                        f"That's ambitious from {player.name}! The coach won't be happy if this doesn't come off!",
                        f"{player.name} thinks he's Buddy Franklin! Let's see how this goes...",
                        f"Is {player.name} crazy or brilliant? We're about to find out!",
                        f"The crowd roars as {player.name} winds up from long range!"
                    ])
                }
        
        # Pressure-based decisions
        if pressure > 0.8:  # Very high pressure
            actions['handball'] = 0.5 * mental_clarity
            actions['kick_clear'] = 0.3
            actions['panic_kick'] = 0.2
            
            if random.random() > mental_clarity:  # Player panics
                print(f"Player {player.name} panics")
                return {
                    'action': 'panic_kick',
                    'commentary': random.choice([
                        f"{player.name} just throws it on the boot in panic!",
                        f"The pressure gets to {player.name}! That kick could go anywhere!",
                        f"{player.name}'s kicked that like he's trying to get rid of a hot potato!",
                        f"Not sure what {player.name} was thinking there - probably wasn't!",
                        f"That's what we call a 'hope and pray' kick from {player.name}!"
                    ])
                }
        elif pressure > 0.5:  # Moderate pressure
            actions['handball'] = 0.4 * mental_clarity
            actions['kick_to_player'] = 0.4 * mental_clarity
            actions['kick_clear'] = 0.2
        else:  # Low pressure
            actions['kick_to_player'] = 0.6 * mental_clarity
            actions['handball'] = 0.3 * mental_clarity
            actions['bounce'] = 0.1
        
        # Choose action based on weighted probabilities
        action = max(actions.items(), key=lambda x: x[1])[0]
        print(f"Action: {action}")
        # Generate appropriate commentary
        commentary = self._get_action_commentary(action, player, pressure, mental_clarity)
        print(f"Commentary: {commentary}")
        return {'action': action, 'commentary': commentary}
        
    def _get_action_commentary(self, action: str, player: Any, pressure: float, mental_clarity: float) -> str:
        """Generate contextual commentary for player actions"""
        if action == 'kick_to_player':
            if mental_clarity > 0.8:
                return random.choice([
                    f"{player.name} spots up a target with precision!",
                    f"Beautiful vision from {player.name}!",
                    f"That's what you expect from a player of {player.name}'s caliber!",
                ])
            else:
                return random.choice([
                    f"{player.name} looks for options...",
                    f"{player.name} goes for a teammate...",
                    f"Let's see if {player.name} can find a target...",
                ])
        elif action == 'handball':
            if pressure > 0.8:
                return random.choice([
                    f"{player.name} gets it out quickly under immense pressure!",
                    f"Somehow {player.name} gets the handball away!",
                    f"Quick hands from {player.name} in traffic!",
                ])
            else:
                return random.choice([
                    f"Clean handball from {player.name}",
                    f"{player.name} dishes it off",
                    f"Neat hands from {player.name}",
                ])
        elif action == 'kick_clear':
            return random.choice([
                f"{player.name} just boots it clear of the pack!",
                f"Safety first from {player.name}!",
                f"{player.name} sends it long and hopes for the best!",
            ])
        elif action == 'bounce':
            return random.choice([
                f"{player.name} takes on the defender!",
                f"Showing some flair, {player.name} bounces the ball!",
                f"{player.name} tries to create some space!",
            ])
        return ""

    def _calculate_target_score(self, kicker: Any, target: Any, phase: str) -> float:
        """Calculate how good a target player is"""
        if not kicker.current_position or not target.current_position:
            return 0
            
        # Base score from marking ability and mental stats
        score = (target.ability_stats.marking + target.ability_stats.tactical) / 40
        
        # Distance factor - minimum 10m for a mark
        dx = target.current_position[0] - kicker.current_position[0]
        dy = target.current_position[1] - kicker.current_position[1]
        distance = math.sqrt(dx*dx + dy*dy)
        
        # Distance scoring
        if distance < 10:
            score *= 0.3  # Too close for a mark
        elif distance > 50:
            score *= 0.5  # Very long kick, higher risk
            
        # Factor in kicker's vision/decision making under pressure
        pressure = self._calculate_pressure(kicker)
        vision_factor = max(0.2, (kicker.ability_stats.tactical / 20) * (1 - pressure * 0.7))
        score *= vision_factor
        
        # Add some randomness - players don't always see the best option
        score *= random.uniform(0.7, 1.0)
        
        # Bias towards forward movement
        if kicker.team_side == 'home' and dx > 0:
            score *= 1.2
        elif kicker.team_side == 'away' and dx < 0:
            score *= 1.2
            
        return score

    def _calculate_pressure(self, player: Any) -> float:
        """Calculate pressure on a player"""
        if not player.current_position:
            return 0
            
        # Get opponents
        opponents = self.away_team_players.values() if player.team_side == 'home' else self.home_team_players.values()
        
        total_pressure = 0
        for opponent in opponents:
            if not opponent.current_position:
                continue
                
            # Calculate distance
            dx = opponent.current_position[0] - player.current_position[0]
            dy = opponent.current_position[1] - player.current_position[1]
            distance = math.sqrt(dx*dx + dy*dy)
            
            # Add pressure based on distance and opponent's tackling ability
            if distance < 2:
                pressure = (2 - distance) * opponent.ability_stats.tackling / 20
                total_pressure += pressure
                
        return min(total_pressure, 1.0)
    
    def _calculate_distance_to_goal(self, position: Optional[Tuple[int, int]], team_side: str) -> float:
        """Calculate distance to goal"""
        if not position:
            return float('inf')
            
        x, y = position
        if team_side == 'home':
            goal_x = self.ground.length
        else:
            goal_x = 0
        goal_y = self.ground.width / 2
        
        return math.sqrt((x - goal_x)**2 + (y - goal_y)**2)
        
    def _get_quarter_name(self, quarter: int) -> str:
        """Get quarter name"""
        if quarter == 1:
            return "first"
        elif quarter == 2:
            return "second"
        elif quarter == 3:
            return "third"
        else:
            return "fourth"
            
    async def _process_quarter_break(self) -> None:
        """Process quarter break"""
        # Update player fatigue
        for player in list(self.home_team_players.values()) + list(self.away_team_players.values()):
            player.fatigue = max(0, player.fatigue - 30)  # Recover some fatigue
            
        # Update tactics based on score
        score_diff = (
            self.state.score['home']['total'] -
            self.state.score['away']['total']
        )
        self.home_coordinator.update_tactics(score_diff)
        self.away_coordinator.update_tactics(-score_diff)
        
        # Pause for break
        await asyncio.sleep(1)  # 1 second break between quarters

    def _get_players_in_contest(self) -> List[Any]:
        """Get players involved in a contest"""
        if not self.state.ball_position:
            print("No ball position set - staying in current position")
            """Should never happen!!!!"""
            sys.exit(1)
            #return []
            
        contest_radius = 5  # 5 meters
        players = []
        
        # Check all players
        for player in list(self.home_team_players.values()) + list(self.away_team_players.values()):
            if not player.current_position:
                continue
                
            # Calculate distance to ball
            dx = player.current_position[0] - self.state.ball_position[0]
            dy = player.current_position[1] - self.state.ball_position[1]
            distance = math.sqrt(dx*dx + dy*dy)
            
            if distance <= contest_radius:
                players.append(player)
                
        return sorted(
            players,
            key=lambda p: (p.physical_stats.strength + p.ability_stats.tackling) / 2,
            reverse=True
        )

    async def _process_throw_in(self) -> Optional[Dict[str, Any]]:
        """Process throw in phase after ball goes out of bounds"""
        # Find closest players from each team
        team_1_players = [p for p in self.state.players if p.team_side == 'team_1']
        team_2_players = [p for p in self.state.players if p.team_side == 'team_2']
        
        closest_team_1 = min(team_1_players, key=lambda p: self._distance(p.current_position, self.state.ball_position))
        closest_team_2 = min(team_2_players, key=lambda p: self._distance(p.current_position, self.state.ball_position))
        
        # Move players into position
        throw_in_x = self.state.ball_position[0]
        throw_in_y = max(2, min(self.ground.width - 2, self.state.ball_position[1]))
        
        closest_team_1.current_position = (throw_in_x, throw_in_y - 1)
        closest_team_2.current_position = (throw_in_x, throw_in_y + 1)
        
        # Determine who wins the throw in (50-50 chance)
        winner = random.choice([closest_team_1, closest_team_2])
        
        # Set game state
        self.state.ball_carrier = winner
        self.state.ball_position = winner.current_position
        self.state.phase = 'open_play'
        self.state.last_possession = winner
        
        # Create event
        event = self.event_manager.create_event(
            'throw_in',
            self.state.quarter,
            player=winner.name
        )
        return event
        
    def _find_kick_target(self, kicker: Any) -> Optional[Dict[str, Any]]:
        """Find best target for a kick based on player stats and field position"""
        if not kicker.current_position:
            return None
            
        # Get potential targets from kicker's team
        potential_targets = [
            p for p in (self.home_team_players.values() if kicker.team_side == 'home' else self.away_team_players.values())
            if p.id != kicker.id and p.current_position
        ]
        
        if not potential_targets:
            return None
            
        # Calculate kick range based on player stats
        base_kick_distance = 20 + (kicker.ability_stats.kicking / 20) * 40  # 20-60m range
        print(f"Kicker {kicker.name} base kick distance: {base_kick_distance}m")
        kick_accuracy = kicker.ability_stats.kicking / 20  # 0-1 range
        
        best_target = None
        best_score = -1
        
        # Get kicker's goal direction
        attacking_goal_x = self.ground.length if kicker.team_side == 'home' else 0
        
        for target in potential_targets:
            # Calculate distance and angles
            dx = target.current_position[0] - kicker.current_position[0]
            dy = target.current_position[1] - kicker.current_position[1]
            distance = math.sqrt(dx*dx + dy*dy)
            
            # Skip if too close (less than 10m) unless under extreme pressure
            kicker_pressure = self._calculate_pressure(kicker)
            if distance < 10 and kicker_pressure < 0.8:
                print(f"Target {target.name} too close ({distance}m) and not under pressure")
                continue
                
            # Skip if too far
            if distance > base_kick_distance:
                print(f"Target {target.name} too far ({distance}m)")
                continue
                
            # Calculate various factors for scoring
            
            # Distance factor - prefer kicks between 20-40m
            optimal_distance = 30
            distance_factor = 1 - abs(distance - optimal_distance) / base_kick_distance
            
            # Forward progress factor - prefer targets closer to goal
            target_to_goal = abs(target.current_position[0] - attacking_goal_x)
            kicker_to_goal = abs(kicker.current_position[0] - attacking_goal_x)
            forward_progress = 1.0 if target_to_goal < kicker_to_goal else 0.3
            
            # Target's marking ability factor
            marking_factor = target.ability_stats.marking / 20
            
            # Space factor - how much space target has
            target_pressure = self._calculate_pressure(target)
            space_factor = 1 - target_pressure
            
            # Leading factor - is target moving into space
            if hasattr(target, 'previous_position') and target.previous_position:
                movement_vector = (
                    target.current_position[0] - target.previous_position[0],
                    target.current_position[1] - target.previous_position[1]
                )
                # Reward targets moving into space
                leading_factor = 1.2 if space_factor > 0.7 and movement_vector[0] != 0 else 1.0
            else:
                leading_factor = 1.0
                
            # Tactical factor - consider game situation
            tactical_factor = 1.0
            if self.state.score and kicker.team_side in self.state.score:
                score_diff = (
                    self.state.score['home']['total'] - 
                    self.state.score['away']['total']
                )
                if (kicker.team_side == 'home' and score_diff < 0) or \
                (kicker.team_side == 'away' and score_diff > 0):
                    # Behind in score - reward more aggressive options
                    tactical_factor = 1.3 if target_to_goal < 50 else 1.0
            
            # Calculate final score
            score = (
                distance_factor * 0.2 +
                forward_progress * 0.3 +
                marking_factor * 0.2 +
                space_factor * 0.2 +
                leading_factor * 0.1
            ) * tactical_factor
            
            print(f"Target {target.name} score breakdown:")
            print(f"  Distance({distance:.1f}m): {distance_factor:.2f}")
            print(f"  Forward Progress: {forward_progress:.2f}")
            print(f"  Marking: {marking_factor:.2f}")
            print(f"  Space: {space_factor:.2f}")
            print(f"  Leading: {leading_factor:.2f}")
            print(f"  Final Score: {score:.2f}")
            
            if score > best_score:
                best_score = score
                accuracy_modifier = kick_accuracy * (1 - kicker_pressure * 0.5)
                best_target = {
                    'player': target,
                    'distance': distance,
                    'accuracy': accuracy_modifier,
                    'landing_pos': (
                        target.current_position[0] + random.uniform(-3, 3) * (1 - accuracy_modifier),
                        target.current_position[1] + random.uniform(-3, 3) * (1 - accuracy_modifier)
                    )
                }
        
        if best_target:
            print(f"Selected target: {best_target['player'].name} at {best_target['distance']:.1f}m")
        else:
            print(f"No target found")
        return best_target
        
    def _find_handball_target(self, handballer: Any) -> Optional[Dict[str, Any]]:
        """Find best target for a handball based on player stats and field position"""
        if not handballer.current_position:
            return None
            
        # Get potential targets from handballer's team within handball range (5m)
        potential_targets = [
            p for p in (self.home_team_players.values() if handballer.team_side == 'home' else self.away_team_players.values())
            if p.id != handballer.id and p.current_position and 
            self._distance(p.current_position, handballer.current_position) <= 5
        ]
        
        if not potential_targets:
            return None
            
        # Calculate handball accuracy based on player stats
        handball_accuracy = handballer.ability_stats.handball / 20  # 0-1 range
        
        best_target = None
        best_score = -1
        
        for target in potential_targets:
            # Calculate distance
            distance = self._distance(target.current_position, handballer.current_position)
            
            # Calculate target score
            distance_factor = 1 - (distance / 5)  # Prefer closer targets
            pressure_factor = 1 - self._calculate_pressure(target)  # Prefer less pressured targets
            
            # Factor in game direction - prefer targets moving forward
            direction_factor = 1.0
            if handballer.team_side == 'home':
                direction_factor = 1.2 if target.current_position[0] > handballer.current_position[0] else 0.8
            else:
                direction_factor = 1.2 if target.current_position[0] < handballer.current_position[0] else 0.8
                
            score = (distance_factor + pressure_factor) * direction_factor
            
            if score > best_score:
                best_score = score
                best_target = {
                    'player': target,
                    'accuracy': handball_accuracy * (1 - distance/5),
                    'landing_pos': (
                        target.current_position[0] + random.uniform(-1, 1) * (1 - handball_accuracy),
                        target.current_position[1] + random.uniform(-1, 1) * (1 - handball_accuracy)
                    )
                }
        
        return best_target
        
    def _calculate_kick_destination(self, kicker: Any, kick_type: str) -> Tuple[float, float]:
        """Calculate where a kick will land based on kick type and player stats"""
        if not kicker.current_position:
            return kicker.current_position
            
        # Base distances and accuracies
        if kick_type == 'kick_clear':
            base_distance = 30 + (kicker.ability_stats.kicking / 20) * 15  # 30-45m
            accuracy_factor = kicker.ability_stats.kicking / 40  # Halved accuracy for clearing kicks
        elif kick_type == 'panic_kick':
            base_distance = 20 + (kicker.ability_stats.kicking / 20) * 10  # 20-30m
            accuracy_factor = kicker.ability_stats.kicking / 60  # Third accuracy for panic kicks
        else:
            base_distance = 20 + (kicker.ability_stats.kicking / 20) * 40  # 20-60m
            accuracy_factor = kicker.ability_stats.kicking / 20
            
        # Apply pressure effects
        pressure = self._calculate_pressure(kicker)
        distance_modifier = 1 - (pressure * 0.3)
        accuracy_modifier = 1 - (pressure * 0.5)
        
        # Calculate actual distance and deviation
        actual_distance = base_distance * distance_modifier * random.uniform(0.8, 1.2)
        deviation = (20 * (1 - accuracy_factor * accuracy_modifier)) * random.uniform(-1, 1)
        
        # Calculate target position
        if kicker.team_side == 'home':
            target_x = kicker.current_position[0] + actual_distance
        else:
            target_x = kicker.current_position[0] - actual_distance
            
        target_y = kicker.current_position[1] + deviation
        
        return (target_x, target_y)
        
    def _move_ball_to_position(self, target_pos: Tuple[float, float], speed: float = 1.0) -> None:
        """Move the ball towards a target position"""
        if not self.state.ball_position:
            self.state.ball_position = target_pos
            return
            
        # Calculate direction vector
        dx = target_pos[0] - self.state.ball_position[0]
        dy = target_pos[1] - self.state.ball_position[1]
        distance = math.sqrt(dx*dx + dy*dy)
        
        if distance < 0.1:
            self.state.ball_position = target_pos
            return
            
        # Move ball portion of distance based on speed
        move_factor = min(1.0, speed)
        new_x = self.state.ball_position[0] + (dx * move_factor)
        new_y = self.state.ball_position[1] + (dy * move_factor)
        
        # Ensure position is in bounds
        new_x = max(0, min(new_x, self.ground.length))
        new_y = max(0, min(new_y, self.ground.width))
        
        self.state.ball_position = (new_x, new_y)

    async def _process_loose_ball(self) -> Optional[Dict[str, Any]]:
        """Process loose ball phase"""
        # First, find all players within a larger radius (30m) who could potentially contest
        potential_contestants = self._get_players_near_ball(radius=30)
        
        if not potential_contestants:
            # Ball is very isolated - move it slightly with momentum
            if self.state.ball_position:
                momentum_x = random.uniform(-0.5, 0.5)
                momentum_y = random.uniform(-0.5, 0.5)
                new_x = self.state.ball_position[0] + momentum_x
                new_y = self.state.ball_position[1] + momentum_y
                self.state.ball_position = (new_x, new_y)
            return self.event_manager.create_event(
                'loose_ball',
                self.state.quarter,
                commentary="Ball spills into space!"
            )

        # Group players by team
        home_players = [p for p in potential_contestants if p.team_side == 'home']
        away_players = [p for p in potential_contestants if p.team_side == 'away']
        
        # Calculate distances and movement ratings for all potential contestants
        player_ratings = []
        for player in potential_contestants:
            if not self.state.ball_position or not player.current_position:
                continue
                
            dx = self.state.ball_position[0] - player.current_position[0]
            dy = self.state.ball_position[1] - player.current_position[1]
            distance = math.sqrt(dx*dx + dy*dy)
            
            # Calculate movement rating based on speed, agility, and current distance
            movement_rating = (
                player.physical_stats.speed * 0.5 +
                player.physical_stats.agility * 0.3 +
                player.ability_stats.tackling * 0.2
            ) / 20
            
            # Players very close to ball (within 1m) can attempt to gather
            can_gather = distance <= 1
            
            player_ratings.append({
                'player': player,
                'distance': distance,
                'movement_rating': movement_rating,
                'can_gather': can_gather,
                'dx': dx,  # Store direction vector
                'dy': dy
            })
        
        # Sort by distance and movement rating
        player_ratings.sort(key=lambda x: (x['distance'], -x['movement_rating']))
        
        # Process players who can gather (within 1m)
        gatherers = [p for p in player_ratings if p['can_gather']]
        if gatherers:
            # Multiple players within gathering distance - contest situation
            gather_chances = []
            for data in gatherers:
                player = data['player']
                # Calculate gather rating based on relevant stats
                gather_rating = (
                    player.physical_stats.agility * 0.4 +
                    player.ability_stats.tackling * 0.3 +
                    player.physical_stats.speed * 0.2 +
                    player.ability_stats.marking * 0.1
                ) / 20
                gather_rating *= random.uniform(0.8, 1.2)
                gather_chances.append((player, gather_rating))
            
            # Sort by gather rating
            gather_chances.sort(key=lambda x: x[1], reverse=True)
            best_gatherer = gather_chances[0][0]
            gather_chance = 0.7  # Higher base chance when player is actually over the ball
            
            if random.random() < gather_chance:
                self.state.ball_carrier = best_gatherer
                self.state.phase = 'open_play'
                
                # Update stats
                self.stats_manager.update_stats(
                    'contested_possession',
                    best_gatherer,
                    best_gatherer.team_side,
                    self.state.quarter
                )
                return self.event_manager.create_event(
                    'gather',
                    self.state.quarter,
                    player=best_gatherer.name,
                    commentary=f"{best_gatherer.name} gathers cleanly!"
                )
        
        # No successful gather - update player positions for the foot race
        for data in player_ratings:
            player = data['player']
            if not player.current_position or not self.state.ball_position:
                continue
            
            # Use stored direction vector
            dx, dy = data['dx'], data['dy']
            distance = data['distance']
            
            if distance > 0:
                # Normalize vector
                dx = dx / distance
                dy = dy / distance
                
                # Calculate movement speed based on player attributes
                speed = self._get_player_speed(player)
                
                # Determine if player should take supporting position
                should_support = self._should_take_supporting_position(player, player_ratings)
                
                if should_support:
                    # Calculate support position perpendicular to ball direction
                    perp_dx = -dy  # Perpendicular vector
                    perp_dy = dx
                    support_distance = 5  # 5m support distance
                    
                    # Add support vector to movement
                    dx = dx * 0.3 + (perp_dx * support_distance / distance) * 0.7
                    dy = dy * 0.3 + (perp_dy * support_distance / distance) * 0.7
                    
                    # Renormalize
                    mag = math.sqrt(dx*dx + dy*dy)
                    if mag > 0:
                        dx = dx / mag
                        dy = dy / mag
                
                # Apply movement
                new_x = player.current_position[0] + (dx * speed)
                new_y = player.current_position[1] + (dy * speed)
                
                # Ensure position is in bounds
                new_x = max(0, min(new_x, self.ground.length))
                new_y = max(0, min(new_y, self.ground.width))
                
                player.current_position = (new_x, new_y)
        
        # Ball movement - reduce bobbling over time
        ball_momentum = 0.6
        #ball_momentum *= 0.8  # Decay factor
        ball_momentum -= 0.1
        # If momentum is very low, ball stops
        if ball_momentum < 0.1:
            ball_momentum = 0
        else:
            # Initialize momentum for new loose ball
            ball_momentum = 1.0

        # Update ball position with decreasing momentum
        if self.state.ball_position and ball_momentum > 0:
                momentum_x = random.uniform(-0.5, 0.5) * ball_momentum
                momentum_y = random.uniform(-0.5, 0.5) * ball_momentum
                new_x = self.state.ball_position[0] + momentum_x
                new_y = self.state.ball_position[1] + momentum_y
                self.state.ball_position = (new_x, new_y)
                
                # Generate appropriate commentary based on the situation
                commentary = self._get_loose_ball_commentary(player_ratings)
                
                return self.event_manager.create_event(
                    'loose_ball',
                    self.state.quarter,
                    commentary=commentary
                )

    def _should_take_supporting_position(self, player: Any, player_ratings: List[Dict]) -> bool:
        """Determine if a player should take a supporting position instead of contesting directly."""
        # Find the best-positioned teammate
        teammates = [p for p in player_ratings if p['player'].team_side == player.team_side]
        if not teammates:
            return False
            
        best_teammate = min(teammates, key=lambda x: x['distance'])
        
        # If this player isn't the closest teammate and has good tactical awareness,
        # they might take a supporting position
        if (player != best_teammate['player'] and 
            len(teammates) > 1 and 
            random.random() < 0.3 + (player.ability_stats.tactical / 20)):
            return True
        
        return False

    def _get_loose_ball_commentary(self, player_ratings: List[Dict]) -> str:
        """Generate appropriate commentary for the loose ball situation."""
        if not player_ratings:
            return "Ball spills into space!"
            
        closest_players = player_ratings[:2]  # Get two closest players
        
        if len(closest_players) == 1:
            player = closest_players[0]['player']
            return f"{player.name} racing to gather the loose ball!"
        else:
            player1 = closest_players[0]['player']
            player2 = closest_players[1]['player']
            
            if player1.team_side == player2.team_side:
                return f"{player1.name} and {player2.name} working together to secure possession!"
            else:
                return f"{player1.name} and {player2.name} in a foot race for the loose ball!"

    def _get_players_near_ball(self, radius: float = 5.0) -> List[Any]:
        """Get all players within specified radius of the ball"""
        if not self.state.ball_position:
            return []
            
        nearby_players = []
        for player in list(self.home_team_players.values()) + list(self.away_team_players.values()):
            if not player.current_position:
                continue
                
            # Calculate distance to ball
            dx = player.current_position[0] - self.state.ball_position[0]
            dy = player.current_position[1] - self.state.ball_position[1]
            distance = math.sqrt(dx*dx + dy*dy)
            
            if distance <= radius:
                nearby_players.append(player)
                
        return nearby_players

    def _process_tackle_attempt(self, tackler: Any, target: Any) -> Optional[Dict[str, Any]]:
        """Process a tackle attempt between two players"""
        if not tackler.current_position or not target.current_position:
            return None
            
        # Calculate distance between players
        dx = target.current_position[0] - tackler.current_position[0]
        dy = target.current_position[1] - tackler.current_position[1]
        distance = math.sqrt(dx*dx + dy*dy)
        
        # Only allow tackles within 2 meters
        if distance > 2:
            return None
            
        # Calculate base tackle success chance
        base_chance = tackler.ability_stats.tackling / 20  # 0-1 range
        
        # Modify based on various factors
        
        # Speed differential - faster players harder to tackle
        speed_diff = target.physical_stats.speed - tackler.physical_stats.speed
        speed_mod = max(0.5, 1 - (speed_diff / 40))  # Max 50% reduction
        
        # Fatigue factors
        tackler_fatigue_mod = max(0.6, 1 - (tackler.fatigue / 100))
        target_fatigue_mod = max(0.6, 1 - (target.fatigue / 100))
        
        # Agility factor - more agile players better at avoiding tackles
        agility_diff = target.physical_stats.agility - tackler.physical_stats.agility

    def _should_attempt_tackle(self, tackler: Any, target: Any) -> bool:
        """Determine if a tackler should attempt a tackle based on various factors"""
        if not tackler.current_position or not target.current_position:
            return False
            
        # Calculate distance between tackler and target
        dx = target.current_position[0] - tackler.current_position[0]
        dy = target.current_position[1] - tackler.current_position[1]
        distance = math.sqrt(dx*dx + dy*dy)
        
        # Only allow tackles within 2 meters
        if distance > 2:
            return False
            
        # Calculate base tackle success chance
        base_chance = tackler.ability_stats.tackling / 20  # 0-1 range
        
        # Modify based on various factors
        
        # Speed differential - faster players harder to tackle
        speed_diff = target.physical_stats.speed - tackler.physical_stats.speed
        speed_mod = max(0.5, 1 - (speed_diff / 40))  # Max 50% reduction
        
        # Fatigue factors
        tackler_fatigue_mod = max(0.6, 1 - (tackler.fatigue / 100))
        target_fatigue_mod = max(0.6, 1 - (target.fatigue / 100))
        
        # Agility factor - more agile players better at avoiding tackles
        agility_diff = target.physical_stats.agility - tackler.physical_stats.agility

DEFAULT_GROUND_CONFIG = {
    'length': 160,  # Length in meters
    'width': 120,   # Width in meters
} 