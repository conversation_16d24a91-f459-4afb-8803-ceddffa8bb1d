from django.db import models
from django.shortcuts import render, get_object_or_404
from matchengine.match_engine.match_engine_refactored_v3 import MatchEngine
from matches.models import Match
from teams.models import Team, Player
import random, sys, asyncio, traceback
from asgiref.sync import async_to_sync, sync_to_async
from channels.layers import get_channel_layer

async def simulate_match(match_id):
    # Get match data
    match = await sync_to_async(
        lambda: Match.objects.select_related('home_team__user', 'away_team__user').get(id=match_id)
    )()
    
    home_team = match.home_team
    away_team = match.away_team
    
    # Get lineups and tactics
    if match.home_team.user.is_bot or match.home_team_lineup is False:
        home_lineup_json = await set_bot_lineup(home_team)
        home_tactics = await set_bot_tactics(home_team)
    else:
        home_lineup_json = match.home_team_lineup
        home_tactics = match.home_team_tactics

    if match.away_team.user.is_bot or match.away_team_lineup is False:
        away_lineup_json = await set_bot_lineup(away_team)
        away_tactics = await set_bot_tactics(away_team)
    else:
        away_lineup_json = match.away_team_lineup
        away_tactics = match.away_team_tactics
    
    home_lineup = await prepare_lineup(home_team, home_lineup_json)
    away_lineup = await prepare_lineup(away_team, away_lineup_json)

    # Set up channel layer
    channel_layer = get_channel_layer()
    match_group_name = f"match_{match_id}"
    print(f"Models: Channel Layer ID = {id(channel_layer)}")
    
    # Wait for WebSocket connection to be established
    max_retries = 5
    for attempt in range(max_retries):
        try:
            connection = await channel_layer.connection(0)
            group_key = f"{channel_layer.prefix}:groups:{match_group_name}"
            members = await connection.smembers(group_key)
            print(f"Models: Attempt {attempt + 1} - Group members: {members}")
            
            if members:
                print(f"Models: Found group members: {members}")
                break
            
            if attempt < max_retries - 1:
                print(f"Models: No members found, waiting...")
                await asyncio.sleep(1)  # Wait 1 second before next attempt
                
        except Exception as e:
            print(f"Models: Error checking group members: {e}")
            if attempt == max_retries - 1:
                print("Models: Failed to verify group membership")
    
    # Initialize match engine with verified channel layer
    engine = MatchEngine(
        home_team, away_team, 
        home_lineup, away_lineup, 
        home_tactics, away_tactics, 
        match_group_name,
        channel_layer=channel_layer
    )
    
    # Run simulation
    try:
        async for event in engine.simulate_match():
            if event:
                yield event  # Yield the event instead of broadcasting
                await asyncio.sleep(0.1)  # Give the event loop a chance to process
    except Exception as e:
        print(f"Models: Error during match simulation: {e}")
        print(traceback.format_exc())

async def set_bot_lineup(team):
    """
    Sets a basic random lineup for a bot-controlled team in JSON format.
    """
    positions = [
        "LB", "FB", "RB",
        "LHB", "CHB", "RHB",
        "LWing", "Centre", "RWing",
        "LHF", "CHF", "RHF",
        "LF", "FF", "RF",
        "Ruck", "RuckRover", "Rover",
        #"Follower1", "Follower2", "Follower3", "Follower4", "Sub",
    ]

    # Get the players for the team
    players = await sync_to_async(
        lambda: list(Player.objects.filter(team_id=team.id))
    )()
    
    # Shuffle players and positions
    random.shuffle(players)
    random.shuffle(positions)

    # Create a JSON-like lineup format with positions as keys and player IDs as values
    lineup = {}
    for position in positions:
        if players:
            player = players.pop(0)
            lineup[position] = str(player.id)  # Ensure player ID is a string to match JSON format
    
    return lineup
    
async def set_bot_tactics(team):
    """
    Sets a random tactical strategy for a bot-controlled team.
    """
    # Possible values for each tactic
    mentalities = ['attacking', 'defensive']
    defense_strategies = ['man_mark', 'zone_mark']
    offense_strategies = ['direct', 'stay_wide']
    push_factors = ['1', '2', '3']

    # Randomly select a tactic for each strategy
    tactics = {
        'mentality': random.choice(mentalities),
        'defense_strategy': random.choice(defense_strategies),
        'offense_strategy': random.choice(offense_strategies),
        'push_factor': random.choice(push_factors),
    }

    # Convert tactics to JSON format
    #tactics_json = json.dumps(tactics)
    
    return tactics


async def prepare_lineup(team, lineup_json):
    """
    Converts JSON lineup into a dictionary of position -> Player instances.
    """
    lineup = {}

    # Convert IDs to integers for consistency
    lineup_json = {pos: int(player_id) for pos, player_id in lineup_json.items()}

    # Fetch players with related fields preloaded
    players = await sync_to_async(
        lambda: list(
            Player.objects.filter(id__in=lineup_json.values())
            .select_related("ability_stats", "physical_stats")  # Prefetch related fields
        )
    )()

    # Construct a dictionary of player ID -> Player instance
    player_dict = {player.id: player for player in players}

    # Map positions to Player instances
    for position, player_id in lineup_json.items():
        db_player = player_dict.get(player_id)
        if db_player:
            lineup[position] = Player(
                id=player_id,
                name=db_player.name,
                team=team,
                ability_stats=db_player.ability_stats,
                physical_stats=db_player.physical_stats,
            )

    return lineup



