# Generated by Django 5.0.7 on 2024-11-12 05:20

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('teams', '0022_set_empty_league_to_null'),
    ]

    operations = [
        migrations.CreateModel(
            name='Ladder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('division', models.IntegerField()),
                ('games_played', models.IntegerField(default=0)),
                ('wins', models.IntegerField(default=0)),
                ('losses', models.IntegerField(default=0)),
                ('draws', models.IntegerField(default=0)),
                ('points', models.IntegerField(default=0)),
                ('percentage', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('team', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='ladder', to='teams.team')),
            ],
        ),
    ]
