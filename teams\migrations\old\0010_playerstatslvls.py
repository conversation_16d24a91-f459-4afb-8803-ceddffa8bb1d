# Generated by Django 5.0.7 on 2024-09-20 04:09

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('teams', '0009_alter_playerstatsability_marking'),
    ]

    operations = [
        migrations.CreateModel(
            name='PlayerStatsLvls',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('stat_0', models.<PERSON><PERSON><PERSON><PERSON>(max_length=50)),
                ('stat_1', models.Char<PERSON><PERSON>(max_length=50)),
                ('stat_2', models.Char<PERSON>ield(max_length=50)),
                ('stat_3', models.Char<PERSON>ield(max_length=50)),
                ('stat_4', models.<PERSON><PERSON><PERSON><PERSON>(max_length=50)),
                ('stat_5', models.Char<PERSON><PERSON>(max_length=50)),
                ('stat_6', models.<PERSON>r<PERSON><PERSON>(max_length=50)),
                ('stat_7', models.<PERSON><PERSON><PERSON><PERSON>(max_length=50)),
                ('stat_8', models.Char<PERSON><PERSON>(max_length=50)),
                ('stat_9', models.Char<PERSON><PERSON>(max_length=50)),
                ('stat_10', models.Char<PERSON><PERSON>(max_length=50)),
                ('stat_11', models.Char<PERSON>ield(max_length=50)),
                ('stat_12', models.CharField(max_length=50)),
                ('stat_13', models.CharField(max_length=50)),
                ('stat_14', models.CharField(max_length=50)),
                ('stat_15', models.CharField(max_length=50)),
                ('stat_16', models.CharField(max_length=50)),
                ('stat_17', models.CharField(max_length=50)),
                ('stat_18', models.CharField(max_length=50)),
                ('stat_19', models.CharField(max_length=50)),
                ('stat_20', models.CharField(max_length=50)),
            ],
        ),
    ]
