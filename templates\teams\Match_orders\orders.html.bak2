<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Set Orders</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    body {
      background-color: #f0f4f8;
    }

	.field-container {
	  background: url('../../../static/teams/images/afl-field.png') no-repeat center center;
	  background-size: contain; /* Ensures the field image scales proportionally */
	  aspect-ratio: 300 / 424; /* Locks the aspect ratio */
	  max-width: 700px; /* Optional: Sets a maximum width */
	  width: 100%; /* Scales responsively within the parent container */
	  position: relative; /* Enables absolutely positioned child elements */
	  margin: 0 auto; /* Centers the field horizontally */
	  border: 4px solid #FFF; /* Field border */
	  border-radius: 50% / 50%; /* Horizontal radius is much larger than vertical */
	  box-shadow: 0px 8px 16px rgba(0, 0, 0, 0.2); /* Adds depth */
	}
	
	.player-rect {
	  width: 7%;
	  height: 7%;
	  background-color: #ffffff; /* A distinct color for the rectangle */
	  opacity: 0.3;
	  color: #000;
	  display: flex;
	  align-items: center;
	  justify-content: center;
	  border-radius: 5px; /* Slightly rounded edges */
	  font-size: 12px;
	  text-align: center;
	  position: absolute;
	  cursor: pointer;
	  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.2); /* Depth for better visuals */
	}
	
	.player-name {
	  font-size: 12px;
	  color: #000;
	  text-align: center;
	}

    .player-rect:hover {
      transform: scale(1.1);
      box-shadow: 0px 6px 12px rgba(0, 0, 0, 0.3);
    }

    .tabs-container {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
    }

    .tabs-container .tab-btn {
      padding: 10px 20px;
      background-color: #ecf0f1;
      border-radius: 10px;
      cursor: pointer;
      transition: background-color 0.2s;
    }

    .tabs-container .tab-btn:hover {
      background-color: #bdc3c7;
    }

    .tabs-container .tab-btn.active {
      background-color: #27ae60;
      color: #fff;
    }

    .player {
      background-color: #ffffff;
      border: 2px solid #e0e0e0;
      border-radius: 10px;
      padding: 10px;
      margin-bottom: 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      transition: transform 0.2s;
	  max-width: 150px;
    }

    .player:hover {
      transform: scale(1.03);
      border-color: #3498db;
    }

    .progress-bar {
      height: 8px;
      border-radius: 5px;
      overflow: hidden;
    }

    .progress-bar div {
      height: 100%;
      border-radius: 5px;
    }
  </style>
</head>
<body class="p-6">

  <!-- Navigation Tabs -->
  <div class="tabs-container">
    <button class="tab-btn active">Lineup</button>
    <button class="tab-btn">Tactics</button>
    <button class="tab-btn">Penalty Takers</button>
    <button class="tab-btn">Review</button>
    <button class="tab-btn bg-green-600 text-white font-bold px-6">Send Orders</button>
  </div>

  <div class="grid grid-cols-3 gap-4">
    <!-- Field Section -->
    <div class="col-span-2">
      <div class="field-container">
        <!-- Example player positions -->
        <div class="player-rect" style="top: 10%; left: 48%;">FB</div>
        <div class="player-rect" style="top: 20%; left: 40%;">RBP</div>
        <div class="player-rect" style="top: 20%; left: 60%;">LBP</div>
      </div>
    </div>

    <!-- Player List -->
 <div class="players-list">
<div class="players-list">
  {% for player in players %}
  <div class="player" data-id="{{ player.id }}">{{ player.name }}</div>
  {% endfor %}
</div>
</div>
  </div>
<script>
document.addEventListener('DOMContentLoaded', () => {
  const players = document.querySelectorAll('.player');
  const playerRects = document.querySelectorAll('.player-rect');

  players.forEach((player) => {
    player.draggable = true;

    player.addEventListener('dragstart', (e) => {
      e.dataTransfer.setData('player-id', player.dataset.id);
      e.dataTransfer.setData('player-name', player.textContent.trim());
    });
  });

  playerRects.forEach((rect) => {
    rect.addEventListener('dragover', (e) => {
      e.preventDefault(); // Allow dropping
    });

    rect.addEventListener('drop', (e) => {
      e.preventDefault();

      const playerId = e.dataTransfer.getData('player-id');
      const playerName = e.dataTransfer.getData('player-name');

      // Add the player's name into the .player-rect
      //rect.textContent = playerName;

      // Optionally disable further drops into this .player-rect
      rect.setAttribute('draggable', false);
      //rect.style.backgroundColor = "#ffffff"; // Indicate it's filled
	  rect.style.backgroundImage = "url('../../../static/player/img/guinea.png')";
	  rect.style.backgroundSize = "contain"; // Ensure the image fits within the rectangle
	  rect.style.backgroundColor = "fff"; // Clear any previous color (optional)
	  rect.style.backgroundRepeat = "no-repeat"; // Prevent repeating of the image
	  rect.style.opacity = "0.8";
	  rect.style.backgroundPosition = "center 6px"; // Adjusts the vertical position to "2px" down
	  rect.textContent = "";

      // Remove the player from the right-hand list
      // Remove the player from the right-hand list
      const playerElement = document.querySelector(`.player[data-id="${playerId}"]`);
      if (playerElement) {
	  	  // Find the nearest player-rect (drop target)
		//rect.textContent = "FB"; // Keep the position label
		const playerNameElement = document.createElement('div');
		playerNameElement.textContent = playerName;
		playerNameElement.style.fontSize = "10px";
		playerNameElement.style.color = "#fff";
		playerNameElement.style.marginTop = "200%";

		// Append the player name below the position label
		rect.appendChild(playerNameElement);
  
	  
        playerElement.remove();
      };
    });
  });
});

</script>
</body>
</html>
