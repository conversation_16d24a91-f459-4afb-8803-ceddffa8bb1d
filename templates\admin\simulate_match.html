{% extends "admin/base_site.html" %}
{% load static %}

{% block content %}
<h1>Simulate Match</h1>
<form method="post" id="simulate-match-form">
    {% csrf_token %}
    <div>
        <label for="id_team1">Team 1:</label>
        <select id="id_team1" name="team1">
            <option value="">Select a team</option>
            {% for team in teams %}
            <option value="{{ team.id }}">{{ team.name }}</option>
            {% endfor %}
        </select>
    </div>
    <div>
        <label for="id_team1_ruck">Ruck:</label>
        <select id="id_team1_ruck" name="team1_ruck">
            <option value="">Select a player</option>
        </select>
    </div>
    <div>
        <label for="id_team1_rover">Rover:</label>
        <select id="id_team1_rover" name="team1_rover">
            <option value="">Select a player</option>
        </select>
    </div>
    <div>
        <label for="id_team1_ruckrover">Ruck Rover:</label>
        <select id="id_team1_ruckrover" name="team1_ruckrover">
            <option value="">Select a player</option>
        </select>
    </div>
    <div>
        <label for="id_team1_LBackPocket">Left Back Pocket:</label>
        <select id="id_team1_LBackPocket" name="team1_LBackPocket">
            <option value="">Select a player</option>
        </select>
    </div>
    <div>
        <label for="id_team1_RBackPocket">Right Back Pocket:</label>
        <select id="id_team1_RBackPocket" name="team1_RBackPocket">
            <option value="">Select a player</option>
        </select>
    </div>
    <div>
        <label for="id_team1_fullback">Full Back:</label>
        <select id="id_team1_fullback" name="team1_fullback">
            <option value="">Select a player</option>
        </select>
    </div>
    <div>
        <label for="id_team1_Lhalfback">Left Half Back:</label>
        <select id="id_team1_Lhalfback" name="team1_Lhalfback">
            <option value="">Select a player</option>
        </select>
    </div>
    <div>
        <label for="id_team1_Chalfback">Center Half Back:</label>
        <select id="id_team1_Chalfback" name="team1_Chalfback">
            <option value="">Select a player</option>
        </select>
    </div>
    <div>
        <label for="id_team1_Rhalfback">Right Half Back:</label>
        <select id="id_team1_Rhalfback" name="team1_Rhalfback">
            <option value="">Select a player</option>
        </select>
    </div>
    <div>
        <label for="id_team1_Lwing">Left Wing:</label>
        <select id="id_team1_Lwing" name="team1_Lwing">
            <option value="">Select a player</option>
        </select>
    </div>
    <div>
        <label for="id_team1_centre">Centre:</label>
        <select id="id_team1_centre" name="team1_centre">
            <option value="">Select a player</option>
        </select>
    </div>
    <div>
        <label for="id_team1_Rwing">Right Wing:</label>
        <select id="id_team1_Rwing" name="team1_Rwing">
            <option value="">Select a player</option>
        </select>
    </div>
    <div>
        <label for="id_team1_Lhalfforward">Left Half Forward:</label>
        <select id="id_team1_Lhalfforward" name="team1_Lhalfforward">
            <option value="">Select a player</option>
        </select>
    </div>
    <div>
        <label for="id_team1_halfforward">Center Half Forward:</label>
        <select id="id_team1_halfforward" name="team1_halfforward">
            <option value="">Select a player</option>
        </select>
    </div>
    <div>
        <label for="id_team1_Rhalfforward">Right Half Forward:</label>
        <select id="id_team1_Rhalfforward" name="team1_Rhalfforward">
            <option value="">Select a player</option>
        </select>
    </div>
    <div>
        <label for="id_team1_Lforwardpocket">Left Forward Pocket:</label>
        <select id="id_team1_Lforwardpocket" name="team1_Lforwardpocket">
            <option value="">Select a player</option>
        </select>
    </div>
    <div>
        <label for="id_team1_fullforward">Full Forward:</label>
        <select id="id_team1_fullforward" name="team1_fullforward">
            <option value="">Select a player</option>
        </select>
    </div>
    <div>
        <label for="id_team1_Rforwardpocket">Right Forward Pocket:</label>
        <select id="id_team1_Rforwardpocket" name="team1_Rforwardpocket">
            <option value="">Select a player</option>
        </select>
    </div>
	    <div>
        <label for="id_team2">Team 2:</label>
        <select id="id_team2" name="team2">
            <option value="">Select a team</option>
            {% for team in teams %}
            <option value="{{ team.id }}">{{ team.name }}</option>
            {% endfor %}
        </select>
    </div>
    <div>
        <label for="id_team2_ruck">Ruck:</label>
        <select id="id_team2_ruck" name="team2_ruck">
            <option value="">Select a player</option>
        </select>
    </div>
    <div>
        <label for="id_team2_rover">Rover:</label>
        <select id="id_team2_rover" name="team2_rover">
            <option value="">Select a player</option>
        </select>
    </div>
    <div>
        <label for="id_team2_ruckrover">Ruck Rover:</label>
        <select id="id_team2_ruckrover" name="team2_ruckrover">
            <option value="">Select a player</option>
        </select>
    </div>
    <div>
        <label for="id_team2_LBackPocket">Left Back Pocket:</label>
        <select id="id_team2_LBackPocket" name="team2_LBackPocket">
            <option value="">Select a player</option>
        </select>
    </div>
    <div>
        <label for="id_team2_RBackPocket">Right Back Pocket:</label>
        <select id="id_team2_RBackPocket" name="team2_RBackPocket">
            <option value="">Select a player</option>
        </select>
    </div>
    <div>
        <label for="id_team2_fullback">Full Back:</label>
        <select id="id_team2_fullback" name="team2_fullback">
            <option value="">Select a player</option>
        </select>
    </div>
    <div>
        <label for="id_team2_Lhalfback">Left Half Back:</label>
        <select id="id_team2_Lhalfback" name="team2_Lhalfback">
            <option value="">Select a player</option>
        </select>
    </div>
    <div>
        <label for="id_team2_Chalfback">Center Half Back:</label>
        <select id="id_team2_Chalfback" name="team2_Chalfback">
            <option value="">Select a player</option>
        </select>
    </div>
    <div>
        <label for="id_team2_Rhalfback">Right Half Back:</label>
        <select id="id_team2_Rhalfback" name="team2_Rhalfback">
            <option value="">Select a player</option>
        </select>
    </div>
    <div>
        <label for="id_team2_Lwing">Left Wing:</label>
        <select id="id_team2_Lwing" name="team2_Lwing">
            <option value="">Select a player</option>
        </select>
    </div>
    <div>
        <label for="id_team2_centre">Centre:</label>
        <select id="id_team2_centre" name="team2_centre">
            <option value="">Select a player</option>
        </select>
    </div>
    <div>
        <label for="id_team2_Rwing">Right Wing:</label>
        <select id="id_team2_Rwing" name="team2_Rwing">
            <option value="">Select a player</option>
        </select>
    </div>
    <div>
        <label for="id_team2_Lhalfforward">Left Half Forward:</label>
        <select id="id_team2_Lhalfforward" name="team2_Lhalfforward">
            <option value="">Select a player</option>
        </select>
    </div>
    <div>
        <label for="id_team2_halfforward">Center Half Forward:</label>
        <select id="id_team2_halfforward" name="team2_halfforward">
            <option value="">Select a player</option>
        </select>
    </div>
    <div>
        <label for="id_team2_Rhalfforward">Right Half Forward:</label>
        <select id="id_team2_Rhalfforward" name="team2_Rhalfforward">
            <option value="">Select a player</option>
        </select>
    </div>
    <div>
        <label for="id_team2_Lforwardpocket">Left Forward Pocket:</label>
        <select id="id_team2_Lforwardpocket" name="team2_Lforwardpocket">
            <option value="">Select a player</option>
        </select>
    </div>
    <div>
        <label for="id_team2_fullforward">Full Forward:</label>
        <select id="id_team2_fullforward" name="team2_fullforward">
            <option value="">Select a player</option>
        </select>
    </div>
    <div>
        <label for="id_team2_Rforwardpocket">Right Forward Pocket:</label>
        <select id="id_team2_Rforwardpocket" name="team2_Rforwardpocket">
            <option value="">Select a player</option>
        </select>
    </div>

    <button type="submit">Simulate Match</button>
</form>

<div id="results"></div>

<script src="{% static 'admin/js/match_simulation.js' %}"></script>
<script>

// Sort table function
function sortTable(header) {
    var table = header.parentElement.parentElement.parentElement;
    var rows = Array.from(table.querySelectorAll('tr:nth-child(n+2)')); // Skip the header row
    var index = Array.from(header.parentElement.children).indexOf(header);
    var isNumeric = !isNaN(rows[0].children[index].innerText.trim());
    var ascending = !header.classList.contains('asc');

    rows.sort(function(rowA, rowB) {
        var cellA = rowA.children[index].innerText.trim();
        var cellB = rowB.children[index].innerText.trim();

        if (isNumeric) {
            cellA = parseFloat(cellA);
            cellB = parseFloat(cellB);
        }

        if (ascending) {
            return cellA > cellB ? 1 : -1;
        } else {
            return cellA < cellB ? 1 : -1;
        }
    });

    rows.forEach(function(row) {
        table.appendChild(row);
    });

    // Update the class for sorting indication
    Array.from(header.parentElement.children).forEach(function(th) {
        th.classList.remove('asc', 'desc');
    });
    header.classList.add(ascending ? 'asc' : 'desc');
}

document.getElementById('simulate-match-form').onsubmit = function(e) {
    e.preventDefault();

    // Get selected values for Team 1
    var team1Id = document.getElementById('id_team1').value;
    var team1RuckId = document.getElementById('id_team1_ruck').value;
    var team1RoverId = document.getElementById('id_team1_rover').value;
    var team1RuckRoverId = document.getElementById('id_team1_ruckrover').value;
    var team1LBackPocketId = document.getElementById('id_team1_LBackPocket').value;
    var team1RBackPocketId = document.getElementById('id_team1_RBackPocket').value;
    var team1FullBackId = document.getElementById('id_team1_fullback').value;
    var team1LhalfbackId = document.getElementById('id_team1_Lhalfback').value;
    var team1ChalfbackId = document.getElementById('id_team1_Chalfback').value;
    var team1RhalfbackId = document.getElementById('id_team1_Rhalfback').value;
    var team1LwingId = document.getElementById('id_team1_Lwing').value;
    var team1CentreId = document.getElementById('id_team1_centre').value;
    var team1RwingId = document.getElementById('id_team1_Rwing').value;
    var team1LhalfforwardId = document.getElementById('id_team1_Lhalfforward').value;
    var team1HalfforwardId = document.getElementById('id_team1_halfforward').value;
    var team1RhalfforwardId = document.getElementById('id_team1_Rhalfforward').value;
    var team1LforwardpocketId = document.getElementById('id_team1_Lforwardpocket').value;
    var team1FullforwardId = document.getElementById('id_team1_fullforward').value;
    var team1RforwardpocketId = document.getElementById('id_team1_Rforwardpocket').value;

    // Get selected values for Team 2
    var team2Id = document.getElementById('id_team2').value;
    var team2RuckId = document.getElementById('id_team2_ruck').value;
    var team2RoverId = document.getElementById('id_team2_rover').value;
    var team2RuckRoverId = document.getElementById('id_team2_ruckrover').value;
    var team2LBackPocketId = document.getElementById('id_team2_LBackPocket').value;
    var team2RBackPocketId = document.getElementById('id_team2_RBackPocket').value;
    var team2FullBackId = document.getElementById('id_team2_fullback').value;
    var team2LhalfbackId = document.getElementById('id_team2_Lhalfback').value;
    var team2ChalfbackId = document.getElementById('id_team2_Chalfback').value;
    var team2RhalfbackId = document.getElementById('id_team2_Rhalfback').value;
    var team2LwingId = document.getElementById('id_team2_Lwing').value;
    var team2CentreId = document.getElementById('id_team2_centre').value;
    var team2RwingId = document.getElementById('id_team2_Rwing').value;
    var team2LhalfforwardId = document.getElementById('id_team2_Lhalfforward').value;
    var team2HalfforwardId = document.getElementById('id_team2_halfforward').value;
    var team2RhalfforwardId = document.getElementById('id_team2_Rhalfforward').value;
    var team2LforwardpocketId = document.getElementById('id_team2_Lforwardpocket').value;
    var team2FullforwardId = document.getElementById('id_team2_fullforward').value;
    var team2RforwardpocketId = document.getElementById('id_team2_Rforwardpocket').value;

    // Create WebSocket connection
    var ws = new WebSocket('ws://' + window.location.host.replace(':8000', ':8001') + '/ws/simulate_match/');

    ws.onopen = function() {
        console.log("WebSocket connection opened");

        // Send data to the WebSocket server
        ws.send(JSON.stringify({
            'command': 'start_simulation',
            'team1_id': team1Id,
            'team2_id': team2Id,
            'team1_ruck_id': team1RuckId,
            'team2_ruck_id': team2RuckId,
            'team1_rover_id': team1RoverId,
            'team2_rover_id': team2RoverId,
            'team1_ruckrover_id': team1RuckRoverId,
            'team2_ruckrover_id': team2RuckRoverId,
            'team1_LBackPocket_id': team1LBackPocketId,
            'team2_LBackPocket_id': team2LBackPocketId,
            'team1_RBackPocket_id': team1RBackPocketId,
            'team2_RBackPocket_id': team2RBackPocketId,
            'team1_fullback_id': team1FullBackId,
            'team2_fullback_id': team2FullBackId,
            'team1_Lhalfback_id': team1LhalfbackId,
            'team2_Lhalfback_id': team2LhalfbackId,
            'team1_Chalfback_id': team1ChalfbackId,
            'team2_Chalfback_id': team2ChalfbackId,
            'team1_Rhalfback_id': team1RhalfbackId,
            'team2_Rhalfback_id': team2RhalfbackId,
            'team1_Lwing_id': team1LwingId,
            'team2_Lwing_id': team2LwingId,
            'team1_centre_id': team1CentreId,
            'team2_centre_id': team2CentreId,
            'team1_Rwing_id': team1RwingId,
            'team2_Rwing_id': team2RwingId,
            'team1_Lhalfforward_id': team1LhalfforwardId,
            'team2_Lhalfforward_id': team2LhalfforwardId,
            'team1_halfforward_id': team1HalfforwardId,
            'team2_halfforward_id': team2HalfforwardId,
            'team1_Rhalfforward_id': team1RhalfforwardId,
            'team2_Rhalfforward_id': team2RhalfforwardId,
            'team1_Lforwardpocket_id': team1LforwardpocketId,
            'team2_Lforwardpocket_id': team2LforwardpocketId,
            'team1_fullforward_id': team1FullforwardId,
            'team2_fullforward_id': team2FullforwardId,
            'team1_Rforwardpocket_id': team1RforwardpocketId,
            'team2_Rforwardpocket_id': team2RforwardpocketId,
        }));
    };

    document.addEventListener('click', function(event) {
    if (event.target && event.target.classList.contains('collapsible')) {
        console.log('Button clicked for Quarter'); // Debugging line
        event.target.classList.toggle('active');
        var contentDiv = event.target.nextElementSibling;
        if (contentDiv.style.display === 'block') {
            contentDiv.style.display = 'none';
        } else {
            contentDiv.style.display = 'block';
        }
    }
});

ws.onmessage = function(event) {
    console.log("WebSocket message received");
    var data = JSON.parse(event.data);
    var resultsDiv = document.getElementById('results');
    resultsDiv.innerHTML = ''; // Clear previous results

    if (data.quarter_results) {
        data.quarter_results.forEach(function(quarter_result, index) {
            // Show quarter scores
            resultsDiv.innerHTML += '<h3>Quarter ' + (index + 1) + ' Results:</h3>';
            resultsDiv.innerHTML += '<p>Team 1: ' + quarter_result.team1_score + '</p>';
            resultsDiv.innerHTML += '<p>Team 2: ' + quarter_result.team2_score + '</p>';

            // Show quarter events
            quarter_result.events.forEach(function(event) {
                resultsDiv.innerHTML += '<p>' + event + '</p>';
            });

            // Collapsible player stats
            var button = document.createElement('button');
            button.className = 'collapsible';
            button.innerHTML = 'Quarter ' + (index + 1) + ' Player Stats';
            console.log('Button element created:', button); // Debugging line
            resultsDiv.appendChild(button);

            var contentDiv = document.createElement('div');
            contentDiv.className = 'collapsible-content';
            contentDiv.style.display = 'none'; // Ensure it starts closed

            if (Array.isArray(quarter_result.player_stats)) {
                var statsTable = '<table>';
                statsTable += '<tr>';
                statsTable += '<th onclick="sortTable(this)">Player</th>';
                statsTable += '<th onclick="sortTable(this)">Team</th>';
                statsTable += '<th onclick="sortTable(this)">Hitouts</th>';
                statsTable += '<th onclick="sortTable(this)">Kicks</th>';
                statsTable += '<th onclick="sortTable(this)">Handballs</th>';
                statsTable += '<th onclick="sortTable(this)">Tackles</th>';
                statsTable += '<th onclick="sortTable(this)">Clearances</th>';
                statsTable += '<th onclick="sortTable(this)">Marks</th>';
                statsTable += '<th onclick="sortTable(this)">Goals</th>';
                statsTable += '<th onclick="sortTable(this)">Behinds</th>';
                statsTable += '<th onclick="sortTable(this)">Disposals</th>';
                statsTable += '<th onclick="sortTable(this)">Disposal Efficiency</th>';
                statsTable += '<th onclick="sortTable(this)">Turnovers Won</th>';
                statsTable += '<th onclick="sortTable(this)">Interceptions Won</th>';
                statsTable += '</tr>';
                quarter_result.player_stats.forEach(function(stats) {
                    statsTable += '<tr>';
                    statsTable += '<td>' + stats.name + '</td>';
                    statsTable += '<td>' + stats.team + '</td>';
                    statsTable += '<td>' + stats.hitouts + '</td>';
                    statsTable += '<td>' + stats.kicks + '</td>';
                    statsTable += '<td>' + stats.handballs + '</td>';
                    statsTable += '<td>' + stats.tackles + '</td>';
                    statsTable += '<td>' + stats.clearances + '</td>';
                    statsTable += '<td>' + stats.marks + '</td>';
                    statsTable += '<td>' + stats.goals + '</td>';
                    statsTable += '<td>' + stats.behinds + '</td>';
                    statsTable += '<td>' + stats.disposals + '</td>';
                    statsTable += '<td>' + stats.disposal_efficiency + '%</td>';
                    statsTable += '<td>' + stats.turnovers_won + '</td>';
                    statsTable += '<td>' + stats.interceptions_won + '</td>';
                    statsTable += '</tr>';
                });
                statsTable += '</table>';
                contentDiv.innerHTML = statsTable;
            } else {
                contentDiv.innerHTML = '<p>No player stats available for this quarter.</p>';
            }

            resultsDiv.appendChild(contentDiv);

            button.addEventListener('click', function() {
                console.log('Button clicked for Quarter ' + (index + 1)); // Debugging line
                this.classList.toggle('active');
                if (contentDiv.style.display === 'block') {
                    contentDiv.style.display = 'none';
                } else {
                    contentDiv.style.display = 'block';
                }
            });
        });
    }

    if (data.final_result) {
        resultsDiv.innerHTML += '<h2>' + data.final_result + '</h2>';
    }
};

    ws.onclose = function(e) {
        console.log("WebSocket connection closed");
    };

    ws.onerror = function(e) {
        console.log("WebSocket error:", e);
    };
};
</script>
<style>
    .collapsible {
        cursor: pointer;
        padding: 10px;
        border: none;
        text-align: left;
        outline: none;
        font-size: 15px;
        background-color: #f1f1f1;
        margin-bottom: 5px;
        width: 100%;
    }

    .active, .collapsible:hover {
        background-color: #ddd;
    }

    .collapsible-content {
        padding: 0 18px;
        display: none;
        overflow: hidden;
        background-color: #f9f9f9;
    }

    table {
        width: 100%;
        border-collapse: collapse;
    }

    th, td {
        border: 1px solid #ddd;
        padding: 8px;
    }

    th {
        cursor: pointer;
    }

    th.asc::after {
        content: " \u25B2"; /* Up arrow */
    }

    th.desc::after {
        content: " \u25BC"; /* Down arrow */
    }
</style>
{% endblock %}
