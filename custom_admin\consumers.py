import json
from channels.generic.websocket import WebsocketConsumer
from asgiref.sync import async_to_sync
from matchengine.match_engine.match_engine import MatchEngine
from teams.models import Team, Player

class SimulateMatchConsumer(WebsocketConsumer):
    def connect(self):
        self.accept()

    def disconnect(self, close_code):
        pass

    def receive(self, text_data):
        data = json.loads(text_data)
        command = data.get('command', '')

        if command == 'start_simulation':
            team1_id = data.get('team1_id')
            team2_id = data.get('team2_id')

            # Retrieve player ids for both teams from the data
            team1_players_ids = {
                "Ruck": data.get('team1_ruck_id'),
                "Rover": data.get('team1_rover_id'),
                "RuckRover": data.get('team1_ruckrover_id'),
                "LB": data.get('team1_LBackPocket_id'),
                "FB": data.get('team1_fullback_id'),
                "RB": data.get('team1_RBackPocket_id'),
                "LHB": data.get('team1_Lhalfback_id'),
                "CHB": data.get('team1_Chalfback_id'),
                "RHB": data.get('team1_Rhalfback_id'),
                "LWing": data.get('team1_Lwing_id'),
                "Centre": data.get('team1_centre_id'),
                "RWing": data.get('team1_Rwing_id'),
                "LHF": data.get('team1_Lhalfforward_id'),
                "CHF": data.get('team1_halfforward_id'),
                "RHF": data.get('team1_Rhalfforward_id'),
                "LF": data.get('team1_Lforwardpocket_id'),
                "FF": data.get('team1_fullforward_id'),
                "RF": data.get('team1_Rforwardpocket_id')
            }

            team2_players_ids = {
                "Ruck": data.get('team2_ruck_id'),
                "Rover": data.get('team2_rover_id'),
                "RuckRover": data.get('team2_ruckrover_id'),
                "LB": data.get('team2_LBackPocket_id'),
                "FB": data.get('team2_fullback_id'),
                "RB": data.get('team2_RBackPocket_id'),
                "LHB": data.get('team2_Lhalfback_id'),
                "CHB": data.get('team2_Chalfback_id'),
                "RHB": data.get('team2_Rhalfback_id'),
                "LWing": data.get('team2_Lwing_id'),
                "Centre": data.get('team2_centre_id'),
                "RWing": data.get('team2_Rwing_id'),
                "LHF": data.get('team2_Lhalfforward_id'),
                "CHF": data.get('team2_halfforward_id'),
                "RHF": data.get('team2_Rhalfforward_id'),
                "LF": data.get('team2_Lforwardpocket_id'),
                "FF": data.get('team2_fullforward_id'),
                "RF": data.get('team2_Rforwardpocket_id')
            }

            team1 = Team.objects.get(id=team1_id)
            team2 = Team.objects.get(id=team2_id)

            # Retrieve player objects for both teams
            team1_players_objects = {position: Player.objects.get(id=player_id) for position, player_id in team1_players_ids.items()}
            team2_players_objects = {position: Player.objects.get(id=player_id) for position, player_id in team2_players_ids.items()}

            engine = MatchEngine(team1, team2, team1_players_objects, team2_players_objects)
            events = engine.simulate_match()

            self.send(text_data=json.dumps(events))
        else:
            self.send(text_data=json.dumps({
                'error': 'Invalid command'
            }))
