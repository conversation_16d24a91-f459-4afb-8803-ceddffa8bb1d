# Generated by Django 5.0.7 on 2024-07-26 02:20

import django.db.models.deletion
import teams.utils
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('teams', '0002_playerstatsability_playerstatsphysical_and_more'),
        ('users', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='team',
            name='user',
            field=models.OneToOneField(default=1, on_delete=django.db.models.deletion.CASCADE, to='users.customuser'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='player',
            name='ability_stats',
            field=models.OneToOneField(default=teams.utils.create_random_ability_stats, on_delete=django.db.models.deletion.CASCADE, to='teams.playerstatsability'),
        ),
        migrations.AddField(
            model_name='player',
            name='physical_stats',
            field=models.OneToOneField(default=1, on_delete=django.db.models.deletion.CASCADE, to='teams.playerstatsphysical'),
            preserve_default=False,
        ),
    ]
