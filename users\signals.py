from django.db.models.signals import post_save
from django.forms.models import model_to_dict
from django.dispatch import receiver
from django.contrib.auth import get_user_model
from teams.utils import transfer_team_to_user
from teams.models import Team, Player, PlayerStatsPhysical, PlayerStatsAbility, PlayerStatsAttributes
#from ladder.models import Ladder 
import random
import math
import sys
from faker import Faker



User = get_user_model()
#fake = Faker()
fake = Faker('en_AU')

@receiver(post_save, sender=User)
def create_team_and_players(sender, instance, created, **kwargs):
    if created and not instance.is_bot:
        bot_user = User.objects.filter(is_bot=True, is_active=True).first()
        print(f"Created and not bot user")
        if bot_user:
            print(f"Bot user found: {bot_user}")
            team = transfer_team_to_user(bot_user, instance)
        else:
            # Create a new team if no bot is available
            print("Hit else, bot user not found")
            from teams.utils import create_team_for_user
            create_team_for_user(instance)

#        team = Team.objects.create(user=instance, name=f"{instance.team_name}")
#        print("position weights created")
        is_bot = instance.is_bot

        position_weights = {
            "full_forward": {
                "height": (7, 9),
                "agility": (4, 6),
                "speed": (4, 6),
                "strength": (6, 8),
                "stamina": (5, 7),
                "kicking": (6, 9),
                "goal_kicking": (8, 10),
                "handball": (3, 5),
                "tackling": (3, 5),
                "mental": (5, 7),
                "tactical": (5, 7),
                "versatility": (3, 5),
                "consistency": (6, 8),
                "marking": (8, 10),
            },
            "forward_pocket_1": {  # Same weights for both forward pockets
                "height": (5, 7),
                "agility": (6, 8),
                "speed": (5, 8),
                "strength": (5, 7),
                "stamina": (5, 7),
                "kicking": (5, 8),
                "goal_kicking": (7, 9),
                "handball": (5, 7),
                "tackling": (5, 7),
                "mental": (5, 7),
                "tactical": (4, 6),
                "versatility": (5, 7),
                "consistency": (5, 7),
                "marking": (6, 8),
            },
            "forward_pocket_2": {  # Same weights for both forward pockets
                "height": (5, 7),
                "agility": (6, 8),
                "speed": (5, 8),
                "strength": (5, 7),
                "stamina": (5, 7),
                "kicking": (5, 8),
                "goal_kicking": (7, 9),
                "handball": (5, 7),
                "tackling": (5, 7),
                "mental": (5, 7),
                "tactical": (4, 6),
                "versatility": (5, 7),
                "consistency": (5, 7),
                "marking": (6, 8),
            },
            "center_half_forward": {
                "height": (7, 9),
                "agility": (4, 6),
                "speed": (5, 7),
                "strength": (7, 9),
                "stamina": (6, 8),
                "kicking": (6, 8),
                "goal_kicking": (7, 9),
                "handball": (4, 6),
                "tackling": (4, 6),
                "mental": (6, 8),
                "tactical": (5, 7),
                "versatility": (4, 6),
                "consistency": (6, 8),
                "marking": (7, 9),
            },
            "half_forward_flank_1": {  # Same weights for both half-forward flanks
                "height": (6, 8),
                "agility": (5, 8),
                "speed": (6, 8),
                "strength": (5, 7),
                "stamina": (6, 8),
                "kicking": (6, 8),
                "goal_kicking": (6, 8),
                "handball": (5, 7),
                "tackling": (5, 7),
                "mental": (5, 7),
                "tactical": (6, 8),
                "versatility": (6, 8),
                "consistency": (6, 8),
                "marking": (6, 8),
            },
            "half_forward_flank_2": {  # Same weights for both half-forward flanks
                "height": (6, 8),
                "agility": (5, 8),
                "speed": (6, 8),
                "strength": (5, 7),
                "stamina": (6, 8),
                "kicking": (6, 8),
                "goal_kicking": (6, 8),
                "handball": (5, 7),
                "tackling": (5, 7),
                "mental": (5, 7),
                "tactical": (6, 8),
                "versatility": (6, 8),
                "consistency": (6, 8),
                "marking": (6, 8),
            },
            "ruck": {
                "height": (8, 10),
                "agility": (3, 5),
                "speed": (4, 6),
                "strength": (8, 10),
                "stamina": (6, 8),
                "kicking": (5, 7),
                "goal_kicking": (5, 6),
                "handball": (6, 8),
                "tackling": (5, 7),
                "mental": (6, 8),
                "tactical": (5, 7),
                "versatility": (3, 5),
                "consistency": (6, 8),
                "marking": (7, 9),
            },
            "winger_1": {
                "height": (5, 8),
                "agility": (7, 10),
                "speed": (8, 10),
                "strength": (4, 6),
                "stamina": (8, 10),
                "kicking": (6, 8),
                "goal_kicking": (3, 6),
                "handball": (6, 9),
                "tackling": (5, 7),
                "mental": (6, 8),
                "tactical": (7, 9),
                "versatility": (6, 8),
                "consistency": (6, 8),
                "marking": (4, 7) 
            },
            "winger_2": {
                "height": (5, 8),
                "agility": (7, 10),
                "speed": (8, 10),
                "strength": (4, 6),
                "stamina": (8, 10),
                "kicking": (6, 8),
                "goal_kicking": (3, 6),
                "handball": (6, 9),
                "tackling": (5, 7),
                "mental": (6, 8),
                "tactical": (7, 9),
                "versatility": (6, 8),
                "consistency": (6, 8),
                "marking": (4, 7) 
            },
            "rover": {
                "height": (5, 7),  
                "agility": (8, 10),  
                "speed": (7, 9), 
                "strength": (6, 8),  
                "stamina": (7, 9),  
                "kicking": (7, 9), 
                "goal_kicking": (5, 7),  
                "handball": (8, 10),  
                "tackling": (7, 9),  
                "mental": (7, 9),  
                "tactical": (7, 9),  
                "versatility": (7, 9),
                "consistency": (7, 9),
                "marking": (4, 6)  
            },
            "ruck_rover": {
                "height": (6, 8),  # Slightly taller than rover
                "agility": (7, 9),  # High agility
                "speed": (6, 8),  # Average speed
                "strength": (7, 9),  # Strong
                "stamina": (8, 10),  # High stamina
                "kicking": (6, 8),  # Good kicking ability
                "goal_kicking": (5, 7),  # Occasional goal kicker
                "handball": (7, 9),  # Strong handball skills
                "tackling": (7, 9),  # Strong tackling
                "mental": (7, 9),  # High mental strength
                "tactical": (8, 10),  # Excellent tactical awareness
                "versatility": (7, 9),
                "consistency": (7, 9),
                "marking": (5, 7)  # Decent marking
            },
            "center": {
                "height": (6, 9),  # Taller than average
                "agility": (6, 8),  # Good agility
                "speed": (6, 8),  # Average speed
                "strength": (6, 8),  # Good strength
                "stamina": (8, 10),  # High stamina for full-field coverage
                "kicking": (7, 9),  # Good kicking ability
                "goal_kicking": (4, 6),  # Occasionally scores
                "handball": (7, 9),  # Good handball skills
                "tackling": (6, 8),  # Strong tackling
                "mental": (8, 10),  # Excellent mental strength
                "tactical": (8, 10),  # Very strong tactical awareness
                "versatility": (7, 9),
                "consistency": (7, 9),
                "marking": (6, 8)  # Good marking ability
            },
            "full_back": {
                "height": (7, 9),  # Tall to contest high balls
                "agility": (5, 7),  # Moderate agility
                "speed": (5, 7),  # Moderate speed
                "strength": (8, 10),  # High strength for physical contests
                "stamina": (6, 8),  # Good stamina
                "kicking": (5, 7),  # Decent kicking ability
                "goal_kicking": (1, 3),  # Rarely scores
                "handball": (5, 7),  # Average handball skills
                "tackling": (8, 10),  # Strong tackling
                "mental": (7, 9),  # Good mental strength
                "tactical": (7, 9),  # Strong tactical awareness
                "versatility": (5, 7),
                "consistency": (7, 9),
                "marking": (7, 10)  # Excellent marking ability
            },
            "back_pocket_1": {
                "height": (6, 8),  # Average height
                "agility": (6, 8),  # Good agility to match smaller forwards
                "speed": (6, 8),  # Decent speed
                "strength": (6, 8),  # Strong to contest
                "stamina": (6, 8),  # Good stamina
                "kicking": (5, 7),  # Average kicking ability
                "goal_kicking": (1, 3),  # Rarely scores
                "handball": (5, 7),  # Average handball skills
                "tackling": (8, 10),  # Strong tackling ability
                "mental": (7, 9),  # Good mental strength
                "tactical": (6, 8),  # Strong tactical awareness
                "versatility": (6, 8),
                "consistency": (7, 9),
                "marking": (6, 8)  # Good marking ability
            },
            "back_pocket_2": {
                "height": (6, 8),  # Average height
                "agility": (6, 8),  # Good agility to match smaller forwards
                "speed": (6, 8),  # Decent speed
                "strength": (6, 8),  # Strong to contest
                "stamina": (6, 8),  # Good stamina
                "kicking": (5, 7),  # Average kicking ability
                "goal_kicking": (1, 3),  # Rarely scores
                "handball": (5, 7),  # Average handball skills
                "tackling": (8, 10),  # Strong tackling ability
                "mental": (7, 9),  # Good mental strength
                "tactical": (6, 8),  # Strong tactical awareness
                "versatility": (6, 8),
                "consistency": (7, 9),
                "marking": (6, 8)  # Good marking ability
            },
            "center_half_back": {
                "height": (7, 9),  # Tall build for aerial contests
                "agility": (5, 7),  # Moderate agility
                "speed": (5, 7),  # Moderate speed
                "strength": (8, 10),  # High strength for physical contests
                "stamina": (7, 9),  # Good stamina for full-field coverage
                "kicking": (6, 8),  # Decent kicking ability
                "goal_kicking": (1, 3),  # Rarely scores
                "handball": (6, 8),  # Good handball skills
                "tackling": (8, 10),  # Strong tackling ability
                "mental": (8, 10),  # High mental strength
                "tactical": (8, 10),  # Very strong tactical awareness
                "versatility": (7, 9),
                "consistency": (7, 9),
                "marking": (8, 10)  # Excellent marking ability
            },
            "half_back_flank_1": {
                "height": (6, 8),  # Average height
                "agility": (6, 8),  # Good agility
                "speed": (7, 9),  # Quick to support offensive play
                "strength": (6, 8),  # Good strength
                "stamina": (7, 9),  # High stamina for transition play
                "kicking": (7, 9),  # Strong kicking ability for rebounding
                "goal_kicking": (2, 5),  # Occasionally scores
                "handball": (6, 8),  # Good handball skills
                "tackling": (7, 9),  # Strong tackling
                "mental": (7, 9),  # High mental strength
                "tactical": (7, 9),  # Good tactical awareness
                "versatility": (7, 9),
                "consistency": (7, 9),
                "marking": (6, 8)  # Good marking ability
            },
            "half_back_flank_2": {
                "height": (6, 8),  # Average height
                "agility": (6, 8),  # Good agility
                "speed": (7, 9),  # Quick to support offensive play
                "strength": (6, 8),  # Good strength
                "stamina": (7, 9),  # High stamina for transition play
                "kicking": (7, 9),  # Strong kicking ability for rebounding
                "goal_kicking": (2, 5),  # Occasionally scores
                "handball": (6, 8),  # Good handball skills
                "tackling": (7, 9),  # Strong tackling
                "mental": (7, 9),  # High mental strength
                "tactical": (7, 9),  # Good tactical awareness
                "versatility": (7, 9),
                "consistency": (7, 9),
                "marking": (6, 8)  # Good marking ability
            }
        }
        #print(f"Creating {position_weights}")
        # Create 18 players based on position weights
        
        if is_bot:
            for _ in range(30):
                weights=None
                create_player(team, weights, is_bot)
        else:        
            for position, weights in position_weights.items():
                create_player(team, weights)

            # Create 12 additional random players
            for _ in range(12):
                create_player(team)         
            

def create_player(team, weights=None, is_bot=False):
    def weighted_stat(min_value, max_value):
        return random.randint(min_value, max_value)
    #print(f"Hit create player")
    if weights:
        # Generate stats based on position weights
        physical_stats = PlayerStatsPhysical.objects.create(
            height=weighted_stat(*weights["height"]),
            agility=weighted_stat(*weights["agility"]),
            speed=weighted_stat(*weights["speed"]),
            strength=weighted_stat(*weights["strength"]),
            stamina=weighted_stat(*weights["stamina"]),
            age=random.randint(17, 32)
        )
        ability_stats = PlayerStatsAbility.objects.create(
            kicking=weighted_stat(*weights["kicking"]),
            goal_kicking=weighted_stat(*weights["goal_kicking"]),
            handball=weighted_stat(*weights["handball"]),
            tackling=weighted_stat(*weights["tackling"]),
            mental=weighted_stat(*weights["mental"]),
            tactical=weighted_stat(*weights["tactical"]),
            versatility=weighted_stat(*weights["versatility"]),
            consistency=weighted_stat(*weights["consistency"]),
            marking=weighted_stat(*weights["marking"])
        )
    else:
        # Randomized stats for other players
        physical_stats = PlayerStatsPhysical.objects.create(
            height=random.randint(1, 9),
            agility=random.randint(1, 9),
            speed=random.randint(1, 9),
            strength=random.randint(1, 9),
            stamina=random.randint(1, 9),
            age=random.randint(17, 32)
        )
        ability_stats = PlayerStatsAbility.objects.create(
            kicking=random.randint(1, 9),
            goal_kicking=random.randint(1, 9),
            handball=random.randint(1, 9),
            tackling=random.randint(1, 9),
            mental=random.randint(1, 9),
            tactical=random.randint(1, 9),
            versatility=random.randint(1, 9),
            consistency=random.randint(1, 9),
            marking=random.randint(1, 9)
        )
        
        # Check if the user is a bot
        print(f"User: {is_bot}")
        if is_bot:
            # Perform actions specifically for bot users
            print(f"Team created for bot user: {is_bot}")
            physical_stats = PlayerStatsPhysical.objects.create(
            height=random.randint(1, 4),
            agility=random.randint(1, 4),
            speed=random.randint(1, 4),
            strength=random.randint(1, 4),
            stamina=random.randint(1, 4),
            age=random.randint(17, 32)
            )
            ability_stats = PlayerStatsAbility.objects.create(
                kicking=random.randint(1, 4),
                goal_kicking=random.randint(1, 4),
                handball=random.randint(1, 4),
                tackling=random.randint(1, 4),
                mental=random.randint(1, 4),
                tactical=random.randint(1, 4),
                versatility=random.randint(1, 4),
                consistency=random.randint(1, 4),
                marking=random.randint(1, 4)
            )
 
    # Explicitly fetch only the physical and ability stats (excluding id, age, and height)
    physical_stats_dict = model_to_dict(physical_stats, exclude=['id', 'height', 'age'])
    ability_stats_dict = model_to_dict(ability_stats, exclude=['id'])  # 'id' should be excluded
    #print(f"physical_stats_dict {physical_stats_dict}" )
    #print(f"ability_stats_dict {ability_stats_dict}" )
    # Extract only the relevant values
    filtered_physical_values = list(physical_stats_dict.values())
    filtered_ability_values = list(ability_stats_dict.values())

    # Calculate total stats correctly
    total_stats = sum(filtered_physical_values) + sum(filtered_ability_values)

    #print(f"Filtered Physicals: {filtered_physical_values}")
    #print(f"Filtered Abilities: {filtered_ability_values}")
    #print(f"Total Stats: {total_stats}")

    max_stats = 20 * 13  # Max possible points if each stat is 20
    age_threshold = 30
    drop_off_rate = random.uniform(0.05, 0.20)
    # Non-linear scaling of the wage (logarithmic scaling or controlled exponential scaling)
    # We use a logarithmic scale to increase the wage in a non-linear but controlled way
    #base_wage = 50000 + (math.log(total_stats + 1) / math.log(max_stats + 1)) * (5000000 - 50000)
    base_wage = 50000 + (((total_stats / max_stats) ** 2.0) * (5000000 - 50000))  # Quadratic scaling
    #print(f"Base wage 1 {base_wage}")
    # Apply a random wage variation (between 1% and 15% of the base wage)
    wage_variation = random.uniform(0.01, 0.15)
    base_wage += base_wage * wage_variation  # Apply the variation to the base wage
    #print(f"Base wage 2 {base_wage}")
    # Ensure the wage doesn't exceed the maximum wage
    #max_wage = 5000000
    #base_wage = min(base_wage, max_wage)
    #print(f"Base wage 3 {base_wage}")
    age = physical_stats.age
    if age > age_threshold:
        drop_off_multiplier = 1 - ((age - age_threshold) * drop_off_rate)
        wage = base_wage * max(drop_off_multiplier, 0)  # Ensure wage doesn't go below zero
    else:
        wage = base_wage

    attributes_stats = PlayerStatsAttributes.objects.create(
        experience=random.randint(1, 9),
        leadership=random.randint(1, 9),
        loyalty=random.randint(1, 20),
        wage=wage
    )

    first_name = fake.first_name_male()
    last_name = fake.last_name()
    full_name = f"{first_name} {last_name}"

    # Create player with stats
    Player.objects.create(
        team=team,
        physical_stats=physical_stats,
        ability_stats=ability_stats,
        attributes_stats=attributes_stats,
        name=full_name
    )

