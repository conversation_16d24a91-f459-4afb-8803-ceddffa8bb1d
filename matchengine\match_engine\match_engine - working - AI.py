#from ladder.utils import record_match_result
#from teams.models import Player

import sys
import random
import asyncio, time, traceback
from asgiref.sync import sync_to_async, async_to_sync
from channels.layers import get_channel_layer
sys.setrecursionlimit(10000)


class MatchEngine:
    def __init__(self, team1, team2, team1_players, team2_players, team1_tactics, team2_tactics, match_group_name, channel_layer):
        self.ground_length = 160  # AFL ground length in meters
        self.ground_width = 130   # AFL ground width in meters
        self.grid_length = int(self.ground_length / 10)  # 16 cells
        self.grid_width = int(self.ground_width / 10)    # 13 cells
        self.positions_zones = self.define_dynamic_zones(self.grid_width, self.grid_length)
        self.team1 = team1
        self.team2 = team2
        #print(f"(Team 1 {team1} Player {team1_players}")
        #print(f"(Team 2 {team2} Player {team2_players}")
        self.team1_tactics = team1_tactics
        self.team2_tactics = team2_tactics   
        print(f"T1 Tactics: {team1_tactics}")
        print(f"T2 Tactics: {team2_tactics}")
        #self.team1_tactics = Team(team1).tactics
        #self.team2_tactics = Team(team2).tactics
        #self.team1_players = team1_players
        #self.team2_players = team2_players
        self.team1_players = {pos: Player(player.name, self.team1, pos, player.ability_stats, player.physical_stats)
                              for pos, player in team1_players.items()}
        self.team2_players = {pos: Player(player.name, self.team2, pos, player.ability_stats, player.physical_stats)
                              for pos, player in team2_players.items()}
        self.team1_score = 0
        self.team2_score = 0
        self.ball_position = self.get_center_of_ground()
        self.quarter = 0
        self.events = []
        self.contest_count = 0
        self.mirrored_position = None
        self.out_of_bounds_count = 0
        self.out_of_bounds_count_possession = 0
        self.stats_manager = PlayerStatsManager()
        self.stats_manager.initialize_player_stats(team1_players, team2_players)
        self.channel_layer = channel_layer
        #print(f"Channel layer {self.channel_layer}")
        #sys.exit()
        self.match_group_name = match_group_name
        print(f"Match Engine: Channel Layer = {self.channel_layer}")
        print(f"Match Engine: Group Name = {self.match_group_name}")
        print("MatchEngine initialized")

    async def simulate_match(self):

        try:
            connection = await self.channel_layer.connection(0)
            group_key = f"{self.channel_layer.prefix}:groups:{self.match_group_name}"
            members = await connection.smembers(group_key)
            print(f"\nInitial group membership check:")
            print(f"Group: {self.match_group_name}")
            print(f"Members: {members}")
            
            if not members:
                print("WARNING: No members in group! Messages will be sent but not received.")
                # Optional: wait for members
                for _ in range(5):  # Try 5 times
                    await asyncio.sleep(1)
                    members = await connection.smembers(group_key)
                    if members:
                        print(f"Members joined: {members}")
                        break
                else:
                    print("No members joined after waiting.")
        
        except Exception as e:
            print(f"Error checking group membership: {e}")

        quarter_results = []
        start_team1_score = self.team1_score
        start_team2_score = self.team2_score
        print("Simulating match")
        for quarter in range(1, 5):
            print(f"Simulating quarter {quarter}")
            self.contest_count = 0
            self.out_of_bounds_count = 0
            self.out_of_bounds_count_possession = 0
            self.quarter += 1
            self.ball_position = self.get_center_of_ground()
            self.events = []
            await asyncio.sleep(1)  # Non-blocking sleep to simulate time passing
            yield {
                'type': 'match_event',
                'quarter': quarter,
                'event': f"Starting quarter {quarter}"
            }
            await asyncio.sleep(1)
            # Simulate quarter events
            async for event in self.simulate_quarter():
                if event:
                    yield event

            player_stats = self.stats_manager.collect_player_stats(self.team1_players, self.team2_players)
            
            yield {
            'type': 'score_update',
            'quarter': quarter,
            'team1_score': self.team1_score,
            'team2_score': self.team2_score,
            #'player_stats': player_stats
            }

            yield {
            'type': 'player_stats',
            'quarter': quarter,
            'player_stats': player_stats
            }
            
            quarter_results.append({
                'quarter': quarter,
                'team1_score': self.team1_score,
                'team2_score': self.team2_score,
                'events': [event for event in self.events],
                'player_stats': self.stats_manager.collect_player_stats(self.team1_players, self.team2_players),
            })

        """ 
        yield {
        'type': 'quarter_results',
        'result': quarter_results
        }
        
        yield {
        'type': 'player_stats',
        'quarter': quarter,
        'player_stats': player_stats
        }
        """
        #await self.broadcast_event(final_result)
        

    async def simulate_quarter(self):   
        """Simulate a quarter of play."""
        print(f"Simulating quarter {self.quarter}")
        
        # Initialize quarter parameters
        quarter_time = 20 * 60  # 20 minutes in seconds
        elapsed_time = 0
        self.contest_count = 0
        """
        yield {
            'type': 'match_event',
            'quarter': self.quarter,
            'event': f"Starting quarter {self.quarter}"
        }
        """
        await asyncio.sleep(0.5)
        
        # Main quarter simulation loop
        while elapsed_time < quarter_time and self.contest_count < 1:
            async for contest_event in self.simulate_contest():
                if contest_event:
                    yield contest_event
                    await asyncio.sleep(0.1)
            
            elapsed_time += 1
            await asyncio.sleep(0.1)
        
        # Quarter end event
        yield {
            'type': 'match_event',
            'quarter': self.quarter,
            'event': f"End of quarter {self.quarter}"
        }

        

    async def broadcast_event(self, event):
        """Broadcasts an event with direct Redis inspection."""
        try:
            print("\nChannel Layer Debug:")
            print(f"Channel Layer Type: {type(self.channel_layer)}")
            print(f"Group Name: {self.match_group_name}")
            
            try:
                # Get index from consistent hash
                index = self.channel_layer.consistent_hash(self.match_group_name)
                print(f"Connection index: {index}")
                
                # Get connection using index
                connection = await self.channel_layer.connection(index)
                print(f"Connection obtained: {connection}")
                
                # Check if connection is alive
                await connection.ping()
                print("Redis connection is alive")
                
                # Check group membership
                group_key = f"{self.channel_layer.prefix}:groups:{self.match_group_name}"
                members = await connection.smembers(group_key)
                print(f"Group members: {members}")
                
                # Attempt broadcast
                before_send = time.time()
                formatted_event = {
                    "type": "send_match_event",  # Must match the consumer method name
                    "data": event  # The actual event data
                }
                
                print(f"Broadcasting event: {formatted_event}")
                
                # Send to group
                await self.channel_layer.group_send(
                    self.match_group_name,
                    formatted_event
                )
                after_send = time.time()
                
                # Check message queues after send
                for member in members:
                    channel_key = f"{self.channel_layer.prefix}:channel:{member}"
                    queue_length = await connection.llen(channel_key)
                    print(f"Message queue for {member}: {queue_length} messages")

                                # Debug: Check actual message content
                    if queue_length > 0:
                        messages = await connection.lrange(channel_key, 0, -1)
                        print(f"Queue contents: {messages}")
                
                print(f"Broadcast took {after_send - before_send:.4f} seconds")
                #time.sleep(6)  # Give time to see the output
                
            except Exception as e:
                print(f"Redis operation error: {e}")
                print(traceback.format_exc())
                #time.sleep(6)  # Give time to see the error
                
        except Exception as e:
            print(f"Broadcast error: {e}")
            print(traceback.format_exc())
            #time.sleep(6)  # Give time to see the error
                          
            
            
    """   
    async def test_group_members(self, group_name):
        print("hit test_group")
        channel_layer = get_channel_layer()
        if hasattr(channel_layer, "groups"):
            for group_name, group_data in channel_layer.groups.items():
                print(f"Group: {group_name}")
                print(f"Members: {group_data}")

        # Access channels (active connections)
        if hasattr(channel_layer, "channels"):
            for channel_name, channel_data in channel_layer.channels.items():
                print(f"Channel: {channel_name}")
                print(f"Data: {channel_data}")
    """
    
    def get_grid_map(self):
        """Return the cached grid map or create it if not exists."""
        if not hasattr(self, '_grid_map'):
            self._grid_map = self.define_dynamic_zones(self.grid_width, self.grid_length)
        return self._grid_map
    
    def get_center_of_ground(self):
        """Calculate the center point of the ground, handling both odd and even dimensions."""
        # For a 13x16 grid:
        # Width: 13 cells (0-12) -> center is 6 (rounds down from 6.5)
        # Length: 16 cells (0-15) -> center is 8 (rounds up from 7.5)
        
        # Use float division first to handle even dimensions more accurately
        center_x = (self.grid_width - 1) / 2
        center_y = (self.grid_length - 1) / 2
        
        # Round to nearest integer to get actual grid position
        center_x = round(center_x)
        center_y = round(center_y)
        
        print(f"Center of ground: ({center_x}, {center_y})")
        return (center_x, center_y)
    
    def define_dynamic_zones(self, grid_width, grid_length):
        """Define zones for each position on the ground with dynamic sizing."""
        # For odd-width grounds (e.g., 13 cells):
        # Left: 4, Center: 5, Right: 4
        left_width = (grid_width - 1) // 3  # 4 cells for width=13
        right_width = left_width            # Same as left
        center_width = grid_width - (left_width * 2)  # Center gets the extra cell
        
        # Calculate section length dynamically
        # 5 lines of players (FB, HB, C, HF, FF)
        section_length = grid_length // 5
        # Add extra cells for overlap
        overlap = max(1, int(section_length * 0.2))  # 20% overlap
        
        positions = [
            ["LB", "FB", "RB"],      # Back line
            ["LHB", "CHB", "RHB"],   # Half-back line
            ["LWing", "Centre", "RWing"],  # Centre line
            ["LHF", "CHF", "RHF"],   # Half-forward line
            ["LF", "FF", "RF"]       # Forward line
        ]
        
        positions_zones = {}
        
        # Assign zones with proportional overlap
        for i, row in enumerate(positions):
            for j, position in enumerate(row):
                if position in ['Ruck', 'Rover', 'RuckRover']:
                    continue
                # Determine x range (width) with proportional overlap
                if j == 0:  # Left positions
                    x_range = range(0, left_width + overlap)
                elif j == 1:  # Center positions
                    x_range = range(left_width - overlap, 
                                    left_width + center_width + overlap)
                else:  # Right positions
                    x_range = range(left_width + center_width - overlap, 
                                    grid_width)
                
                # Determine y range (length) with proportional overlap
                start_y = max(0, i * section_length - overlap)
                end_y = min(grid_length, (i + 1) * section_length + overlap)
                y_range = range(start_y, end_y)
                
                # Create zone with overlapping areas
                zone = [(x, y) for x in x_range for y in y_range]
                positions_zones[position] = zone   
 
        
        print(f"Ground dimensions: {grid_width}x{grid_length}")
        print(f"Zone widths - Left: {left_width}, Center: {center_width}, Right: {right_width}")
        print(f"Section length: {section_length} with {overlap} cell overlap")
        
        return positions_zones

    """
    def get_position_map(self):
        return {
            "LB": ["FB", "RB", "LHB", "CHB", "RHB"], 
            "RB": ["FB", "LB", "LHB", "CHB", "RHB"],
            "LHB": ["LWing", "CHB", "RHB", "Centre", "CHF", "LHF", "RHF"],
            "RHB": ["RWing", "CHB", "LHB", "Centre", "CHF", "LHF", "RHF"], 
            "LWing": ["LHF", "Centre", "RWing", "CHF"],
            "RWing": ["RHF", "Centre", "LWing", "CHF"],
            "LHF": ["LF", "CHF", "RF", "FF"],
            "RHF": ["RF", "CHF", "LF", "FF"],
            "LF": ["FF", "RF"],
            "RF": ["FF", "LF"]
        }
    """
    def collect_player_stats(self):
        return self.stats_manager.collect_player_stats()
    
    
    async def simulate_contest(self):
        """Simulate a single contest within the game."""
        self.contest_count += 1
        print(f"Contest Count {self.contest_count}")
            
        try:
            # Start with center bounce
            result = self.simulate_center_bounce()  # Make sure this is awaited
            
            # Maximum actions per contest to prevent infinite loops
            max_actions = 12
            action_count = 0
            
            # Process actions until contest ends or max actions reached
            while action_count < max_actions:
                action_count += 1
                print(f'Current action {action_count}: {result.get("next_action")}')
                
                # Yield current result
                if result:
                    yield {
                        'type': 'match_event',
                        'quarter': self.quarter,
                        'event': result['result'] if 'result' in result else str(result)
                    }
                
                # Check for contest end conditions first
                if result.get('next_action') in ['goal', 'behind', 'ball_up', 'center_bounce']:
                    print(f"Contest ending due to: {result['next_action']}")
                    yield {
                        'type': 'match_event',
                        'quarter': self.quarter,
                        'event': f"Contest ended: {result['next_action']}"
                    }
                    return  # End the contest
                
                # Handle different action types
                if result['next_action'] == 'handle_possession':
                    result = self.handle_possession(
                        result['team'],
                        result['team_players']
                    )
                    
                elif result['next_action'] in ["advance_ball", "handball", "kick"]:
                    self.update_game_state(result['team'])
                    result = self.advance_ball(
                        result['team'],
                        result['team_players'],
                        result['position'],
                        result.get('receiver'),
                        result.get('known_next_position'),
                        result.get('disp_type')
                    )
                    
                elif result['next_action'] in ["kick_in", "out_on_full", "consider_pass"]:
                    self.update_game_state(result['player'].team)
                    next_pos, receiver, action = self.handle_disposal(
                        result['player'],
                        result['position'],
                        result['team_players'],
                        result['opponent_players'],
                        result['team_tactics']   
                    )
                    if receiver != None:
                        team_players = self.team2_players if receiver.team == self.team1 else self.team1_players

                        result = {
                        "result": f"{receiver.name} Chosen From {action}",
                        "next_action": "advance_ball",
                        "team": receiver.team,
                        "team_players": team_players,
                        "position": self.ball_position,
                        "receiver": receiver,
                        "next_position": next_pos,
                        "disp_type": action
                    }
                    else:
                        team_players = self.team2_players if self.disposer.team == self.team1 else self.team1_players
                        print(f"No receiver found for next action {result['next_action']} returned.")
                        result = {
                        "next_action": "handle_possession",
                        "team": self.disposer.team,
                        "team_players": team_players,
                    }
                    


                elif result['next_action'] in ["turnover"]:    
                    print(f"Turnover")
                    sys.exit()
                
                await asyncio.sleep(0.1)
        
            print(f"Contest ended after {action_count} actions")
            yield {
                'type': 'match_event',
                'quarter': self.quarter,
                'event': f"Contest ended after {action_count} actions"
            }
                
        except Exception as e:
            import traceback
            print(f"Error in contest simulation: {e}")
            print("Full traceback:")
            traceback.print_exc()
            sys.exit(1)
            
       
    
    """
    async def simulate_contest(self):
        self.contest_count = 0
        while self.contest_count < 50:
            self.contest_count += 1
            print(f"Contest Count {self.contest_count}")
            #time.sleep(1)
            #print("Broadcasting Result")
            # Start with initial action
            result = self.simulate_center_bounce()
            await self.broadcast_event({
                'quarter': self.quarter,
                'event': result['result']
            })
                # Continue processing actions based on returns
            #while True:
            print(f'Result: {result}')
            if result['next_action'] == 'handle_possession':
                result = self.handle_possession(
                    result['team'],
                    result['team_players']
                )
                #self.contest_count += 1
                #print(f"Contest Count {self.contest_count}")
            elif result['next_action'] in ["advance_ball", "handball", "kick"]:
                result = self.advance_ball(
                    result['team'],
                    result['team_players'],
                    result['position'],
                    result.get('receiver'),
                    result.get('known_next_position'),
                    result.get('disp_type')
                )
                #self.contest_count += 1
                #print(f"Contest Count {self.contest_count}")                
            #elif result['next_action'] == "handball":
            #    result = self.handball(
            #        result['player'],
            #        result['position']   
            #    )
            #elif result['next_action'] == "kick":
            #    result = self.kick(
            #        result['player'],
            #        result['position']   
            #    )
            elif result['next_action'] in ["kick_in", "out_on_full", "consider_pass"]:
                #next_position, receiver, action = def handle_disposal(self, player, current_position, team_players, opponent_players, team_tactics):
                result['next_position'], result['receiver'], result['action'] = self.handle_disposal(
                    result['player'],
                    result['position'],
                    result['team_players'],
                    result['opponent_players'],
                    result['team_tactics']   
                )
                #self.contest_count += 1
                #print(f"Contest Count {self.contest_count}")
                #team_players = self.team2_players if result['receiver'].team == self.team1 else self.team2_players
                team_players = self.team2_players if result['receiver'].team == self.team1 else self.team2_players
                result.update({
                    "result": f"{result['receiver'].name} Chosen From {result['action']}",
                    "next_action": "advance_ball",
                    "team": result['receiver'].team,
                    "team_players": team_players,
                    "position": self.ball_position,
                    "receiver": result['receiver'],
                    "next_position": result['next_position'],
                    "disp_type": result['action']
                })
                #self.contest_count += 1
                #print(f"Contest Count {self.contest_count}")
                # Broadcast each result with rate limiting
                await asyncio.sleep(0.5)  # Add delay before broadcasting
                await self.broadcast_event({
                    'quarter': self.quarter,
                    'event': result['result']
                })
                    # Break if we hit an end condition
                #if result['next_action'] in ['goal', 'behind', 'ball_up']:
                #    print("break")
                #    break
        return "Match simulation completed"
    """

    def simulate_center_bounce(self):
        print("Simulating center bounce")
        ruck1 = self.team1_players["Ruck"]
        ruck2 = self.team2_players["Ruck"]
        winner = self.ruck_contest(ruck1, ruck2)
        
        if winner == self.team1:
            print(f"{self.team1.name} wins the center bounce")
            self.stats_manager.record_stat(ruck1.name, 'hitouts')
            # Continue with clearance but store result for next contest
            winner_team, team_players = self.clearance(self.team1, self.team2, self.team1_players, self.team2_players, self.team1)
            #self.next_contest_result = clearance_result  # Store for next iteration
            return {
           "result": f"{winner_team} wins center bounce through {self.disposer}",
           "next_action": "handle_possession",
           "team": winner_team,
           "team_players": team_players
            }
        else:
            print(f"{self.team2.name} wins the center bounce")
            self.stats_manager.record_stat(ruck2.name, 'hitouts')
            # Continue with clearance but store result for next contest
            winner_team, team_players = self.clearance(self.team1, self.team2, self.team1_players, self.team2_players, self.team1)
            #self.next_contest_result = clearance_result  # Store for next iteration
            return {
           "result": f"{winner_team} wins center bounce through {self.disposer}",
           "next_action": "handle_possession",
           "team": winner_team,
           "team_players": team_players
            }

    def ruck_contest(self, ruck1, ruck2):
        print(f"Ruck contest between {ruck1.name} and {ruck2.name}")
        ruck1_score = self.calculate_ruck_performance(ruck1)
        ruck2_score = self.calculate_ruck_performance(ruck2)
        print(f"{ruck1.name} score: {ruck1_score}, {ruck2.name} score: {ruck2_score}")
        return self.team1 if ruck1_score > ruck2_score else self.team2

    def calculate_ruck_performance(self, ruck):
        height_weight = 1.5
        strength_weight = 1.4
        agility_weight = 1.3
        mental_weight = 1.2
        tactical_weight = 1.1
        consistency_weight = 1.0
        age_weight = 0.9
        stamina_weight = 0.8

        performance = (
            (ruck.physical_stats.height * height_weight) +
            (ruck.physical_stats.strength * strength_weight) +
            (ruck.physical_stats.agility * agility_weight) +
            (ruck.ability_stats.mental * mental_weight) +
            (ruck.ability_stats.tactical * tactical_weight) +
            (ruck.ability_stats.consistency * consistency_weight) +
            (ruck.physical_stats.age * age_weight) +
            (ruck.physical_stats.stamina * stamina_weight)
        )

        # Add a random element to simulate the unpredictability of sports
        random_factor = random.uniform(0.8, 1.2)  # Random factor between 0.8 and 1.2
        performance *= random_factor

        # Print the calculated performance for debugging
        print(f"Calculated performance for {ruck.name}: {performance:.2f}")

        return performance

    def clearance(self, team1, team2, team1_players, team2_players, ruck_winner):
        print("Simulating clearance")
        midfielders_team1 = [team1_players["Rover"], team1_players["RuckRover"], team1_players["Centre"]]
        midfielders_team2 = [team2_players["Rover"], team2_players["RuckRover"], team2_players["Centre"]]

        team1_score = sum(self.calculate_midfielder_performance(player) for player in midfielders_team1)
        team2_score = sum(self.calculate_midfielder_performance(player) for player in midfielders_team2)

        # Add extra weight to the team whose ruck won the center bounce
        if ruck_winner == team1:
            team1_score *= 1.1
        else:
            team2_score *= 1.1

        print(f"Team 1 clearance score: {team1_score:.2f}, Team 2 clearance score: {team2_score:.2f}")

        if team1_score > team2_score:
            winner_team = team1
            winner_players = midfielders_team1
        else:
            winner_team = team2
            winner_players = midfielders_team2

        # Determine the player who gets the ball
        player_with_ball = random.choices(
            winner_players,
            weights=[self.calculate_midfielder_performance(player) for player in winner_players],
            k=1
        )[0]

        self.stats_manager.record_stat(player_with_ball.name, 'clearances')
        #test here
        self.disposer = player_with_ball
        print(f"{player_with_ball.name} from {winner_team.name} gets the ball after clearance")

        if player_with_ball.team == self.team1: 
            team_players = self.team1_players
        else:
            team_players = self.team2_players


        return winner_team, team_players

    def calculate_midfielder_performance(self, player):
        tactical_weight = 1.5
        mental_weight = 1.4
        strength_weight = 1.3
        versatility_weight = 1.2
        consistency_weight = 1.1
        age_weight = 1.0
        stamina_weight = 0.9

        performance = (
            (player.ability_stats.tactical * tactical_weight) +
            (player.ability_stats.mental * mental_weight) +
            (player.physical_stats.strength * strength_weight) +
            (player.ability_stats.versatility * versatility_weight) +
            (player.ability_stats.consistency * consistency_weight) +
            (player.physical_stats.age * age_weight) +
            (player.physical_stats.stamina * stamina_weight)
        )

        # Add a random element to simulate the unpredictability of sports
        random_factor = random.uniform(0.8, 1.2)  # Random factor between 0.8 and 1.2
        performance *= random_factor

        # Print the calculated performance for debugging
        print(f"Calculated performance for {player.name}: {performance:.2f}")

        return performance
    
    def possession_contest(self, receiver, opponent):
        print("Simulating possession contest")

        receiver_weights = {
            'marking': 1.5,
            'strength': 1.4,
            'consistency': 1.3,
            'agility': 1.2,
            'age': 1.1,
            'stamina': 1.0
        }

        opponent_weights = {
            'tackling': 1.5,
            'strength': 1.4,
            'consistency': 1.3,
            'speed': 1.2,
            'age': 1.1,
            'stamina': 1.0
        }
        print(f"Reciever {receiver} Opponent {opponent}")
        receiver_performance = self.calculate_performance(receiver, receiver_weights)
        opponent_performance = self.calculate_performance(opponent, opponent_weights)

        print(f"{receiver.name} performance: {receiver_performance:.2f}")
        print(f"{opponent.name} performance: {opponent_performance:.2f}")

        return receiver_performance, opponent_performance

    def calculate_performance(self, player, weights):
        performance = 0
        for attribute, weight in weights.items():
            performance += getattr(player.ability_stats, attribute, 0) * weight
            performance += getattr(player.physical_stats, attribute, 0) * weight

        random_factor = random.uniform(0.8, 1.2)  # Random factor between 0.8 and 1.2
        performance *= random_factor

        print(f"Calculated performance for {player.name}: {performance:.2f}")
        return performance
    
    def calculate_goal_kicking_performance(self, player):
        goal_kicking_weight = 1.5
        mental_weight = 1.4
        strength_weight = 1.3
        consistency_weight = 1.2
        age_weight = 1.1
        stamina_weight = 1.0

        performance = (
            (player.ability_stats.goal_kicking * goal_kicking_weight) +
            (player.ability_stats.mental * mental_weight) +
            (player.physical_stats.strength * strength_weight) +
            (player.ability_stats.consistency * consistency_weight) +
            (player.physical_stats.age * age_weight) +
            (player.physical_stats.stamina * stamina_weight)
        )

        # Add a small random factor to simulate unpredictability
        random_factor = random.uniform(0.9, 1.1)
        performance *= random_factor

        print(f"Calculated goal kicking performance for {player.name}: {performance:.2f}")

        return performance
    
    def handle_possession(self, team, team_players):
        #time.sleep(10)
        self.where_is_player()
        print(f"{team.name} handling possession")
        self.ball_position_name = self.get_ball_position()
        print(f"Ball position: {self.ball_position} ({self.ball_position_name})")
        self.update_game_state(team)
        
        # Get the player in position
        #player = team_players.get(self.disposer)
        player = self.disposer
        print(f"Got Player {player} Position: {player.current_position}")
        #time.sleep(1)
        if not player or not player.is_in_position(self.ball_position_name):
            print(f"Player {player.name} not in position {self.ball_position_name}")
            #time.sleep(2)
            all_players = list(self.team1_players.values()) + list(self.team2_players.values())
            closest_player = Player.determine_closest_player_to_ball(self, self.ball_position, all_players, exclude_player=player)
            print(f"{closest_player.name} gets to the ball based on proximity and speed/agility")
            team = self.team1 if closest_player.team == self.team1 else self.team2
            player = closest_player
            print(f'Team: {team}')
            return {
                "result": f"{closest_player.name} gets to the ball based on proximity and speed/agility",
                "next_action": "advance_ball",
                "team": closest_player.team,
                "team_players": team_players,
                #"player": player,
                "position": self.ball_position,
                "receiver": closest_player
            }
        #time.sleep(1)
            # Handle scoring positions
        if self.ball_position_name in ["LF", "RF", "FF", "CHF", "RHF", "LHF"]:
            print("Attempt Goal")
            #time.sleep(3)
            result = self.attempt_goal(player, self.ball_position_name)
            if result == "goal":
                self.ball_position_name = self.get_ball_position()
                self.update_game_state(team, "goal")
                action = "center_bounce"
                return {
                "result": result,
                "next_action": action,
                }
            elif result in ["kick_in", "behind"]:
                self.ball_position_name = self.get_ball_position()
                team = self.team2 if team == self.team1 else self.team2
                team_players = self.team2_players if team == self.team1 else self.team2_players
                opponent_players = self.team2_players if team == self.team1 else self.team1_players
                self.update_game_state(team)
                player = next((player for player in team_players.values() if player.position == self.ball_position_name))
                team_tactics = self.team1_tactics if team == self.team1 else self.team2_tactics
                action = "kick_in"
                #next_position, receiver, action = self.handle_disposal(player, self.position, team_players, opponent_players, team_tactics)
                return {
                    "result": f"{result}",
                    "next_action": action,
                    "player": player,
                    "position": self.ball_position,
                    "team_players": team_players,
                    "opponent_players": opponent_players,
                    "team_tactics": team_tactics,
                }
            elif result == ("out_on_full"):
                self.ball_position_name = self.get_ball_position()
                team = self.team2 if team == self.team1 else self.team2
                team_players = self.team2_players if team == self.team1 else self.team2_players
                self.update_game_state(team)
                player = next((player for player in team_players.values() if player.position == self.ball_position_name))
                opponent_players = self.team2_players if team == self.team1 else self.team1_players
                team_tactics = self.team1_tactics if team == self.team1 else self.team2_tactics
                action = "out_on_full"
                return {
                "result": result,
                "next_action": action,
                "player": player,
                "position": self.ball_position_name,
                "team_players": team_players,
                "opponent_players": opponent_players,
                "team_tactics": team_tactics,
                }
            elif result.get("next_action") == ("consider_pass"):
               #team_players = self.team1_players if team == self.team1 else self.team2_players
               team_tactics = self.team1_tactics if team == self.team1 else self.team2_tactics
               team_players = self.team2_players if team == self.team1 else self.team2_players
               opponent_players = self.team2_players if team == self.team1 else self.team1_players
               return {
                    "result": result.get('result'),
                    "next_action": result.get('next_action'),
                    "player": result.get('player'),
                    "position": result.get('position'),
                    "team_players": team_players,
                    "opponent_players": opponent_players,
                    "team_tactics": team_tactics,
                }
            # Handle defensive/midfield positions
        if self.ball_position_name in ["RBP", "LBP", "FB", "LHB", "RHB", "CHB", "LWing", "RWing", "Centre"]:
            team_tactics = self.team1_tactics if team == self.team1 else self.team2_tactics
            opponent_players = self.team2_players if team == self.team1 else self.team1_players
            print(f"Player {player.name} Position {self.ball_position_name}")
            #time.sleep(3)
            #next_position = self.get_next_position(player.position, player, team_tactics)
            next_position, receiver, action = self.handle_disposal(player, self.ball_position, team_players, opponent_players, team_tactics)

            if next_position and receiver and action != "play_on":
                team_players = self.team2_players if receiver.team == self.team1 else self.team2_players      
                return {
                    "result": f"{receiver.name} Chosen From {action}",
                    "next_action": "advance_ball",
                    "team": receiver.team,
                    "team_players": team_players,
                    "position": self.ball_position,
                    "receiver": receiver,
                    "next_position": next_position,
                    "disp_type": action
                }
            elif action == "goal":
                self.ball_position_name = self.get_ball_position()
                self.update_game_state(team, "goal")
                action = "center_bounce"
                return {
                "result": result,
                "next_action": action,
                }
            elif action in ["kick_in", "behind"]:
                self.ball_position_name = self.get_ball_position()
                team = self.team2 if team == self.team1 else self.team2
                team_players = self.team2_players if team == self.team1 else self.team2_players
                opponent_players = self.team2_players if team == self.team1 else self.team1_players
                self.update_game_state(team)
                player = next((player for player in team_players.values() if player.position == "FB"))
                team_tactics = self.team1_tactics if team == self.team1 else self.team2_tactics
                action = "kick_in"
                #next_position, receiver, action = self.handle_disposal(player, self.position, team_players, opponent_players, team_tactics)
                return {
                    "result": f"{result}",
                    "next_action": action,
                    "player": player,
                    "position": self.ball_position,
                    "team_players": team_players,
                    "opponent_players": opponent_players,
                    "team_tactics": team_tactics,
                }
            else:
                #if action in ["tackled", "turnover", "kick", "handball"]:
                team_players = self.team2_players if self.disposer.team == self.team1 else self.team2_players
                self.update_game_state(self.disposer.team)
                return {
                    "result": f"{self.disposer.name} Chosen From {action}",
                    "next_action": "handle_possession",
                    "team": self.disposer.team,
                    "team_players": team_players,
                }
 
        is_near_boundary, _ = self.is_position_near_boundary(self.ball_position)
        if is_near_boundary:
            self.out_of_bounds_count_possession += 1
            if self.out_of_bounds_count_possession >= 2:
                self.out_of_bounds_count_possession = 0
                return {
                    "result": "Ball out of bounds",
                    "next_action": "throw_in",
                    "position": self.ball_position
                }
            # Default case - advance ball
        print("Hit Return")
        #time.sleep(1)    
        return {
            "result": f"{player.name} looks to advance the ball",
            "next_action": "advance_ball",
            "team": team,
            "team_players": team_players,
            #"player": player,
            "position": self.ball_position
        }
        
    """
    def handle_possession(self, team, team_players):
        #time.sleep(3)
        if self.contest_count >= 50:
            return self.simulate_contest()
        self.position = self.get_ball_position()
        print(f"Player Init {self.position}")
        #self.update_player_positions(self.get_play_state())
        self.update_game_state(team)
        print(f"{team.name} handling possession")

        
        print(f"Ball position: {self.ball_position} ({self.position})")
        print(f"Checking position: {self.position}")
        print(f"Team Players: {team_players}")

        player = team_players.get(self.position)

        if not player or not player.is_in_position(self.position):
            all_players = list(self.team1_players.values()) + list(self.team2_players.values())
            closest_player = Player.determine_closest_player_to_ball(self, self.ball_position, all_players)
            print(f"{closest_player.name} gets to the ball based on proximity and speed/agility")
            if closest_player.team == self.team1: 
                team = self.team1
            else:
                team = self.team2
            player = closest_player
            #team, team_players, current_position, receiver=None, known_next_position=None
            
            #return self.advance_ball(closest_player.team, team_players, self.ball_position, closest_player)

        print(f"Player {player.name} is in position")

    

        if self.position in ["LF", "RF", "FF", "CHF", "RHF", "LHF"]:
            result = self.attempt_goal(player, self.position)
            if result == "goal":
                self.position = self.get_ball_position()
                self.update_game_state(team, "goal")
                #sys.exit()
                return self.simulate_center_bounce()
            if result in ["kick_in", "behind"]:
                self.position = self.get_ball_position()
                team = self.team2 if team == self.team1 else self.team2
                team_players = self.team2_players if team == self.team1 else self.team2_players
                self.update_game_state(team)
                player = team_players.get(self.position)
                print(f"Behind FB kicks out {player}")
                #sys.exit()
                self.consider_pass(player,self.position)
            elif result == "out_on_full":
                self.position = self.get_ball_position()
                team = self.team2 if team == self.team1 else self.team2
                team_players = self.team2_players if team == self.team1 else self.team2_players
                self.update_game_state(team)
                player = team_players.get(self.position)
                print(f"out_on_full {player}")
                #sys.exit()
                self.consider_pass(player,self.position)
                #return self.throw_in(self.ball_position)
            #elif result == "pass":
                #return self.handle_possession(team, team_players)
        if player.position in ["LHB", "RHB", "CHB", "LWing", "RWing", "Centre"]:
            if random.random() < 0.6:  # 60% chance to handball in midfield/defensive positions
                return self.handball(player, self.position)
            else:
                return self.kick(player, self.position)
        else:
            is_near_boundary, _ = self.is_position_near_boundary(self.ball_position)
            if is_near_boundary:
                print("Near boundary")
                self.out_of_bounds_count_possession += 1
                print(f"Out of Bounds count Possession: {self.out_of_bounds_count_possession}")
                if self.out_of_bounds_count_possession < 2:
                    print("Ball position out of bounds, retrying")
                    self.advance_ball(team, team_players, self.position, player)
                    return self.handle_possession(team, team_players)  # Retry possession
                else:
                    print("Ball position out of bounds, performing throw-in")
                    self.out_of_bounds_count_possession = 0
                    self.throw_in(self.ball_position)
                    self.simulate_center_bounce()
        print("Hit end of Handle_possession, should run advance ball")
        
        #self.handle_possession(team, team_players)
        self.advance_ball(team, team_players, self.position, player)

        print("hit return")
        time.sleep(3)
        return self.simulate_contest()
        

    def move_ball(self):
        print(f"Current ball position before moving: {self.ball_position}")
        new_x = self.ball_position[0] + random.choice([-1, 0, 1])
        new_y = self.ball_position[1] + random.choice([-1, 0, 1])

        # Introduce a probability check for throw-ins
        if random.random() < 0.2:  # 20% chance for a throw-in
            if new_x < 0 or new_x > 2 or new_y < 0 or new_y > 4:
                print(f"Ball out of bounds at position: ({new_x}, {new_y})")
                self.throw_in(self.ball_position)
                return

        # Ensure the ball stays within the grid
        new_x = max(0, min(new_x, 2))
        new_y = max(0, min(new_y, 4))

        self.ball_position = (new_x, new_y)
        print(f"New ball position after moving: {self.ball_position}")
        return
        
    #def get_ball_position(self):
        grid_map = self.get_grid_map()
        x, y = self.ball_position
        print(f"Get Ball Position {x} {y} and len grid_map {len(grid_map)}")
        if -1 < y < len(grid_map) and -1 < x < len(grid_map[y]):
                #print(f"Returning X Y {x} {y}")
                return grid_map[y][x]
        self.out_of_bounds_count += 1
        print(f"Out of Bounds count Ball Position: {self.out_of_bounds_count}")
        if self.out_of_bounds_count < 3:
            print("Ball position out of bounds, retrying")
            self.move_ball()
            return self.get_ball_position()  # Ensure we return the new position
        else:
            print("Ball position out of bounds, performing throw-in")
            self.out_of_bounds_count = 0
            return self.throw_in(self.ball_position)
    """    
    def get_ball_position(self):
        #x, y = self.ball_position
        position = self.get_position_from_coordinates(self.ball_position)
        if position:
            print(f"The ball is in the {position} position.")
            return position
        else:
            print("The ball is out of bounds!")
            return self.throw_in(self.ball_position)
        
    def update_game_state(self, team_in_possession, goal=None):
        opponent_team = self.get_opponent(team_in_possession)
        opponent_players = self.get_opponent_players(team_in_possession)
        team_players = self.team1_players if team_in_possession == self.team1 else self.team2_players
        
        # Determine game state and zones
        play_state = self.get_play_state(team_in_possession, goal)
        field_zones = self.calculate_field_zones(self.ball_position, play_state)
        print(f"Field Zones: {field_zones}")
        #time.sleep(1)
        # Update positions for all players except disposer
        disposer = self.disposer
        for team, is_possession_team in [(team_players, True), (opponent_players, False)]:
            for player in team.values():
                if player != disposer:
                    self.update_individual_player_position(
                        player=player,
                        is_possession_team=is_possession_team,
                        play_state=play_state,
                        field_zones=field_zones,
                        ball_position=self.ball_position
                    )

    def calculate_field_zones(self, ball_position, play_state):
       """Calculate hot/contest/defensive zones based on ball position and play state."""
       x, y = self.get_coordinates(ball_position)
       grid_width = self.grid_width  # 13
       grid_length = self.grid_length  # 16
       
       # Define larger base zones radiating out from ball
       contest_zone = [(dx, dy) for dx in range(-2, 3) for dy in range(-2, 3)]  # 5x5 area
       support_zone = [(dx, dy) for dx in range(-4, 5) for dy in range(-4, 5) 
                      if (dx, dy) not in contest_zone]  # 9x9 area minus contest zone
       
       # Mobile zone should never be empty - create based on play state
       mobile_zone = []
       if play_state == "attacking":
           # Mobile positions get extended forward movement
           for dx in range(-3, 4):
               for dy in range(0, 5):  # More forward movement
                   if (dx, dy) not in contest_zone and (dx, dy) not in support_zone:
                       mobile_zone.append((dx, dy))
       elif play_state == "defending":
           # Mobile positions drop back
           for dx in range(-3, 4):
               for dy in range(-5, 1):  # More backward movement
                   if (dx, dy) not in contest_zone and (dx, dy) not in support_zone:
                       mobile_zone.append((dx, dy))
       else:  # transitioning
           # Mobile positions spread wide
           for dx in range(-4, 5):
               for dy in range(-3, 4):
                   if (dx, dy) not in contest_zone and (dx, dy) not in support_zone:
                       mobile_zone.append((dx, dy))
       
       # Calculate actual positions and filter out-of-bounds
       def validate_positions(positions):
           valid = []
           for dx, dy in positions:
               new_x, new_y = x + dx, y + dy
               if 0 <= new_x < grid_width and 0 <= new_y < grid_length:
                   valid.append((new_x, new_y))
           return valid
       
       contest_positions = validate_positions(contest_zone)
       support_positions = validate_positions(support_zone)
       mobile_positions = validate_positions(mobile_zone)
       
       print(f"Contest Zone: {contest_positions}")
       print(f"Support Zone: {support_positions}")
       print(f"Mobile Zone: {mobile_positions}")
       
       return {
           'contest_zone': contest_positions,
           'support_zone': support_positions,
           'mobile_zone': mobile_positions,
           'play_state': play_state
       }

    def update_individual_player_position(self, player, is_possession_team, play_state, field_zones, ball_position):
        """Update individual player position based on situation and abilities."""
        
        current_coords = self.get_coordinates(player.current_position)
        ball_coords = self.get_coordinates(ball_position)
        distance_to_ball = player.calculate_distance(ball_coords)
        
        # Base movement probability influenced by player stats
        movement_factor = (
           (player.physical_stats.speed + player.physical_stats.agility) / 40 +  # Physical ability (max 40 -> 0-1)
           (player.ability_stats.tactical + player.ability_stats.mental) / 40 +  # Decision making (max 40 -> 0-1)
           random.uniform(-0.1, 0.1)  # Small random factor
        )
            # Different behavior based on distance to ball and team possession
        if distance_to_ball <= 3.1:  # Close to ball
            if is_possession_team:
                print(f"Close Teammate Movement {player.name} and {player.team}")
                self.handle_close_teammate_movement(player, ball_coords, field_zones, movement_factor)
            else:
                print(f"Close Opponent Movement {player.name} and {player.team}")
                self.handle_close_opponent_movement(player, ball_coords, field_zones, movement_factor)
        else:  # Further from ball
            print(f"Distant Player Movement {player.name} and {player.team}")
            self.handle_distant_player_movement(
                player, ball_coords, field_zones, movement_factor * 1.2,  # Increased movement factor
                is_possession_team, play_state
            )

    def handle_close_teammate_movement(self, player, ball_coords, field_zones, movement_factor):
        """Handle movement for teammates close to the ball."""
        
        # Calculate optimal support positions
        support_positions = self.calculate_support_positions(ball_coords, player.position)
        
        # Weight positions based on player abilities and role
        weighted_positions = []
        for pos in support_positions:
            weight = 1.0
            
            # Adjust weight based on player's natural position
            position_suitability = self.calculate_position_suitability(player, pos)
            weight *= (0.5 + position_suitability)
            
            # Adjust for player abilities
            if pos in field_zones['contest_zone']:
                weight *= (1 + player.physical_stats.strength / 20)
            else:
                weight *= (1 + player.physical_stats.speed / 20)
                
            weighted_positions.append((pos, weight))
        
        # Choose new position
        if weighted_positions:
            new_pos = random.choices(
                [p[0] for p in weighted_positions],
                weights=[p[1] for p in weighted_positions],
                k=1
            )[0]
            
            
            # Apply movement based on movement_factor
            if random.random() < movement_factor:
                player.current_position = self.get_position_from_coordinates(new_pos)
                print(f"New Position: {player.current_position}")  
                #time.sleep(1)

    def handle_close_opponent_movement(self, player, ball_coords, field_zones, movement_factor):
        """Handle movement for opponents close to the ball."""
        
        # Defensive players more likely to stay close and contest
        defensive_tendency = (player.ability_stats.tackling + player.physical_stats.strength) / 40
        
        if random.random() < defensive_tendency:
            # Move to contest position
            contest_positions = [pos for pos in field_zones['contest_zone'] 
                                if self.get_position_from_coordinates(pos)]
            if contest_positions:
                new_pos = min(contest_positions, 
                                key=lambda p: player.calculate_distance(p))
                player.current_position = self.get_position_from_coordinates(new_pos)
        else:
            # Move to defensive position
            self.move_to_defensive_position(player, ball_coords, movement_factor)

    def handle_distant_player_movement(self, player, ball_coords, field_zones, movement_factor, 
                                        is_possession_team, play_state):
        """Handle movement for players further from the ball."""
        
        # Calculate strategic position based on play state and team possession
        strategic_positions = self.calculate_strategic_positions(
            player, play_state, is_possession_team, ball_coords
        )
        
        # Weight positions based on player stamina and current fatigue
        stamina_factor = (player.physical_stats.stamina / 20) * 1.5  # Increased multiplier
        movement_probability = movement_factor * stamina_factor
        
        if strategic_positions and random.random() < movement_probability:
            # Choose position based on tactical ability and role
            weighted_positions = [
                (pos, self.calculate_position_weight(pos, player, play_state))
                for pos in strategic_positions
            ]
            
            new_pos = random.choices(
                [p[0] for p in weighted_positions],
                weights=[p[1] for p in weighted_positions],
                k=1
            )[0]
            
            player.current_position = self.get_position_from_coordinates(new_pos)
            print(f"New Position: {player.current_position}")  
            self.where_is_player()
            #time.sleep(1)

    def calculate_support_positions(self, ball_coords, player_natural_position):
        """Calculate optimal support positions based on ball location and player role."""
        x, y = ball_coords
        grid_width = self.grid_width  # Should get from field dimensions
        grid_length = self.grid_length  # Should get from field dimensions
        
        # Calculate relative distances based on field size
        short_dist = 2
        med_dist = min(3, grid_width // 2)
        long_dist = min(4, grid_width - 1)
       
        
        # Define role-based support patterns
        support_patterns = {
            # Forward line
            "FF": [(0, short_dist), (short_dist, short_dist), (-short_dist, short_dist)],
            "CHF": [(0, short_dist), (med_dist, 0), (-med_dist, 0)],
            "LF": [(-med_dist, short_dist), (-med_dist, 0), (-long_dist, short_dist)],
            "RF": [(med_dist, short_dist), (med_dist, 0), (long_dist, short_dist)],
            "LHF": [(-med_dist, short_dist), (-long_dist, 0), (-med_dist, -short_dist)],
            "RHF": [(med_dist, short_dist), (long_dist, 0), (med_dist, -short_dist)],
            
            # Midfield line
            "LWing": [(-long_dist, 0), (-med_dist, short_dist), (-med_dist, -short_dist)],
            "RWing": [(long_dist, 0), (med_dist, short_dist), (med_dist, -short_dist)],
            
            # Backline
            "FB": [(0, -short_dist), (-short_dist, -short_dist), (short_dist, -short_dist)],
            "CHB": [(0, -short_dist), (med_dist, -short_dist), (-med_dist, -short_dist)],
            "LB": [(-med_dist, -short_dist), (-long_dist, -short_dist)],
            "RB": [(med_dist, -short_dist), (long_dist, -short_dist)],
            "LHB": [(-med_dist, -short_dist), (-long_dist, -short_dist), (-med_dist, 0)],
            "RHB": [(med_dist, -short_dist), (long_dist, -short_dist), (med_dist, 0)]
        }
        
        # Handle mobile positions differently
        mobile_positions = ['Centre', 'Ruck', 'Rover', 'RuckRover']
        if player_natural_position in mobile_positions:
            patterns = []
            for dx in range(-3, 4):  # Increased from med_dist
                for dy in range(-3, 4):
                    if dx == 0 and dy == 0:
                        continue
                    patterns.append((dx, dy))
            support_patterns[player_natural_position] = patterns
        
        # Get the pattern for this position
        pattern = support_patterns.get(player_natural_position, [(0, 1), (1, 0), (-1, 0)])
        
        # Generate actual coordinates and validate they're within bounds
        support_positions = []
        for dx, dy in pattern:
            new_x = x + dx
            new_y = y + dy
            if 0 <= new_x < grid_width and 0 <= new_y < grid_length:
                support_positions.append((new_x, new_y))
        
        print(f"Support positions for {player_natural_position} at {ball_coords}: {support_positions}")
        return support_positions
    
    def calculate_strategic_positions(self, player, play_state, is_possession_team, ball_coords):
        """Calculate strategic positions for players away from the ball."""
        x, y = ball_coords
        
        # Define zones based on play state
        if play_state == "attacking":
            if is_possession_team:
                zones = self.get_forward_zones(player.position, ball_coords)
            else:
                zones = self.get_defensive_zones(player.position, ball_coords)
        elif play_state == "defending":
            if is_possession_team:
                zones = self.get_defensive_support_zones(player.position, ball_coords)
            else:
                zones = self.get_pressing_zones(player.position, ball_coords)
        else:  # transitioning
            zones = self.get_transition_zones(player.position, ball_coords, is_possession_team)
        
        return [pos for pos in zones if not self.is_position_near_boundary(pos)]
    
    def get_forward_zones(self, player_position, ball_coords):
       """Get potential positions for forward movement."""
       x, y = ball_coords
       grid_width = self.grid_width  # 0-12
       grid_length = self.grid_length  # 0-15
       
       def is_valid_coord(new_x, new_y):
           return 0 <= new_x < grid_width and 0 <= new_y < grid_length
       
       if player_position in ["FF", "CHF", "LF", "RF", "LHF", "RHF"]:
           # Forwards look to create space and leading patterns
           return [
               (x + dx, y + dy) 
               for dx in range(-3, 4)  # Wider range
               for dy in range(0, 4)   # More forward movement
               if abs(dx) + abs(dy) <= 5 and is_valid_coord(x + dx, y + dy)
           ]
       elif player_position in ["LWing", "RWing"]:
           # Wings stay wide and push forward
           wing_side = 4 if "R" in player_position else -4  # More extreme wing positioning
           return [
               (x + wing_side + dx, y + dy)
               for dx in range(-1, 2)
               for dy in range(-1, 3)
               if abs(dx) + abs(dy) <= 3 and is_valid_coord(x + wing_side + dx, y + dy)
           ]
       else:
           # Defenders push up cautiously
           return [
               (x + dx, y + dy)
               for dx in range(-2, 3)
               for dy in range(-1, 2)
               if abs(dx) + abs(dy) <= 3 and is_valid_coord(x + dx, y + dy)
           ]
        
    def get_defensive_zones(self, player_position, ball_coords):
        """Get potential positions for defensive setup."""
        x, y = ball_coords
        grid_width = self.grid_width
        grid_length = self.grid_length
        
        def is_valid_coord(new_x, new_y):
            return 0 <= new_x < grid_width and 0 <= new_y < grid_length
        
        if player_position in ["FB", "CHB", "LB", "RB", "LHB", "RHB"]:
            # Defenders maintain defensive structure
            return [
                (x + dx, y + dy)
                for dx in range(-3, 4)  # Wider defensive coverage
                for dy in range(-4, 1)  # More depth in defense
                if abs(dx) + abs(dy) <= 5 and is_valid_coord(x + dx, y + dy)
            ]
        else:
            # Other players provide defensive pressure
            return [
                (x + dx, y + dy)
                for dx in range(-2, 3)
                for dy in range(-2, 1)
                if abs(dx) + abs(dy) <= 3 and is_valid_coord(x + dx, y + dy)
            ]
        
    def get_transition_zones(self, player_position, ball_coords, is_possession_team):
        """Calculate positions during transition play (ball in motion/contested)."""
        x, y = ball_coords
        grid_width = 13
        grid_length = 16
        
        def is_valid_coord(new_x, new_y):
            return 0 <= new_x < grid_width and 0 <= new_y < grid_length
        
        forwards = ["FF", "CHF", "LF", "RF", "LHF", "RHF"]
        defenders = ["FB", "CHB", "LB", "RB", "LHB", "RHB"]
        midfielders = ["Centre", "Rover", "RuckRover"]
        wings = ["LWing", "RWing"]
        
        if is_possession_team:
            if player_position in forwards:
                # Forwards make leading runs
                return [
                    (x + dx, y + dy)
                    for dx in range(-3, 4)
                    for dy in range(1, 5)  # Extended forward movement
                    if abs(dx) + abs(dy) <= 6 and is_valid_coord(x + dx, y + dy)
                ]
            elif player_position in midfielders:
                # Midfielders provide running support
                return [
                    (x + dx, y + dy)
                    for dx in range(-3, 4)
                    for dy in range(-2, 4)
                    if abs(dx) + abs(dy) <= 5 and is_valid_coord(x + dx, y + dy)
                ]
            elif player_position in wings:
                # Wings maintain extreme width in transition
                wing_side = 5 if "R" in player_position else -5
                return [
                    (x + wing_side + dx, y + dy)
                    for dx in range(-1, 2)
                    for dy in range(-2, 3)
                    if abs(dx) + abs(dy) <= 3 and is_valid_coord(x + wing_side + dx, y + dy)
                ]
            else:  # defenders
                return [
                    (x + dx, y + dy)
                    for dx in range(-2, 3)
                    for dy in range(-2, 2)
                    if abs(dx) + abs(dy) <= 3 and is_valid_coord(x + dx, y + dy)
                ]
        else:  # Defensive transition
            if player_position in defenders:
                return [
                    (x + dx, y + dy)
                    for dx in range(-3, 4)
                    for dy in range(-5, 0)  # Deeper defensive positioning
                    if abs(dx) + abs(dy) <= 5 and is_valid_coord(x + dx, y + dy)
                ]
            elif player_position in midfielders:
                return [
                    (x + dx, y + dy)
                    for dx in range(-2, 3)
                    for dy in range(-2, 2)
                    if abs(dx) + abs(dy) <= 3 and is_valid_coord(x + dx, y + dy)
                ]
            else:  # forwards and wings
                return [
                    (x + dx, y + dy)
                    for dx in range(-2, 3)
                    for dy in range(-3, 1)
                    if abs(dx) + abs(dy) <= 4 and is_valid_coord(x + dx, y + dy)
                ]
            
    def get_pressing_zones(self, player_position, ball_coords):
        """Calculate positions for pressing/defensive pressure."""
        x, y = ball_coords
        grid_width = self.grid_width
        grid_length = self.grid_length
        
        def is_valid_coord(new_x, new_y):
            return 0 <= new_x < grid_width and 0 <= new_y < grid_length
        
        if player_position in ["FF", "CHF", "LF", "RF", "LHF", "RHF"]:
            # Forwards press high
            return [
                (x + dx, y + dy)
                for dx in range(-2, 3)
                for dy in range(-2, 1)
                if abs(dx) + abs(dy) <= 3 and is_valid_coord(x + dx, y + dy)
            ]
        elif player_position in ["Centre", "Rover", "RuckRover"]:
            # Midfielders press aggressively
            return [
                (x + dx, y + dy)
                for dx in range(-2, 3)
                for dy in range(-2, 2)
                if abs(dx) + abs(dy) <= 2 and is_valid_coord(x + dx, y + dy)
            ]
        else:
            # Defenders and wings maintain structure while pressing
            return [
                (x + dx, y + dy)
                for dx in range(-3, 4)
                for dy in range(-3, 1)
                if abs(dx) + abs(dy) <= 4 and is_valid_coord(x + dx, y + dy)
            ]
        
    def get_defensive_support_zones(self, player_position, ball_coords):
        """Calculate positions for defensive support structure."""
        x, y = ball_coords
        grid_width = self.grid_width
        grid_length = self.grid_length
        
        def is_valid_coord(new_x, new_y):
            return 0 <= new_x < grid_width and 0 <= new_y < grid_length
        
        if player_position in ["FB", "CHB", "LB", "RB", "LHB", "RHB"]:
            # Key defenders maintain defensive structure
            return [
                (x + dx, y + dy)
                for dx in range(-4, 5)  # Wide defensive coverage
                for dy in range(-5, -1)  # Deep defensive positioning
                if abs(dx) + abs(dy) <= 6 and is_valid_coord(x + dx, y + dy)
            ]
        elif player_position in ["LWing", "RWing"]:
            # Wings provide wide defensive options
            wing_side = 4 if "R" in player_position else -4
            return [
                (x + wing_side + dx, y + dy)
                for dx in range(-1, 2)
                for dy in range(-3, 1)
                if abs(dx) + abs(dy) <= 3 and is_valid_coord(x + wing_side + dx, y + dy)
            ]
        else:
            # Midfielders and forwards drop back to help
            return [
                (x + dx, y + dy)
                for dx in range(-3, 4)
                for dy in range(-3, 0)
                if abs(dx) + abs(dy) <= 4 and is_valid_coord(x + dx, y + dy)
            ]
    def move_to_defensive_position(self, player, ball_coords, movement_factor):
        """Calculate and move to optimal defensive position based on player role and situation."""    
        x, y = ball_coords
        grid_width = 13  # 0-12
        grid_length = 16  # 0-15
        
        def is_valid_coord(new_x, new_y):
            return 0 <= new_x < grid_width and 0 <= new_y < grid_length
        
        # Define defensive positioning based on player's natural role
        defensive_roles = {
            # Key defenders - maintain defensive structure
            "FB": {"preferred_distance": 2, "preferred_side": 0},    # Stay central and close
            "CHB": {"preferred_distance": 2, "preferred_side": 0},
            "LB": {"preferred_distance": 2, "preferred_side": -3},   # Stay wide left
            "RB": {"preferred_distance": 2, "preferred_side": 3},    # Stay wide right
            "LHB": {"preferred_distance": 3, "preferred_side": -4},  # Very wide left
            "RHB": {"preferred_distance": 3, "preferred_side": 4},   # Very wide right
            
            # Midfield line
            "LWing": {"preferred_distance": 2, "preferred_side": -4},   # Wide left
            "RWing": {"preferred_distance": 2, "preferred_side": 4},    # Wide right
            
            # Mobile positions - more dynamic defensive positioning
            "Ruck": {"preferred_distance": 1, "preferred_side": 0},
            "Rover": {"preferred_distance": 1, "preferred_side": 0},
            "Centre": {"preferred_distance": 1, "preferred_side": 0}, 
            
            # Forward line - defensive pressure
            "FF": {"preferred_distance": 4, "preferred_side": 0},
            "CHF": {"preferred_distance": 3, "preferred_side": 0},
            "LF": {"preferred_distance": 4, "preferred_side": -3},
            "RF": {"preferred_distance": 4, "preferred_side": 3},
            "LHF": {"preferred_distance": 3, "preferred_side": -4},
            "RHF": {"preferred_distance": 3, "preferred_side": 4}
        }
        
        role_prefs = defensive_roles.get(player.position, {"preferred_distance": 2, "preferred_side": 0})
        
        # Special handling for mobile positions
        mobile_positions = ['Rover', 'RuckRover', 'Centre']
        if player.position in mobile_positions:
            # Mobile positions can defend more aggressively
            search_range = (-4, 5)   # Wider range of movement
            height_range = (-3, 3)   # Can move both forward and back
        else:
            search_range = (-3, 4)
            height_range = (-3, 2)   # Favor positions goal-side of the ball
        
        # Generate potential defensive positions
        defensive_positions = []
        preferred_distance = role_prefs["preferred_distance"]
        preferred_side = role_prefs["preferred_side"]
        
        for dx in range(*search_range):
            for dy in range(*height_range):
                new_x = x + dx
                new_y = y + dy
                
                if not is_valid_coord(new_x, new_y):
                    continue
                
                # Calculate position rating
                rating = self.calculate_defensive_position_rating(
                    player=player,
                    current_pos=(new_x, new_y),
                    ball_pos=ball_coords,
                    preferred_distance=preferred_distance,
                    preferred_side=preferred_side
                )
                
                # Mobile positions get bonus rating for central positions
                if player.position in mobile_positions:
                    central_bonus = 1 - (abs(dx) / 5)  # Higher rating for central positions
                    rating *= (1 + central_bonus)
                
                defensive_positions.append(((new_x, new_y), rating))
        
        # Apply movement factor and player attributes
        movement_chance = movement_factor * (
            0.5 +  # Base chance
            (player.physical_stats.speed / 40) +      # Speed influence (max 20 -> 0.5)
            (player.ability_stats.tactical / 40) +    # Tactical awareness (max 20 -> 0.5)
            (player.physical_stats.stamina / 40)      # Stamina influence (max 20 -> 0.5)
        )
        
        # Mobile positions get increased movement chance
        if player.position in mobile_positions:
            movement_chance *= 1.2
        
        if defensive_positions and random.random() < movement_chance:
            # Choose position based on ratings
            chosen_pos = random.choices(
                [pos for pos, _ in defensive_positions],
                weights=[rating for _, rating in defensive_positions],
                k=1
            )[0]
            
            player.current_position = self.get_position_from_coordinates(chosen_pos)
            print(f"from move_to_defensive_position New Position: {player.current_position}")

    def calculate_defensive_position_rating(self, player, current_pos, ball_pos, preferred_distance, preferred_side):
        """Calculate rating for a defensive position based on multiple factors."""
        x, y = current_pos
        ball_x, ball_y = ball_pos
        
        rating = 1.0
        
        # Distance to preferred defensive position
        actual_distance = abs(ball_x - x) + abs(ball_y - y)
        distance_rating = 1.0 - abs(actual_distance - preferred_distance) / 6  # Increased scale for larger field
        rating *= (1 + distance_rating)
        
        # Side preference
        side_rating = 1.0 - abs(preferred_side - (x - ball_x)) / 5  # Increased scale for wider field
        rating *= (1 + side_rating)
        
        # Goal-side positioning (higher rating for positions between ball and goal)
        if y < ball_y:  # Position is goal-side
            rating *= 1.3
        
        # Player attributes influence based on position groups
        key_defenders = ["FB", "CHB", "LB", "RB"]
        half_backs = ["LHB", "RHB"]
        mobile_positions = ["Centre", "Rover", "RuckRover"]
        wings = ["LWing", "RWing"]
        forwards = ["FF", "CHF", "LF", "RF", "LHF", "RHF"]
        
        if player.position in key_defenders:
            # Key defenders value positioning more
            rating *= (1 + player.ability_stats.tactical / 20)  # Adjusted for 0-20 scale
            # Extra bonus for being directly goal-side
            if abs(x - ball_x) < 2 and y < ball_y:
                rating *= 1.2
        
        elif player.position in half_backs:
            # Half backs balance between positioning and pressure
            rating *= (1 + (player.ability_stats.tactical + player.physical_stats.speed) / 40)
            # Bonus for wide defensive positions
            if abs(x - ball_x) >= 3:  # Increased for wider field
                rating *= 1.1
        
        elif player.position in mobile_positions:
            # Mobile positions value ability to apply pressure
            rating *= (1 + player.physical_stats.speed / 20)
            # Bonus for central positions
            if abs(x - ball_x) < 3:  # Increased for wider field
                rating *= 1.2
        
        elif player.position in wings:
            # Wings prefer wide defensive positions
            if abs(x - ball_x) >= 4:  # Increased for wider field
                rating *= 1.3
            rating *= (1 + player.physical_stats.speed / 20)
        
        elif player.position in forwards:
            # Forwards in defensive positions prefer to stay higher
            if y > ball_y - 3:  # Adjusted for larger field
                rating *= 1.2
        
        # Pressure ability influence
        tackle_factor = player.ability_stats.tackling / 20
        rating *= (0.8 + tackle_factor)
        
        # Stamina influence - tired players prefer less demanding positions
        stamina_factor = player.physical_stats.stamina / 20
        if actual_distance > 3:  # Adjusted for larger field
            rating *= stamina_factor
        
        # Mobile positions get less stamina penalty
        if player.position in mobile_positions and actual_distance > 3:
            rating *= 1.2
        
        # Add small random variation
        rating *= random.uniform(0.9, 1.1)
        
        return max(0.1, min(2.0, rating))
        
    def adjust_zones_for_player_attributes(self, zones, player):
        """Adjust zones based on player attributes."""
        # Calculate player's movement range based on stamina and speed
        base_range = 4  # Increased for larger field
        movement_range = base_range * (
            0.8 + 
            (player.physical_stats.stamina / 40) +   # Adjusted for 0-20 scale
            (player.physical_stats.speed / 40)
        )
        
        # Filter zones based on distance and player attributes
        filtered_zones = []
        for zone in zones:
            distance = player.calculate_distance(
                self.get_coordinates(player.current_position),
                zone
            )
            
            if distance <= movement_range:
                # Weight zone based on player attributes
                weight = 1.0
                
                # Speed and agility influence ability to reach further zones
                if distance > base_range:
                    weight *= (player.physical_stats.speed + player.physical_stats.agility) / 40
                
                # Tactical awareness influences positioning
                weight *= (0.5 + player.ability_stats.tactical / 40)
                
                # Add some randomness
                weight *= random.uniform(0.9, 1.1)
                
                filtered_zones.append((zone, weight))
        
        return filtered_zones
        
    def calculate_position_weight(self, position, player, play_state):
        """Calculate weight for a potential position based on player attributes and situation."""
        weight = 1.0
        
        # Base weight on position suitability
        position_suitability = self.calculate_position_suitability(player, position)
        weight *= (0.5 + position_suitability)
        
        # Adjust for player stamina
        stamina_factor = player.physical_stats.stamina / 20
        weight *= (0.7 + (0.3 * stamina_factor))
        
        # Adjust for play state
        if play_state == "attacking":
            if position in ["FF", "CHF", "LF", "RF", "LHF", "RHF"]:
                weight *= (1 + player.ability_stats.goal_kicking / 20)
        elif play_state == "defending":
            if position in ["FB", "CHB", "LB", "RB", "LHB", "RHB"]:
                weight *= (1 + player.ability_stats.tackling / 20)
        
        # Add small random variation
        weight *= random.uniform(0.9, 1.1)
        
        return weight
    
    def calculate_position_suitability(self, player, position):
        """Calculate how suitable a position is for a player based on AFL tactical considerations."""
        # Get team tactics
        team = self.team1 if player in self.team1_players.values() else self.team2
        team_tactics = self.team1_tactics if team == self.team1 else self.team2_tactics
        
        # Define position groups for zonal considerations
        forward_positions = ["FF", "CHF", "LF", "RF", "LHF", "RHF"]
        midfield_positions = ["Centre", "Rover", "RuckRover", "LWing", "RWing"]
        defensive_positions = ["FB", "CHB", "LB", "RB", "LHB", "RHB"]
        ruck_positions = ["Ruck"]

        # Get coordinates
        natural_pos_coords = self.get_coordinates(player.position)
        target_pos_coords = self.get_coordinates(position)
        
        suitability = 1.0
        
        # Base positional zones
        if player.position in forward_positions:
            # Forwards should generally stay in forward half
            if target_pos_coords[1] < self.grid_length / 2:
                suitability *= 0.5
        elif player.position in defensive_positions:
            # Defenders should generally stay in defensive half
            if target_pos_coords[1] > self.grid_length / 2:
                suitability *= 0.5
        elif player.position in ruck_positions:
            # Rucks should stay relatively central
            if abs(target_pos_coords[0] - self.grid_width/2) > 3:
                suitability *= 0.7
        
        # Tactical adjustments based on team strategy
        if team_tactics['defense_strategy'] == 'man_mark':
            # Find marked opponent
            marked_opponent = self.find_marked_opponent(player)
            if marked_opponent:
                opp_coords = self.get_coordinates(marked_opponent.current_position)
                marking_dist = player.calculate_distance(target_pos_coords, opp_coords)
                # Higher suitability for positions closer to marked opponent
                suitability *= (1.0 - (marking_dist / 8))  # 8 cells as max reasonable marking distance
        
        elif team_tactics['defense_strategy'] == 'zone_mark':
            # Prefer positions within assigned zone
            zone = self.get_player_zone(player, team_tactics)
            if zone and self.is_position_in_zone(target_pos_coords, zone):
                suitability *= 1.3
        
        # Role-specific adjustments
        if player.position in midfield_positions:
            # Midfielders have more freedom to roam
            suitability *= 0.9  # Smaller penalty for being out of position
        else:
            # Other positions more strictly positioned
            distance = player.calculate_distance(natural_pos_coords, target_pos_coords)
            position_penalty = max(0.5, 1.0 - (distance / 6))  # Less harsh penalty over distance
            suitability *= position_penalty
        
        # Adjust for player attributes
        suitability *= (0.7 + (player.ability_stats.tactical / 20) * 0.3)  # Tactical awareness influence
        
        # Wing positions should prefer wide positions
        if player.position in ["LWing", "RWing"]:
            distance_from_boundary = min(target_pos_coords[0], self.grid_width - target_pos_coords[0])
            if distance_from_boundary <= 2:  # Prefer positions near boundary
                suitability *= 1.2
        
        return max(0.1, min(1.0, suitability))
    
    def find_marked_opponent(self, player):

        """Find opponent player in mirror position."""
        # Get opponent team
        opponent_team = self.team2_players if player in self.team1_players.values() else self.team1_players
        """
        # Get mirrored position name
        mirrored_positions = {
            # Back line mirrors to forward line
            "LB": "RF",    # Left Back to Right Forward
            "FB": "FF",    # Full Back to Full Forward
            "RB": "LF",    # Right Back to Left Forward
            "LHB": "RHF",  # Left Half Back to Right Half Forward
            "CHB": "CHF",  # Center Half Back to Center Half Forward
            "RHB": "LHF",  # Right Half Back to Left Half Forward
            
            # Forward line mirrors to back line
            "LF": "RB",    # Left Forward to Right Back
            "FF": "FB",    # Full Forward to Full Back
            "RF": "LB",    # Right Forward to Left Back
            "LHF": "RHB",  # Left Half Forward to Right Half Back
            "CHF": "CHB",  # Center Half Forward to Center Half Back
            "RHF": "LHB",  # Right Half Forward to Left Half Back
            
            # Midfield mirrors to itself
            "LWing": "RWing",  # Left Wing to Right Wing
            "Centre": "Centre",
            "RWing": "LWing",  # Right Wing to Left Wing
            "Ruck": "Ruck",
            "Rover": "RuckRover",
            "RuckRover": "Rover"
        }
        """
        mirrored_coords = self.get_mirrored_position(player.current_position)
        mirror_pos = self.get_position_from_coordinates(mirrored_coords)
        #mirror_pos = mirrored_positions.get(player.position)
        if not mirror_pos:
            print(f"Error: Could not find mirrored position for {player.name}")
            sys.exit()
            
            
        # Find opponent in mirrored position
        for opp in opponent_team.values():
            if opp.position == mirror_pos:
                # Calculate chance of successfully marking based on player attributes
                marking_ability = (
                    (player.physical_stats.speed / 20) * 0.3 +     # Speed influence
                    (player.physical_stats.stamina / 20) * 0.3 +   # Stamina influence
                    (player.ability_stats.tactical / 20) * 0.4     # Tactical influence
                )
                
                # Random factor to determine if player maintains marking
                if random.random() < marking_ability:
                    return opp
        
        return None
    
    def get_player_zone(self, player, team_tactics):
        """Get player's zone based on position group and field section."""
        # Define position groups
        defensive_positions = ["FB", "CHB", "LB", "RB", "LHB", "RHB"]
        midfield_positions = ["Centre", "Rover", "RuckRover", "LWing", "RWing", "Ruck"]
        forward_positions = ["FF", "CHF", "LF", "RF", "LHF", "RHF"]
        
        # Define zones based on field dimensions
        defensive_third = self.grid_length // 3
        forward_third = (self.grid_length * 2) // 3
        
        # Get specific zone based on position
        if player.position in defensive_positions:
            base_zone = {
                'y_range': (0, defensive_third + 2),  # Allow some forward movement
                'x_range': self.get_horizontal_zone(player.position)
            }
        elif player.position in midfield_positions:
            base_zone = {
                'y_range': (defensive_third - 2, forward_third + 2),  # Overlap with both ends
                'x_range': self.get_horizontal_zone(player.position)
            }
        elif player.position in forward_positions:
            base_zone = {
                'y_range': (forward_third - 2, self.grid_length),  # Allow some backward movement
                'x_range': self.get_horizontal_zone(player.position)
            }
        else:
            return None
            
        return base_zone

    def get_horizontal_zone(self, position):
        """Get horizontal (x) range based on position."""
        grid_third = self.grid_width // 3
        
        # Left side positions
        if position in ["LB", "LHB", "LWing", "LHF", "LF"]:
            return (0, grid_third + 2)  # Allow some central movement
            
        # Right side positions
        elif position in ["RB", "RHB", "RWing", "RHF", "RF"]:
            return (grid_third * 2 - 2, self.grid_width)
            
        # Central positions
        elif position in ["FB", "CHB", "Centre", "Ruck", "Rover", "RuckRover", "CHF", "FF"]:
            return (grid_third - 2, (grid_third * 2) + 2)  # Wide central zone
        
        
    def is_position_in_zone(self, position, zone):
        """Check if a position falls within a defined zone."""
        x, y = position
        x_range = zone['x_range']
        y_range = zone['y_range']
        
        return (x_range[0] <= x <= x_range[1] and 
                y_range[0] <= y <= y_range[1])



    """
    def get_role_bonus(self, natural_position, target_position):
        # Define position groups
        forwards = ["FF", "CHF", "LF", "RF", "LHF", "RHF"]
        defenders = ["FB", "CHB", "LB", "RB", "LHB", "RHB"]
        midfielders = ["Centre", "Rover", "RuckRover"]
        Leftwings = ["LWing", "Centre", "LHF", "LHB", 'CHF', 'CHB']
        Rightwings = ["RWing", "Centre", "RHF", "RHB", 'CHF', 'CHB']

        # If both positions are in the same group, give bonus
        for group in [forwards, defenders, midfielders, Leftwings, Rightwings]:
            if natural_position in group and target_position in group:
                return 0.4
                
        return 0.0
    """



    """
    def update_game_state(self, team_in_possession, goal=None):
        opponent_team = self.get_opponent(team_in_possession)
        opponent_team_players = self.get_opponent_players(team_in_possession)
        
        play_state = self.get_play_state(team_in_possession, goal)
        
        if play_state == "goal":
            opponent_play_state = "goal"
        elif play_state == "defending":
            opponent_play_state = "attacking"
        elif play_state == "attacking":
            opponent_play_state = "defending"
        else: 
            opponent_play_state = "transitioning"
        
        print(f"Play State: {play_state}")
        print(f"Opp Play State: {opponent_play_state}")

        team_in_possession_players = self.team1_players if opponent_team == self.team1 else self.team2_players

        self.update_player_positions(play_state, team_in_possession_players)
        self.update_opponent_positions(opponent_play_state, opponent_team_players)
        #sys.exit()
    """
    
    def get_play_state(self, team_in_possession, goal=None):
        """Determine the current play state based on ball position and possession."""
        attacking_positions = ["FF", "CHF", "LHF", "RHF", "LF", "RF"]
        defending_positions = ["FB", "CHB", "LHB", "RHB", "LB", "RB"]
        
        if goal == "goal":
            return "goal"
        
        # For team1, attacking is in forward positions
        # For team2, attacking is in defensive positions (since they attack the opposite way)
        if team_in_possession == self.team1:
            if self.ball_position_name in attacking_positions:
                return "attacking"
            elif self.ball_position_name in defending_positions:
                return "defending"
        else:  # team2
            if self.ball_position_name in defending_positions:
                return "attacking"  # team2 attacks towards team1's defensive end
            elif self.ball_position_name in attacking_positions:
                return "defending"  # team2 defends their defensive end
                
        return "transitioning"
        

    """
    def update_player_positions(self, play_state, team_in_possession):
        opponent_team = self.get_opponent(team_in_possession)
        for player in team_in_possession.values():
            print(f"Players {player}")
            #if isinstance(player, Player):
            self.adjust_position_for_play_state(player, play_state, opponent_team)
            #else:
            #    print(f"Error: {player} is not a Player object")
            #    sys.exit()

    def update_opponent_positions(self, opponent_play_state, opponent_team):
        for player in opponent_team.values():
            self.adjust_position_for_play_state(player, opponent_play_state, opponent_team)
            # Assuming mirroring is needed for opponents:
            if isinstance(player, Player):
                print(f"Player  Position {player.current_position}")
                mirrored_position = self.get_mirrored_position(player.current_position)
                player.current_position = mirrored_position if not None else player.current_position
                print(f"Player  Position {player.current_position}")
                #sys.exit()
            else:
                print(f"Error: {player} is not a Player object")
                sys.exit()

    def adjust_position_for_play_state(self, player, play_state, opponent_team):
        print(f"Player state {play_state}")

        if play_state == "goal":
            #print("Player_State goal move")
            for player in self.team1_players.values():
                player.return_to_natural_position()
            for player in self.team2_players.values():
                player.return_to_natural_position()

        # Use the player's decision-making function based on team tactics and play state
        tactics = self.team1_tactics if opponent_team == self.team1 else self.team2_tactics
        #print(f"player class: {player.__class__}")
        possible_positions = player.determine_position_based_on_tactics(self.ball_position, play_state, tactics)

        # Choose position based on player stats and team tactics
        if possible_positions:
            position_choice = self.choose_position_based_on_stats(player, possible_positions)
            player.current_position = position_choice
            print(f"{player.name} moves to {position_choice} based on tactics.")

    def choose_position_based_on_stats(self, player, possible_positions):
        # Determine position based on player's mental and tactical stats
        mental_tactical_factor = player.ability_stats.mental + player.ability_stats.tactical
        weights = [mental_tactical_factor] * len(possible_positions)
        return random.choices(possible_positions, weights=weights, k=1)[0]
    """
    def get_coordinates(self, position):
        """Get the (x, y) coordinates from a grid position name, or return the input if it's already coordinates."""
        # If position is already coordinates, return directly
        if isinstance(position, tuple):
            return position
            
        # Define mobile positions
        mobile_positions = ['Ruck', 'Rover', 'RuckRover']
        
        # If it's a mobile position, we need to find where that player actually is
        if position in mobile_positions:
            # Find the player with this position
            for team in [self.team1_players, self.team2_players]:
                for player in team.values():
                    if player.position == position:
                        print(f"Player {player.name} is at {player.current_position}")
                        # Check if current_position is also a mobile position to avoid loops
                        if player.current_position in mobile_positions:
                            print(f"Mobile player {player.name} defaulting to Centre")
                            return self.get_coordinates('Centre')
                        # Return coordinates of where this player currently is
                    return self.get_coordinates(player.current_position)
            print(f"Warning: Could not find player for mobile position {position}")
            return self.get_coordinates('Centre')  # Default to centre if player not found
        
        # For fixed positions, get coordinates from grid map
        grid_map = self.get_grid_map()
        for zone_name, coordinates_list in grid_map.items():
            if zone_name == position:
                if coordinates_list:
                    #print(f"Coordinates for {position}: {coordinates_list}")
                    return coordinates_list[0]  # Return the first coordinate for the named zone
        
        print(f"Error: Could not get coordinates for position {position}")
        sys.exit()
        return None
    
    def mirror_coordinates(self, coordinates):
        """ Mirror the (x, y) coordinates horizontally based on grid width. """
        grid_map = self.get_grid_map()

        # Extract all coordinates from the grid map and find the max x value (for mirroring)
        all_coordinates = [coord for positions in grid_map.values() for coord in positions]
        if not all_coordinates:
            print("Error: Grid map is empty")
            return None

        # Find the max x coordinate to mirror horizontally
        max_x = max(coord[0] for coord in all_coordinates)
        
        x, y = coordinates
        mirrored_x = max_x - x  # Mirror horizontally
        mirrored_y = y  # Keep y the same, unless vertical mirroring is needed

        return mirrored_x, mirrored_y

    def get_mirrored_position(self, position):
        """Get the mirrored position or coordinates based on team zones or grid coordinates."""
        # Define a dictionary that maps each position to its mirror
        mirrored_positions = {
            # Back line mirrors to forward line
            "LB": "RF",    # Left Back to Right Forward
            "FB": "FF",    # Full Back to Full Forward
            "RB": "LF",    # Right Back to Left Forward
            
            # Half-back line mirrors to half-forward line
            "LHB": "RHF",  # Left Half Back to Right Half Forward
            "CHB": "CHF",  # Center Half Back to Center Half Forward
            "RHB": "LHF",  # Right Half Back to Left Half Forward
            
            # Center line mirrors to itself (but swaps sides)
            "LWing": "RWing",
            "Centre": "Centre",
            "RWing": "LWing",
            
            # Half-forward line mirrors to half-back line
            "LHF": "RHB",  # Left Half Forward to Right Half Back
            "CHF": "CHB",  # Center Half Forward to Center Half Back
            "RHF": "LHB",  # Right Half Forward to Left Half Back
            
            # Forward line mirrors to back line
            "LF": "RB",    # Left Forward to Right Back
            "FF": "FB",    # Full Forward to Full Back
            "RF": "LB",    # Right Forward to Left Back
            
            # These positions mirror to themselves
            "Ruck": "Ruck",
            "Rover": "Rover",
            "RuckRover": "RuckRover",
        }
            # If the position is coordinates
        # If the position is coordinates
        if isinstance(position, tuple):
            x, y = position
            # Mirror both x and y coordinates
            center_x = (self.grid_width - 1) / 2
            center_y = (self.grid_length - 1) / 2
            mirrored_x = round(center_x - (x - center_x))  # Mirror around center x
            mirrored_y = round(center_y - (y - center_y))  # Mirror around center y
            print(f"Mirroring x={x},y={y} around center=({center_x},{center_y}) to {mirrored_x},{mirrored_y}")
            mirrored_coords = (mirrored_x, mirrored_y)
            print(f"Tuple position {position} mirrored to {mirrored_coords}")
            return mirrored_coords
        
        # For named positions
        if position in mirrored_positions:
            mirrored_name = mirrored_positions[position]
            mirrored_coords = self.get_coordinates(mirrored_name)
            print(f"Position {position} mirrored to {mirrored_name} at {mirrored_coords}")
            #time.sleep(3)
            return mirrored_coords
        print(f"Warning: Could not mirror position {position}")
        sys.exit()
        return position
    

    def get_position_from_coordinates(self, coordinates):
        """Get the most appropriate position name from (x, y) coordinates."""
        if not coordinates:
            return "Out of Bounds"
            
        x, y = coordinates
        grid_map = self.get_grid_map()
        
        # Find all positions whose zones contain these coordinates
        matching_positions = []
        for position, zone in grid_map.items():
            if (x, y) in zone:
                matching_positions.append(position)
        
        if not matching_positions:
            return "Out of Bounds"
        
        # If only one match, return it
        if len(matching_positions) == 1:
            return matching_positions[0]
        
        # If multiple matches (overlapping zones), choose the most appropriate one
        # Priority: Center positions over wings, key positions over pockets
        position_priority = {
            # Key positions have higher priority
            "FF": 5, "CHF": 5, "Centre": 5, "CHB": 5, "FB": 5,
            # Flankers next
            "LHF": 4, "RHF": 4, "LHB": 4, "RHB": 4,
            # Wings
            "LWing": 3, "RWing": 3,
            # Pockets
            "LF": 2, "RF": 2, "LB": 2, "RB": 2,
            # Mobile positions adapt to ball position
            "Ruck": 1, "Rover": 1, "RuckRover": 1
        }
        
        # Sort by priority and return highest priority position
        return max(matching_positions, 
                    key=lambda pos: position_priority.get(pos, 0))


    """
    def get_position_from_coordinates(self, coordinates):
         #Get the grid position name from (x, y) coordinates.
        x, y = coordinates
        for position, zone in self.positions_zones.items():
            if (x, y) in zone:
                return position
        return "Out of Bounds"  # If no match found
    """    

    def handle_disposal(self, player, current_position, team_players, opponent_players, team_tactics):
        """Handle the disposal decision and execution."""
        
        # Find all viable disposal options
        options, disposer_pressure = self.find_disposal_options(
            player,
            current_position,
            team_players,
            opponent_players,
            team_tactics
        )
        
        # Choose best option
        next_position, receiver, disposal_type = self.choose_disposal_option(
            player,
            options,
            team_tactics,
            disposer_pressure
        )
        #print(f"Next Position {next_position} Receiver {receiver} Disposal Type {disposal_type}")
        if not next_position or not receiver:
            print(f"{player.name} has no clear disposal options! Disposal type {disposal_type}")
            #time.sleep(5)
            # Handle fallback logic
            return next_position, receiver, disposal_type
        
        print(f"{player.name} decides to {disposal_type} to {receiver.name} at {next_position}")
        """"
        if disposal_type == "kick":
            return self.execute_kick(player, receiver, next_position)
        else:
            return self.execute_handball(player, receiver, next_position)           
        """
        return next_position, receiver, disposal_type

    def find_disposal_options(self, player, current_position, team_players, opponent_players, team_tactics):
        """Analyze the field to find disposal options based on player's tactical ability."""
        
        # Calculate the maximum kick distance based on player's stats
        min_kick_distance = 15  # Minimum kick distance (1 cell)
        max_kick_distance = 60
        kick_distance = min_kick_distance + ((player.ability_stats.kicking - 1) / 19) * (max_kick_distance - min_kick_distance)
        print(f"Max Kick Distance {kick_distance}")
        # Convert the kick distance to grid cells (1 cell = 10 meters)
        meters_per_cell_length = self.ground_length / self.grid_length  # Should be 10 (160m/16)
        meters_per_cell_width = self.ground_width / self.grid_width    # Should be 10 (130m/13)
        # Use average of length and width for consistent cell size
        meters_per_cell = (meters_per_cell_length + meters_per_cell_width) / 2
        # Convert kick distance to grid cells
        max_kick_cells = max(1, int(kick_distance / meters_per_cell))
        print(f"Max Kick Cells {max_kick_cells}")

        min_handball_distance = 1  # Minimum handball distance
        max_handball_distance = 20
        handball_distance = min_handball_distance + ((player.ability_stats.handball - 1) / 19) * (max_handball_distance - min_handball_distance)
        print(f"Max handball Distance {handball_distance}")
        # Convert the kick distance to grid cells (1 cell = 10 meters)
        max_handball_cells = max(1, int(handball_distance / meters_per_cell))  # Ensure at least 1 cell
        print(f"Max handball Cells {max_handball_cells}")

        # Get possible positions within range based on player's kicking/handball abilities
        #kick_range = self.calculate_kick_range(player)
        #handball_range = self.calculate_handball_range(player)
        
        kick_positions = self.get_dynamic_possible_positions(current_position, max_kick_cells)
        handball_positions = self.get_dynamic_possible_positions(current_position, max_handball_cells)
        
        disposal_options = []
        print(f"Kick Positions {kick_positions}")
        print(f"Handball Positions {handball_positions}")
        tactical_vision = player.ability_stats.tactical / 100  # How well they read the play
        
        # Analyze each possible position
        for pos in set(kick_positions + handball_positions):
            # Find teammates in or moving towards this position
            teammates = [p for p in team_players.values() 
                        if p != player and p.current_position == pos]
            print(f"Teammates {teammates}")
            print(f"Team Players {team_players}")
            #time.sleep(2)
            if not teammates:
                continue
                
            for teammate in teammates:
                # Find closest opponent to this teammate
                closest_opponent = None
                min_opponent_distance = float('inf')
                
                for opp in opponent_players.values():
                    distance = player.calculate_distance(
                        self.get_coordinates(teammate.current_position),
                        self.get_coordinates(opp.current_position)
                    )
                    #print(f"Distance from {teammate.name} to opponent {opp.name}: {distance}")
                    if distance < min_opponent_distance:
                        min_opponent_distance = distance
                        closest_opponent = opp
            
                # If no valid opponents found, consider the space completely open
                if min_opponent_distance == float('inf'):
                    space_rating = 1.0
                    disposer_pressure = 0.0
                else:
                    # New space rating calculation
                    space_rating = min(1.0, max(0.0, min_opponent_distance - 1))
                    disposer_pressure = max(0.0, min(1.0, 2 - min_opponent_distance))
                
                print(f"Teammate {teammate.name} at {pos}")
                print(f"Closest opponent: {closest_opponent.name if closest_opponent else 'None'}")
                print(f"Opponent distance: {min_opponent_distance}")
                print(f"Space rating: {space_rating}")
                print(f"Disposer {player.name} under pressure: {disposer_pressure}")
   
                #time.sleep(2)
                    # This will give:
                    # distance 1 or less = 0.0 (heavily pressured)
                    # distance 6 or more = 1.0 (completely open)
                    # linear scale between
                
                # Calculate if position is in preferred tactical area
                tactical_rating = self.calculate_tactical_rating(
                    pos, 
                    team_tactics['offense_strategy'],
                    team_tactics['mentality']
                )
                
                # Determine if in kick or handball range
                in_kick_range = pos in kick_positions
                in_handball_range = pos in handball_positions
                
                disposal_options.append({
                    'position': pos,
                    'receiver': teammate,
                    'opponent': closest_opponent,
                    'space_rating': space_rating,
                    #'disposer_pressure': disposer_pressure,
                    'tactical_rating': tactical_rating,
                    'can_kick': in_kick_range,
                    'can_handball': in_handball_range,
                    'distance': player.calculate_distance(pos)
                })
        print(f"Disposal Options {disposal_options}")
        #time.sleep(2)
        # Filter options based on tactical ability
        vision_threshold = 1.0 - tactical_vision  # Better vision = lower threshold
        print(f"Tactical Vision {tactical_vision} Vision Threshold {vision_threshold}")
        disposal_options = [opt for opt in disposal_options] 
                            #if opt['space_rating'] > vision_threshold]
        print(f"Disposal Options after vision filter {disposal_options}")
        #time.sleep(2)
        return disposal_options, disposer_pressure
        
    def choose_disposal_option(self, player, options, team_tactics, disposer_pressure):
        """Choose the best disposal option based on situation and tactics."""
        if not options:
            print(f"No options found for {player.name}")
            sys.exit()
            return None, None, "kick"  # Default fallback
        
        tactical_vision = player.ability_stats.tactical #/ 100
        print(f"pressure: {disposer_pressure}")
        # Adjust vision based on pressure
        #effective_vision = tactical_vision * (1 - disposer_pressure)
        effective_vision = tactical_vision - disposer_pressure
        print(f"Effective vision: {effective_vision} (base vision: {tactical_vision}, pressure: {disposer_pressure}")
        #time.sleep(2)
        # First, determine which options the player can "see"
        visible_options = []
        for opt in options:
            # Base visibility chance on tactical vision
            visibility_chance = effective_vision
            
            # Adjust for how open the teammate is
            visibility_chance *= (1 + opt['space_rating'])
            
            # Adjust for distance - harder to spot far options
            distance_factor = max(0.2, 1 - (opt['distance'] / 10))
            visibility_chance *= distance_factor
            
            # Random check if player spots this option
            if random.random() < visibility_chance:
                visible_options.append(opt)
                print(f"Spotted option: {opt['receiver'].name} at {opt['position']}")
        
        if not visible_options:
            print("No options spotted, What will they do?")
            # Fall back to closest teammate if under pressure
            return self.handle_no_options(player, options, disposer_pressure)
   


        # Weight each option
        weighted_options = []
        for opt in options:
            weight = 1.0
            
            # Prefer options that align with team tactics
            weight *= (1 + opt['tactical_rating'])
            
            # Prefer open players
            weight *= (1 + opt['space_rating'] * 2)
            """
            # Adjust for player abilities
            if opt['can_kick']:
                weight *= (1 + player.ability_stats.kicking / 100)
            if opt['can_handball']:
                weight *= (1 + player.ability_stats.handball / 100)
            """
            # Adjust for risk/reward based on mentality
            if team_tactics['mentality'] == 'attacking':
                # Reward forward movement more
                if opt['position'] in ["FF", "CHF", "LF", "RF", 'LHF', 'RHF']:
                    weight *= 1.3
            elif team_tactics['mentality'] == 'defensive':
                # Reward safer options
                weight *= (1 + opt['space_rating'])

                   # Disposal type preference based on skills and situation
            if opt['can_kick'] and opt['can_handball']:
                kick_pref = player.ability_stats.kicking / 100
                handball_pref = player.ability_stats.handball / 100
                
                # Under pressure, prefer handballs
                if disposer_pressure > 0.5:
                    handball_pref *= 1.5
                
                # For longer distances, prefer kicks
                if opt['distance'] > 2:
                    kick_pref *= 1.3
                
                # Add appropriate skill weighting
                weight *= max(kick_pref, handball_pref)
            
            weighted_options.append((opt, weight))
        
        # Choose final option
        chosen = random.choices(
            weighted_options,
            weights=[w for _, w in weighted_options],
            k=1
        )[0][0]
            
            #weighted_options.append((opt, weight))
        """
            # Choose option based on weights
            chosen = random.choices(
                weighted_options,
                weights=[w for _, w in weighted_options],
                k=1
            )[0][0]
            
        # Decide disposal type
        if chosen['can_kick'] and chosen['can_handball']:
            # Decide based on situation and abilities
            kick_weight = (player.ability_stats.kicking / 100) * (1 + chosen['distance'] / 10)
            handball_weight = (player.ability_stats.handball / 100) * (1 - chosen['space_rating'])
            
            disposal_type = "kick" if kick_weight > handball_weight else "handball"
        else:
            disposal_type = "kick" if chosen['can_kick'] else "handball"
        print(f"Chosen Position {chosen['position']} Receiver {chosen['receiver']} Disposal Type {disposal_type}")
        """
        disposal_type = self.decide_disposal_type(player, chosen, disposer_pressure)
        print(f"Chosen Position {chosen['position']} Receiver {chosen['receiver']} Disposal Type {disposal_type}")
        #time.sleep(2)
        return chosen['position'], chosen['receiver'], disposal_type
    
    def decide_disposal_type(self, player, option, disposer_pressure):
        """Decide whether to kick or handball based on situation."""
        if not option['can_kick']:
            return "handball"
        if not option['can_handball']:
            return "kick"
        
        # Calculate preference for each disposal type
        kick_pref = player.ability_stats.kicking / 100
        handball_pref = player.ability_stats.handball / 100
        
        # Adjust for pressure
        if disposer_pressure > 0.5:
            handball_pref *= 1.5  # Prefer handballs under pressure
        
        # Adjust for distance
        if option['distance'] > 2:
            kick_pref *= 1.3  # Prefer kicks for longer distances
        
        # Adjust for how open the target is
        if option['space_rating'] > 0.7:
            kick_pref *= 1.2  # Prefer kicks to open targets
        
        return "kick" if kick_pref > handball_pref else "handball"
    
    """
    def find_closest_teammate(self, player, options):
        #Fall back to closest teammate when under pressure.
        if not options:
            return None, None, "kick"
            
        closest = min(options, key=lambda x: x['distance'])
        disposal_type = "handball" if closest['can_handball'] else "kick"
        
        return closest['position'], closest['receiver'], disposal_type
    """    

    def handle_no_options(self, player, options, disposer_pressure):
        """Handle situation when player can't spot clear options."""
        print(f"{player.name} can't spot any clear options...")
        
        # Different decisions based on pressure and player attributes
        decision_weights = {
            'play_on': 0.0,
            'hold_ball': 0.0,
            'kick_to_space': 0.0,
            'blind_handball': 0.0,
            'long_bomb': 0.0
        }
        
        # Adjust weights based on pressure
        if disposer_pressure > 0.7:  # Heavy pressure
            decision_weights.update({
                'blind_handball': 20 + player.ability_stats.handball,
                'play_on': 10 + player.physical_stats.speed,
                'kick_to_space': 15 + player.ability_stats.kicking
            })
        elif disposer_pressure > 0.3:  # Moderate pressure
            decision_weights.update({
                'hold_ball': 25 + player.ability_stats.tactical,
                'play_on': 20 + player.physical_stats.speed,
                'kick_to_space': 20 + player.ability_stats.kicking
            })
        else:  # Light pressure
            decision_weights.update({
                'hold_ball': 30 + player.ability_stats.tactical,
                'long_bomb': 15 + player.ability_stats.kicking,
                'play_on': 15 + player.physical_stats.speed
            })
        
        # Adjust for field position
        if self.is_defensive_position(player.current_position):
            decision_weights['kick_to_space'] += 20  # Safer option in defense
            decision_weights['blind_handball'] -= 10
        elif self.is_forward_position(player.current_position):
            decision_weights['long_bomb'] += 15  # More aggressive in attack
        
        # Choose action based on weights
        action = random.choices(
            list(decision_weights.keys()),
            weights=list(decision_weights.values()),
            k=1
        )[0]

        print(f"Chosen action: {action}")
        #time.sleep(2)
        
        # Execute chosen action
        if action == 'play_on':
            return self.handle_play_on(player)
        elif action == 'hold_ball':
            return self.handle_hold_ball(player)
        elif action == 'kick_to_space':
            return self.handle_kick_to_space(player)
        elif action == 'blind_handball':
            return self.handle_blind_handball(player)
        else:  # long_bomb
            return self.handle_long_bomb(player)

    def handle_play_on(self, player):
        """Player attempts to break tackle/evade."""
        success_chance = (
            (player.physical_stats.speed + player.physical_stats.agility) / 200 +
            random.uniform(0.1, 0.3)
        )
        
        if random.random() < success_chance:
            print(f"{player.name} successfully breaks away!")
            return player.current_position, player, "play_on"
        else:
            print(f"{player.name} caught in tackle while trying to play on!")
            self.ball_position = self.update_ball_position(player.current_position, player.current_position)
            mirrored_position = self.get_mirrored_position(self.ball_position)
            opponent_players = self.get_opponent_players(player.team)
            opponent = Player.determine_closest_opponent_player_to_ball(self, mirrored_position, list(opponent_players.values()), 8)
            self.stats_manager.record_stat(opponent.name, 'turnovers_won')
            self.ball_position_name =  self.get_ball_position()
            self.disposer = opponent
            self.ball_position = self.update_ball_position(mirrored_position, mirrored_position)
            self.ball_position_name =  self.get_ball_position()
            self.disposer.current_position = self.ball_position_name
            return self.ball_position, None, "tackled"
    
    def handle_hold_ball(self, player):
        """Player tries to hold the ball and draw a free kick."""
        print(f"{player.name} holds the ball, looking for options...")
        return player.current_position, player, "hold"
   
    def handle_kick_to_space(self, player):
        """Kick to a strategic position in space."""
        # Find strategic positions based on field position
        if self.is_defensive_position(player.current_position):
            print(f"{player.name} is in a defensive position")
            target_position = self.find_clearing_kick_position(player)
        else:
            print(f"{player.name} is in an attacking position")
            target_position = self.find_attacking_space(player)
        
        print(f"{player.name} kicks to space at {target_position}")
        self.ball_position = self.update_ball_position(player.current_position, target_position)
        self.stats_manager.record_stat(player.name, 'kicks')
        return target_position, None, "kick"

    def handle_blind_handball(self, player):
        """Quick handball in hope of finding teammate."""
        # Higher risk but might work under pressure
        success_chance = player.ability_stats.handball / 200  # Very low chance
        
        if random.random() < success_chance:
            print(f"{player.name} somehow gets a handball away!")
            return player.current_position, None, "handball"
        else:
            print(f"{player.name} turns it over with a rushed handball! and the ball is at {self.ball_position} his position is {player.current_position}")
            self.ball_position = self.update_ball_position(player.current_position, player.current_position)
            mirrored_position = self.get_mirrored_position(self.ball_position)
            opponent_players = self.get_opponent_players(player.team)
            opponent = Player.determine_closest_opponent_player_to_ball(self, mirrored_position, list(opponent_players.values()), 8)
            self.stats_manager.record_stat(opponent.name, 'turnovers_won')
            self.ball_position_name =  self.get_ball_position()
            self.disposer = opponent
            print(f"Disposer {self.disposer.name}")
            self.ball_position = self.update_ball_position(mirrored_position, mirrored_position)
            self.ball_position_name =  self.get_ball_position()
            self.disposer.current_position = self.ball_position_name
            print(f"Disposer position {self.disposer.current_position}")
            return self.ball_position, None, "turnover"
        
    def handle_long_bomb(self, player):
        """Kick long towards goal or advantage."""
        if self.is_forward_position(player.current_position):
            print(f"{player.name} launches a long shot at goal!")
            result = self.attempt_goal(player, player.current_position)
            return None, None, result
        else:
            print(f"{player.name} bombs it long!")        
            self.ball_position = self.update_ball_position(player.current_position, self.find_long_kick_target(player))
            self.stats_manager.record_stat(player.name, 'kicks')
            self.disposer = None
            return None, None, "kick"
        

    def is_defensive_position(self, position):
        """Check if a position is in the defensive area of the ground."""
        if isinstance(position, tuple):
            # If given coordinates
            x, y = position
            return y <= 1  # First two rows are defensive (FB and HB lines)
        else:
            # If given position name
            defensive_positions = [
                "FB", "CHB", "LB", "RB",  # Key defensive positions
                "LHB", "RHB",             # Half-back flankers
                #"Ruck" if self.ball_position_name in ["FB", "CHB", "LB", "RB"] else None  # Ruck in defensive contest
            ]
            return position in defensive_positions

    def is_forward_position(self, position):
        """Check if a position is in the forward area of the ground."""
        if isinstance(position, tuple):
            # If given coordinates
            x, y = position
            return y >= 3  # Last two rows are forward (HF and FF lines)
        else:
            # If given position name
            forward_positions = [
                "FF", "CHF", "LF", "RF",  # Key forward positions
                "LHF", "RHF",             # Half-forward flankers
                "Ruck" if self.ball_position_name in ["FF", "CHF", "LF", "RF"] else None  # Ruck in forward contest
            ]
            return position in forward_positions
    def is_midfield_position(self, position):
        """Check if a position is in the midfield area of the ground."""
        if isinstance(position, tuple):
            # If given coordinates
            x, y = position
            return y == 2  # Middle row is midfield
        else:
            # If given position name
            midfield_positions = [
                "Centre", "Rover", "RuckRover",  # Inside midfielders
                "LWing", "RWing",                # Wing positions
                "Ruck" if self.ball_position_name in ["Centre", "Rover", "RuckRover"] else None  # Ruck in midfield contest
            ]
            return position in midfield_positions
        
    def get_position_zone(self, position):
        """Get the zone type for a given position (more detailed than just forward/back)."""
        if isinstance(position, tuple):
            x, y = position
            if y == 0:
                return "fullback_line"
            elif y == 1:
                return "halfback_line"
            elif y == 2:
                return "midfield"
            elif y == 3:
                return "halfforward_line"
            elif y == 4:
                return "fullforward_line"
        else:
            zone_mapping = {
                # Defensive zones
                "FB": "fullback_line",
                "LB": "fullback_line",
                "RB": "fullback_line",
                "CHB": "halfback_line",
                "LHB": "halfback_line",
                "RHB": "halfback_line",
                
                # Midfield zone
                "Centre": "midfield",
                "LWing": "midfield",
                "RWing": "midfield",
                "Rover": "midfield",
                "RuckRover": "midfield",
                
                # Forward zones
                "CHF": "halfforward_line",
                "LHF": "halfforward_line",
                "RHF": "halfforward_line",
                "FF": "fullforward_line",
                "LF": "fullforward_line",
                "RF": "fullforward_line",
                
                # Ruck - depends on ball position
                "Ruck": self.get_position_zone(self.ball_position_name)
            }
            return zone_mapping.get(position, "midfield")  # Default to midfield if unknown  
        
    def find_clearing_kick_position(self, player):
        """Find a safe position to kick to when clearing from defense."""
        print(f"Finding clearing kick position for {player.name}")
        current_coords = self.get_coordinates(player.current_position)
        x, y = current_coords
        grid_width = self.grid_width  # Should get from field dimensions
        
        # Calculate kick distance based on player's kicking ability
        # Calculate maximum kick distance based on player's kicking ability
        #max_kick_distance = 2 + (player.ability_stats.kicking / 25)  # 2-6 cells based on skill
        min_kick_distance = 15  # Minimum kick distance (1 cell)
        max_kick_distance = 60
        kick_distance = min_kick_distance + ((player.ability_stats.kicking - 1) / 19) * (max_kick_distance - min_kick_distance)
        
        potential_positions = []
        
        # Define corridors for clearing kicks
        corridors = {
            'central': {'x_range': (1, 1), 'weight': 1.0},
            'left': {'x_range': (0, 0), 'weight': 1.2 if x == 0 or player.position in ['LB', 'LHB'] else 0.8},
            'right': {'x_range': (2, 2), 'weight': 1.2 if x == 2 or player.position in ['RB', 'RHB'] else 0.8}
        }
        
        # Adjust corridor weights based on player position
        if player.position in ['FB', 'CHB', 'Centre']:
            corridors['central']['weight'] *= 1.2
        
        for corridor, details in corridors.items():
            for dx in range(details['x_range'][0], details['x_range'][1] + 1):
                for dy in range(1, int(kick_distance) + 1):
                    new_pos = (dx, min(y + dy, grid_width - 1))  # Ensure within bounds
                    
                    if self.get_position_from_coordinates(new_pos):
                        rating = self.rate_clearing_position(
                            player, new_pos, current_coords, details['weight']
                        )
                        potential_positions.append((new_pos, rating))
        print(f"Potential positions: {potential_positions}")
        if potential_positions:
            chosen_pos = random.choices(
                [pos for pos, _ in potential_positions],
                weights=[rating for _, rating in potential_positions],
                k=1
            )[0]
            return self.get_position_from_coordinates(chosen_pos)
        
        # Fallback to straight up the ground
        return self.get_position_from_coordinates((x, min(y + 1, grid_width - 1)))

    def find_attacking_space(self, player):
        """Find space to kick to in attacking moves."""
        current_coords = self.get_coordinates(player.current_position)
        x, y = current_coords
        
        # Calculate maximum kick distance based on player's kicking ability
        #max_kick_distance = 2 + (player.ability_stats.kicking / 25)
        min_kick_distance = 15  # Minimum kick distance (1 cell)
        max_kick_distance = 60
        kick_distance = min_kick_distance + ((player.ability_stats.kicking - 1) / 19) * (max_kick_distance - min_kick_distance)
        
        potential_positions = []
        
        # Check positions in forward direction
        for dx in range(-1, 2):  # Left, Center, Right
            for dy in range(0, int(kick_distance) + 1):
                new_x = x + dx
                new_y = min(4, y + dy)  # Cap at forward line (y=4)
                new_pos = (new_x, new_y)
                
                if self.get_position_from_coordinates(new_pos):
                    # Calculate position rating
                    rating = self.rate_attacking_position(
                        player, new_pos, current_coords
                    )
                    potential_positions.append((new_pos, rating))
        
        if potential_positions:
            chosen_pos = random.choices(
                [pos for pos, _ in potential_positions],
                weights=[rating for _, rating in potential_positions],
                k=1
            )[0]
            return self.get_position_from_coordinates(chosen_pos)
        
        # Fallback to straight ahead
        return self.get_position_from_coordinates((x, min(y + 1, 4)))
    
    def find_long_kick_target(self, player):
        """Find position for long kicks/bombs."""
        current_coords = self.get_coordinates(player.current_position)
        x, y = current_coords
        
        # Determine target area based on field position
        if y < 2:  # Defensive half
            target_y_range = (2, 3)  # Aim for center/half-forward
        else:  # Attacking half
            target_y_range = (3, 4)  # Aim for forward line
        
        potential_positions = []
        
        # Generate potential long kick targets
        for target_y in range(target_y_range[0], target_y_range[1] + 1):
            for target_x in range(3):  # Full width of ground
                target_pos = (target_x, target_y)
                
                if self.get_position_from_coordinates(target_pos):
                    rating = self.rate_long_kick_target(
                        player, target_pos, current_coords
                    )
                    potential_positions.append((target_pos, rating))
        
        if potential_positions:
            chosen_pos = random.choices(
                [pos for pos, _ in potential_positions],
                weights=[rating for _, rating in potential_positions],
                k=1
            )[0]
            return self.get_position_from_coordinates(chosen_pos)
        
        # Fallback to straight ahead
        return self.get_position_from_coordinates((x, min(y + 2, 4)))
    
    def rate_clearing_position(self, player, position, current_coords, corridor_weight):
        """Rate a potential clearing kick position."""
        
        rating = corridor_weight
        x, y = position
        current_x, current_y = current_coords
        
        # Prefer positions further from defensive goal
        distance_from_goal = y
        rating *= (1 + distance_from_goal / 5)
        
        # Prefer positions that maintain possession
        if self.is_position_contested(position):
            rating *= 0.7
        
        # Adjust for player's kicking ability
        kick_difficulty = player.calculate_distance(position, current_coords) / 2
        kicking_factor = player.ability_stats.kicking / 100
        rating *= (1 + kicking_factor - kick_difficulty)
        
        # Prefer wider positions if under pressure
        if self.is_under_pressure(player):
            if x in (0, 2):  # Wide positions
                rating *= 1.3
        print(f"Clearing position rating: {rating}")
        return max(0.1, rating)
    
    def rate_attacking_position(self, player, position, current_coords):
        """Rate a potential attacking kick position."""
        rating = 1.0
        x, y = position
        
        # Prefer positions closer to goal
        rating *= (1 + y / 4)
        
        # Prefer central positions in forward line
        if y >= 3:  # Forward line
            if x == 1:  # Central
                rating *= 1.3
        
        # Consider player's goal kicking if near goal
        if y == 4:  # Full forward line
            rating *= (1 + player.ability_stats.goal_kicking / 100)
        
        # Adjust for kick difficulty
        kick_difficulty = player.calculate_distance(position, current_coords) / 2
        kicking_factor = player.ability_stats.kicking / 100
        rating *= (1 + kicking_factor - kick_difficulty)
        
        return max(0.1, rating)
    
    def rate_long_kick_target(self, player, position, current_coords):
        """Rate a potential long kick target position."""
        rating = 1.0
        x, y = position
        
        # Prefer positions that maximize ground gained
        distance_gained = y - current_coords[1]
        rating *= (1 + distance_gained / 4)
        
        # Adjust for player's kicking ability
        kick_distance = player.calculate_distance(position, current_coords)
        kicking_factor = player.ability_stats.kicking / 100
        rating *= (1 + kicking_factor - (kick_distance / 4))
        
        # Prefer central positions slightly
        if x == 1:
            rating *= 1.2
        
        # Consider contested marking ability if targeting forwards
        if y >= 3:
            rating *= (1 + player.ability_stats.marking / 100)
        
        return max(0.1, rating)
    
    def is_position_contested(self, position):
        """Check if a position is heavily contested by opponents."""
        opponent_players = self.get_opponent_players(self.disposer.team)
        nearby_players = self.get_nearby_players(position)
       
        # Count opponents in the area
        opponent_count = sum(1 for player in nearby_players 
                          if player in opponent_players.values())
        
        # Consider mobile positions as additional contesters
        mobile_count = sum(1 for player in nearby_players 
                            if player.position in ['Ruck', 'Rover', 'RuckRover'])
        
        return (opponent_count + (mobile_count * 0.5)) > 1
    
    def is_under_pressure(self, player):
        """Determine if a player is under significant pressure."""
        #nearby_opponents = [p for p in self.get_nearby_players(player.current_position)
        #                    if p.team != player.team]
        nearby_opponents = self.get_opponent_players(player.team)
        
        # Base pressure from number of opponents
        pressure = len(nearby_opponents) * 0.3
        
        # Adjust pressure based on player attributes
        pressure *= (1 - player.physical_stats.agility / 200)  # Better agility reduces pressure
        
        # Mobile positions handle pressure better
        if player.position in ['Ruck', 'Rover', 'RuckRover']:
            pressure *= 0.8
        
        # Key defenders handle pressure better
        if player.position in ['FB', 'CHB', 'LB', 'RB', 'LHB', 'RHB']:
            pressure *= 0.9
        
        return pressure > 0.5  
    
    
    def get_nearby_players(self, position, radius=1):
        """
        Find all players within a specified radius of a position.
        
        Args:
            position: Either coordinates (x,y) or position name (e.g., "FF")
            radius: Search radius in grid cells (default=1 for immediate vicinity)
        """
        # Convert position to coordinates if it's a position name
        if isinstance(position, str):
            center_x, center_y = self.get_coordinates(position)
        else:
            center_x, center_y = position
            
        nearby_players = []
        
        # Get all players from both teams
        all_players = {**self.team1_players, **self.team2_players}
        
        for player in all_players.values():
            # Skip if player has no current position (injured, benched, etc.)
            if not hasattr(player, 'current_position') or not player.current_position:
                continue
                
            # Get player's coordinates
            player_coords = self.get_coordinates(player.current_position)
            player_x, player_y = player_coords
            
            # Calculate Manhattan distance
            distance = abs(center_x - player_x) + abs(center_y - player_y)
            
            # Add player if within radius
            if distance <= radius:
                # Store player with their distance for sorting
                nearby_players.append({
                    'player': player,
                    'distance': distance,
                    'coords': player_coords
                })
        
        # Sort by distance (closest first)
        nearby_players.sort(key=lambda x: x['distance'])
        
        return [p['player'] for p in nearby_players]

    def get_player_congestion(self, position, radius=1):
        """Calculate congestion level at a position."""
        nearby_players = self.get_nearby_players(position, radius)
        
        # Base congestion on number of players
        congestion = len(nearby_players) / ((2 * radius + 1) ** 2)  # Normalize by area
        
        # Adjust congestion based on player sizes/attributes
        for player in nearby_players:
            # Taller players take up more space
            height_factor = player.physical_stats.height / 200  # Normalize height
            # Stronger players create more congestion
            strength_factor = player.physical_stats.strength / 200
            
            congestion += (height_factor + strength_factor) / 4
        
        return min(1.0, congestion)  # Cap at 1.0
    
    def get_space_rating(self, position, team):
        """
        Calculate how much space/opportunity exists at a position.
        Higher rating means more space/better opportunity.
        """
        # Get basic congestion
        congestion = self.get_player_congestion(position)
        space_rating = 1.0 - congestion
        
        # Get nearby players for detailed analysis
        nearby_players = self.get_nearby_players(position, radius=2)
        
        # Count teammates and opponents
        teammates = [p for p in nearby_players if p.team == team]
        opponents = [p for p in nearby_players if p.team != team]
        
        # Analyze tactical situation
        for player in teammates:
            # Teammates nearby can be good (options) or bad (congestion)
            distance = player.calculate_distance(position, player.current_position)
            if distance <= 1:
                # Very close teammates reduce space
                space_rating *= 0.9
            else:
                # Slightly further teammates provide options
                space_rating *= 1.1
        
        for player in opponents:
            # Opponents always reduce space
            distance = player.calculate_distance(position, player.current_position)
            if distance <= 1:
                # Close opponents significantly reduce space
                space_rating *= (0.7 - (player.ability_stats.tackling / 200))
            else:
                # Further opponents have less impact
                space_rating *= (0.9 - (player.ability_stats.tackling / 400))
        
        # Adjust for field position
        x, y = self.get_coordinates(position)
        
        # More space on wings
        if x in [0, 2]:  # Wide positions
            space_rating *= 1.2
        
        # Adjust for proximity to goals
        if self.is_forward_position(position):
            space_rating *= 0.9  # Usually more congested near goals
        
        # Add small random variation
        space_rating *= random.uniform(0.9, 1.1)
        
        return max(0.1, min(1.0, space_rating))
    
    def get_optimal_movement_positions(self, player, radius=2):
        """Find optimal positions for player movement."""
        current_coords = self.get_coordinates(player.current_position)
        potential_positions = []
        
        # Check all positions within radius
        for dx in range(-radius, radius + 1):
            for dy in range(-radius, radius + 1):
                new_x = current_coords[0] + dx
                new_y = current_coords[1] + dy
                new_pos = (new_x, new_y)
                
                if not self.get_position_from_coordinates(new_pos):
                    continue
                    
                # Calculate position rating
                space = self.get_space_rating(new_pos, player.team)
                tactical_value = self.calculate_tactical_rating(
                    new_pos, 
                    self.team1_tactics if player.team == self.team1 else self.team2_tactics
                )
                
                # Adjust for player attributes
                movement_rating = (
                    space * 0.4 +
                    tactical_value * 0.4 +
                    (player.ability_stats.tactical / 100) * 0.2
                )
                
                potential_positions.append((new_pos, movement_rating))
        
        return potential_positions
        
    def calculate_tactical_rating(self, position, offense_strategy, mentality):
        """Calculate how well a position fits the tactical plan."""
        rating = 1.0
        
        # Adjust for offensive strategy
        if offense_strategy == 'direct':
            if position in ["Centre", "CHF", "FF"]:
                rating *= 1.5
        elif offense_strategy == 'stay_wide':
            if position in ["LWing", "RWing", "LF", "RF"]:
                rating *= 1.5
        
        # Adjust for mentality
        if mentality == 'attacking':
            if position in ["FF", "CHF", "LF", "RF"]:
                rating *= 1.3
        elif mentality == 'defensive':
            if position in ["CHB", "FB", "LB", "RB"]:
                rating *= 1.3
        
        return rating   
    
    
    def advance_ball(self, team, team_players, current_position, receiver=None, known_next_position=None, disp_type=None):
        print(f'Team: {team}')
        print(f"Advancing ball from {current_position}")
        print(f"receiver {receiver} and currently at {receiver.current_position}")
        #print(f"Team Players {team_players}")
        #time.sleep(3)
        if receiver is not None:
            if receiver.team == self.team1: 
                team = self.team1
            else:
                team = self.team2
        
        if disp_type is None:
            disp_list =  ("kick", "handball")
            disp_type = random.choice(disp_list)
        
        
        print(f"Disposer: {self.disposer}")
        #self.stats_manager.record_stat(disposer.name, 'kicks')
        #time.sleep(0.5)

        if known_next_position:
            print(f"Known Next Position {known_next_position}")
            next_position = known_next_position
            known_next_position=None
        else:
            if disp_type == "kick":
                min_kick_distance = 15  # Minimum kick distance (1 cell)
                max_kick_distance = 60
                kick_distance = min_kick_distance + ((self.disposer.ability_stats.kicking - 1) / 19) * (max_kick_distance - min_kick_distance)
                print(f"Max Kick Distance {kick_distance}")

                meters_per_cell_length = self.ground_length / self.grid_length  # Should be 10 (160m/16)
                meters_per_cell_width = self.ground_width / self.grid_width 
                meters_per_cell = (meters_per_cell_length + meters_per_cell_width) / 2

                max_kick_cells = max(1, int(kick_distance / meters_per_cell))
                print(f"Max Kick Cells {max_kick_cells}")
                if receiver is None:
                    next_position = self.get_dynamic_possible_positions(current_position, max_kick_cells)
                    next_position = random.choices(
                    next_position,
                    #weights=[1.2 if pos == "FF" else 1.0 for pos in next_position],
                    k=1
                    )[0]
                else:
                    next_position = receiver.current_position
                # Choose a position based on performance and tactical decision

                print(f"Next Position was not Known, now: {next_position}")    
            elif disp_type == "handball":
                if receiver is None:
                    next_position = self.get_dynamic_possible_positions(current_position, max_kick_cells)
                    next_position = random.choices(
                    next_position,
                    #weights=[1.2 if pos == "Centre" else 1.0 for pos in next_position],
                    k=1
                    )[0]
                else:
                    next_position = receiver.current_position

                print(f"Next Position not Known {next_position}")

        
        
        #next_position = self.get_next_position(current_position)
        #receiver = team_players.get(next_position)
        
        #if not receiver:
        #    print(f"No receiver found for position {next_position}, ball up")
        #    return "ball_up"
        backward_pass = None
        # Determine if the pass is backward
        backward_pass = (
            (current_position == "RHF" and next_position == "RWing") or
            (current_position == "LHF" and next_position == "LWing") or
            (current_position == "LB" and next_position == "FB") or
            (current_position == "RB" and next_position == "FB") or
            (current_position == "CHF" and next_position == "Centre")
        )


        opponent_team = self.get_opponent(team)
        opponent_players = self.get_opponent_players(team)

        mirrored_position = self.get_mirrored_position(next_position)
        print(f"Mirrored: {mirrored_position}")

        #opponent = opponent_players.get(mirrored_position)
        #if not opponent.is_in_position(mirrored_position):
        opponent = Player.determine_closest_opponent_player_to_ball(
            self, 
            current_position,  # Use current position instead of next position
            list(opponent_players.values()),
            min_distance_send=6  # Adjust this value as needed
        )
        #print(f"{opponent.name} is closest opponent")
        
        print(f"reciever {receiver}")
        print(f"opponent {opponent}")

                
        if opponent is None:
            print(f"No opponent found at position {mirrored_position}. {receiver.name} advances unopposed!")
            if random.random() < 0.005:  # 2% chance of a risky move
                risky_event = random.choice([
                    f"{receiver.name} tries a fancy trick shot!",
                    f"{receiver.name} does a blind turn and risks losing balance!",
                    f"{receiver.name} decides to show off with a banana kick!"
                ])
                print(f"Risky Event: {risky_event}")
                self.events.append({'quarter': self.quarter, 'event': risky_event})

                if random.random() < 0.5:  # 50% chance of success
                    print(f"{receiver.name} successfully pulls off the risky move!")
                    self.ball_position = self.update_ball_position(current_position, next_position)
                    self.ball_position_name = self.get_ball_position()
                    self.stats_manager.record_stat(self.disposer.name, disp_type.lower()+'s')
                    self.disposer = receiver
                    self.disposer.current_position = next_position
                    return {
                        "result": f"{receiver.name} successfully pulls off the risky move!",
                        "next_action": "handle_possession",
                        "team": team,
                        "team_players": team_players
                    }
                else:
                    print(f"{receiver.name} fails the risky move and fumbles the ball!")
                    self.ball_position = self.update_ball_position(current_position, next_position)
                    opponent = Player.determine_closest_opponent_player_to_ball(self, mirrored_position, list(opponent_players.values()), 8)
                    self.mirrored_position = self.get_mirrored_position(self.ball_position)
                    self.ball_position = self.mirrored_position
                    self.stats_manager.record_stat(opponent.name, 'turnovers_won')
                    self.ball_position_name = self.get_ball_position()
                    self.disposer = opponent
                    self.disposer.current_position = self.ball_position_name
                    return {
                        "result": f"{receiver.name} fails the risky move and fumbles the ball!",
                        "next_action": "handle_possession",
                        "team": opponent.team,
                        "team_players": opponent.team_players,
                    }
            else:
                self.ball_position = self.update_ball_position(current_position, next_position)
                self.ball_position_name = self.get_ball_position()
                print(f"Ball Position {self.ball_position}")
                #time.sleep(1)
                self.stats_manager.record_stat(self.disposer.name, disp_type.lower()+'s')
                self.disposer = receiver
                self.disposer.current_position = next_position
                return {
                        "result": f"{self.disposer} Has no opponent so play on",
                        "next_action": "handle_possession",
                        "team": team,
                        "team_players": team_players
                    }    


        if backward_pass:
            # Increase pass success probability for backward passes
            #receiver_performance *= 1.2
            print(f"Backward pass from {current_position} to {next_position}, increased receiver performance")
            receiver_performance = 1
            opponent_performance = 0
        else:
            print("Pass not backwars")    
            receiver_performance, opponent_performance = self.possession_contest(receiver, opponent)
        
        Current_team_tactics = self.team1_tactics if team == self.team1 else self.team2_tactics
        opponent_team_tactics = self.team2_tactics if opponent_team == self.team2 else self.team1_tactics
        print(f"Current Team Tactics {Current_team_tactics}")
        print(f"Opponent Team Tactics {opponent_team_tactics}")

        receiver_performance = Team.Tactics_handler (team, next_position, receiver, opponent_team, receiver_performance, opponent_performance, Current_team_tactics, opponent_team_tactics)
        print(f"rec Performance {receiver_performance} Opp Performomarnce {opponent_performance} ")
        #opponent_performance = Team.Tactics_handler (team, self.ball_position, receiver, opponent_team, receiver_performance, opponent_performance, opponent_team_tactics)

        if random.random() < 0.005:  # 2% chance of a unique or fun event
            unique_event = random.choice([
                f"{receiver.name} is temporarily blinded by the sun, misjudging the ball!",
                "A seagull swoops down and distracts the players!",
                "A sudden gust of wind changes the ball's trajectory!",
                f"The crowd starts chanting {receiver.name}'s name, boosting his confidence!",
                f"{receiver.name} is distracted by a rogue balloon floating onto the field!",
                f"{receiver.name} decides to taunt the opponent, losing focus!",
                f"{receiver.name} is distracted by a streaker on the field!",
                f"{receiver.name} takes a moment to adjust his boots, risking a turnover!",
                f"{receiver.name} gets caught in a spider-web of passes and fumbles the ball!",
                "A gust of wind blows the ball off course, surprising everyone!"
            ])
            print(f"Unique Event: {unique_event}")
            event = {'quarter': self.quarter, 'event': unique_event}
            self.events.append({'quarter': self.quarter, 'event': unique_event})
            #self.broadcast_event(event)
            #time.sleep(30)

            # Implement the effects of the unique event
            if "blinded by the sun" in unique_event or "distracted" in unique_event:
                print(f"{receiver.name} misjudges the ball due to the distraction!")
                turnover_probability = (
                    opponent.ability_stats.tackling * 1.5 +
                    opponent.ability_stats.mental * 0.3 +
                    opponent.ability_stats.tactical * 0.3
                ) / 100 + random.uniform(0.01, 0.5)

                if random.random() < turnover_probability:
                    print(f"{opponent.name} capitalizes on the mistake, causing a turnover!")
                    self.ball_position = self.update_ball_position(current_position, next_position)
                    self.stats_manager.record_stat(opponent.name, 'turnovers_won')
                    self.mirrored_position = self.get_mirrored_position(self.ball_position)
                    self.ball_position = self.mirrored_position
                    self.ball_position_name = self.get_ball_position()
                    self.disposer = opponent
                    self.disposer.current_position = self.ball_position_name
                    return {
                        "result": f"{opponent.name} capitalizes on the mistake, causing a turnover!",
                        "next_action": "handle_possession",
                        "team": opponent_team,
                        "team_players": opponent_players
                    }
                else:
                    print(f"{receiver.name} recovers just in time and retains possession!")
                    self.ball_position = self.update_ball_position(current_position, next_position)
                    self.ball_position_name = self.get_ball_position()
                    self.disposer = receiver
                    self.disposer.current_position = next_position
                    return {
                        "result": f"{receiver.name} recovers just in time and retains possession!",
                        "next_action": "handle_possession",
                        "team": team,
                        "team_players": team_players
                    }
            elif "boosting his confidence" in unique_event:
                # Temporarily boost receiver's performance
                receiver_performance *= 1.5
                print(f"{receiver.name}'s performance is temporarily boosted!")

            # Example: Mental stats affecting response to distractions
            if "distracted" in unique_event:
                distraction_penalty = max(0.2, 1 - (receiver.ability_stats.mental * 0.05))
                receiver_performance *= distraction_penalty
                print(f"{receiver.name}'s performance is reduced due to distraction: {receiver_performance}")

            # Introduce player fatigue and errors
        fatigue_effect = receiver.physical_stats.stamina * 0.02
        if random.random() < fatigue_effect:
            print(f"{receiver.name} makes a fatigue-induced error! Fatigue Effect {fatigue_effect}")
            #self.stats_manager.record_stat(receiver.name, 'turnovers_won')
            #self.position = self.get_ball_position()
            #self.disposer = opponent.name
            #return self.handle_possession(opponent_team, opponent_players)

        print(f"Disp type {disp_type}")
        if disp_type=="kick":
            # Determine the outcome of the contest
            if receiver_performance > opponent_performance:
                # Calculate the probability of dropping the mark based on the receiver's attributes
                print (f"Receiver Performance {receiver_performance} Opponent Performance {opponent_performance}")
                drop_mark_probability = (
                        receiver.ability_stats.marking * 1.5 +
                        receiver.ability_stats.consistency * 0.7 +
                        receiver.ability_stats.mental * 0.6
                    ) / 100 + random.uniform(0.01, 0.02)
                if backward_pass:
                    # Decrease the probability of dropping the mark for backward passes
                    drop_mark_probability *= 0.2  # Reduce by 80%
                    print(f"Drop Mark Prob adjusted for backward pass: {drop_mark_probability}")

                print(f"Drop Mark Prob: {drop_mark_probability}")
                if random.random() < drop_mark_probability:  # Receiver might drop the mark
                    turnover_probability = (
                        opponent.ability_stats.tackling * 1.5 +
                        opponent.ability_stats.mental * 0.3 +
                        opponent.ability_stats.tactical * 0.3
                    ) / 100 + random.uniform(0.01, 0.5)
                    print(f"Turnover Prob: {turnover_probability}")
                    if backward_pass:
                        # Decrease the probability of dropping the mark for backward passes
                        print(f"Drops mark but Backwars pass so continue")
                        self.ball_position = self.update_ball_position(current_position, next_position)
                        self.ball_position_name = self.get_ball_position()
                        self.stats_manager.record_stat(self.disposer.name, 'kicks')
                        self.disposer = receiver
                        self.disposer.current_position = next_position
                        return {
                            "result": f"{receiver.name} Drops mark but Backwars pass so continue",
                            "next_action": "handle_possession",
                            "team": team,
                            "team_players": team_players
                        }
                    elif random.random() < turnover_probability:  # Opponent might cause a turnover
                        print(f"{opponent.name} tackles {receiver.name}, causing a turnover")
                        self.ball_position = self.update_ball_position(current_position, next_position)
                        self.mirrored_position = self.get_mirrored_position(self.ball_position)
                        self.ball_position = self.mirrored_position
                        self.stats_manager.record_stat(self.disposer.name, 'kicks')
                        self.stats_manager.record_stat(opponent.name, 'turnovers_won')
                        self.ball_position_name = self.get_ball_position()
                        self.disposer = opponent
                        self.disposer.current_position = self.ball_position_name
                        return {
                            "result": f"{opponent.name} tackles {receiver.name}, causing a turnover",
                            "next_action": "handle_possession",
                            "team": opponent_team,
                            "team_players": opponent_players
                        }
                    else:
                        print(f"{receiver.name} drops the mark but holds possession")
                        self.ball_position = self.update_ball_position(current_position, next_position)
                        self.ball_position_name = self.get_ball_position()
                        self.stats_manager.record_stat(self.disposer.name, 'kicks')
                        self.disposer = receiver
                        self.disposer.current_position = next_position
                        return {
                            "result": f"{receiver.name} drops the mark but holds possession",
                            "next_action": "handle_possession",
                            "team": team,
                            "team_players": team_players
                        }
                else:
                    print(f"{receiver.name} successfully marks the ball")
                    self.stats_manager.record_stat(receiver.name, 'marks')
                    self.stats_manager.record_stat(self.disposer.name, 'kicks')
                    self.ball_position = self.update_ball_position(current_position, next_position)
                    print("Return from update_ball_position")
                    print(f"ball position {self.ball_position}")
                    print(f"Team Players {team_players}")
                    self.ball_position_name = self.get_ball_position()
                    self.disposer = receiver
                    self.disposer.current_position = next_position
                    return {
                            "result": f"{receiver.name} successfully marks the ball",
                            "next_action": "handle_possession",
                            "team": team,
                            "team_players": team_players
                        }
            else:
                interception_probability = (
                    opponent.ability_stats.marking * 0.8 +
                    opponent.ability_stats.mental * 0.3 +
                    opponent.ability_stats.tactical * 0.3
                ) / 100 + random.uniform(0.01, 1.0)
                print(f"intercept Prob: {interception_probability}")
                if random.random() < interception_probability:  # Opponent might intercept the mark
                    print(f"{opponent.name} intercepts the mark, causing a turnover")
                    self.ball_position = self.update_ball_position(current_position, next_position)
                    self.mirrored_position = self.get_mirrored_position(self.ball_position)
                    self.ball_position = self.mirrored_position
                    self.stats_manager.record_stat(self.disposer.name, 'kicks')
                    self.stats_manager.record_stat(opponent.name, 'marks')
                    self.stats_manager.record_stat(opponent.name, 'interceptions_won')
                    self.ball_position_name = self.get_ball_position()
                    self.disposer = opponent
                    self.disposer.current_position = self.ball_position_name
                    return {
                        "result": f"{opponent.name} intercepts the mark, causing a turnover",
                        "next_action": "handle_possession",
                        "team": opponent_team,
                        "team_players": opponent_players
                    }
                else:
                    print(f"{opponent.name} fails to intercept, {receiver.name} holds possession")
                    self.ball_position = self.update_ball_position(current_position, next_position)
                    print("Return from update_ball_position")
                    print(f"ball position {self.ball_position}")
                    self.stats_manager.record_stat(self.disposer.name, 'kicks')
                    self.ball_position_name = self.get_ball_position()
                    self.disposer = receiver
                    self.disposer.current_position = next_position
                    return {
                            "result": f"{opponent.name} fails to intercept, {receiver.name} holds possession",
                            "next_action": "handle_possession",
                            "team": team,
                            "team_players": team_players
                        }
        
        if disp_type=="handball":
            if receiver_performance > opponent_performance:
                if backward_pass:
                        # Decrease the probability of dropping the mark for backward passes
                        print(f"Drops mark but Backwars pass so continue")
                        self.ball_position = self.update_ball_position(current_position, next_position)
                        self.ball_position_name = self.get_ball_position()
                        self.stats_manager.record_stat(self.disposer.name, 'handballs')
                        self.disposer = receiver
                        self.disposer.current_position = next_position
                        return {
                                "result": f"{receiver.name} Drops mark but Backwars pass so continue",
                                "next_action": "handle_possession",
                                "team": team,
                                "team_players": team_players
                            }
                else:
                        print(f"{receiver.name} Takes possession")
                        self.ball_position = self.update_ball_position(current_position, next_position)
                        self.ball_position_name = self.get_ball_position()
                        self.stats_manager.record_stat(self.disposer.name, 'handballs')
                        self.disposer = receiver
                        self.disposer.current_position = next_position
                        return {
                                "result": f"{receiver.name} Takes possession",
                                "next_action": "handle_possession",
                                "team": team,
                                "team_players": team_players
                            }
            else:    
                interception_probability = (
                    opponent.ability_stats.marking * 0.8 +
                    opponent.ability_stats.mental * 0.3 +
                    opponent.ability_stats.tactical * 0.3
                ) / 100 + random.uniform(0.01, 1.0)
                print(f"intercept Prob: {interception_probability}")
                if random.random() < interception_probability:  # Opponent might intercept the mark
                    print(f"{opponent.name} intercepts the handball, causing a turnover")
                    self.ball_position = self.update_ball_position(current_position, next_position)
                    self.mirrored_position = self.get_mirrored_position(self.ball_position)
                    self.ball_position = self.mirrored_position
                    #self.stats_manager.record_stat(opponent.name, 'marks')
                    self.stats_manager.record_stat(self.disposer.name, 'handballs')
                    self.stats_manager.record_stat(opponent.name, 'interceptions_won')
                    self.ball_position_name = self.get_ball_position()
                    self.disposer = opponent
                    self.disposer.current_position = self.ball_position_name
                    return {
                        "result": f"{opponent.name} intercepts the handball, causing a turnover",
                        "next_action": "handle_possession",
                        "team": opponent_team,
                        "team_players": opponent_players
                    }
                else:
                    print(f"{opponent.name} fails to intercept, {receiver.name} holds possession")
                    self.stats_manager.record_stat(self.disposer.name, 'handballs')
                    self.ball_position = self.update_ball_position(current_position, next_position)
                    print("Return from update_ball_position")
                    print(f"ball position {self.ball_position}")
                    self.ball_position_name = self.get_ball_position()
                    self.disposer = receiver
                    self.disposer.current_position = next_position
                    return {
                            "result": f"{opponent.name} fails to intercept, {receiver.name} holds possession",
                            "next_action": "handle_possession",
                            "team": team,
                            "team_players": team_players
                        }
                
    def attempt_goal(self, player, position):
        print(f"{player.name} attempting goal from {position}")

        # Calculate the goal kicking performance
        performance = self.calculate_goal_kicking_performance(player)

        # Adjust difficulty based on position
        if position in ["RHF", "LHF"]:
            performance *= 0.50  # Decrease performance for harder angles

        # Adjust difficulty based on position
        if position in ["CHF"]:
            performance *= 0.70  # Decrease performance for harder angles

        # Decide whether to attempt the goal or pass
        if position in ["CHF"] and random.random() < 0.5:
            # Consider passing the ball instead of attempting a difficult goal
            return {
                    "result": f"{player.name} Choose to pass instead of attempt goal from {position}",
                    "next_action": "consider_pass",
                    "player": player,
                    "position": position
                }
        
        # Decide whether to attempt the goal or pass
        if position in ["RHF", "LHF"] and random.random() < 0.7:
            # Consider passing the ball instead of attempting a difficult goal
            return {
                    "result": f"{player.name} Choose to pass instead of attempt goal from {position}",
                    "next_action": "consider_pass",
                    "player": player,
                    "position": position
                }
        
        if position in ["LF", "RF"] and random.random() < 0.3:
            # Consider passing the ball instead of attempting a goal from LF/RF
            if player.ability_stats.goal_kicking < 0.7 * performance:
                return {
                    "result": f"{player.name} Choose to pass instead of attempt goal from {position}",
                    "next_action": "consider_pass",
                    "player": player,
                    "position": position
                }
        else:
             performance *= 0.20   
        
        # Goal attempt
        if performance > random.uniform(50, 120):
            print(f"{player.name} scores a goal!")
            self.team1_score += 6 if player.team == self.team1 else 0
            self.team2_score += 6 if player.team == self.team2 else 0
            self.events.append({'quarter': self.quarter, 'event': f'Goal scored for {player.team} thanks to {player}'})
            event = (f'Goal scored for {player.team} thanks to {player}')
            self.ball_position = self.get_center_of_ground()
            self.ball_position_name = self.get_ball_position()
            #self.update_player_positions("goal")
            self.stats_manager.record_stat(player.name, 'goals')
            #self.send_match_event(self.events)
            #time.sleep(10) 
            
            return "goal"
        else:
            # Consider random events like skewing the kick
            if random.random() < 0.1:  # 10% chance of kicking out on the full
                print(f"{player.name}'s kick skewed off the foot, out on the full")
                possible_positions = ["LB", "RB"]
                ball_position = random.choice(possible_positions)
                self.ball_position = self.update_ball_position(self.ball_position, ball_position)
                self.ball_position_name = self.get_ball_position()
                #self.mirrored_position = self.turnover(self.ball_position)
                #self.ball_position = self.mirrored_position
                return "out_on_full"
            else:
                print(f"{player.name} scores a behind")
                self.team1_score += 1 if player.team == self.team1 else 0
                self.team2_score += 1 if player.team == self.team2 else 0
                ball_position = "FB"
                self.ball_position = self.update_ball_position(ball_position, ball_position)
                self.ball_position_name = self.get_ball_position()
                #self.mirrored_position = self.turnover(self.ball_position)
                #self.ball_position = self.mirrored_position
                self.stats_manager.record_stat(player.name, 'behinds')
                return "behind"
    """
    def consider_pass(self, player, position):
        print(f"{player.name} considering a pass from {position}")

        # Decide between kicking and handballing based on distance and player stats
        if random.random() < 0.4 or player.ability_stats.handball > player.ability_stats.kicking:
            # Choose to handball if the player's handballing stat is higher, or randomly
            return self.handball(player, position)
        else:
            return self.kick(player, position)
    """
    """
    def kick(self, player, position):
        print(f"{player.name} considering a kick from {position}")

        # Calculate the maximum kick distance based on player's stats
        min_kick_distance = 15  # Minimum kick distance (1 cell)
        max_kick_distance = 60
        kick_distance = min_kick_distance + ((player.ability_stats.kicking - 1) / 19) * (max_kick_distance - min_kick_distance)
        print(f"Max Kick Distance {kick_distance}")
        # Convert the kick distance to grid cells (1 cell = 10 meters)
        max_kick_cells = int(kick_distance / 10)
        print(f"Max Kick Cells {max_kick_cells}")
        # Potential positions to pass to based on current position
        possible_positions = self.get_dynamic_possible_positions(position, max_kick_cells)
        potential_receivers = []
        # Choose a position based on performance and tactical decision
        print(f"Kick Posible possitions: {possible_positions}")
        #sys.exit()
        next_position = random.choices(
            possible_positions,
            weights=[1.2 if pos == "FF" else 1.0 for pos in possible_positions],
            k=1
        )[0]

        print(f"{player.name} passes to {next_position}")

           # Find the player in the next position
        team_players = self.team1_players.values() if player.team == self.team1 else self.team2_players.values()
        receiver = None
        if receiver:
            print(f"Receiver: {receiver.name} at {next_position}")
        else:
            print(f"No specific receiver found at {next_position}, finding best receiver...")
     
            
            # Find potential receivers from the same team in those positions
            #for pos in next_position:
            for p in team_players:
                print(f"Player {p.name} at {p.current_position} and next position {next_position}")
                time.sleep(1)
                if p != player and p.current_position == next_position:  # Exclude current player
                    distance = player.calculate_distance(next_position)
                    receiving_potential = (
                        (100 - distance) * 
                        (p.ability_stats.marking / 100) * 
                        (p.physical_stats.agility / 100)
                    )
                    potential_receivers.append((p, receiving_potential))
            
            if potential_receivers:
                # Sort by receiving potential and add some randomness
                potential_receivers.sort(key=lambda x: x[1] * random.uniform(0.8, 1.2), reverse=True)
                receiver = potential_receivers[0][0]
                print(f"Found receiver {receiver.name} based on position and abilities")
            else:
                print("No receivers in range, finding closest players...")
                # Fallback to closest teammate as last resort
                all_players = list(self.team1_players.values()) + list(self.team2_players.values())
                for p in all_players:
                    print(f"Player {p.name} at {p.current_position} and next position {next_position}")
                    time.sleep(1)
                    if p != player and p.current_position == next_position:  # Exclude current player
                        distance = player.calculate_distance(next_position)
                        receiving_potential = (
                            (500 - distance) * 
                            (p.ability_stats.mental / 100) * 
                            (p.physical_stats.agility / 100)
                        )
                        potential_receivers.append((p, receiving_potential))
                potential_receivers.sort(key=lambda x: x[1] * random.uniform(0.8, 1.2), reverse=True)
                receiver = potential_receivers[0][0]
                print(f"Closest player: {receiver.name} based on proximity and speed/agility")
        
        if receiver.team == player.team:
            action = "advance_ball"
            if receiver.team == self.team1:
                print(f"Reciever team {receiver.team}") 
                team_players = self.team1_players
            else:
                team_players = self.team2_players
                print(f"Reciever team {receiver.team}")
            time.sleep(3)
            return {
            "result": f"{player.name} looks to advance the ball",
            "next_action": action,
            "team": receiver.team,
            "team_players": team_players,
            #"player": player,
            "position": position,
            "receiver": receiver,
            "known_next_position": next_position,
            "Disp_type":"Kick"
            }
        else:
            action = "handle_possession"
            if receiver.team == self.team1:
                print(f"Reciever team {receiver.team}") 
                team_players = self.team1_players
                self.mirrored_position = self.turnover(self.ball_position)
                self.ball_position = self.mirrored_position
                #self.stats_manager.record_stat(opponent.name, 'marks')
                self.position = self.get_ball_position()
            else:
                team_players = self.team2_players
                print(f"Reciever team {receiver.team}") 
                self.mirrored_position = self.turnover(self.ball_position)
                self.ball_position = self.mirrored_position
                #self.stats_manager.record_stat(opponent.name, 'marks')
                self.position = self.get_ball_position()
                time.sleep(3)
            return {
                        "result": f"{receiver.team} intercepts the handball, causing a turnover",
                        "next_action": action,
                        "team": receiver,
                        "team_players": team_players
                    }
        

        receiver = None
        for p in team_players:
            if p.current_position == next_position:
                receiver = p
                break
        
        all_players = list(self.team1_players.values()) + list(self.team2_players.values())
        
        if receiver:
            print(f"Receiver: {receiver.name} at {next_position}")
        else:
            print(f"No specific receiver found at {next_position}, considering other factors...")

            # If no player is directly in the next position, find the closest player
            closest_player = Player.determine_closest_player_to_ball(self, self.get_coordinates(next_position), all_players)
            print(f"Closest player: {closest_player.name} based on proximity and speed/agility")
            receiver = closest_player

        if receiver.team == self.team1:
            print(f"Reciever team {receiver.team}") 
            team_players = self.team1_players
        else:
            team_players = self.team2_players
            print(f"Reciever team {receiver.team}") 
        
        print(f"Player Team {player.team}")       
        self.disposer = player
        return self.advance_ball(player.team, team_players, position, receiver, known_next_position=next_position, Disp_type="Kick")
        """
    """
    def handball(self, player, position):
        print(f"{player.name} considering a handball from {position}")

        # Calculate the maximum kick distance based on player's stats
        min_handball_distance = 1  # Minimum handball distance
        max_handball_distance = 20
        handball_distance = min_handball_distance + ((player.ability_stats.handball - 1) / 19) * (max_handball_distance - min_handball_distance)
        print(f"Max handball Distance {handball_distance}")
        # Convert the kick distance to grid cells (1 cell = 10 meters)
        max_handball_cells = max(1, int(handball_distance / 10))  # Ensure at least 1 cell
        print(f"Max handball Cells {max_handball_cells}")
        # Potential positions to pass to based on current position
        possible_positions = self.get_dynamic_possible_positions(position, max_handball_cells)
        potential_receivers = []
        # Choose a position based on proximity and performance
        next_position = random.choices(
            possible_positions,
            weights=[1.2 if pos == "Centre" else 1.0 for pos in possible_positions],
            k=1
        )[0]

        print(f"{player.name} handballs to {next_position}")

        team_players = self.team1_players.values() if player.team == self.team1_players else self.team2_players.values()
        receiver = None
        if receiver:
            print(f"Receiver: {receiver.name} at {next_position}")
        else:
            print(f"No specific receiver found at {next_position}, finding best receiver...")       
            
            # Find potential receivers from the same team in those positions
            #for pos in next_position:
            for p in team_players:
                print(f"Player {p.name} at {p.current_position} and next position {next_position}")
                time.sleep(1)
                if p != player and p.current_position == next_position:  # Exclude current player
                    distance = player.calculate_distance(next_position)
                    receiving_potential = (
                        (100 - distance) * 
                        (p.ability_stats.mental / 100) * 
                        (p.physical_stats.agility / 100)
                    )
                    potential_receivers.append((p, receiving_potential))
            
            if potential_receivers:
                # Sort by receiving potential and add some randomness
                potential_receivers.sort(key=lambda x: x[1] * random.uniform(0.8, 1.2), reverse=True)
                receiver = potential_receivers[0][0]
                print(f"Found receiver {receiver.name} based on position and abilities")
            else:
                print("No receivers in range, finding closest players...")
                # Fallback to closest teammate as last resort
                all_players = list(self.team1_players.values()) + list(self.team2_players.values())
                for p in all_players:
                    print(f"Player {p.name} at {p.current_position} and next position {next_position}")
                    time.sleep(1)
                    if p != player and p.current_position == next_position:  # Exclude current player
                        distance = player.calculate_distance(next_position)
                        receiving_potential = (
                            (100 - distance) * 
                            (p.ability_stats.mental / 100) * 
                            (p.physical_stats.agility / 100)
                        )
                        potential_receivers.append((p, receiving_potential))
                potential_receivers.sort(key=lambda x: x[1] * random.uniform(0.8, 1.2), reverse=True)
                receiver = potential_receivers[0][0]
                print(f"Closest player: {receiver.name} based on proximity and speed/agility")
        
        if receiver.team == player.team:
            action = "advance_ball"
            if receiver.team == self.team1:
                print(f"Reciever team {receiver.team}") 
                team_players = self.team1_players
            else:
                team_players = self.team2_players
                print(f"Reciever team {receiver.team}")
            time.sleep(3)
            return {
            "result": f"{player.name} looks to advance the ball",
            "next_action": action,
            "team": receiver.team,
            "team_players": team_players,
            #"player": player,
            "position": position,
            "receiver": receiver,
            "known_next_position": next_position,
            "Disp_type":"Handball"
            }
        else:
            action = "handle_possession"
            if receiver.team == self.team1:
                print(f"Reciever team {receiver.team}") 
                team_players = self.team1_players
                self.mirrored_position = self.turnover(self.ball_position)
                self.ball_position = self.mirrored_position
                #self.stats_manager.record_stat(opponent.name, 'marks')
                self.position = self.get_ball_position()
            else:
                team_players = self.team2_players
                print(f"Reciever team {receiver.team}") 
                self.mirrored_position = self.turnover(self.ball_position)
                self.ball_position = self.mirrored_position
                #self.stats_manager.record_stat(opponent.name, 'marks')
                self.position = self.get_ball_position()
                time.sleep(3)
            return {
                        "result": f"{receiver.team} intercepts the handball, causing a turnover",
                        "next_action": action,
                        "team": receiver,
                        "team_players": team_players
                    }
    """   
    def get_next_position(self, current_position, player, team_tactics, max_distance):
        possible_positions = self.get_dynamic_possible_positions(current_position, max_distance)
        
        if not possible_positions:
            return current_position

        # Define position weights based on tactics and field zones
        position_weights = {}
        
        # Base weights for different zones
        central_positions = ["Centre", "CHB", "CHF"]
        wide_positions = ["LWing", "RWing", "LHF", "RHF"]
        forward_positions = ["CHF", "FF", "LHF", "RHF", "LF", "RF"]
        defensive_positions = ["CHB", "FB", "LHB", "RHB", "LB", "RB"]
        
        for pos in possible_positions:
            # Start with base weight
            weight = 1.0
            
            # Adjust based on offensive strategy
            if team_tactics['offense_strategy'] == 'direct':
                if pos in central_positions:
                    weight *= 1.5
                elif pos in forward_positions:
                    weight *= 1.3
            elif team_tactics['offense_strategy'] == 'stay_wide':
                if pos in wide_positions:
                    weight *= 1.5
            
            # Adjust based on mentality
            if team_tactics['mentality'] == 'attacking':
                if pos in forward_positions:
                    weight *= 1.4
            elif team_tactics['mentality'] == 'defensive':
                if pos in defensive_positions:
                    weight *= 1.3
            
            position_weights[pos] = weight
        
        # Make the choice
        next_position = random.choices(
            list(position_weights.keys()),
            weights=list(position_weights.values()),
            k=1
        )[0]
        
        return next_position
        
    def get_dynamic_possible_positions(self, position, max_distance):
        """Get all possible field positions within the max_distance from the current position."""
        grid_map = self.get_grid_map()

           # Convert coordinates to position name if needed
        if isinstance(position, tuple):
            position = self.get_position_from_coordinates(position)
            if not position:
                print(f"Warning: Could not convert coordinates {position} to position name")
                sys.exit()
                return []
        
        current_coords = self.get_coordinates(position)
        possible_positions = []
        
        field_positions = [
            "LB", "FB", "RB",
            "LHB", "CHB", "RHB",
            "LWing", "Centre", "RWing",
            "LHF", "CHF", "RHF",
            "LF", "FF", "RF"
        ]
        
        # Define the vertical lines (from back to forward)
        vertical_lines = [
            ["LB", "LHB", "LWing", "LHF", "LF"],    # Left line
            ["FB", "CHB", "Centre", "CHF", "FF"],    # Center line
            ["RB", "RHB", "RWing", "RHF", "RF"]     # Right line
        ]
        
        if current_coords is None:
            print(f"Warning: Could not get coordinates for position {position}")
            return possible_positions
        
        # Find which line and position in line the current position is in
        current_line_idx = None
        current_pos_idx = None
        
        # Debug print to check position value
        print(f"Looking for position: {position}")
        
        for line_idx, line in enumerate(vertical_lines):
            print(f"Checking line {line_idx}: {line}")
            if position in line:
                current_line_idx = line_idx
                current_pos_idx = line.index(position)
                print(f"Found position in line {line_idx} at index {current_pos_idx}")
                break
        
        if current_line_idx is None or current_pos_idx is None:
            print(f"Error: Could not find position {position} in vertical lines")
            return possible_positions
        
        print(f"Checking positions from {position}")
        print(f"Current line: {current_line_idx}, Position in line: {current_pos_idx}")
        print(f"Max distance in cells: {max_distance}")
        
        for grid_position in field_positions:
            # Find target position's line and position
            target_line_idx = None
            target_pos_idx = None
            
            for line_idx, line in enumerate(vertical_lines):
                if grid_position in line:
                    target_line_idx = line_idx
                    target_pos_idx = line.index(grid_position)
                    break
            
            if target_line_idx is None or target_pos_idx is None:
                print(f"Warning: Could not find position {grid_position} in vertical lines")
                continue
            
            # Calculate horizontal distance (between lines)
            horizontal_dist = abs(current_line_idx - target_line_idx)
            
            # Calculate vertical distance (positions up/down the line)
            vertical_dist = abs(current_pos_idx - target_pos_idx)
            
            # Calculate total distance giving more weight to crossing lines
            total_distance = vertical_dist + (horizontal_dist * 1.5)
            
            print(f"Checking {grid_position}")
            print(f"Horizontal distance: {horizontal_dist}, Vertical distance: {vertical_dist}")
            print(f"Total distance: {total_distance} cells")
            
            if total_distance <= max_distance and grid_position not in possible_positions:
                possible_positions.append(grid_position)
        
        print(f"Possible positions from {position} within {max_distance} cells: {possible_positions}")
        return possible_positions

    #def get_next_position(self, current_position):
        position_map = {
            "LB": ["FB", "RB", "LHB", "CHB", "RHB"],  # Left Back Pocket can pass to Left Half Back or Center Half Back
            "FB": ["LB", "RB", "LHB", "CHB", "RHB"],  # Full Back can pass to Center Half Back or Right Half Back
            "RB": ["FB", "LB", "LHB", "CHB", "RHB"],  # Right Back Pocket can pass to Right Half Back or Center Half Back
            "LHB": ["LWing", "CHB", "RHB", "Centre", "CHF", "LHF", "RHF"],  # Left Half Back can pass to Left Wing or Center Half Back
            "CHB": ["LWing", "Centre", "RWing", "LHB", "RHB", "CHF", "LHF", "RHF"],  # Center Half Back can pass to Left Wing, Centre, or Right Wing
            "RHB": ["RWing", "CHB", "LHB", "Centre", "CHF", "LHF", "RHF"],  # Right Half Back can pass to Right Wing or Center Half Back
            "LWing": ["LHF", "Centre", "RWing", "CHF"],  # Left Wing can pass to Left Half Forward or Centre
            "Centre": ["CHF", "LHF", "RHF", "LWing", "RWing"],  # Centre can pass to Center Half Forward, Left Wing, or Right Wing
            "RWing": ["RHF", "Centre", "LWing", "CHF"],  # Right Wing can pass to Right Half Forward or Centre
            "LHF": ["LF", "CHF", "RF", "FF"],  # Left Half Forward can pass to Left Forward or Center Half Forward
            "CHF": ["LF", "FF", "RF", "LHF", "RHF"],  # Center Half Forward can pass to Left Forward, Full Forward, or Right Forward
            "RHF": ["RF", "CHF", "LF", "FF"],  # Right Half Forward can pass to Right Forward or Center Half Forward
            "LF": ["FF", "RF"],  # Left Forward can pass to Full Forward or attempt to score from Left Forward
            "FF": ["FF"],  # Full Forward stays Full Forward for scoring attempt
            "RF": ["FF", "LF"]  # Right Forward can pass to Full Forward or attempt to score from Right Forward
        }
        next_position_list = position_map.get(current_position, ["FF", "LF", "RF"])
        next_position = random.choice(next_position_list)
        print(f"Next position for {current_position}: {next_position}")
        return next_position

    #def pass_ball(self, receiver):
        success = random.choice([True, False])
        print(f"Passing ball to {receiver.name}, success: {success}")
        return success

    def update_ball_position(self, current_position, next_position):
        """Update the ball position based on either coordinates or position name."""
        print(f"Updating ball position from {current_position} to {next_position}")
        
        # If next_position is already coordinates
        if isinstance(next_position, tuple):
            if self.get_position_from_coordinates(next_position):
                self.ball_position = next_position
                self.ball_position_name = self.get_ball_position()
                print(f"Updated ball position from coordinates to: {next_position} ({self.ball_position_name})")
                return next_position
        
        # If next_position is a position name
        elif isinstance(next_position, str):
            grid_map = self.get_grid_map()
            if next_position in grid_map:
                # Get the center coordinates of the zone rather than just the first set
                zone_coords = grid_map[next_position]
                center_coords = self.get_zone_center(zone_coords)
                self.ball_position = center_coords
                self.ball_position_name = self.get_ball_position()
                print(f"Updated ball position from position name to: {center_coords} ({next_position})")
                return center_coords
        
        # If we get here, something went wrong
        print(f"Warning: Invalid position {next_position}, defaulting to Centre")
        default_pos = self.get_center_of_ground()
        self.ball_position = default_pos
        self.ball_position_name = "Centre"
        return default_pos
    
    def get_zone_center(self, zone_coords):
        """Calculate the center point of a zone."""
        if not zone_coords:
            return self.get_center_of_ground()
        
        # Calculate average x and y coordinates
        avg_x = sum(x for x, _ in zone_coords) / len(zone_coords)
        avg_y = sum(y for _, y in zone_coords) / len(zone_coords)
        
        # Round to nearest grid position
        return (round(avg_x), round(avg_y))

    def get_opponent(self, team):
        if team == self.team2:
            opponent = self.team1
            #print(f"Opponent Team: {self.team1}")
            #print(f"Current Team: {self.team2}")
        else: 
            opponent = self.team2
            #print(f"Opponent Team: {self.team2}")
            #print(f"Current Team: {self.team1}")
            #print(f" self.team1_players { self.team1_players}")
        return opponent

    def get_opponent_players(self, team):
          
        if team == self.team2:
            opponent_players = self.team1_players
            print(f"Opponent players: {opponent_players}")
            print(f"Current players: {self.team2_players}")
        else: 
            opponent_players = self.team2_players
            print(f"Opponent players: {opponent_players}")
            print(f"Current players: {self.team1_players}")
            #print(f" self.team1_players { self.team1_players}")
        
        return opponent_players

    def throw_in(self, last_position):
        grid_map = self.get_grid_map()
        position_map = self.get_position_map()
        x, y = last_position
        is_near_boundary, last_position_name = self.is_position_near_boundary(last_position)

        if is_near_boundary and last_position_name in position_map:
            next_position_list = position_map[last_position_name]
            next_position = random.choice(next_position_list)
            print(f"Performing throw-in near {last_position_name}")
            self.ball_position = last_position  # Keep the same ball position after throw-in
        else:
            print(f"Throw-in not possible from {last_position_name}, finding closest valid boundary position")

            # Find the closest valid boundary position
            min_distance = float('inf')
            closest_valid_position_name = None
            for pos_name, valid_positions in position_map.items():
                for row_index, row in enumerate(grid_map):
                    if pos_name in row:
                        pos_x = row.index(pos_name)
                        pos_y = row_index
                        distance = abs(pos_x - x) + abs(pos_y - y)
                        if distance < min_distance:
                            min_distance = distance
                            closest_valid_position_name = pos_name

            print(f"Closest valid boundary position: {closest_valid_position_name}")

            # Find coordinates of closest valid boundary position name
            for row_index, row in enumerate(grid_map):
                if closest_valid_position_name in row:
                    pos_x = row.index(closest_valid_position_name)
                    pos_y = row_index
                    self.ball_position = (pos_x, pos_y)
                    sys.exit()
        print("returning ball up from throw in")            
        return

    def is_position_near_boundary(self, coordinates):
        #print("Boundary check")
        boundary_positions = ["LB", "RB", "LHB", "RHB", "LWing", "RWing", "LHF", "RHF", "LF", "RF"]
        
        x, y = coordinates
        grid_map = self.get_grid_map()

        # Search through the grid_map to find if the coordinates match any position
        for position_name, zones in grid_map.items():
            if (x, y) in zones:
                # Check if the position name is in boundary positions
                return (position_name in boundary_positions, position_name)

        # If no match is found, return False
        return (False, None)
    
    def turnover(self, position):
        # Get the x, y coordinates of the current position
        x, y = position
        
        grid_map = self.get_grid_map()  # Get the grid map
        
        # Find the current zone (e.g., LB, RB, etc.) that the (x, y) belongs to
        current_position_name = None
        for zone, coordinates in grid_map.items():
            if (x, y) in coordinates:
                current_position_name = zone
                break
        
        if not current_position_name:
            print(f"Position {position} not found in grid map.")
            sys.exit()
            return position  # Return original position if not found in grid map
        
        # Define the mirror for each zone based on the actual structure of the grid map
        mirror_map = {
            'LB': 'RF', 'FB': 'FF', 'RB': 'LF',
            'LHB': 'RHF', 'CHB': 'CHF', 'RHB': 'LHF',
            'LWing': 'RWing', 'Centre': 'Centre', 'RWing': 'LWing',
            'LHF': 'RHB', 'CHF': 'CHB', 'RHF': 'LHB',
            'LF': 'RB', 'FF': 'FB', 'RF': 'LB'
        }
        
        # Get the mirrored zone
        mirrored_position_name = mirror_map.get(current_position_name, current_position_name)
        
        # Find the mirrored coordinates
        mirrored_position = position  # Default to current position if no mirror found
        if mirrored_position_name in grid_map:
            # Get the mirrored coordinates from the mirrored zone
            mirrored_coordinates = grid_map[mirrored_position_name]
            
            # Ensure the mirrored index matches the original position's index in the current zone
            original_index = grid_map[current_position_name].index((x, y))
            mirrored_position = mirrored_coordinates[min(len(mirrored_coordinates) - 1, original_index)]
        
        # Extract the mirrored (x, y) coordinates
        mirrored_x, mirrored_y = mirrored_position
        print(f"Mirrored ball position from {position} ({current_position_name}) to ({mirrored_x}, {mirrored_y}) ({mirrored_position_name})")
        
        return (mirrored_x, mirrored_y)
    
    def where_is_player(self):
        """Debug function to show all player positions"""
        print("\n=== Player Positions ===")
        
        # Team 1 players
        print(f"\n{self.team1.name}:")
        for player in self.team1_players.values():
            print(f"Name: {player.name:<20} Current: {player.current_position:<8} Position: {player.position:<8} Team: {player.team.name}")
        
        # Team 2 players
        print(f"\n{self.team2.name}:")
        for player in self.team2_players.values():
            print(f"Name: {player.name:<20} Current: {player.current_position:<8} Position: {player.position:<8} Team: {player.team.name}")
        
        print("\n=== End Player Positions ===\n")
        #time.sleep(8)


class Midfield:
    def __init__(self, team1_players, team2_players, ball_position, grid_map):
        self.team1_players = team1_players
        self.team2_players = team2_players
        self.ball_position = ball_position
        self.grid_map = grid_map

    def __repr__(self):
        return f"Midfield(ball_position={self.ball_position}, team1_players={len(self.team1_players)}, team2_players={len(self.team2_players)})"
       
class Team:
    def __init__(self, name):
        self.name = name
        #self.tactics = self.assign_tactics()

    #def assign_tactics(self):
        #if str(self.name) == "user2's Team":
            #return {
            #    'mentality': 'attacking',  # Can be 'attacking' or 'defensive'
            #    'defense_strategy': 'man_mark',  # Can be 'man_mark' or 'zone_mark'
            #    'offense_strategy': 'direct',  # Can be 'direct' or 'stay_wide'
            #    'push_factor': 3,
            #}
        #else:
            #return {
                #'mentality': 'defensive',
                #'defense_strategy': 'zone_mark',
                #'offense_strategy': 'stay_wide',
                #'push_factor': 1,
            #}


    def Tactics_handler (self, position, receiver, opponent_team, receiver_performance, opponent_performance, Current_team_tactics, opponent_team_tactics):
        tactics = Current_team_tactics
        opponent_tactics = opponent_team_tactics
        print(f"Team Tactics {tactics}")
        print(f"Opp Team Tactics {opponent_tactics}")

        if tactics['mentality'] == 'attacking':
            # Positive: Increase goal-scoring chances
            if position in ["LF", "FF", "RF"]:
                receiver_performance = receiver_performance * 1.1
                print(f"Attacking mentality boosts goal-scoring chances for {receiver.name}, rec boost {receiver_performance}")
            # Negative: Increase turnover risk if intercepted
            turnover_risk = 0.1
            if random.random() < turnover_risk:
                print(f"Turnover risk due to attacking mentality!")
                #return MatchEngine.handle_turnover(opponent_team, opponent_players)

        elif tactics['mentality'] == 'defensive':
            # Positive: Reduce opponent's effectiveness if turnover happens
            if opponent_tactics['mentality'] == 'attacking':
                opponent_performance = opponent_performance * 0.9
                print(f"Defensive mentality reduces opponent's effectiveness after turnover")

            # Negative: Lower goal-scoring chances due to caution
            if position in ["LF", "FF", "RF"]:
                receiver_performance = receiver_performance * 0.9
                print(f"Defensive mentality lowers goal-scoring chances for {receiver.name}")
        print(f"Hit Tactics_handler return, receiver_performance {receiver_performance}")
        return receiver_performance


class Player:
    def __init__(self, name, team, position, ability_stats, physical_stats):
        self.name = name
        self.team = team
        self.position = position
        self.current_position = position if position in ["Ruck", "RuckRover", "Rover"] else position
        self.ability_stats = ability_stats
        self.physical_stats = physical_stats
        self.current_position = position  # Starting position
        self.fatigue = 0  # Track player fatigue affecting movement and decisions
        #print(f"Player Init {self.position}")

    def __str__(self):
        return f"{self.name} - {self.position}"

    def __repr__(self):
        return self.__str__()    
    
    def determine_closest_player_to_ball(self, ball_position, players, exclude_player=None):
        closest_player = None
        min_distance = float('inf')
        closest_players = []
        
        # Create a set to track unique players we've already considered
        seen_players = set()
        for player in players:
            # Skip if this is the player we want to exclude (current possessor)
            if exclude_player and player == exclude_player:
                continue
                
            # Skip if we've already seen this player (prevent duplicates)
            if player.name in seen_players:
                continue
                
            seen_players.add(player.name)
            
            distance = player.calculate_distance(ball_position)
            print(f"Player {player.name} at distance {distance} from ball position {ball_position}")
            
            if distance < min_distance:
                min_distance = distance
                closest_players = [player]  # Reset list with the new closest player
                print(f"New closest player: {player.name}")
            elif distance == min_distance:
                closest_players.append(player)  # Add player if they are equally close
                print(f"Added equally close player: {player.name}")
        if not closest_players:
            print("No eligible players found!")
            return None
        if len(closest_players) > 1:
            print(f"Multiple closest players at distance {min_distance}: {[p.name for p in closest_players]}")
            # Multiple players are equally close; decide based on speed/agility
            weights = [p.physical_stats.speed + p.physical_stats.agility for p in closest_players]
            closest_player = random.choices(closest_players, weights=weights, k=1)[0]
            print(f"Selected {closest_player.name} based on speed/agility")
        else:
            closest_player = closest_players[0]
            print(f"Single closest player: {closest_player.name}")
        
        return closest_player
        
    
    """
    def determine_closest_player_to_ball(self, ball_position, players):
        closest_player = None
        min_distance = float('inf')
        closest_players = []

        for player in players:
            distance = player.calculate_distance(ball_position)
            print(f"Distance {distance} min distance {min_distance} ball position {ball_position} Player {player}")
            if distance < min_distance:
                min_distance = distance
                closest_players = [player]  # Reset list with the new closest player
                print(f"Closest player {[player]}")
            elif distance == min_distance:
                print(f"Closest player Amended {closest_players.append(player)}")
                closest_players.append(player)  # Add player if they are equally close

        if len(closest_players) > 1:
            print(f"More than 1 Closest player {closest_players}")
            # Multiple players are equally close; decide based on speed/agility
            weights = [p.physical_stats.speed + p.physical_stats.agility for p in closest_players]
            closest_player = random.choices(closest_players, weights=weights, k=1)[0]
            print(f"Closest player {closest_player}")
        else:
            closest_player = closest_players[0]
            print(f"Closest player {closest_player}")
        return closest_player
    """
    def determine_closest_opponent_player_to_ball(self, ball_position, players, min_distance_send=None):
        closest_player = None
        min_distance = 4 if min_distance_send is None else min_distance_send
        closest_players = []
        
        # Create a set to track unique players
        seen_players = set()
        
        for player in players:
            if player.name in seen_players:
                continue
                
            seen_players.add(player.name)
            
            distance = player.calculate_distance(ball_position)
            print(f"Opponent {player.name} at distance {distance} from ball position {ball_position}")
            
            if distance <= min_distance:  # Changed from < to <= to be more inclusive
                if not closest_players or distance < min_distance:
                    min_distance = distance
                    closest_players = [player]
                    print(f"New closest opponent: {player.name}")
                elif distance == min_distance:
                    closest_players.append(player)
                    print(f"Added equally close opponent: {player.name}")
        
        if closest_players:
            # Choose based on defensive abilities
            weights = [
                (p.physical_stats.speed + p.physical_stats.agility + p.ability_stats.tackling) 
                for p in closest_players
            ]
            closest_player = random.choices(closest_players, weights=weights, k=1)[0]
            print(f"Selected defender {closest_player.name} based on defensive abilities")
        return closest_player

    """
    def determine_closest_opponent_player_to_ball(self, ball_position, players, min_distance_send=None):
        closest_player = None
        min_distance = 4 if min_distance_send is None else min_distance_send
        #min_distance = 1
        closest_players = []

        for player in players:
            distance = player.calculate_distance(ball_position)
            print(f"Distance {distance} min distance {min_distance} ball position {ball_position}")
            if distance < min_distance:
                min_distance = distance
                closest_players = [player]  # Reset list with the new closest player
                print(f"Closest player {[player]}")
            elif distance == min_distance:
                print(f"Closest player Amended {closest_players.append(player)}")
                closest_players.append(player)  # Add player if they are equally close

        if len(closest_players) > 1:            
            print(f"More than 1 Closest opponent player {closest_players}")
            # Multiple players are equally close; decide based on speed/agility
            weights = [p.physical_stats.speed + p.physical_stats.agility for p in closest_players]
            closest_player = random.choices(closest_players, weights=weights, k=1)[0]
            print(f"Closest opponent player {closest_player}")
        if len(closest_players) < 1:
            closest_player = None
            print(f"No Closest opponent player {closest_player}")
        return closest_player
    """
    
    def is_in_position(self, ball_position):
        """Determine if the player is close enough to contest based on their current physical position"""
        distance = self.calculate_distance(ball_position)
        radius = self.get_influence_radius()
        
        # For midfield positions (including Ruck), consider them interchangeable during contests
        mobile_positions = ["Centre", "Rover", "RuckRover", "Ruck"]
        print(f"Player {self.name} at {self.current_position} (nominal position: {self.position} and ball at {ball_position})")
        
        # If player is in a mobile position, they can contest from anywhere within their influence radius
        if self.position in mobile_positions:
            print(f"Mobile player {self.name} is at {self.current_position}, ball at {ball_position}")
            # Give mobile positions a slightly larger radius for contesting
            mobile_radius = radius * 1.2  # 20% larger radius for mobile positions
            return distance < mobile_radius
        
        print(f"Player {self.name} at {self.current_position} (nominal position: {self.position}), "
                f"ball at {ball_position}, distance: {distance}, radius: {radius}")
        return distance < radius

    def calculate_distance(self, target_position, alt_position=None):
        """
        Calculate Manhattan distance between two positions.
        
        Args:
            target_position: Either (x,y) coordinates or position name
            alt_position: Optional alternative start position (default: self.current_position)
        
        Returns:
            float: Distance between positions (or inf if invalid positions)
        """
        try:
            mobile_positions = ['Ruck', 'Rover', 'RuckRover']
            
            # Handle start coordinates
            if isinstance(alt_position, tuple):
                start_coords = alt_position
            elif isinstance(self.current_position, tuple):
                start_coords = self.current_position
            else:
                start_pos = alt_position if alt_position else self.current_position
                # If it's a mobile position, use Centre coordinates
                if start_pos in mobile_positions:
                    grid_map = self.get_grid_map()
                    start_coords = grid_map['Centre'][0]  # Use first Centre coordinate
                else:
                    # For fixed positions, use the grid map
                    grid_map = self.get_grid_map()
                    for zone_name, coords_list in grid_map.items():
                        if zone_name == start_pos:
                            if coords_list:
                                start_coords = coords_list[0]
                                break
                    else:
                        print(f"Could not find coordinates for start position: {start_pos}")
                        sys.exit()
            
            # Handle target coordinates similarly
            if isinstance(target_position, tuple):
                end_coords = target_position
            else:
                # If target is a mobile position, use Centre
                if target_position in mobile_positions:
                    end_coords = grid_map['Centre'][0]
                else:
                    for zone_name, coords_list in grid_map.items():
                        if zone_name == target_position:
                            if coords_list:
                                end_coords = coords_list[0]
                                break
                    else:
                        print(f"Could not find coordinates for target position: {target_position}")
                        sys.exit()
            
            # Calculate Manhattan distance
            x1, y1 = start_coords
            x2, y2 = end_coords
            distance = abs(x1 - x2) + abs(y1 - y2)
            return distance
                
        except Exception as e:
            print(f"Error calculating distance: {e}")
            print(f"Start position: {self.current_position or alt_position}")
            print(f"Target position: {target_position}")
            sys.exit()


    """
    def calculate_distance(self, target_position, alt_position=None):
        if alt_position is None:
            coords1 = self.current_position if isinstance(self.current_position, tuple) else MatchEngine.get_coordinates(self, self.current_position)
        else:
            coords1 = alt_position if isinstance(alt_position, tuple) else MatchEngine.get_coordinates(self, alt_position)

        coords2 = target_position if isinstance(target_position, tuple) else MatchEngine.get_coordinates(self, target_position)


        if coords1 is None or coords2 is None:
            print(f"Error: Could not calculate distance because one of the positions is invalid. {self.current_position} -> {target_position}")
            return float('inf')  # Return a very large distance if coordinates are invalid

        print(f"coords1 current position {self.current_position}, coords2 target position {target_position}, coords1 {coords1}, coords2 {coords2}")
        x1, y1 = coords1
        x2, y2 = coords2
        alt_position=None
        return abs(x1 - x2) + abs(y1 - y2)

            grid_map = self.get_grid_map()
        current_coords = self.get_coordinates(position)
        possible_positions = []

        # Default max distance if not provided
        max_distance = 6 if max_distance is None else max_distance
        print(f"Dynamic Current Coords: {current_coords}")
        print(f"Position: {position}")
        """
    def get_influence_radius(self):
        # Calculate base influence radius
        base_radius = 1.0

        # Factor in player's mental ability
        mental_factor = self.ability_stats.mental * 0.2  # Mental toughness can increase influence

        # Factor in player's stamina
        stamina_factor = self.physical_stats.stamina * 0.2  # Higher stamina increases influence

        # Factor in fatigue (reduces influence as it increases)
        fatigue_penalty = max(0.5, 1 - (self.fatigue * 0.05))  # Penalty based on fatigue

        # Final influence radius calculation
        influence_radius = base_radius + mental_factor + stamina_factor
        influence_radius *= fatigue_penalty

        # Ensure the radius is not too large
        influence_radius = min(influence_radius, 5.0)  # Cap the influence radius to a max value

        print(f"Player {self.name} influence radius: {influence_radius}")
        return influence_radius
    
    def get_coordinates(self, position):
        """Get the (x, y) coordinates from a grid position name, or return the input if it's already coordinates."""
        # If position is already coordinates, return directly
        if isinstance(position, tuple):
            return position
            # Define mobile positions
        mobile_positions = ['Ruck', 'Rover', 'RuckRover']
        
        # If it's a mobile position, we need to find where that player actually is
        if position in mobile_positions:
            # Find the player with this position
            for team in [MatchEngine.team1_players, MatchEngine.team2_players]:
                for player in team.values():
                    if player.position == position:
                        # Return coordinates of where this player currently is
                        return self.get_coordinates(player.current_position)
            print(f"Warning: Could not find player for mobile position {position}")
            return self.get_coordinates('Centre')  # Default to centre if player not found
        
        # For fixed positions, get coordinates from grid map
        grid_map = self.get_grid_map()
        for zone_name, coordinates_list in grid_map.items():
            if zone_name == position:
                if coordinates_list:
                    return coordinates_list[0]  # Return the first coordinate for the named zone
            print(f"Error: Could not get coordinates for position {position}")
            sys.exit()
        return None

    def get_grid_map(self):
        """Return the cached grid map or create it if not exists."""
        if not hasattr(self, '_grid_map'):
            self._grid_map = MatchEngine.define_dynamic_zones(self, 13, 16)
        return self._grid_map
    
    #def move(self, direction):
        
        # Adjust the player's current position based on the play state and randomness
            # If the player is moving backward and their current position isn't their original one, return to original position
        if direction == "goal":
            print(f"{self.name} is returning to their natural position after goal: {self.position}")
            self.current_position = self.position
        elif direction == "forward":
            if self.position in ["Ruck", "Centre", "Rover", "RuckRover"]:
                possible_positions = ["CHF", "RHF", "LHF", "LF", "RF"]
            elif self.position in ["LWing"]:
                possible_positions = ["CHF", "LHF", "LF"]
            elif self.position in ["RWing"]:
                possible_positions = ["CHF", "RHF", "RF"]    
            elif self.position in ["LHF"]:
                possible_positions = ["FF", "CHF", "LF", "RHF"]
            elif self.position in ["RHF"]:
                possible_positions = ["FF", "CHF", "RF", "LHF"]
            elif self.position in ["LHB"]:
                possible_positions = ["LHB", "Centre"]
            elif self.position in ["RHB"]:
                possible_positions = ["RHB", "Centre"]         
        elif direction == "backward":
            if self.position in ["Ruck", "Centre", "Rover", "RuckRover"]:
                possible_positions = ["CHB", "RHB", "LHB", "LB", "RB"]
            elif self.position in ["LWing"]:
                possible_positions = ["CHB", "LHF", "LB"]
            elif self.position in ["RWing"]:
                possible_positions = ["CHB", "RHF", "RB"]
            elif self.position in ["LHB"]:
                possible_positions = ["FB", "CHB", "LB", "RHB"]
            elif self.position in ["RHB"]:
                possible_positions = ["FB", "CHB", "LB", "LHB"]
            elif self.position in ["LHF"]:
                possible_positions = ["Lwing"]
            elif self.position in ["RHF"]:
                possible_positions = ["Rwing"]        

        # Factor in player stats and randomness
        fatigue_penalty = max(0.5, 1 - (self.fatigue * 0.05))  # Reduce effectiveness based on fatigue
        mental_tactical_factor = (self.ability_stats.mental * 0.5 + self.ability_stats.tactical * 0.5) * fatigue_penalty
        weights = [mental_tactical_factor] * len(possible_positions)

        # Choose a new position based on tactical and mental attributes
        position_choice = random.choices(possible_positions, weights=weights, k=1)[0]
        self.current_position = position_choice

        # Update fatigue based on the movement
        distance_moved = self.calculate_distance(self.get_coordinates(self.current_position))
        self.fatigue += distance_moved * 0.1  # Increase fatigue based on distance moved
        print(f"{self.name} moved to {self.current_position} (from {self.position}), fatigue: {self.fatigue}")

    def return_to_natural_position(self):
    #if self.current_position != self.position:
        self.current_position = self.position

    def determine_position_based_on_tactics(self, ball_position, play_state, tactics):
        #print("hit determine_position_based_on_tactics")
        if play_state == 'attacking':
            if tactics['offense_strategy'] == 'direct':
                return self.get_central_positions(tactics['push_factor'])
            elif tactics['offense_strategy'] == 'stay_wide':
                return self.get_wide_positions(tactics['push_factor'])
        elif play_state == 'defensive':
            if tactics['defense_strategy'] == 'man_mark':
                return self.get_man_mark_positions(ball_position)
            elif tactics['defense_strategy'] == 'zone_mark':
                return self.get_zone_mark_positions()

    def choose_position_based_on_stats(self, ball_position, possible_positions):
        best_position = None
        best_score = -float('inf')

        for position in possible_positions:
            distance_to_ball = self.calculate_distance(ball_position)
            anticipation_score = self.ability_stats.mental * 0.5 + self.ability_stats.tactical * 0.5
            ability_to_contest = (1 / (1 + distance_to_ball)) * anticipation_score

            # Consider fatigue
            fatigue_penalty = max(0.5, 1 - (self.fatigue * 0.05))
            final_score = ability_to_contest * fatigue_penalty

            if final_score > best_score:
                best_score = final_score
                best_position = position

        return best_position or random.choice(possible_positions)  # Fallback to random if no best position found

    def get_central_positions(self, push_factor):
        grid_map = self.get_grid_map()
        print(f"Self Position {self.position}")
        x, y = self.get_coordinates(self.position)
        print(f"Self Position {self.position} X {x}, Y {y}")
        #print(f"Grid Map {grid_map}")
        zone_coordinates = grid_map.get(self.position, [])
        print(f"Zone Coordinates for {self.position}: {zone_coordinates}")
        print(f"push_factor {push_factor}")
        push_factor = int(push_factor)
        # Calculate potential positions based on push factor
        possible_positions = []
        # Check the zone for valid positions based on the push factor
        for i in range(1, push_factor + 1, push_factor):
            print(f'Push factor {push_factor}')
            #time.sleep(0.5)
            # Move forward
            forward_position = (x, y + i)
            if forward_position in zone_coordinates:
                possible_positions.append(forward_position)
            
            # Move forward-right
            forward_right_position = (x + i, y + i)
            if forward_right_position in zone_coordinates:
                possible_positions.append(forward_right_position)
            
            # Move forward-left
            forward_left_position = (x - i, y + i)
            if forward_left_position in zone_coordinates:
                possible_positions.append(forward_left_position)

        return possible_positions

    def get_wide_positions(self, push_factor):
        grid_map = self.get_grid_map()
        print(f"Self Position {self.position}")
        x, y = self.get_coordinates(self.position)
        print(f"Self Position {self.position} X {x}, Y {y}")
        #print(f"Grid Map {grid_map}")
        zone_coordinates = grid_map.get(self.position, [])
        print(f"Zone Coordinates for {self.position}: {zone_coordinates}")
        push_factor = int(push_factor)
        possible_positions = []
        # Check the zone for valid positions based on the push factor
        for i in range(1, push_factor + 1, push_factor):
            print(f'Push factor {push_factor}')
            #time.sleep(0.5)
            # Move forward
            forward_position = (x, y + i)
            if forward_position in zone_coordinates:
                possible_positions.append(forward_position)
            
            # Move forward-right
            forward_right_position = (x + i, y + i)
            if forward_right_position in zone_coordinates:
                possible_positions.append(forward_right_position)
            
            # Move forward-left
            forward_left_position = (x - i, y + i)
            if forward_left_position in zone_coordinates:
                possible_positions.append(forward_left_position)

        return possible_positions

    def get_man_mark_positions(self, ball_position):
        # Stay close to the opponent based on ball position
        return [self.mirror_position(ball_position)]

    def get_zone_mark_positions(self):
        # Spread out defensively
        if self.position in ["LHB", "RHB"]:
            return ["CHB", "FB"]
        elif self.position in ["LB", "RB"]:
            return ["LHB", "RHB"]

class PlayerStatsManager:
    def __init__(self):
        self.player_stats = {}

    def initialize_player_stats(self, team1_players, team2_players):
        stats = {}
        for player in team1_players.values():
            stats[player.name] = self.create_empty_stats()
        for player in team2_players.values():
            stats[player.name] = self.create_empty_stats()
        self.player_stats = stats

    def create_empty_stats(self):
        return {
            'hitouts': 0,
            'kicks': 0,
            'handballs': 0,
            'tackles': 0,
            'clearances': 0,
            'marks': 0,
            'goals': 0,
            'behinds': 0,
            'disposals': 0,
            'successful_disposals': 0,  # Track successful disposals
            'disposal_efficiency': 0.0,
            'turnovers_won': 0,
            'interceptions_won': 0,
        }
    
    def format_player_stats(self, player):
        stats = self.player_stats[player.name]
        return {
            'name': player.name,
            'team': player.team.name,
            'hitouts': stats['hitouts'],
            'kicks': stats['kicks'],
            'handballs': stats['handballs'],
            'tackles': stats['tackles'],
            'clearances': stats['clearances'],
            'marks': stats['marks'],
            'goals': stats['goals'],
            'behinds': stats['behinds'],
            'disposals': stats['disposals'],
            'disposal_efficiency': stats['disposal_efficiency'],
            'turnovers_won': stats['turnovers_won'],
            'interceptions_won': stats['interceptions_won'],
        }

    def record_stat(self, player_name, stat_name, value=1):
        if player_name in self.player_stats:
            if stat_name in self.player_stats[player_name]:
                self.player_stats[player_name][stat_name] += value
                if stat_name in ['kicks', 'handballs']:
                    self.player_stats[player_name]['disposals'] += value
                    # Simulate a successful disposal if passing or kicking to a teammate
                    if random.random() > 0.1:  # Assuming 90% success rate for simplicity
                        self.player_stats[player_name]['successful_disposals'] += value
                # Update disposal efficiency whenever disposals are recorded
                self.update_disposal_efficiency(player_name)
            else:
                raise ValueError(f"Stat '{stat_name}' not recognized.")
        else:
            raise ValueError(f"Player '{player_name}' not recognized in stats.")

    def update_disposal_efficiency(self, player_name):
        disposals = self.player_stats[player_name]['disposals']
        successful_disposals = self.player_stats[player_name]['successful_disposals']
        if disposals > 0:
            self.player_stats[player_name]['disposal_efficiency'] = round((successful_disposals / disposals) * 100, 2)
        else:
            self.player_stats[player_name]['disposal_efficiency'] = 0.0

    def collect_player_stats(self, team1_players, team2_players):
        all_stats = []
        for player in team1_players.values():
            all_stats.append(self.format_player_stats(player))
        for player in team2_players.values():
            all_stats.append(self.format_player_stats(player))
        return all_stats