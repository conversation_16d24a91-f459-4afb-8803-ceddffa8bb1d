from django.core.management.base import BaseCommand
from django.utils.timezone import now
from datetime import timedelta
from leagues.models import League
from seasons.models import Season
from seasons.utils import generate_schedule_utils, trigger_promotions_and_relegations_utils
from ladder.models import Ladder
from matches.models import Match

class Command(BaseCommand):
    help = 'Create the first season for all leagues without one.'

    def handle(self, *args, **kwargs):
        leagues = League.objects.all()
        for league in leagues:
            # Check if a season already exists for the league
            latest_season = Season.objects.filter(league=league).order_by('-start_date').first()
            if not latest_season or latest_season.is_completed:
                start_date = now().date()

                # Align the start_date to the next Sunday
                while start_date.weekday() != 6:
                    start_date += timedelta(days=1)

                # Estimated season duration based on the number of teams
                ladder_entries = Ladder.objects.filter(league=league)
                num_teams = len(ladder_entries)
                if num_teams < 2:
                    self.stdout.write(f"Skipping league '{league.name}' due to insufficient teams.")
                    continue

                end_date = start_date + timedelta(weeks=num_teams * 2)  # Adjust duration as needed

                # Create the season
                season = Season.objects.create(
                    league=league,
                    start_date=start_date,
                    end_date=end_date
                )
                self.stdout.write(f"Created season for league '{league.name}' from {start_date} to {end_date}.")

                # Generate the schedule
                schedule = generate_schedule_utils(league, start_date)
                for match_data in schedule:
                    Match.objects.create(
                        league=league,
                        home_team=match_data['home_team'],
                        away_team=match_data['away_team'],
                        date=match_data['date'],
                        season=season
                    )
                self.stdout.write(f"Generated {len(schedule)} matches for league '{league.name}'.")