from django.core.management.base import BaseCommand
from teams.models import Team
from teams.match_engine.match_engine import MatchEngine

class Command(BaseCommand):
    help = 'Simulate a match between two teams'

    def add_arguments(self, parser):
        parser.add_argument('team1_id', type=int, help='The ID of the first team')
        parser.add_argument('team2_id', type=int, help='The ID of the second team')

    def handle(self, *args, **kwargs):
        team1_id = kwargs['team1_id']
        team2_id = kwargs['team2_id']

        try:
            team1 = Team.objects.get(id=team1_id)
            team2 = Team.objects.get(id=team2_id)
        except Team.DoesNotExist:
            self.stdout.write(self.style.ERROR('One or both teams do not exist.'))
            return

        engine = MatchEngine(team1, team2)
        quarter_results, team1_score, team2_score = engine.simulate_match()

        result = (
            f"Quarter results:\n"
            f"Team 1: {', '.join(map(str, quarter_results['team1']))}\n"
            f"Team 2: {', '.join(map(str, quarter_results['team2']))}\n"
            f"Final Score: {team1.name} {team1_score} - {team2.name} {team2_score}"
        )
        self.stdout.write(self.style.SUCCESS(result))
