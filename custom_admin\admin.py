# custom_admin/admin.py

from django.contrib import admin
from django.urls import path
from django.shortcuts import render
from django.http import HttpResponseRedirect
from teams.models import Team, Player
from matches.models import Match
from matchengine.match_engine.match_engine import MatchEngine
from .forms import MatchSimulationForm
#from teams.match_engine.match_engine import MatchEngine
from .views import matches_list_view, force_simulate_match


@admin.register(Team)
class TeamAdmin(admin.ModelAdmin):
    change_list_template = "admin/teams/change_list.html"

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('simulate_match/', self.admin_site.admin_view(self.simulate_match), name='simulate_match'),
        ]
        return custom_urls + urls

    def simulate_match(self, request):
        print("simulate_match called")
        if request.method == 'POST':
            print("POST request received")
            form = MatchSimulationForm(request.POST)
            if form.is_valid():
                print("Form is valid")
                team1 = form.cleaned_data['team1']
                team2 = form.cleaned_data['team2']
                team1_ruck = form.cleaned_data['team1_ruck']
                team2_ruck = form.cleaned_data['team2_ruck']
                            
                # Debugging output
                print(f"Team 1: {team1}")
                print(f"Team 2: {team2}")
                print(f"Team 1 Ruck: {team1_ruck}")
                print(f"Team 2 Ruck: {team2_ruck}")
                
                # Ensure the selected players belong to the correct teams
                if team1_ruck.team != team1 or team2_ruck.team != team2:
                    self.message_user(request, "Selected players do not belong to the chosen teams.", level='error')
                    return HttpResponseRedirect(request.path_info)

                engine = MatchEngine(team1, team2, team1_ruck, team2_ruck)
                team1_score, team2_score, quarter_results = engine.simulate_match()

                self.message_user(request, f"Final Score - {team1.name}: {team1_score}, {team2.name}: {team2_score}")
                self.message_user(request, f"Quarter Results: {quarter_results}")
                for event in event_log:
                    self.message_user(request, event)
                
                return HttpResponseRedirect(request.path_info)
            else:
                print("Form is invalid")
                print(form.errors.as_json())
        else:
            print("GET request received")
            form = MatchSimulationForm()

        teams = Team.objects.all()
        context = dict(
            self.admin_site.each_context(request),
            form=form,
            teams=teams,
        )
        return render(request, "admin/simulate_match.html", context)

@admin.register(Match)
class MatchAdmin(admin.ModelAdmin):
    #print("hit match admin")
    change_list_template = "admin/matches/change_list.html"
    
    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('matches-list/', matches_list_view, name='matches_list'),
            path('matches/simulate/<int:match_id>/', force_simulate_match, name='force_simulate_match'),
        ]
        print(f"custom urls {urls}")
        return custom_urls + urls
          
   
