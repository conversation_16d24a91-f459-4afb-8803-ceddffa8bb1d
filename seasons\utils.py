from datetime import timedelta
from datetime import timedelta
from django.utils.timezone import now
from teams.models import Team
from matches.models import Match  # Or adjust the path
from ladder.models import Ladder  # Or adjust the path
from leagues.models import League

def generate_schedule_utils(league, start_date):
    """
    Generate a round-robin schedule for a league where every team plays
    every other team twice (home and away).
    """
    ladder_entries = Ladder.objects.filter(league=league)
    teams = [entry.team for entry in ladder_entries]
    
    if len(teams) < 2:
        raise ValueError(f"League '{league.name}' must have at least two teams to schedule matches.")

    schedule = []
    num_rounds = len(teams) - 1
    match_date = start_date

    # Ensure scheduling starts on a Sunday
    while match_date.weekday() != 6:
        match_date += timedelta(days=1)

    # Generate the first set of matches (home and away)
    for round_num in range(num_rounds):
        for i, team in enumerate(teams):
            opponent = teams[(i + round_num + 1) % len(teams)]
            if team.id < opponent.id:  # Avoid duplicate match pairs
                # First match: home team is `team`, away team is `opponent`
                schedule.append({
                    'home_team': team,
                    'away_team': opponent,
                    'date': match_date
                })
        match_date += timedelta(weeks=1)

    # Mirror the schedule for the second set of matches
    for match in schedule[:]:  # Copy the first set of matches
        schedule.append({
            'home_team': match['away_team'],  # Swap home and away
            'away_team': match['home_team'],
            'date': match['date'] + timedelta(weeks=num_rounds)  # Offset by number of rounds
        })

    return schedule

def trigger_promotions_and_relegations_utils(league, standings, num_promotions=2, num_relegations=2):
    """
    Handle promotion and relegation based on standings, league hierarchy, and rules.
    """
    higher_league = league.get_higher_league()
    lower_league = league.get_lower_league()

    promotions = []
    relegations = []

    # Handle promotions
    if higher_league:
        promotions = standings[:num_promotions]
        for team in promotions:
            team.league = higher_league
            team.save()

    # Handle relegations
    if lower_league:
        relegations = standings[-num_relegations:]
        for team in relegations:
            team.league = lower_league
            team.save()

    return promotions, relegations
    
    def save_ladder(self):
        """
        Save the final ladder standings for the season.
        """
        
        """
        standings = self.league.get_standings()
        if not standings:
            raise ValueError(f"Unable to retrieve standings for league '{self.league.name}'.")

        for rank, team in enumerate(standings, start=1):
            Ladder.objects.create(
                season=self,
                league=self.league,
                team=team,
                rank=rank,
                points=team.get_points(),
                wins=team.get_wins(),
                losses=team.get_losses()
            )
        """    

    def create_next_season(self, delay_weeks=3):
        """
        Create the next season after a delay (default: 3 weeks).
        """
        next_start_date = self.end_date + timedelta(weeks=delay_weeks)
        next_end_date = next_start_date + timedelta(weeks=len(self.league.teams.all()) * 2)  # Estimated duration
        
        next_season = Season.objects.create(
            start_date=next_start_date,
            end_date=next_end_date,
            league=self.league
        )
        next_season.generate_schedule()
        return next_season

    @staticmethod
    def generate_missing_seasons():
        """
        Generate seasons for leagues without a current season.
        """
        leagues = League.objects.all()
        for league in leagues:
            latest_season = league.season_set.order_by('-start_date').first()
            if not latest_season or latest_season.is_completed:
                # Create a new season starting this Sunday
                start_date = now().date()
                while start_date.weekday() != 6:  # Find next Sunday
                    start_date += timedelta(days=1)
                end_date = start_date + timedelta(weeks=len(league.teams.all()) * 2)  # Estimated duration
                new_season = Season.objects.create(
                    start_date=start_date,
                    end_date=end_date,
                    league=league
                )
                new_season.generate_schedule()

    def __str__(self):
        return f"{self.league.name} Season {self.start_date.year}-{self.end_date.year}"
