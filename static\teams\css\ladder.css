/* Ladder Container */
.ladder-container {
    background-color: #f7f7f7;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    width: 800px; /* Increase width for better layout */
    margin: 20px;
    padding: 15px;
    transition: transform 0.2s;
    flex-direction: row;
}

.ladder-container:hover {
    transform: scale(1.02);
}

/* Ladder Header */
.ladder-header {
    text-align: center;
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 20px;
    color: #003f87;
}

.ladder-table tr.auto-promotion td {
    background-color: #80aad9 !important;
    font-weight: bold;
}

.ladder-table tr.playoff td {
    background-color: #a8cdf7 !important;
    /*font-weight: bold;*/
}

.ladder-table tr.midtable td {
    background-color: #ffffff !important;
    /*font-weight: bold;*/
}

.ladder-table tr.relegation-playoff td {
    background-color: #d8dde3 !important;
    /*font-weight: bold;*/
}

.ladder-table tr.relegation td {
    background-color: #b6babf !important;
    font-weight: bold;
}

.ladder-table {
    width: 100%;
    margin: 0 auto;
    
}
/* Table Header */
.ladder-table th {
    background-color: #003f87;
    color: #ffffff;
    padding: 10px;
	align: center;
    text-align: center;
    border-radius: 5px;
}

/* Table Row Styling */
.ladder-table td {
    background-color: #ffffff;
    padding: 10px;
    text-align: center;
    border-bottom: 1px solid #dddddd;
}

/* Table Alternating Rows */
.ladder-table tr:nth-child(even) td {
    background-color: #f1f1f1;
}

.ladder-table tr:hover td {
    background-color: #e0f2ff;
}

/* Position Column */
.ladder-position {
    font-weight: bold;
    color: #003f87;
}

/* Team Name */
.ladder-team {
    font-weight: bold;
    color: #003f87;
}

/* Points Column */
.ladder-points {
    font-weight: bold;
    color: #003f87;
}

/* Footer */
.footer {
    text-align: center;
    padding: 15px;
    background-color: #003f87;
    color: #ffffff;
    border-radius: 0 0 10px 10px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .ladder-container {
        width: 95%;
        padding: 15px;
    }

    .ladder-table th, .ladder-table td {
        padding: 8px;
        font-size: 14px;
    }
}

.hidden {
    display: none;
}

.toggle-buttons {
    margin-bottom: 10px;
}

.toggle-buttons button {
    padding: 8px 12px;
    background-color: #003f87;
    color: #ffffff;
    border: none;
    border-radius: 5px;
    margin-right: 5px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.toggle-buttons button:hover {
    background-color: #3891f5;
}
