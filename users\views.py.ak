from django.shortcuts import render, redirect
from django.contrib.auth import login
from .forms import CustomUserCreationForm
from teams.models import Team

def register(request):
    if request.method == 'POST':
        form = CustomUserCreationForm(request.POST)
        if form.is_valid():
            user = form.save()
            team_name = form.cleaned_data.get('team_name')
            team = Team.objects.create(name=team_name, user=user)
            login(request, user)  # Log in the user
            return redirect('team', team_id=team.id)
    else:
        form = CustomUserCreationForm()
    return render(request, 'registration/register.html', {'form': form})

def home(request):
    from django.contrib.auth.forms import AuthenticationForm
    form = AuthenticationForm()
    if request.method == 'POST':
        form = AuthenticationForm(data=request.POST)
        if form.is_valid():
            user = form.get_user()
            login(request, user)
            return redirect('team', team_id=user.team.id)
    return render(request, 'home.html', {'form': form})

