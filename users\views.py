from django.shortcuts import render, redirect
from django.contrib.auth import login, logout as auth_logout
from django.contrib.auth.forms import AuthenticationForm
from .forms import CustomUserCreationForm
from teams.models import Team#, Player, PlayerStatsPhysical, PlayerStatsAbility, PlayerStatsAttributes
import random#, logging
from django.db import transaction
from django.contrib.auth.decorators import login_required
#from faker import Faker

#logging.basicConfig(level=logging.INFO)

def home_view(request):
    form = AuthenticationForm()
    return render(request, 'home.html', {'form': form})

#fake = Faker()

def register(request):
    if request.method == 'POST':
        form = CustomUserCreationForm(request.POST)
        if form.is_valid():
            user = form.save()
            team_name = form.cleaned_data.get('team_name')

            # Check if the user already has a team
            team, created = Team.objects.get_or_create(user=user, defaults={'name': team_name})

        login(request, user)
        return redirect('dashboard')
            
   
    else:
        form = CustomUserCreationForm()
    return render(request, 'registration/register.html', {'form': form})

def login_view(request):
    if request.method == 'POST':
        form = AuthenticationForm(request, data=request.POST)
        if form.is_valid():
            user = form.get_user()
            login(request, user)
            return redirect('dashboard')
    else:
        form = AuthenticationForm()
    return render(request, 'home.html', {'form': form})
    
def logout_view(request):
    auth_logout(request)
    return redirect('home')
    
