import sys
import random
sys.setrecursionlimit(10000)

class MatchEngine:
    def __init__(self, team1, team2, team1_players, team2_players):
        self.team1 = team1
        self.team2 = team2
        self.team1_players = team1_players
        self.team2_players = team2_players
        self.team1_score = 0
        self.team2_score = 0
        self.ball_position = (3, 2)  # Center of the ground (Centre position)
        self.quarter = 0
        self.events = []
        self.contest_count = 0
        self.mirrored_position = None
        self.out_of_bounds_count = 0
        self.out_of_bounds_count_possession = 0
        print("MatchEngine initialized")

    def simulate_match(self):
        print("Simulating match")
        for quarter in range(0, 4):
            print(f"Simulating quarter {quarter}")
            self.contest_count = 0
            self.out_of_bounds_count = 0
            self.out_of_bounds_count_possession = 0
            self.quarter += 1
            self.ball_position = (1, 2)  # Center of the ground (Centre position)
            self.simulate_quarter()
            self.events.append({
                'quarter': quarter,
                'events': [event for event in self.events if event['quarter'] == quarter]
            })
        final_result = f'{self.team1.name}: {self.team1_score}, {self.team2.name}: {self.team2_score}'
        print(f"Final result: {final_result}")
        return {
            'quarter_results': self.events,
            'final_result': final_result
        }

    def simulate_quarter(self):
        print(f"Simulating quarter {self.quarter}")
        self.simulate_contest()
        print(f"Quarter {self.quarter} events: {len(self.events)}")
        
    def get_grid_map(self):
        return [
            ["LB", "FB", "RB"],
            ["LHB", "CHB", "RHB"],
            ["LWing", "Centre", "RWing"],
            ["LHF", "CHF", "RHF"],
            ["LF", "FF", "RF"]
        ]

    def get_position_map(self):
        return {
            "LB": ["FB", "RB", "LHB", "CHB", "RHB"], 
            "RB": ["FB", "LB", "LHB", "CHB", "RHB"],
            "LHB": ["LWing", "CHB", "RHB", "Center", "CHF", "LHF", "RHF"],
            "RHB": ["RWing", "CHB", "LHB", "Center", "CHF", "LHF", "RHF"], 
            "LWing": ["LHF", "Centre", "RWing", "CHF"],
            "RWing": ["RHF", "Centre", "LWing", "CHF"],
            "LHF": ["LF", "CHF", "RF", "FF"],
            "RHF": ["RF", "CHF", "LF", "FF"],
            "LF": ["FF", "RF"],
            "RF": ["FF", "LF"]
        }    
   
    def simulate_contest(self):
        print("Simulating contest")
        print(f"Contest Count {self.contest_count}")

        print(f"Contest Count {self.contest_count}")
        result = self.simulate_center_bounce()
        if result == "ball_up":
            print("Ball up, re-running contest")
            return  # Re-run contest if ball-up

    def simulate_center_bounce(self):
        print("Simulating center bounce")
        ruck1 = self.team1_players["Ruck"]
        ruck2 = self.team2_players["Ruck"]
        winner = self.ruck_contest(ruck1, ruck2)
        if winner == self.team1:
            print(f"{self.team1.name} wins the center bounce")
            return self.clearance(self.team1, self.team2, self.team1_players, self.team2_players, self.team1)
        else:
            print(f"{self.team2.name} wins the center bounce")
            return self.clearance(self.team1, self.team2, self.team1_players, self.team2_players, self.team2)

    def ruck_contest(self, ruck1, ruck2):
        print(f"Ruck contest between {ruck1.name} and {ruck2.name}")
        ruck1_score = self.calculate_ruck_performance(ruck1)
        ruck2_score = self.calculate_ruck_performance(ruck2)
        print(f"{ruck1.name} score: {ruck1_score}, {ruck2.name} score: {ruck2_score}")
        return self.team1 if ruck1_score > ruck2_score else self.team2

    def calculate_ruck_performance(self, ruck):
        height_weight = 1.5
        strength_weight = 1.4
        agility_weight = 1.3
        mental_weight = 1.2
        tactical_weight = 1.1
        consistency_weight = 1.0
        age_weight = 0.9
        stamina_weight = 0.8

        performance = (
            (ruck.physical_stats.height * height_weight) +
            (ruck.physical_stats.strength * strength_weight) +
            (ruck.physical_stats.agility * agility_weight) +
            (ruck.ability_stats.mental * mental_weight) +
            (ruck.ability_stats.tactical * tactical_weight) +
            (ruck.ability_stats.consistency * consistency_weight) +
            (ruck.physical_stats.age * age_weight) +
            (ruck.physical_stats.stamina * stamina_weight)
        )

        # Add a random element to simulate the unpredictability of sports
        random_factor = random.uniform(0.8, 1.2)  # Random factor between 0.8 and 1.2
        performance *= random_factor

        # Print the calculated performance for debugging
        print(f"Calculated performance for {ruck.name}: {performance:.2f}")

        return performance

    def clearance(self, team1, team2, team1_players, team2_players, ruck_winner):
        print("Simulating clearance")
        midfielders_team1 = [team1_players["Rover"], team1_players["RuckRover"], team1_players["Centre"]]
        midfielders_team2 = [team2_players["Rover"], team2_players["RuckRover"], team2_players["Centre"]]

        team1_score = sum(self.calculate_midfielder_performance(player) for player in midfielders_team1)
        team2_score = sum(self.calculate_midfielder_performance(player) for player in midfielders_team2)

        # Add extra weight to the team whose ruck won the center bounce
        if ruck_winner == team1:
            team1_score *= 1.1
        else:
            team2_score *= 1.1

        print(f"Team 1 clearance score: {team1_score:.2f}, Team 2 clearance score: {team2_score:.2f}")

        if team1_score > team2_score:
            winner_team = team1
            winner_players = midfielders_team1
        else:
            winner_team = team2
            winner_players = midfielders_team2

        # Determine the player who gets the ball
        player_with_ball = random.choices(
            winner_players,
            weights=[self.calculate_midfielder_performance(player) for player in winner_players],
            k=1
        )[0]

        print(f"{player_with_ball.name} from {winner_team.name} gets the ball after clearance")
        return self.handle_possession(winner_team, self.team2_players)

    def calculate_midfielder_performance(self, player):
        tactical_weight = 1.5
        mental_weight = 1.4
        strength_weight = 1.3
        versatility_weight = 1.2
        consistency_weight = 1.1
        age_weight = 1.0
        stamina_weight = 0.9

        performance = (
            (player.ability_stats.tactical * tactical_weight) +
            (player.ability_stats.mental * mental_weight) +
            (player.physical_stats.strength * strength_weight) +
            (player.ability_stats.versatility * versatility_weight) +
            (player.ability_stats.consistency * consistency_weight) +
            (player.physical_stats.age * age_weight) +
            (player.physical_stats.stamina * stamina_weight)
        )

        # Add a random element to simulate the unpredictability of sports
        random_factor = random.uniform(0.8, 1.2)  # Random factor between 0.8 and 1.2
        performance *= random_factor

        # Print the calculated performance for debugging
        print(f"Calculated performance for {player.name}: {performance:.2f}")

        return performance
    
    def possession_contest(self, receiver, opponent):
        print("Simulating possession contest")

        receiver_weights = {
            'marking': 1.5,
            'strength': 1.4,
            'consistency': 1.3,
            'agility': 1.2,
            'age': 1.1,
            'stamina': 1.0
        }

        opponent_weights = {
            'tackling': 1.5,
            'strength': 1.4,
            'consistency': 1.3,
            'speed': 1.2,
            'age': 1.1,
            'stamina': 1.0
        }

        receiver_performance = self.calculate_performance(receiver, receiver_weights)
        opponent_performance = self.calculate_performance(opponent, opponent_weights)

        print(f"{receiver.name} performance: {receiver_performance:.2f}")
        print(f"{opponent.name} performance: {opponent_performance:.2f}")

        return receiver_performance, opponent_performance

    def calculate_performance(self, player, weights):
        performance = 0
        for attribute, weight in weights.items():
            performance += getattr(player.ability_stats, attribute, 0) * weight
            performance += getattr(player.physical_stats, attribute, 0) * weight

        random_factor = random.uniform(0.8, 1.2)  # Random factor between 0.8 and 1.2
        performance *= random_factor

        print(f"Calculated performance for {player.name}: {performance:.2f}")
        return performance
    
    def calculate_goal_kicking_performance(self, player):
        goal_kicking_weight = 1.5
        mental_weight = 1.4
        strength_weight = 1.3
        consistency_weight = 1.2
        age_weight = 1.1
        stamina_weight = 1.0

        performance = (
            (player.ability_stats.goal_kicking * goal_kicking_weight) +
            (player.ability_stats.mental * mental_weight) +
            (player.physical_stats.strength * strength_weight) +
            (player.ability_stats.consistency * consistency_weight) +
            (player.physical_stats.age * age_weight) +
            (player.physical_stats.stamina * stamina_weight)
        )

        # Add a small random factor to simulate unpredictability
        random_factor = random.uniform(0.9, 1.1)
        performance *= random_factor

        print(f"Calculated goal kicking performance for {player.name}: {performance:.2f}")

        return performance

    def handle_possession(self, team, team_players):
        while self.contest_count < 55:
            self.contest_count += 1
            print(f"{team.name} handling possession")
            #self.move_ball()  # Move the ball to a new position
            #self.advance_ball(team, team_players, self.ball_position)
            position = self.get_ball_position()
            print(f"Ball position: {self.ball_position} ({position})")
            print(f"Checking position: {position}")
            print(f"Team players: {team_players.keys() if isinstance(team_players, dict) else 'Not a dict'}")

            player = team_players.get(position)
            if not player:
                print("No player found for position, ball up")
                return "ball_up"
            if position in ["LF", "RF", "FF", "CHF", "RHF", "LHF"]:
                result = self.attempt_goal(player, position)
                if result == "goal":
                    return self.simulate_center_bounce()
                if result in ["kick_in", "behind"]:
                    opponent = self.get_opponent(team)
                    opponent_players = self.get_opponent_players(team)
                    return self.handle_possession(opponent, opponent_players)
                elif result == "out_on_full":
                    return self.throw_in(self.ball_position)
                elif result == "pass":
                    return self.handle_possession(team, team_players)
            else:
                is_near_boundary, _ = self.is_position_near_boundary(self.ball_position)
                if is_near_boundary:
                    print("Near boundary")
                    self.out_of_bounds_count_possession += 1
                    print(f"Out of Bounds count Possession: {self.out_of_bounds_count_possession}")
                    if self.out_of_bounds_count_possession < 2:
                        print("Ball position out of bounds, retrying")
                        self.advance_ball(team, team_players, position)
                        return self.handle_possession(team, team_players)  # Retry possession
                    else:
                        print("Ball position out of bounds, performing throw-in")
                        self.out_of_bounds_count_possession = 0
                        self.throw_in(self.ball_position)
                        self.simulate_center_bounce()
            return self.advance_ball(team, team_players, position)

    def move_ball(self):
        print(f"Current ball position before moving: {self.ball_position}")
        new_x = self.ball_position[0] + random.choice([-1, 0, 1])
        new_y = self.ball_position[1] + random.choice([-1, 0, 1])

        # Introduce a probability check for throw-ins
        if random.random() < 0.2:  # 20% chance for a throw-in
            if new_x < 0 or new_x > 2 or new_y < 0 or new_y > 4:
                print(f"Ball out of bounds at position: ({new_x}, {new_y})")
                self.throw_in(self.ball_position)
                return

        # Ensure the ball stays within the grid
        new_x = max(0, min(new_x, 2))
        new_y = max(0, min(new_y, 4))

        self.ball_position = (new_x, new_y)
        print(f"New ball position after moving: {self.ball_position}")
        return

    def get_ball_position(self):
        grid_map = self.get_grid_map()
        x, y = self.ball_position
        print(f"Get Ball Position {x} {y} and len grid_map {len(grid_map)}")
        if -1 < y < len(grid_map) and -1 < x < len(grid_map[y]):
                #print(f"Returning X Y {x} {y}")
                return grid_map[y][x]
        self.out_of_bounds_count += 1
        print(f"Out of Bounds count Ball Position: {self.out_of_bounds_count}")
        if self.out_of_bounds_count < 3:
            print("Ball position out of bounds, retrying")
            self.move_ball()
            return self.get_ball_position()  # Ensure we return the new position
        else:
            print("Ball position out of bounds, performing throw-in")
            self.out_of_bounds_count = 0
        return self.throw_in(self.ball_position)

    def advance_ball(self, team, team_players, current_position, known_next_position=None):
        print(f"Advancing ball from {current_position}")

        if known_next_position:
            print(f"Known Next Position {known_next_position}")
            next_position = known_next_position
            known_next_position=None
        else:
            next_position = self.get_next_position(current_position)
            print(f"Next Position not Known {next_position}")

        #next_position = self.get_next_position(current_position)
        receiver = team_players.get(next_position)

        if not receiver:
            print(f"No receiver found for position {next_position}, ball up")
            return "ball_up"

        opponent_team = self.get_opponent(team)
        opponent_players = self.get_opponent_players(team)
        opponent = opponent_players.get(next_position)

        if not opponent:
            print(f"No opponent found for position {next_position}, ball up")
            return "ball_up"

        receiver_performance, opponent_performance = self.possession_contest(receiver, opponent)

        # Determine the outcome of the contest
        if receiver_performance > opponent_performance:
            # Calculate the probability of dropping the mark based on the receiver's attributes
            drop_mark_probability = (
                    receiver.ability_stats.marking * 1.5 +
                    receiver.ability_stats.consistency * 0.7 +
                    receiver.ability_stats.mental * 0.6
                ) / 100 + random.uniform(0.01, 0.02)
            print(f"Drop Mark Prob: {drop_mark_probability}")
            if random.random() < drop_mark_probability:  # Receiver might drop the mark
                turnover_probability = (
                    opponent.ability_stats.tackling * 1.5 +
                    opponent.ability_stats.mental * 0.3 +
                    opponent.ability_stats.tactical * 0.3
                ) / 100 + random.uniform(0.01, 0.5)
                print(f"Turnover Prob: {turnover_probability}")
                if random.random() < turnover_probability:  # Opponent might cause a turnover
                    print(f"{opponent.name} tackles {receiver.name}, causing a turnover")
                    self.ball_position = self.update_ball_position(current_position, next_position)
                    self.mirrored_position = self.turnover(self.ball_position)
                    self.ball_position = self.mirrored_position
                    return self.handle_possession(opponent_team, opponent_players)
                else:
                    print(f"{receiver.name} drops the mark but holds possession")
                    return self.handle_possession(team, team_players)
            else:
                print(f"{receiver.name} successfully marks the ball")
                self.ball_position = self.update_ball_position(current_position, next_position)
                return self.handle_possession(team, team_players)
        else:
            interception_probability = (
                opponent.ability_stats.marking * 0.8 +
                opponent.ability_stats.mental * 0.3 +
                opponent.ability_stats.tactical * 0.3
            ) / 100 + random.uniform(0.01, 1.0)
            print(f"intercept Prob: {interception_probability}")
            if random.random() < interception_probability:  # Opponent might intercept the mark
                print(f"{opponent.name} intercepts the mark, causing a turnover")
                self.ball_position = self.update_ball_position(current_position, next_position)
                self.mirrored_position = self.turnover(self.ball_position)
                self.ball_position = self.mirrored_position
                return self.handle_possession(opponent_team, opponent_players)
            else:
                print(f"{opponent.name} fails to intercept, {receiver.name} holds possession")
                self.ball_position = self.update_ball_position(current_position, next_position)
                return self.handle_possession(team, team_players)

    def attempt_goal(self, player, position):
        print(f"{player.name} attempting goal from {position}")

        # Calculate the goal kicking performance
        performance = self.calculate_goal_kicking_performance(player)

        # Adjust difficulty based on position
        if position in ["RHF", "LHF"]:
            performance *= 0.50  # Decrease performance for harder angles

        # Adjust difficulty based on position
        if position in ["CHF"]:
            performance *= 0.70  # Decrease performance for harder angles

        # Decide whether to attempt the goal or pass
        if position in ["CHF"] and random.random() < 0.5:
            # Consider passing the ball instead of attempting a difficult goal
            return self.consider_pass(player, position)
        
        # Decide whether to attempt the goal or pass
        if position in ["RHF", "LHF"] and random.random() < 0.7:
            # Consider passing the ball instead of attempting a difficult goal
            return self.consider_pass(player, position)
        
        if position in ["LF", "RF"] and random.random() < 0.3:
            # Consider passing the ball instead of attempting a goal from LF/RF
            if player.ability_stats.goal_kicking < 0.7 * performance:
                return self.consider_pass(player, position)
        
        # Goal attempt
        if performance > random.uniform(50, 120):
            print(f"{player.name} scores a goal!")
            self.team1_score += 6 if player.team == self.team1 else 0
            self.team2_score += 6 if player.team == self.team2 else 0
            self.ball_position = (1, 2)
            return "goal"
        else:
            # Consider random events like skewing the kick
            if random.random() < 0.1:  # 10% chance of kicking out on the full
                print(f"{player.name}'s kick skewed off the foot, out on the full")
                return "out_on_full"
            else:
                print(f"{player.name} scores a behind")
                self.team1_score += 1 if player.team == self.team1 else 0
                self.team2_score += 1 if player.team == self.team2 else 0
                self.ball_position = (1, 0)
                return "behind"


    def consider_pass(self, player, position):
        print(f"{player.name} considering a pass from {position}")

        # Potential positions to pass to based on current position
        possible_positions = []
        if position == "CHF":
            possible_positions = ["LHF", "RHF", "LF", "RF", "FF"]
        elif position == "RHF":
            possible_positions = ["RWing", "CHF", "FF"]
        elif position == "LHF":
            possible_positions = ["LWing", "CHF", "FF"]
        elif position == "LF":
            possible_positions = ["LHF", "CHF", "FF"]
        elif position == "RF":
            possible_positions = ["RHF", "CHF", "FF"]

        # Choose a position based on performance and tactical decision
        next_position = random.choices(
            possible_positions,
            weights=[1.2 if pos == "FF" else 1.0 for pos in possible_positions],
            k=1
        )[0]

        print(f"{player.name} passes to {next_position}")
        return self.advance_ball(player.team, self.get_opponent_players(player.team), position, next_position)


    def get_next_position(self, current_position):
        position_map = {
            "LB": ["FB", "RB", "LHB", "CHB", "RHB"],  # Left Back Pocket can pass to Left Half Back or Center Half Back
            "FB": ["LB", "RB", "LHB", "CHB", "RHB"],  # Full Back can pass to Center Half Back or Right Half Back
            "RB": ["FB", "LB", "LHB", "CHB", "RHB"],  # Right Back Pocket can pass to Right Half Back or Center Half Back
            "LHB": ["LWing", "CHB", "RHB", "Center", "CHF", "LHF", "RHF"],  # Left Half Back can pass to Left Wing or Center Half Back
            "CHB": ["LWing", "Centre", "RWing", "LHB", "RHB", "CHF", "LHF", "RHF"],  # Center Half Back can pass to Left Wing, Centre, or Right Wing
            "RHB": ["RWing", "CHB", "LHB", "Center", "CHF", "LHF", "RHF"],  # Right Half Back can pass to Right Wing or Center Half Back
            "LWing": ["LHF", "Centre", "RWing", "CHF"],  # Left Wing can pass to Left Half Forward or Centre
            "Centre": ["CHF", "LHF", "RHF", "LWing", "RWing"],  # Centre can pass to Center Half Forward, Left Wing, or Right Wing
            "RWing": ["RHF", "Centre", "LWing", "CHF"],  # Right Wing can pass to Right Half Forward or Centre
            "LHF": ["LF", "CHF", "RF", "FF"],  # Left Half Forward can pass to Left Forward or Center Half Forward
            "CHF": ["LF", "FF", "RF", "LFH", "RHF"],  # Center Half Forward can pass to Left Forward, Full Forward, or Right Forward
            "RHF": ["RF", "CHF", "LF", "FF"],  # Right Half Forward can pass to Right Forward or Center Half Forward
            "LF": ["FF", "RF"],  # Left Forward can pass to Full Forward or attempt to score from Left Forward
            "FF": ["FF"],  # Full Forward stays Full Forward for scoring attempt
            "RF": ["FF", "LF"]  # Right Forward can pass to Full Forward or attempt to score from Right Forward
        }
        next_position_list = position_map.get(current_position, ["FF", "LF", "RF"])
        next_position = random.choice(next_position_list)
        print(f"Next position for {current_position}: {next_position}")
        return next_position

    def pass_ball(self, receiver):
        success = random.choice([True, False])
        print(f"Passing ball to {receiver.name}, success: {success}")
        return success

    def update_ball_position(self, current_position, next_position):
        grid_map = self.get_grid_map()
        for y, row in enumerate(grid_map):
            for x, pos in enumerate(row):
                if pos == next_position:
                    print(f"Updated ball position to: ({x}, {y}) {next_position}")
                    return (x, y)
        print("Failed to update ball position, defaulting to Centre")
        return (3, 2)  # Default to center if not found

    #def attempt_score(self, team, player):
        success = random.choice([True, False])
        print(f"Quarter {self.quarter} events: {len(self.events)}")
        if success:
            self.team1_score += 6 if team == self.team1 else 0
            self.team2_score += 6 if team == self.team2 else 0
            event = f'{player.name} scored a goal!'
            print(event)
            self.events.append({'quarter': self.quarter, 'event': event})
            self.ball_position = (3, 2)  # Reset ball to center for a center bounce
            print(f"Contest Count {self.contest_count}")
            return "center_bounce"
        else:
            self.team1_score += 1 if team == self.team1 else 0
            self.team2_score += 1 if team == self.team2 else 0
            event = f'{player.name} scored a point!'
            print(event)
            self.events.append({'quarter': self.quarter, 'event': event})
            self.ball_position = (1, 0) # Set ball to the opponent's full back position for a kick-in
            print(f"Point scored, ball set to {self.ball_position}")
            print(f"Contest Count {self.contest_count}")
            return "kick_in"

    def get_opponent(self, team):
        opponent = self.team1 if team == self.team2 else self.team2
        print(f"Opponent team: {opponent.name}")
        return opponent

    def get_opponent_players(self, team):
        opponent_players = self.team1_players if team == self.team2 else self.team2_players
        print(f"Opponent players: {opponent_players}")
        return opponent_players

    def throw_in(self, last_position):
        grid_map = self.get_grid_map()
        position_map = self.get_position_map()
        x, y = last_position
        is_near_boundary, last_position_name = self.is_position_near_boundary(last_position)

        if is_near_boundary and last_position_name in position_map:
            next_position_list = position_map[last_position_name]
            next_position = random.choice(next_position_list)
            print(f"Performing throw-in near {last_position_name}")
            self.ball_position = last_position  # Keep the same ball position after throw-in
        else:
            print(f"Throw-in not possible from {last_position_name}, finding closest valid boundary position")

            # Find the closest valid boundary position
            min_distance = float('inf')
            closest_valid_position_name = None
            for pos_name, valid_positions in position_map.items():
                for row_index, row in enumerate(grid_map):
                    if pos_name in row:
                        pos_x = row.index(pos_name)
                        pos_y = row_index
                        distance = abs(pos_x - x) + abs(pos_y - y)
                        if distance < min_distance:
                            min_distance = distance
                            closest_valid_position_name = pos_name

            print(f"Closest valid boundary position: {closest_valid_position_name}")

            # Find coordinates of closest valid boundary position name
            for row_index, row in enumerate(grid_map):
                if closest_valid_position_name in row:
                    pos_x = row.index(closest_valid_position_name)
                    pos_y = row_index
                    self.ball_position = (pos_x, pos_y)
                    break
        print("returning ball up from throw in")            
        return

    def is_position_near_boundary(self, position):
        print("Boundry check")
        boundary_positions = ["LB", "RB", "LHB", "RHB", "LWing", "RWing", "LHF", "RHF", "LF", "RF"]
        x, y = position
        grid_map = self.get_grid_map()
        if -1 < y < len(grid_map) and -1 < x < len(grid_map[y]):
            position_name = grid_map[y][x]
            return (position_name in boundary_positions, position_name)
        return (False, None)
    
    def turnover(self, position):
        x, y = position
        grid_width = 2  # Max x index for the grid
        grid_height = 4  # Max y index for the grid

        # Mirror horizontally (x-axis)
        mirrored_x = grid_width - x

        # Mirror vertically (y-axis)
        mirrored_y = grid_height - y

        print(f"Mirrored ball position from {position} to ({mirrored_x}, {mirrored_y})")
        return (mirrored_x, mirrored_y)
