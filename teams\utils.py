#import random
#from django.apps import apps
from django.conf import settings
from teams.models import Team, Player

def create_random_ability_stats():
    PlayerStatsAbility = apps.get_model('teams', 'PlayerStatsAbility')
    return PlayerStatsAbility.objects.create(
        kicking=random.randint(1, 21),
        goal_kicking=random.randint(1, 21),
        handball=random.randint(1, 21),
        tackling=random.randint(1, 21),
        mental=random.randint(1, 21),
        tactical=random.randint(1, 21),
        versatility=random.randint(1, 21),
        consistency=random.randint(1, 21)
    ).id

def transfer_team_to_user(bot_user, new_user):
    """
    Transfers team ownership from a bot to a new user.
    Disables the bot user and replaces the bot players with new players.
    """
    try:
        # Find the bot's team
        team = Team.objects.get(user=bot_user)

        # Transfer ownership of the team to the new user
        team.user = new_user
        team.save()

        # Set the original bot user as inactive
        bot_user.is_active = False
        bot_user.save()

        # Delete existing bot players on the team
        Player.objects.filter(team=team).delete()

        # Generate new players for the team (use your existing player creation logic here)
        #create_players_for_team(team)
        return team

    except Team.DoesNotExist:
        print("Bot team not found. Creating a new team for the user.")
        # If no bot team exists, fall back to creating a new team
       
        team = Team.objects.create(user=new_user, name=f"{instance.team_name}")
        #create_players_for_team(team)

#def create_players_for_team(team):
    """
    Creates a fresh roster of players for the team.
    Customize this function based on your player creation logic.
    """
    # Example logic to create 18 players for the team, then 12 more.
#    for _ in range(18):  # For main players based on position
#        Player.objects.create(team=team, position="main")
#    for _ in range(12):  # For additional random players
#        Player.objects.create(team=team, position="reserve")
