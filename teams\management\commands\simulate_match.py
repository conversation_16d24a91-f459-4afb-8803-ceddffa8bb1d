from django.core.management.base import BaseCommand, CommandError
from teams.models import Team, Player
from teams.match_engine.match_engine import MatchEngine

class Command(BaseCommand):
    help = 'Simulate a match between two teams'

    def add_arguments(self, parser):
        parser.add_argument('team1_id', type=int, help='ID of the first team')
        parser.add_argument('team2_id', type=int, help='ID of the second team')
        parser.add_argument('--ruck1', type=int, help='ID of the ruck player for team 1')
        parser.add_argument('--ruck2', type=int, help='ID of the ruck player for team 2')

    def handle(self, *args, **options):
        team1_id = options['team1_id']
        team2_id = options['team2_id']
        ruck1_id = options.get('ruck1')
        ruck2_id = options.get('ruck2')

        try:
            team1 = Team.objects.get(id=team1_id)
        except Team.DoesNotExist:
            raise CommandError(f"Team with ID {team1_id} does not exist")

        try:
            team2 = Team.objects.get(id=team2_id)
        except Team.DoesNotExist:
            raise CommandError(f"Team with ID {team2_id} does not exist")

        ruck1 = Player.objects.get(id=ruck1_id) if ruck1_id else None
        ruck2 = Player.objects.get(id=ruck2_id) if ruck2_id else None

        engine = MatchEngine(team1, team2, ruck1, ruck2)
        team1_score, team2_score, quarter_results = engine.simulate_match()
        self.stdout.write(self.style.SUCCESS(f"Final Score - Team 1: {team1_score}, Team 2: {team2_score}"))
        self.stdout.write(self.style.SUCCESS(f"Quarter Results: {quarter_results}"))

