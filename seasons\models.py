from django.db import models
from datetime import timedel<PERSON>, date
from django.utils.timezone import now
from leagues.models import League
from matches.models import Match
from seasons.utils import generate_schedule_utils, trigger_promotions_and_relegations_utils


class Season(models.Model):
    start_date = models.DateField()
    end_date = models.DateField()
    league = models.ForeignKey('leagues.League', on_delete=models.CASCADE)
    is_completed = models.BooleanField(default=False)

    def generate_schedule(self):
        schedule_data = generate_schedule_utils(self.league, self.start_date)
        for match_info in schedule_data:
            Match.objects.create(
                league=self.league,
                season=self,
                home_team=match_info['home_team'],
                away_team=match_info['away_team'],
                date=match_info['date'],
                played=False
            )

    def trigger_promotions_and_relegations(self):
        standings = self.league.get_standings()
        trigger_promotions_and_relegations_utils(
            self.league,
            standings,
            num_promotions=2,  # Adjustable
            num_relegations=2  # Adjustable
        )
        
    def archive_season(self):
        """
        Archive the season, including saving the final ladder.
        """
        self.save_ladder()
        self.is_completed = True
        self.save()

