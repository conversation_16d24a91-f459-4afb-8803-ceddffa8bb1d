import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import math
import os
import json

class AFLFieldPlotter:
    def __init__(self, root):
        self.root = root
        self.root.title("AFL Field Position Plotter")
        self.root.geometry("1200x800")
        
        # Field dimensions (in meters)
        self.field_length = 160
        self.field_width = 130
        
        # Scaling factors for display
        self.canvas_width = 800
        self.canvas_height = 650
        self.scale_x = self.canvas_width / self.field_length
        self.scale_y = self.canvas_height / self.field_width
        
        # Player data
        self.players = []
        self.ball_position = None
        self.current_team = "home"  # Default team
        self.current_position = "Centre"  # Default position
        
        # Create UI elements
        self.create_ui()
        
        # Draw the field
        self.draw_field()
        
        # Bind events
        self.canvas.bind("<Button-1>", self.on_canvas_click)
        
    def create_ui(self):
        # Create main frame
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Create canvas for field
        self.canvas = tk.Canvas(main_frame, width=self.canvas_width, height=self.canvas_height, 
                               bg="#4a8f29", highlightthickness=2, highlightbackground="white")
        self.canvas.pack(side=tk.LEFT, padx=5, pady=5)
        
        # Create control panel
        control_panel = ttk.Frame(main_frame)
        control_panel.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Team selection
        team_frame = ttk.LabelFrame(control_panel, text="Team")
        team_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.team_var = tk.StringVar(value="home")
        ttk.Radiobutton(team_frame, text="Home Team", variable=self.team_var, value="home", 
                       command=self.update_team).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Radiobutton(team_frame, text="Away Team", variable=self.team_var, value="away", 
                       command=self.update_team).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Radiobutton(team_frame, text="Ball", variable=self.team_var, value="ball", 
                       command=self.update_team).pack(side=tk.LEFT, padx=5, pady=5)
        
        # Player name entry
        name_frame = ttk.LabelFrame(control_panel, text="Player Name")
        name_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(name_frame, text="Name:").pack(side=tk.LEFT, padx=5, pady=5)
        self.name_entry = ttk.Entry(name_frame, width=20)
        self.name_entry.pack(side=tk.LEFT, padx=5, pady=5, fill=tk.X, expand=True)
        
        # Position selection
        position_frame = ttk.LabelFrame(control_panel, text="Position")
        position_frame.pack(fill=tk.X, padx=5, pady=5)
        
        positions = [
            "FB", "LB", "RB",           # Back line
            "CHB", "LHB", "RHB",        # Half-back line
            "Centre", "LWing", "RWing", # Centre line
            "CHF", "LHF", "RHF",        # Half-forward line
            "FF", "LF", "RF",           # Forward line
            "Ruck", "Rover", "RuckRover" # Followers
        ]
        
        self.position_var = tk.StringVar(value="Centre")
        position_combo = ttk.Combobox(position_frame, textvariable=self.position_var, values=positions)
        position_combo.pack(fill=tk.X, padx=5, pady=5)
        position_combo.bind("<<ComboboxSelected>>", self.update_position)
        
        # Player list
        list_frame = ttk.LabelFrame(control_panel, text="Players")
        list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Create treeview for player list
        columns = ("Name", "Position", "Team", "X", "Y")
        self.player_tree = ttk.Treeview(list_frame, columns=columns, show="headings")
        
        # Set column headings
        for col in columns:
            self.player_tree.heading(col, text=col)
            self.player_tree.column(col, width=70)
        
        self.player_tree.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Add scrollbar
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.player_tree.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.player_tree.configure(yscrollcommand=scrollbar.set)
        
        # Bind double-click to remove player
        self.player_tree.bind("<Double-1>", self.remove_player)
        
        # Buttons
        button_frame = ttk.Frame(control_panel)
        button_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(button_frame, text="Clear All", command=self.clear_all).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(button_frame, text="Export", command=self.export_positions).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(button_frame, text="Import", command=self.import_positions).pack(side=tk.LEFT, padx=5, pady=5)
        
        # Coordinates display
        coord_frame = ttk.LabelFrame(control_panel, text="Coordinates")
        coord_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.coord_label = ttk.Label(coord_frame, text="X: 0, Y: 0")
        self.coord_label.pack(padx=5, pady=5)
        
        # Bind mouse movement to update coordinates
        self.canvas.bind("<Motion>", self.update_coordinates)
        
    def update_team(self):
        self.current_team = self.team_var.get()
        
    def update_position(self, event=None):
        self.current_position = self.position_var.get()
        
    def update_coordinates(self, event):
        # Convert canvas coordinates to field coordinates
        field_x = event.x / self.scale_x
        field_y = event.y / self.scale_y
        
        # Convert to grid coordinates (0-80 for x, 0-65 for y)
        grid_x = field_x * 80 / self.field_length
        grid_y = field_y * 65 / self.field_width
        
        self.coord_label.config(text=f"X: {grid_x:.2f}, Y: {grid_y:.2f}")
        
    def draw_field(self):
        # Clear canvas
        self.canvas.delete("all")
        
        # Draw oval field
        self.canvas.create_oval(
            10, 10, 
            self.canvas_width - 10, self.canvas_height - 10, 
            outline="white", width=2, fill="#4a8f29"
        )
        
        # Draw center circle
        center_x = self.canvas_width / 2
        center_y = self.canvas_height / 2
        radius = 3 * self.scale_x  # 3m radius
        self.canvas.create_oval(
            center_x - radius, center_y - radius,
            center_x + radius, center_y + radius,
            outline="white", width=2
        )
        
        # Draw center square (50m x 50m)
        square_size_x = 25 * self.scale_x
        square_size_y = 25 * self.scale_y
        self.canvas.create_rectangle(
            center_x - square_size_x, center_y - square_size_y,
            center_x + square_size_x, center_y + square_size_y,
            outline="white", width=2
        )
        
        # Draw 50m arcs
        arc_radius_x = 50 * self.scale_x
        arc_radius_y = 50 * self.scale_y
        
        # Left 50m arc
        self.canvas.create_arc(
            0, center_y - arc_radius_y,
            arc_radius_x * 2, center_y + arc_radius_y,
            start=270, extent=180,
            style=tk.ARC, outline="white", width=2
        )
        
        # Right 50m arc
        self.canvas.create_arc(
            self.canvas_width - arc_radius_x * 2, center_y - arc_radius_y,
            self.canvas_width, center_y + arc_radius_y,
            start=90, extent=180,
            style=tk.ARC, outline="white", width=2
        )
        
        # Draw goal posts
        goal_width = 6.4 * self.scale_y
        post_width = 3.2 * self.scale_y
        
        # Left goal posts
        self.canvas.create_line(0, center_y - goal_width, 0, center_y - post_width, fill="white", width=4)
        self.canvas.create_line(0, center_y - post_width, 0, center_y + post_width, fill="white", width=8)
        self.canvas.create_line(0, center_y + post_width, 0, center_y + goal_width, fill="white", width=4)
        
        # Right goal posts
        self.canvas.create_line(self.canvas_width, center_y - goal_width, self.canvas_width, center_y - post_width, fill="white", width=4)
        self.canvas.create_line(self.canvas_width, center_y - post_width, self.canvas_width, center_y + post_width, fill="white", width=8)
        self.canvas.create_line(self.canvas_width, center_y + post_width, self.canvas_width, center_y + goal_width, fill="white", width=4)
        
        # Draw field markings
        self.canvas.create_text(center_x, 5, text="CENTER", fill="white", font=("Arial", 10))
        self.canvas.create_text(5, center_y, text="DEFENSIVE GOAL (HOME)", fill="white", font=("Arial", 10), angle=90)
        self.canvas.create_text(self.canvas_width - 5, center_y, text="FORWARD GOAL (HOME)", fill="white", font=("Arial", 10), angle=90)
        
        # Draw players
        self.draw_players()
        
    def draw_players(self):
        # Draw all players
        for player in self.players:
            # Convert grid coordinates to canvas coordinates
            canvas_x = player["position"]["x"] * self.field_length / 80 * self.scale_x
            canvas_y = player["position"]["y"] * self.field_width / 65 * self.scale_y
            
            # Draw player
            team_color = "blue" if player["team"] == "home" else "red"
            self.canvas.create_oval(
                canvas_x - 5, canvas_y - 5,
                canvas_x + 5, canvas_y + 5,
                fill=team_color, outline="white"
            )
            
            # Draw player name and position
            if "-" in player["name"]:
                name, position = player["name"].split("-", 1)
                label = f"{name}\n{position}"
            else:
                label = f"{player['name']}\n{player['position']}"
                
            self.canvas.create_text(
                canvas_x, canvas_y - 15,
                text=label, fill="white", font=("Arial", 8),
                width=100  # Wrap text if too long
            )
        
        # Draw ball if position is set
        if self.ball_position:
            # Convert grid coordinates to canvas coordinates
            canvas_x = self.ball_position["x"] * self.field_length / 80 * self.scale_x
            canvas_y = self.ball_position["y"] * self.field_width / 65 * self.scale_y
            
            # Draw ball
            self.canvas.create_oval(
                canvas_x - 4, canvas_y - 4,
                canvas_x + 4, canvas_y + 4,
                fill="yellow", outline="black"
            )
            self.canvas.create_text(
                canvas_x, canvas_y - 15,
                text="BALL", fill="white", font=("Arial", 8)
            )
    
    def on_canvas_click(self, event):
        # Convert canvas coordinates to field coordinates
        field_x = event.x / self.scale_x
        field_y = event.y / self.scale_y
        
        # Convert to grid coordinates (0-80 for x, 0-65 for y)
        grid_x = field_x * 80 / self.field_length
        grid_y = field_y * 65 / self.field_width
        
        # Check if inside oval field
        center_x = self.canvas_width / 2
        center_y = self.canvas_height / 2
        a = self.canvas_width / 2 - 10  # Semi-major axis
        b = self.canvas_height / 2 - 10  # Semi-minor axis
        
        if ((event.x - center_x) ** 2 / a ** 2 + (event.y - center_y) ** 2 / b ** 2) > 1:
            messagebox.showwarning("Out of Bounds", "Position is outside the field!")
            return
        
        # Add player or ball
        if self.team_var.get() == "ball":
            self.ball_position = {"x": grid_x, "y": grid_y}
        else:
            player_name = self.name_entry.get()
            if not player_name:
                messagebox.showwarning("Missing Name", "Please enter a player name!")
                return
            
            # Create player with position suffix
            full_name = f"{player_name}-{self.current_position}"
            
            # Add player
            self.players.append({
                "name": full_name,
                "position": {"x": grid_x, "y": grid_y},
                "team": self.current_team,
                "position_name": self.current_position
            })
            
            # Add to treeview
            self.player_tree.insert("", "end", values=(
                full_name, 
                self.current_position, 
                self.current_team, 
                f"{grid_x:.2f}", 
                f"{grid_y:.2f}"
            ))
            
            # Clear name entry
            self.name_entry.delete(0, tk.END)
        
        # Redraw field
        self.draw_field()
    
    def remove_player(self, event):
        # Get selected item
        selected_item = self.player_tree.selection()
        if not selected_item:
            return
        
        # Get player data
        values = self.player_tree.item(selected_item, "values")
        player_name = values[0]
        
        # Remove from players list
        for i, player in enumerate(self.players):
            if player["name"] == player_name:
                self.players.pop(i)
                break
        
        # Remove from treeview
        self.player_tree.delete(selected_item)
        
        # Redraw field
        self.draw_field()
    
    def clear_all(self):
        # Clear players
        self.players = []
        self.ball_position = None
        
        # Clear treeview
        for item in self.player_tree.get_children():
            self.player_tree.delete(item)
        
        # Redraw field
        self.draw_field()
    
    def export_positions(self):
        # Create export text
        export_text = ""
        
        # Add players
        for player in self.players:
            export_text += f'player_name:"{player["name"]}"\n'
            export_text += f'position:{{x: {player["position"]["x"]:.2f}, y: {player["position"]["y"]:.2f}}}\n'
            export_text += f'team:"{player["team"]}"\n\n'
        
        # Add ball
        if self.ball_position:
            export_text += f'player_name:"ball"\n'
            export_text += f'position:{{x: {self.ball_position["x"]:.2f}, y: {self.ball_position["y"]:.2f}}}\n'
            export_text += f'team:\n\n'
        
        # Save to file
        file_path = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        
        if file_path:
            with open(file_path, "w") as f:
                f.write(export_text)
            messagebox.showinfo("Export Successful", f"Positions exported to {file_path}")
    
    def import_positions(self):
        # Open file dialog
        file_path = filedialog.askopenfilename(
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        
        if not file_path:
            return
        
        # Clear current data
        self.clear_all()
        
        # Read file
        with open(file_path, "r") as f:
            text = f.read()
        
        # Parse player data
        lines = text.strip().split('\n')
        current_player = {}
        
        for line in lines:
            line = line.strip()
            if not line:
                # Empty line, save current player if complete
                if current_player and "name" in current_player and "position" in current_player and "team" in current_player:
                    if current_player["name"] == "ball":
                        self.ball_position = current_player["position"]
                    else:
                        # Extract position from name
                        if "-" in current_player["name"]:
                            _, position = current_player["name"].split("-", 1)
                            current_player["position_name"] = position
                        else:
                            current_player["position_name"] = "Unknown"
                        
                        self.players.append(current_player)
                        
                        # Add to treeview
                        self.player_tree.insert("", "end", values=(
                            current_player["name"],
                            current_player.get("position_name", "Unknown"),
                            current_player["team"],
                            f"{current_player['position']['x']:.2f}",
                            f"{current_player['position']['y']:.2f}"
                        ))
                
                current_player = {}
                continue
                
            if line.startswith('player_name:'):
                # Start of a new player
                current_player["name"] = line.split(':"')[1].split('"')[0]
            elif line.startswith('position:{'):
                # Position data
                pos_part = line.split(':{')[1].split('}')[0]
                x = float(pos_part.split('x:')[1].split(',')[0].strip())
                y = float(pos_part.split('y:')[1].strip())
                current_player["position"] = {"x": x, "y": y}
            elif line.startswith('team:'):
                # Team data
                if '"' in line:
                    current_player["team"] = line.split(':"')[1].split('"')[0]
                else:
                    current_player["team"] = line.split(':')[1].strip()
        
        # Add the last player if there is one
        if current_player and "name" in current_player and "position" in current_player and "team" in current_player:
            if current_player["name"] == "ball":
                self.ball_position = current_player["position"]
            else:
                # Extract position from name
                if "-" in current_player["name"]:
                    _, position = current_player["name"].split("-", 1)
                    current_player["position_name"] = position
                else:
                    current_player["position_name"] = "Unknown"
                
                self.players.append(current_player)
                
                # Add to treeview
                self.player_tree.insert("", "end", values=(
                    current_player["name"],
                    current_player.get("position_name", "Unknown"),
                    current_player["team"],
                    f"{current_player['position']['x']:.2f}",
                    f"{current_player['position']['y']:.2f}"
                ))
        
        # Redraw field
        self.draw_field()
        messagebox.showinfo("Import Successful", f"Positions imported from {file_path}")

if __name__ == "__main__":
    root = tk.Tk()
    app = AFLFieldPlotter(root)
    root.mainloop()
