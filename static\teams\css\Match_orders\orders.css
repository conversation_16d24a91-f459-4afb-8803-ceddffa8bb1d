

		/* Ensure columns sit side-by-side */
		.set-orders-container {
			display: flex;
			flex-direction: row; /* Side-by-side layout */
			width: 75%; /* Ensure it fills the parent container */
		}

		.left-column {
			flex: 3; /* Adjust width ratio */
		}

		.right-column {
			flex: 1; /* Adjust width ratio */
			justify-content: center;
		}

		/* Default hidden state for tabs */
.tab-content {
	display: none;
  }
  
  .tab-content.active {
	display: block;
  }
  
  .tab-btn.active {
	background-color: #007bff;
	color: #fff;
  }

	.field-container {
	  background: url('../../../teams/images/afl-field.png') no-repeat center center;
	  background-size: contain; /* Ensures the field image scales proportionally */
	  aspect-ratio: 300 / 424; /* Locks the aspect ratio */
	  max-width: 600px; /* Optional: Sets a maximum width */
	  width: 100%; /* Scales responsively within the parent container */
	  position: relative; /* Enables absolutely positioned child elements */
	  margin: 0 auto; /* Centers the field horizontally */
	  border: 4px solid #FFF; /* Field border */
	  border-radius: 50% / 50%; /* Horizontal radius is much larger than vertical */
	  box-shadow: 0px 8px 16px rgba(0, 0, 0, 0.2); /* Adds depth */
	}
	
	.player-rect {
	  width: 7%;
	  height: 7%;
	  background-color: rgba(255, 255, 255, 0.3);
	  display: flex;
	  align-items: center;
	  justify-content: center;
	  border-radius: 5px; /* Slightly rounded edges */
	  font-size: 12px;
	  text-align: center;
	  position: absolute;
	  cursor: pointer;
	  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.2); /* Depth for better visuals */
	}
	
	.player-name {
	  font-size: 12px;
	  color: #000;
	  text-align: center;
	}

    .player-rect:hover {
      transform: scale(1.1);
      box-shadow: 0px 6px 12px rgba(0, 0, 0, 0.3);
    }

    .tabs-container {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
	  width: 60%;
    }

    .tabs-container .tab-btn {
      padding: 10px 20px;
      background-color: #ecf0f1;
      border-radius: 10px;
      cursor: pointer;
      transition: background-color 0.2s;
    }

    .tabs-container .tab-btn:hover {
      background-color: #bdc3c7;
    }

    .tabs-container .tab-btn.active {
      background-color: #27ae60;
      color: #fff;
    }
/*
    .player {
      background-color: #ffffff;
      border: 2px solid #e0e0e0;
      border-radius: 10px;
      padding: 10px;
      margin-bottom: 3px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      transition: transform 0.2s;
	  max-width: 150px;
    }

    .player:hover {
      transform: scale(1.03);
      border-color: #3498db;
    }*/
	
	.player.faded {
	opacity: 0.5;
	pointer-events: none; 
	background-color: #d3d3d3; 
	} 
	
	.followers-container {
		display: flex; /* Align items side by side */
			background-size: contain;
		  width: 50%;
		  height: 8%;
		  gap: 1rem; /* Adds spacing between items */
		  margin-top: 1.5rem;
		  padding: 1rem;
		  background-color: #f7f7f7;
		  border-radius: 8px;
		  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
		  justify-content: flex-start; /* Align items to the left (default) */
		}

		.followers-container h3 {
		  font-size: 1.25rem;
		  font-weight: bold;
		  color: #555;
		  margin-bottom: 1rem;
		}

		.followers-container .player-rectf {
		  margin-top: .5rem;
		  width: 17%;
		  height: 70%;
		  display: flex;
		align-items: center;
		justify-content: center;
		  background-color: #f3f4f6; /* Light gray for visibility */
		  padding: 0.5rem 1rem; /* Adds spacing inside each box */
		  border-radius: 8px; /* Rounded corners */
		  text-align: center; /* Centers the text */
		  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* Subtle shadow for depth */
		  cursor: pointer; /* Indicates interactivity */
		}

		.followers-container .player-rectf:hover {
		  background-color: #1e73ba;
		}
		
		.Interchange-container {
		  display: flex; /* Align items side by side */
		  width: 60%;
		  height: 8%;
		  gap: 1rem; /* Adds spacing between items */
		  margin-top: 1.5rem;
		  padding: 1rem;
		  background-color: #f7f7f7;
		  border-radius: 8px;
		  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
		  justify-content: flex-start; /* Align items to the left (default) */
		}

		.Interchange-container h3 {
		  font-size: 1.25rem;
		  font-weight: bold;
		  color: #555;
		  margin-bottom: 1rem;
		}

		.Interchange-container .player-recti {
		  margin-top: .5rem;
		  width: 20%;
		  height: 70%;
		  display: flex;
			align-items: center;
			justify-content: center;
		  background-color: #f3f4f6; /* Light gray for visibility */
		  padding: 0.5rem 1rem; /* Adds spacing inside each box */
		  border-radius: 8px; /* Rounded corners */
		  text-align: center; /* Centers the text */
		  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* Subtle shadow for depth */
		  cursor: pointer; /* Indicates interactivity */
		}

		.Interchange-container .player-recti:hover {
		  background-color: #1e73ba;
		}
		
		.Interchange-container .player-rects {
		  margin-top: .5rem;
		  width: 20%;
		  height: 70%;
		  display: flex;
			align-items: center;
			justify-content: center;
		  background-color: #f3f4f6; /* Light gray for visibility */
		  padding: 0.5rem 1rem; /* Adds spacing inside each box */
		  border-radius: 8px; /* Rounded corners */
		  text-align: center; /* Centers the text */
		  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* Subtle shadow for depth */
		  cursor: pointer; /* Indicates interactivity */
		}

		.Interchange-container .player-rects:hover {
		  background-color: #1e73ba;
		}
		
.players-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    padding: 10px;
	background-color: #c3d6e6;
	/*align-content: center;*/
	justify-content: center;
	border-radius: 10px;
	overflow-y: auto; /* Enable vertical scrolling */
	scrollbar-width: thin; /* For modern browsers, set a thin scrollbar */
	max-height: 780px; /* Adjust this height as needed to show about 10 players */
}

.players-list::-webkit-scrollbar {
	width: 8px; /* Width of the scrollbar */
  }
  
  .players-list::-webkit-scrollbar-thumb {
	background-color: #888; /* Color of the scroll bar thumb */
	border-radius: 4px; /* Rounded corners for the thumb */
  }
  
  .players-list::-webkit-scrollbar-thumb:hover {
	background-color: #555; /* Darker thumb on hover */
  }
  
  .players-list::-webkit-scrollbar-track {
	background-color: #f1f1f1; /* Background color of the track */
  }

.player {
    position: relative;
    width: 80%;
    height: 50px;
    background-color: #ffffff;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    padding: 2px;
    margin: 0px;
    cursor: grab;
	/*align-items: center;*/
    text-align: center;
    font-size: 14px;
	transition: transform 0.2s;
}


.player:hover {
    background-color: #e0e0e0;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.player-stats {
	position: fixed; /* Make the card float independently */
	transition: opacity 0.3s ease, visibility 0.3s ease;
	display: none; /* Hidden by default */
    width: 400px;
	text-align: left; 
    background: rgba(255, 255, 255, 0.8); /* Semi-transparent black */
	border-radius: 8px;
    border: 1px solid #ddd;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.2);
    padding: 2px;
    display: none;
    z-index: 10;
    font-size: 12px;
}

/* Styling individual stat lines */
.player-stats p {
	margin-bottom: 5px;
	margin-left: 20px;
	margin-right: 20px;
    font-size: 14px;
    font-weight: bold;
}

.player-stats h3 {
	margin-bottom: 5px;
	margin-left: 20px;
	margin-right: 20px;
    font-size: 16px;
    font-weight: bold;
}
/*
.player-stats::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-top: 10px solid rgba(0, 0, 0, 0.8); 
}*/


.player:hover .player-stats {
    display: block;
}	

.progress-bar-container {
    margin-bottom: 5px;
	margin-left: 20px;
	margin-right: 20px;
    position: relative; /* Enable positioning context for the label */
	transition: transform 0.2s ease;
}

.progress-bar {
    width: 100%;
    background-color: #003f87;
    border-radius: 10px;
    overflow: hidden;
    height: 20px;
}

.progress-bar-fill {
    height: 100%; 
	background-color: #3891f5;
    /* background-color: #003f87; */
	transition: transform 0.2s ease;
}

/* Center the label within the entire container */
.progress-label {
    position: absolute;
	top: 50%;
    left: 50%;
    transform: translate(-50%, +15%);
    color: #fff; /* Text color inside the progress bar */
    white-space: nowrap;
    z-index: 1; /* Ensure label is on top */
}
.stat-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2px;
	text-align: left; 
}

.card-body.two-columns {
    display: flex;
    gap: 10px; /* Space between columns */
}

.player-column {
    width: 100%;
}

.sort-container {
	margin-bottom: .5rem;
	width: 80%;
	display: flex;
	align-items: center;
	padding-top: 5px;
	padding-bottom: 5px;
	gap: 0.5rem;
	border-radius: 8px;
    border: 1px solid #ddd;
	justify-content: center;
	justify-self: center;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.2);
  }
  
  .sort-rules {
	justify-content: center;
	font-size: 2rem;
	
  }

  .tactics-container {
	margin-bottom: .5rem;
	width: 80%;
	display: flex;
	align-items: center;
	padding-top: 5px;
	padding-bottom: 5px;
	gap: 0.5rem;
	border-radius: 8px;
    border: 1px solid #ddd;
	justify-content: center;
	justify-self: left;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.2);
  }

  .review-card {
	width: 30%;
    background-color: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.review-card h3 {
    margin-top: 0;
    color: #333;
}

.error-text {
    color: #d9534f;
    font-weight: bold;
}

.success-text {
    color: #5cb85c;
    font-weight: bold;
}

ul {
    list-style-type: none;
    padding: 0;
    margin: 0;
}

.lineup-positions, .followers-positions, .interchange-positions, .missing-positions, .tactics-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.lineup-position, .followers-position, .interchange-position, .missing-position {
    display: flex;
    justify-content: space-between;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #fff;
}

.missing-position .status {
    color: #d9534f;
}

.lineup-position .followers-position, .interchange-position, .player-name, .missing-position .status {
    font-weight: bold;
}

.position-name {
    font-weight: bold;
}

.tactics-details li {
    padding: 4px 0;
}
