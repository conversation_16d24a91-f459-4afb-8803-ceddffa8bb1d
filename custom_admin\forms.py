from django import forms
from teams.models import Team, Player


class MatchSimulationForm(forms.Form):
    team1 = forms.ModelChoiceField(queryset=Team.objects.all(), label="Team 1")
    team2 = forms.ModelChoiceField(queryset=Team.objects.all(), label="Team 2")
    team1_ruck = forms.ModelChoiceField(queryset=Player.objects.none(), label="Team 1 Ruck")
    team2_ruck = forms.ModelChoiceField(queryset=Player.objects.none(), label="Team 2 Ruck")

    def __init__(self, *args, **kwargs):
        super(MatchSimulationForm, self).__init__(*args, **kwargs)
        if 'team1' in self.data:
            try:
                team1_id = int(self.data.get('team1'))
                self.fields['team1_ruck'].queryset = Player.objects.filter(team_id=team1_id)
            except (ValueError, TypeError):
                pass  # invalid input from the client; ignore and fallback to empty Player queryset
        if 'team2' in self.data:
            try:
                team2_id = int(self.data.get('team2'))
                self.fields['team2_ruck'].queryset = Player.objects.filter(team_id=team2_id)
            except (ValueError, TypeError):
                pass  # invalid input from the client; ignore and fallback to empty Player queryset


    class Media:
        js = ('admin/js/match_simulation.js',)