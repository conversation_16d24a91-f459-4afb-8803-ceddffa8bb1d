# Generated by Django 5.0.7 on 2024-07-26 02:20

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('teams', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='PlayerStatsAbility',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('kicking', models.IntegerField()),
                ('goal_kicking', models.IntegerField()),
                ('handball', models.IntegerField()),
                ('tackling', models.IntegerField()),
                ('mental', models.IntegerField()),
                ('tactical', models.IntegerField()),
                ('versatility', models.IntegerField()),
                ('consistency', models.IntegerField()),
            ],
        ),
        migrations.CreateModel(
            name='PlayerStatsPhysical',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('height', models.IntegerField()),
                ('agility', models.IntegerField()),
                ('speed', models.IntegerField()),
                ('strength', models.IntegerField()),
                ('stamina', models.IntegerField()),
                ('age', models.IntegerField()),
            ],
        ),
        migrations.RemoveField(
            model_name='player',
            name='agility',
        ),
        migrations.RemoveField(
            model_name='player',
            name='consistency',
        ),
        migrations.RemoveField(
            model_name='player',
            name='goal_kicking',
        ),
        migrations.RemoveField(
            model_name='player',
            name='handball',
        ),
        migrations.RemoveField(
            model_name='player',
            name='height',
        ),
        migrations.RemoveField(
            model_name='player',
            name='kicking',
        ),
        migrations.RemoveField(
            model_name='player',
            name='mental',
        ),
        migrations.RemoveField(
            model_name='player',
            name='speed',
        ),
        migrations.RemoveField(
            model_name='player',
            name='stamina',
        ),
        migrations.RemoveField(
            model_name='player',
            name='strength',
        ),
        migrations.RemoveField(
            model_name='player',
            name='tackling',
        ),
        migrations.RemoveField(
            model_name='player',
            name='tactical',
        ),
        migrations.RemoveField(
            model_name='player',
            name='versatility',
        ),
        migrations.RemoveField(
            model_name='team',
            name='players',
        ),
        migrations.AddField(
            model_name='player',
            name='team',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, related_name='players', to='teams.team'),
            preserve_default=False,
        ),
    ]
