import sys
import random
import asyncio
import time
import traceback
import math
from typing import Dict, List, Tuple, Optional, Any, Generator, TYPE_CHECKING
from asgiref.sync import sync_to_async, async_to_sync
from channels.layers import get_channel_layer

if TYPE_CHECKING:
    from .game_clock import GameClock

class Ground:
    """Represents the AFL ground and its dimensions"""
    def __init__(self):
        self.length = 160  # AFL ground length in meters
        self.width = 130   # AFL ground width in meters
        self.grid_length = int(self.length / 10)  # 16 cells
        self.grid_width = int(self.width / 10)    # 13 cells
        self.zones = self._initialize_zones()
        
    def get_center(self) -> Tuple[float, float]:
        """Get center point of ground"""
        return (self.length / 2, self.width / 2)
        
    def is_in_bounds(self, position: Tuple[float, float]) -> bool:
        """Check if position is within ground boundaries"""
        x, y = position
        return 0 <= x <= self.length and 0 <= y <= self.width
        
    def get_zone(self, position: Tuple[float, float]) -> str:
        """Get the zone name for a given position"""
        x, y = position
        for zone_name, zone_bounds in self.zones.items():
            if (zone_bounds['x'][0] <= x <= zone_bounds['x'][1] and 
                zone_bounds['y'][0] <= y <= zone_bounds['y'][1]):
                return zone_name
        return 'out_of_bounds'
        
    def get_distance_to_goal(self, position: Tuple[float, float], team_side: str) -> float:
        """Calculate distance to goal"""
        x, y = position
        goal_x = self.length if team_side == 'home' else 0
        goal_y = self.width / 2
        return math.sqrt((x - goal_x)**2 + (y - goal_y)**2)
        
    def _initialize_zones(self) -> Dict[str, Dict[str, Tuple[float, float]]]:
        """Initialize ground zones"""
        return {
            'forward_50': {'x': (110, 160), 'y': (0, 130)},
            'forward_flank': {'x': (80, 110), 'y': (0, 130)},
            'center': {'x': (60, 100), 'y': (0, 130)},
            'back_flank': {'x': (50, 80), 'y': (0, 130)},
            'back_50': {'x': (0, 50), 'y': (0, 130)}
        }

class Player:
    """Represents a player in the match"""
    def __init__(self, name: str, team: Any, position: str, ability_stats: Any, physical_stats: Any):
        self.id = id(self)  # Unique ID for each player
        self.name = name
        self.team = team
        self.team_side = None  # Will be set when added to team
        self.position = position
        self.ability_stats = ability_stats
        self.physical_stats = physical_stats
        self.current_position = None
        self.previous_position = None
        self.target_position = None
        self.fatigue = 0
        self.last_action_time = 0
        self.recent_actions = []
        
    def update(self, delta_time: float) -> None:
        """Update player state"""
        self.previous_position = self.current_position
        self.fatigue = max(0, self.fatigue - (delta_time * 0.1))
        self.recent_actions = [
            action for action in self.recent_actions
            if time.time() - action['time'] < 60
        ]
        
    def can_act(self) -> bool:
        """Check if player can perform actions"""
        return self.fatigue < 90
        
    def add_action(self, action_type: str) -> None:
        """Record a player action"""
        self.recent_actions.append({
            'type': action_type,
            'time': time.time(),
            'position': self.current_position
        })
        
    def get_influence_radius(self) -> float:
        """Calculate player's area of influence"""
        base_radius = 5.0  # Base 5 meter radius
        speed_bonus = self.physical_stats.speed * 0.1
        agility_bonus = self.physical_stats.agility * 0.05
        fatigue_penalty = self.fatigue * 0.02
        return max(2.0, base_radius + speed_bonus + agility_bonus - fatigue_penalty)

class Team:
    """Represents a team in the match"""
    def __init__(self, id: int, name: str, side: str, tactics: Dict[str, Any]):
        self.id = id
        self.name = name
        self.side = side  # 'home' or 'away'
        self.tactics = tactics
        self.players = []
        self.score = {'goals': 0, 'behinds': 0, 'total': 0}
        
    def add_player(self, player: Player) -> None:
        """Add player to team"""
        self.players.append(player)
        player.team = self
        player.team_side = self.side
        
    def get_players_by_position(self, position: str) -> List[Player]:
        """Get all players in a specific position"""
        return [p for p in self.players if p.position == position]
        
    def update_score(self, score_type: str) -> None:
        """Update team score"""
        if score_type == 'goal':
            self.score['goals'] += 1
            self.score['total'] += 6
        elif score_type == 'behind':
            self.score['behinds'] += 1
            self.score['total'] += 1
            
    def update_tactics(self, game_state: Any, game_clock: 'GameClock') -> None:
        """Update team tactics based on game state"""
        score_diff = game_state.get_score_difference(self.side)
        time_remaining = game_clock.get_time_remaining()
        
        # Adjust tactics based on score and time
        if time_remaining < 300:  # Last 5 minutes
            if score_diff < -6:  # Down by a goal
                self.tactics['mentality'] = 'all_out_attack'
            elif score_diff > 6:  # Up by a goal
                self.tactics['mentality'] = 'defensive'
        else:
            if score_diff < -12:  # Two goals down
                self.tactics['mentality'] = 'attacking'
            elif score_diff > 12:  # Two goals up
                self.tactics['mentality'] = 'defensive'
            else:
                self.tactics['mentality'] = 'balanced'

class MovementEngine:
    """Handles player and ball movement"""
    def __init__(self, ground: Ground):
        self.ground = ground
        self.movement_speeds = {
            'sprint': 8.0,  # Meters per second
            'run': 6.0,
            'jog': 4.0,
            'walk': 2.0
        }
        self.movement_states = {}  # Track movement states for interpolation
        
    def update_movement_state(
        self,
        player: Player,
        target_pos: Tuple[float, float],
        movement_type: str = 'run'
    ) -> None:
        """Update movement state for a player"""
        if not player.current_position:
            player.current_position = target_pos
            return
            
        self.movement_states[player.id] = {
            'start_pos': player.current_position,
            'target_pos': target_pos,
            'movement_type': movement_type,
            'progress': 0.0,
            'start_time': time.time()
        }
        
    def update_positions(self, delta_time: float) -> List[Dict[str, Any]]:
        """Update all moving entities positions"""
        updates = []
        
        # Update each moving entity
        for player_id, state in list(self.movement_states.items()):
            if not state['start_pos'] or not state['target_pos']:
                continue
                
            # Calculate movement based on speed and time
            speed = self.movement_speeds[state['movement_type']]
            distance = self._calculate_distance(state['start_pos'], state['target_pos'])
            
            if distance == 0:
                continue
                
            # Update progress
            movement_time = distance / speed
            elapsed_time = time.time() - state['start_time']
            progress = min(1.0, elapsed_time / movement_time)
            
            # Calculate new position
            new_pos = self._interpolate_position(
                state['start_pos'],
                state['target_pos'],
                progress
            )
            
            # Add to updates
            updates.append({
                'player_id': player_id,
                'position': {
                    'x': new_pos[0],
                    'y': new_pos[1]
                }
            })
            
            # Remove completed movements
            if progress >= 1.0:
                del self.movement_states[player_id]
                
        return updates
        
    def calculate_ball_movement(
        self,
        current_pos: Tuple[float, float],
        target_pos: Tuple[float, float],
        movement_type: str = 'kick',
        height: float = 0.0
    ) -> Tuple[float, float]:
        """Calculate ball movement with trajectory"""
        if not current_pos or not target_pos:
            return current_pos
            
        distance = self._calculate_distance(current_pos, target_pos)
        if distance == 0:
            return current_pos
            
        # Get movement parameters based on type
        if movement_type == 'kick':
            speed = self.movement_speeds['sprint'] * 1.5
            max_height = 15.0  # Maximum height for kicks
        elif movement_type == 'handball':
            speed = self.movement_speeds['sprint']
            max_height = 2.0   # Lower height for handballs
        else:  # ground ball
            speed = self.movement_speeds['run']
            max_height = 0.5   # Minimal height for ground balls
            
        # Calculate progress
        time_to_target = distance / speed
        progress = min(1.0, time.time() / time_to_target)
        
        # Calculate height using parabolic trajectory
        if height > 0:
            current_height = self._calculate_height(progress, max_height)
        else:
            current_height = 0
            
        # Get interpolated ground position
        ground_pos = self._interpolate_position(current_pos, target_pos, progress)
        
        return (ground_pos[0], ground_pos[1], current_height)
        
    def _interpolate_position(
        self,
        start_pos: Tuple[float, float],
        target_pos: Tuple[float, float],
        progress: float
    ) -> Tuple[float, float]:
        """Interpolate between two positions"""
        x = start_pos[0] + (target_pos[0] - start_pos[0]) * progress
        y = start_pos[1] + (target_pos[1] - start_pos[1]) * progress
        
        # Ensure position is within bounds
        x = max(0, min(x, self.ground.length))
        y = max(0, min(y, self.ground.width))
        
        return (x, y)
        
    def _calculate_height(self, progress: float, max_height: float) -> float:
        """Calculate height at given progress point"""
        # Parabolic trajectory: h = 4hmax * p(1-p) where p is progress (0 to 1)
        return 4 * max_height * progress * (1 - progress)
        
    def _calculate_distance(
        self,
        pos1: Tuple[float, float],
        pos2: Tuple[float, float]
    ) -> float:
        """Calculate distance between two positions"""
        return math.sqrt(
            (pos2[0] - pos1[0])**2 +
            (pos2[1] - pos1[1])**2
        )

class TacticsEngine:
    """Handles team tactics and player positioning"""
    def __init__(self, ground: Ground):
        self.ground = ground
        self.formations = self._initialize_formations()
        
    def _initialize_formations(self) -> Dict[str, Dict[str, Tuple[float, float]]]:
        """Initialize formation templates"""
        return {
            'center_bounce': {
                'Ruck': (0.5, 0.5),
                'RuckRover': (0.48, 0.45),
                'Rover': (0.48, 0.55),
                'Centre': (0.45, 0.5),
                'LWing': (0.5, 0.2),
                'RWing': (0.5, 0.8),
                'LHF': (0.7, 0.3),
                'RHF': (0.7, 0.7),
                'CHF': (0.75, 0.5),
                'LF': (0.9, 0.3),
                'RF': (0.9, 0.7),
                'LHB': (0.3, 0.3),
                'RHB': (0.3, 0.7),
                'CHB': (0.25, 0.5),
                'LB': (0.1, 0.3),
                'RB': (0.1, 0.7),
            },
            'kickout': {
                # Similar structure but different positions
            },
            'general_play': {
                # Default positions for open play
            }
        }
        
    def get_position_for_formation(
        self,
        position: str,
        formation: str,
        team_side: str
    ) -> Tuple[float, float]:
        """Get position in specific formation"""
        if formation not in self.formations:
            return self.get_default_position(position, team_side)
            
        base_pos = self.formations[formation].get(position, (0.5, 0.5))
        return self._adjust_for_team_side(base_pos, team_side)
        
    def get_default_position(
        self,
        position: str,
        team_side: str
    ) -> Tuple[float, float]:
        """Get default position for role"""
        # Default positions when no specific formation
        return self._adjust_for_team_side((0.5, 0.5), team_side)
        
    def _adjust_for_team_side(
        self,
        position: Tuple[float, float],
        team_side: str
    ) -> Tuple[float, float]:
        """Adjust position based on team side"""
        x, y = position
        if team_side == 'away':
            x = 1 - x  # Flip x-coordinate for away team
        return (x * self.ground.length, y * self.ground.width)

class ActionEngine:
    """Handles player actions and their outcomes"""
    def __init__(self, ground: Ground):
        self.ground = ground
        
    def process_action(
        self,
        player: Player,
        action_type: str,
        target: Optional[Player] = None,
        game_state: Optional[Any] = None
    ) -> Dict[str, Any]:
        """Process a player action"""
        base_chance = self._get_base_chance(player, action_type)
        modified_chance = self._apply_modifiers(base_chance, player, action_type, target, game_state)
        
        success = random.random() < modified_chance
        
        return {
            'type': action_type,
            'player': player,
            'target': target,
            'success': success,
            'position': player.current_position
        }
        
    def _get_base_chance(self, player: Player, action_type: str) -> float:
        """Get base success chance for action"""
        if action_type == 'kick':
            return player.ability_stats.kicking / 20
        elif action_type == 'handball':
            return player.ability_stats.handball / 20
        elif action_type == 'mark':
            return player.ability_stats.marking / 20
        elif action_type == 'tackle':
            return player.ability_stats.tackling / 20
        return 0.5
        
    def _apply_modifiers(
        self,
        base_chance: float,
        player: Player,
        action_type: str,
        target: Optional[Player],
        game_state: Optional[Any]
    ) -> float:
        """Apply modifiers to base success chance"""
        modified_chance = base_chance
        
        # Fatigue modifier
        fatigue_mod = max(0.5, 1 - (player.fatigue / 100))
        modified_chance *= fatigue_mod
        
        # Pressure modifier
        if target:
            pressure = self._calculate_pressure(player, target)
            modified_chance *= (1 - pressure * 0.5)
            
        return min(modified_chance, 1.0)
        
    def _calculate_pressure(self, player: Player, opponent: Player) -> float:
        """Calculate pressure from opponent"""
        if not player.current_position or not opponent.current_position:
            return 0
            
        distance = math.sqrt(
            (player.current_position[0] - opponent.current_position[0])**2 +
            (player.current_position[1] - opponent.current_position[1])**2
        )
        
        base_pressure = max(0, 1 - distance/10)
        tackle_factor = opponent.ability_stats.tackling / 20
        agility_factor = opponent.physical_stats.agility / 20
        
        return min(base_pressure * (tackle_factor + agility_factor) / 2, 1.0)

    def calculate_pressure(self, player: Player, game_state: Any) -> float:
        """Calculate pressure on a player from nearby opponents"""
        if not player.current_position:
            return 0.0
            
        pressure = 0.0
        opponent_players = (
            game_state.team2_players.values() 
            if player in game_state.team1_players.values() 
            else game_state.team1_players.values()
        )
        
        for opponent in opponent_players:
            if not opponent.current_position:
                continue
                
            distance = math.sqrt(
                (player.current_position[0] - opponent.current_position[0])**2 +
                (player.current_position[1] - opponent.current_position[1])**2
            )
            
            # Apply pressure based on distance and opponent stats
            if distance <= opponent.get_influence_radius():
                base_pressure = 1.0 - (distance / opponent.get_influence_radius())
                stat_modifier = (
                    opponent.physical_stats.speed * 0.4 +
                    opponent.physical_stats.strength * 0.3 +
                    opponent.ability_stats.tackling * 0.3
                ) / 100.0
                
                pressure += base_pressure * stat_modifier
                
        return min(pressure, 1.0)  # Cap pressure at 1.0

class CommentaryEngine:
    """Generates match commentary"""
    def __init__(self):
        self.phrases = {
            'mark': [
                "{player} takes a strong grab!",
                "What a mark by {player}!",
                "{player} climbs high and takes a beauty!",
                "Specky from {player}! That'll be on the highlights reel!",
                "{player} shows great hands with that mark."
            ],
            'goal': [
                "{player} slots it through for a major!",
                "GOAL! {player} makes no mistake!",
                "That's six points! Beautiful finish from {player}!",
                "{player} threads the needle for a goal!",
                "The crowd goes wild as {player} kicks truly!"
            ],
            'behind': [
                "Just a minor score for {player}",
                "{player} pushes it a bit wide for a behind",
                "One point as {player}'s shot drifts to the right",
                "Not quite straight enough from {player}",
                "A rushed behind as the defense scrambles"
            ],
            'center_bounce': [
                "We're back in the middle with {ruck1} and {ruck2} going at it",
                "The big men {ruck1} and {ruck2} face off in the center",
                "Back to the center we go, {ruck1} versus {ruck2}",
                "{ruck1} and {ruck2} ready for another ruck contest",
                "The umpire holds the ball aloft as {ruck1} and {ruck2} prepare to do battle"
            ],
            'tackle': [
                "Brilliant tackle from {player}!",
                "{player} wraps them up perfectly!",
                "No getting out of that one! Great tackle by {player}",
                "{player} shows perfect technique in that tackle",
                "Bone-crunching tackle from {player}!"
            ],
            'handball': [
                "Quick hands from {player}",
                "{player} dishes it off nicely",
                "Clever handball from {player}",
                "{player} gets it away under pressure",
                "Slick handball delivery from {player}"
            ],
            'kick': [
                "{player} sends it long",
                "Beautiful kick from {player}",
                "{player} puts it into space",
                "Great field kick by {player}",
                "{player} launches it forward"
            ],
            'hitout': [
                "{player} taps it down perfectly",
                "Great tap work from {player}",
                "{player} gives first use to the rovers",
                "Textbook ruck work from {player}",
                "{player} dominates the hitout"
            ],
            'clearance': [
                "{player} bursts clear from the stoppage",
                "Brilliant clearance work from {player}",
                "{player} extracts it from the congestion",
                "Clean hands in traffic from {player}",
                "{player} breaks through the pack"
            ],
            'quarter_start': [
                "And we're underway in the {quarter} quarter!",
                "The {quarter} quarter begins!",
                "We're back for the {quarter} quarter of this contest",
                "The umpire bounces the ball to start the {quarter} quarter",
                "Here we go for the {quarter} quarter!"
            ],
            'quarter_end': [
                "That's the end of the {quarter} quarter!",
                "The siren sounds to end the {quarter} quarter",
                "{quarter} quarter complete!",
                "And there's the siren! End of the {quarter} quarter",
                "The {quarter} quarter comes to a close"
            ]
        }
        
        self.jokes = [
            "That kick was so bad it might get nominated for the Brownlow!",
            "He's been quieter than a Collingwood supporter after a Grand Final loss!",
            "That was a bigger flop than the Gold Coast Suns' premiership aspirations!",
            "He's got more space than a Carlton defender in September!",
            "That was more confusing than the AFL's rule changes!",
            "He's been more elusive than a Richmond player at a nightclub!",
            "That was more dramatic than an Eddie McGuire press conference!",
            "He's got more moves than a Dusty Martin don't argue!",
            "That was shakier than Essendon's supplement program!",
            "He's got more pressure than a Fremantle player in front of goal!"
        ]
        
    def generate_commentary(self, event_type: str, **data: Any) -> str:
        """Generate commentary for an event"""
        if event_type not in self.phrases:
            return ""
            
        # Get random phrase for this event type
        phrase = random.choice(self.phrases[event_type])
        
        # Add a joke occasionally
        if random.random() < 0.05:  # 5% chance of a joke
            phrase += " " + random.choice(self.jokes)
            
        # Format with event data
        try:
            return phrase.format(**data)
        except KeyError:
            return phrase

class EventManager:
    """Handles event creation and broadcasting"""
    def __init__(self, channel_layer, match_group_name):
        self.commentary = CommentaryEngine()
        self.channel_layer = channel_layer
        self.match_group_name = match_group_name
        
    def create_event(self, event_type: str, quarter: int, event_category: str = "match_event", **data: Any) -> Dict[str, Any]:
        """Create a game event"""
        event = {
            'type': event_category,
            'quarter': quarter,
            'timestamp': time.time(),
            'event_type': event_type,
            'data': data
        }
        
        # Add commentary if applicable
        commentary = self.commentary.generate_commentary(event_type, **data)
        if commentary:
            event['data']['commentary'] = commentary
            
        return event
        
    async def broadcast_event(self, event: Dict[str, Any]) -> None:
        """Broadcast event to all connected clients"""
        try:
            formatted_event = {
                "type": "send_match_event",
                "data": event
            }
            
            await self.channel_layer.group_send(
                self.match_group_name,
                formatted_event
            )
            
        except Exception as e:
            print(f"Broadcast error: {e}")
            print(traceback.format_exc())

class GameState:
    """Manages the current state of the game"""
    def __init__(self, ground: Ground, team1_players: Dict[str, Player], team2_players: Dict[str, Player]):
        self.ground = ground
        self.team1_players = team1_players
        self.team2_players = team2_players
        self.quarter = 1
        self.phase = 'pre_game'
        self.ball_carrier = None
        self.ball_position = ground.get_center()
        self.score = {
            'home': {'goals': 0, 'behinds': 0, 'total': 0},
            'away': {'goals': 0, 'behinds': 0, 'total': 0}
        }
        self.weather_system = WeatherSystem()
        self.last_possession = None
        self.contest_count = 0
        
    def update_score(self, team_side: str, score_type: str) -> None:
        """Update team score"""
        if score_type == 'goal':
            self.score[team_side]['goals'] += 1
            self.score[team_side]['total'] += 6
        elif score_type == 'behind':
            self.score[team_side]['behinds'] += 1
            self.score[team_side]['total'] += 1

    def get_score_difference(self, team_side: str) -> int:
        """Get score difference from perspective of given team"""
        if team_side == 'home':
            return self.score['home']['total'] - self.score['away']['total']
        else:
            return self.score['away']['total'] - self.score['home']['total']

    def get_leading_team(self) -> Optional[str]:
        """Get the team that is currently leading"""
        diff = self.get_score_difference('home')
        if diff > 0:
            return 'home'
        elif diff < 0:
            return 'away'
        return None  # Draw

class WeatherSystem:
    """Manages weather conditions and their effects on gameplay"""
    def __init__(self, condition: str = 'clear'):
        self.condition = condition
        self.conditions = {
            'clear': {'handling': 1.0, 'kicking': 1.0, 'visibility': 1.0},
            'light_rain': {'handling': 0.9, 'kicking': 0.9, 'visibility': 0.95},
            'heavy_rain': {'handling': 0.7, 'kicking': 0.7, 'visibility': 0.8},
            'wet_ground': {'handling': 0.8, 'kicking': 0.85, 'visibility': 1.0},
            'windy': {'handling': 0.9, 'kicking': 0.8, 'visibility': 1.0}
        }
        self.wind_direction = random.randint(0, 359)  # degrees
        self.wind_strength = random.uniform(0, 1)  # 0 to 1
        
    def get_modifiers(self) -> Dict[str, float]:
        """Get current weather modifiers"""
        modifiers = self.conditions[self.condition].copy()
        modifiers['wind_direction'] = self.wind_direction
        modifiers['wind_strength'] = self.wind_strength
        modifiers['condition'] = self.condition  # Add current condition to modifiers
        return modifiers

    def update(self) -> None:
        """Update weather conditions"""
        # Small chance of weather change each quarter
        if random.random() < 0.1:
            self.condition = random.choice(list(self.conditions.keys()))
        # Wind can shift slightly
        self.wind_direction = (self.wind_direction + random.randint(-20, 20)) % 360
        self.wind_strength = max(0, min(1, self.wind_strength + random.uniform(-0.1, 0.1)))

class GameClock:
    """Manages game time and quarter transitions"""
    def __init__(self, quarter_length: int = 20 * 60):  # 20 minutes in seconds
        self.quarter_length = quarter_length
        self.current_quarter = 1
        self.time_elapsed = 0
        self.is_paused = False
        
    def update(self, delta_time: float) -> None:
        """Update game clock"""
        if not self.is_paused:
            self.time_elapsed += delta_time
            
    def get_time_remaining(self) -> float:
        """Get time remaining in current quarter"""
        return max(0, self.quarter_length - self.time_elapsed)
        
    def should_end_quarter(self) -> bool:
        """Check if quarter should end"""
        return self.time_elapsed >= self.quarter_length
        
    def next_quarter(self) -> None:
        """Move to next quarter"""
        self.current_quarter += 1
        self.time_elapsed = 0
        
    def is_game_over(self) -> bool:
        """Check if game is over"""
        return self.current_quarter > 4 and self.should_end_quarter()

class MatchEngine:
    """Main match simulation engine"""
    def __init__(
        self,
        team1: Any,
        team2: Any,
        team1_players: Dict[str, Any],
        team2_players: Dict[str, Any],
        team1_tactics: Dict[str, Any],
        team2_tactics: Dict[str, Any],
        match_group_name: str,
        channel_layer: Any
    ):
        # Initialize core components
        self.ground = Ground()
        self.game_clock = GameClock()
        self.weather_system = WeatherSystem()
        
        # Initialize engines
        self.movement_engine = MovementEngine(self.ground)
        self.action_engine = ActionEngine(self.ground)
        self.contest_engine = ContestEngine(self.ground, self.action_engine)
        self.phase_engine = PhaseEngine(self.ground, self.action_engine, self.contest_engine)
        self.scoring_engine = ScoringEngine(self.ground)
        self.tactics_engine = TacticsEngine(self.ground)
        
        # Initialize teams
        self.team1 = Team(team1.id, team1.name, 'home', team1_tactics)
        self.team2 = Team(team2.id, team2.name, 'away', team2_tactics)
        
        # Initialize players
        self.team1_players = {
            pos: Player(player.name, self.team1, pos, player.ability_stats, player.physical_stats)
            for pos, player in team1_players.items()
        }
        self.team2_players = {
            pos: Player(player.name, self.team2, pos, player.ability_stats, player.physical_stats)
            for pos, player in team2_players.items()
        }
        #print(f'Team 1 Player {self.team1_players}')
        #print(f'Team 2 Player {self.team2_players}')
        # Initialize managers
        self.event_manager = EventManager(channel_layer, match_group_name)
        self.stats_manager = StatsManager()
        self.stats_manager.initialize_player_stats(team1_players, team2_players)
        
        # Initialize game state
        self.game_state = GameState(self.ground, self.team1_players, self.team2_players)
        
        print("MatchEngine initialized")
        
    async def simulate_match(self) -> Generator[Dict[str, Any], None, None]:
        """Main match simulation loop"""
        # Pre-match setup
        print("Simulate Match")
        self._initialize_positions()
        
        # Simulate quarters
        while not self.game_clock.is_game_over():
            # Start quarter
            yield self.event_manager.create_event(
                'quarter_start',
                self.game_clock.current_quarter,
                event_category='match_event',
                name=self._get_quarter_name(self.game_clock.current_quarter)
            )
            
            # Simulate quarter
            async for event in self._simulate_quarter():
                yield event
                
                # Update and broadcast positions
                position_event = self._update_positions()
                if position_event:
                    yield position_event
                    
            # End quarter
            yield self.event_manager.create_event(
                'quarter_end',
                self.game_clock.current_quarter,
                event_category='match_event',
                name=self._get_quarter_name(self.game_clock.current_quarter)
            )
            
            # Update and broadcast stats
            player_stats = self.stats_manager.collect_player_stats(
                self.team1_players,
                self.team2_players
            )
            
            yield self.event_manager.create_event(
                'player_stats',
                self.game_clock.current_quarter,
                event_category='player_stats',
                player_stats=player_stats
            )
            
            # Process quarter break
            if not self.game_clock.is_game_over():
                self.game_clock.next_quarter()
                await self._process_quarter_break()
                
    async def _simulate_quarter(self) -> Generator[Dict[str, Any], None, None]:
        """Simulate a quarter of play"""
        # Start with center bounce
        self._setup_center_bounce()
        
        while not self.game_clock.should_end_quarter():
            # Process current phase
            event = await self.phase_engine.process_phase(self.game_state)
            print(f"event {event}")
            if event:
                yield event
                
                # Update and broadcast positions after each event
                position_event = self._update_positions()
                if position_event:
                    yield position_event
            
            # Update game state
            self._update_game_state()
            
            # Small delay for real-time simulation
            await asyncio.sleep(0.1)
            
    def _initialize_positions(self) -> None:
        """Initialize player positions"""
        for team_players in [self.team1_players, self.team2_players]:
            for player in team_players.values():
                base_pos = self.tactics_engine.get_position_for_formation(
                    player.position,
                    'center_bounce',
                    player.team_side
                )
                player.current_position = base_pos
                
    def _setup_center_bounce(self) -> None:
        """Setup for center bounce"""
        print("Setup Center Bounce")
        self.game_state.phase = 'center_bounce'
        self.game_state.ball_carrier = None
        self.game_state.ball_position = self.ground.get_center()
        
        # Position players in center bounce formation
        for team_players in [self.team1_players, self.team2_players]:
            for player in team_players.values():
                formation_pos = self.tactics_engine.get_position_for_formation(
                    player.position,
                    'center_bounce',
                    player.team_side
                )
                player.current_position = formation_pos
                
    def _update_game_state(self) -> None:
        """Update game state"""
        # Update clock
        self.game_clock.update(0.1)  # 0.1 second per update
        
        # Update weather
        self.weather_system.update()
        
        # Update player states
        for team_players in [self.team1_players, self.team2_players]:
            for player in team_players.values():
                player.update(0.1)
                
        # Update team tactics
        self.team1.update_tactics(self.game_state, self.game_clock)
        self.team2.update_tactics(self.game_state, self.game_clock)
        
    async def _process_quarter_break(self) -> None:
        """Process quarter break"""
        # Update player fatigue
        for team_players in [self.team1_players, self.team2_players]:
            for player in team_players.values():
                player.fatigue = max(0, player.fatigue - 30)  # Recover some fatigue
                
        # Pause for break
        await asyncio.sleep(1)
        
    def _get_quarter_name(self, quarter: int) -> str:
        """Get quarter name"""
        if quarter == 1:
            return "first"
        elif quarter == 2:
            return "second"
        elif quarter == 3:
            return "third"
        else:
            return "fourth"
        
    def _update_positions(self) -> Dict[str, Any]:
        """Update and broadcast position changes"""
        position_updates = []
        print("Position Update")

        # Get opponent team and players
        team_in_possession = self.team1 if self.game_state.ball_carrier in self.team1_players.values() else self.team2
        opponent_team = self.team2 if team_in_possession == self.team1 else self.team1
        opponent_players = self.team2_players if team_in_possession == self.team1 else self.team1_players
        team_players = self.team1_players if team_in_possession == self.team1 else self.team2_players
        
        # Determine game state and zones
        play_state = self._get_play_state(team_in_possession)
        field_zones = self._calculate_field_zones(self.game_state.ball_position, play_state)
        
        # Update positions for all players except ball carrier
        for team, is_possession_team in [(team_players, True), (opponent_players, False)]:
            for player in team.values():
                if player != self.game_state.ball_carrier:
                    self._update_individual_player_position(
                        player=player,
                        is_possession_team=is_possession_team,
                        play_state=play_state,
                        field_zones=field_zones,
                        ball_position=self.game_state.ball_position
                    )

        # Create position updates for broadcast
        for team_players in [self.team1_players, self.team2_players]:
            for player in team_players.values():
                if not player.current_position:
                    continue
                    
                position_updates.append({
                    'player_name': player.name,
                    'team': player.team_side,
                    'position': {
                        'x': player.current_position[0],
                        'y': player.current_position[1]
                    }
                })

        # Add ball position
        if self.game_state.ball_position:
            position_updates.append({
                'player_name': 'ball',
                'team': None,
                'position': {
                    'x': self.game_state.ball_position[0],
                    'y': self.game_state.ball_position[1]
                }
            })

        # Create position update event
        if position_updates:
            return self.event_manager.create_event(
                'position_update',
                self.game_clock.current_quarter,
                'position_update',
                positions=position_updates
            )
        return None

    def _get_play_state(self, team_in_possession=None) -> str:
        """Get current play state"""
        if not team_in_possession:
            return "contest"
            
        if self.game_state.phase == "set_shot":
            return "goal"
            
        # Get ball position zone
        ball_zone = self.ground.get_zone(self.game_state.ball_position)
        
        # Determine if team is attacking or defending based on ball position
        if team_in_possession == self.team1:
            if ball_zone in ["forward", "forward_50"]:
                return "attacking"
            elif ball_zone in ["back", "back_50"]:
                return "defending"
        else:
            if ball_zone in ["forward", "forward_50"]:
                return "defending" 
            elif ball_zone in ["back", "back_50"]:
                return "attacking"
                
        return "transition"

    def _calculate_field_zones(self, ball_position: Tuple[float, float], play_state: str) -> Dict[str, List[Tuple[float, float]]]:
        """Calculate dynamic field zones based on play state"""
        zones = {}
        
        # Get grid coordinates
        x, y = ball_position
        grid_width = self.ground.width
        grid_length = self.ground.length
        
        # Define zone boundaries
        forward_line = int(grid_length * 0.7)
        defensive_line = int(grid_length * 0.3)
        
        # Calculate zones based on play state
        if play_state == "attacking":
            zones["scoring"] = [(x,y) for x in range(forward_line, grid_length) 
                              for y in range(grid_width)]
            zones["forward"] = [(x,y) for x in range(defensive_line, forward_line)
                              for y in range(grid_width)]
            zones["defensive"] = [(x,y) for x in range(defensive_line)
                                for y in range(grid_width)]
                                
        elif play_state == "defending":
            zones["defensive"] = [(x,y) for x in range(forward_line, grid_length)
                                for y in range(grid_width)]
            zones["forward"] = [(x,y) for x in range(defensive_line, forward_line)
                              for y in range(grid_width)]
            zones["scoring"] = [(x,y) for x in range(defensive_line)
                              for y in range(grid_width)]
                              
        # Add common zones
        zones["contest"] = [(x,y) for x in range(max(0, int(ball_position[0]-10)),
                                                min(grid_length, int(ball_position[0]+10)))
                           for y in range(max(0, int(ball_position[1]-10)),
                                        min(grid_width, int(ball_position[1]+10)))]
                                        
        zones["corridor"] = [(x,y) for x in range(grid_length)
                           for y in range(int(grid_width*0.3), int(grid_width*0.7))]
                           
        zones["wide"] = [(x,y) for x in range(grid_length)
                        for y in range(grid_width)
                        if y < grid_width*0.3 or y > grid_width*0.7]
                        
        return zones

    def _update_individual_player_position(
        self,
        player: Player,
        is_possession_team: bool,
        play_state: str,
        field_zones: Dict[str, List[Tuple[float, float]]],
        ball_position: Tuple[float, float]
    ) -> None:
        """Update individual player position based on game state"""
        if play_state == "goal":
            # Return to natural position for set shots
            player.current_position = self.tactics_engine.get_default_position(
                player.position,
                player.team_side
            )
            return

        # Handle close to ball movement
        if self._is_player_nearby(player, ball_position, 20.0):
            if is_possession_team:
                self._handle_close_teammate_movement(player, ball_position, field_zones)
            else:
                self._handle_close_opponent_movement(player, ball_position, field_zones)
            return

        # Handle distant player movement
        self._handle_distant_player_movement(player, ball_position, field_zones, is_possession_team, play_state)

    def _handle_close_teammate_movement(
        self,
        player: Player,
        ball_position: Tuple[float, float],
        field_zones: Dict[str, List[Tuple[float, float]]]
    ) -> None:
        """Handle movement for teammates close to the ball"""
        if player.position in ['FF', 'CHF', 'LF', 'RF']:
            # Forwards provide options ahead of ball
            target_zone = field_zones.get("scoring", []) or field_zones.get("forward", [])
        else:
            # Others provide support around ball
            target_zone = field_zones.get("contest", [])
            
        if target_zone:
            # Move to closest position in target zone
            target_pos = min(target_zone, 
                key=lambda pos: ((pos[0] - player.current_position[0])**2 + 
                               (pos[1] - player.current_position[1])**2)**0.5)
            player.current_position = target_pos

    def _handle_close_opponent_movement(
        self,
        player: Player,
        ball_position: Tuple[float, float],
        field_zones: Dict[str, List[Tuple[float, float]]]
    ) -> None:
        """Handle movement for opponents close to the ball"""
        # Move to contest zone to apply pressure
        target_zone = field_zones.get("contest", [])
        if target_zone:
            target_pos = min(target_zone,
                key=lambda pos: ((pos[0] - ball_position[0])**2 + 
                               (pos[1] - ball_position[1])**2)**0.5)
            player.current_position = target_pos

    def _handle_distant_player_movement(
        self,
        player: Player,
        ball_position: Tuple[float, float],
        field_zones: Dict[str, List[Tuple[float, float]]],
        is_possession_team: bool,
        play_state: str
    ) -> None:
        """Handle movement for players far from the ball"""
        # Get appropriate zone based on position and play state
        if player.position in ['FF', 'CHF', 'LF', 'RF']:
            target_zone = field_zones.get("scoring" if is_possession_team else "forward", [])
        elif player.position in ['C', 'R', 'RR']:
            target_zone = field_zones.get("forward" if is_possession_team else "contest", [])
        else:
            target_zone = field_zones.get("forward" if is_possession_team else "defensive", [])
            
        if target_zone:
            # Add some randomness to prevent bunching
            variation = 5.0
            target_pos = min(target_zone,
                key=lambda pos: ((pos[0] - player.current_position[0])**2 + 
                               (pos[1] - player.current_position[1])**2)**0.5)
            target_pos = (
                target_pos[0] + random.uniform(-variation, variation),
                target_pos[1] + random.uniform(-variation, variation)
            )
            
            # Ensure position is in bounds
            target_pos = (
                max(0, min(target_pos[0], self.ground.length)),
                max(0, min(target_pos[1], self.ground.width))
            )
            
            player.current_position = target_pos

    def _calculate_tactical_position(
        self,
        player: Player,
        phase: str,
        has_possession: bool,
        ball_position: Tuple[float, float]
    ) -> Optional[Tuple[float, float]]:
        """Calculate tactical position for player based on game state"""
        if phase == 'center_bounce':
            return self.tactics_engine.get_position_for_formation(
                player.position,
                'center_bounce',
                player.team_side
            )
        elif phase == 'contest':
            # Players move towards ball if nearby
            if self._is_player_nearby(player, ball_position, radius=20.0):
                return self._calculate_contest_position(player, ball_position)
            else:
                return self.tactics_engine.get_position_for_formation(
                    player.position,
                    'general_play',
                    player.team_side
                )
        elif phase == 'open_play':
            if has_possession:
                return self._calculate_attacking_position(player, ball_position)
            else:
                return self._calculate_defensive_position(player, ball_position)
        
        return None

    def _is_player_nearby(
        self,
        player: Player,
        position: Tuple[float, float],
        radius: float
    ) -> bool:
        """Check if player is within radius of position"""
        if not player.current_position:
            return False
            
        distance = math.sqrt(
            (player.current_position[0] - position[0])**2 +
            (player.current_position[1] - position[1])**2
        )
        return distance <= radius

    def _calculate_contest_position(
        self,
        player: Player,
        ball_position: Tuple[float, float]
    ) -> Tuple[float, float]:
        """Calculate position for contesting ball"""
        # Move towards ball but maintain some spacing
        dx = ball_position[0] - player.current_position[0]
        dy = ball_position[1] - player.current_position[1]
        distance = math.sqrt(dx*dx + dy*dy)
        
        if distance < 2.0:  # Already close enough
            return player.current_position
            
        # Move closer but maintain minimum distance
        factor = (distance - 2.0) / distance
        return (
            player.current_position[0] + dx * factor,
            player.current_position[1] + dy * factor
        )

    def _calculate_attacking_position(
        self,
        player: Player,
        ball_position: Tuple[float, float]
    ) -> Tuple[float, float]:
        """Calculate position when team has possession"""
        # Get base position from tactics
        base_pos = self.tactics_engine.get_position_for_formation(
            player.position,
            'general_play',
            player.team_side
        )
        
        # Modify based on ball position and player role
        if player.position in ['FF', 'CHF', 'LF', 'RF']:  # Forwards
            # Push forward and wide
            return (
                min(base_pos[0] + 10, self.ground.length),
                base_pos[1] + random.uniform(-10, 10)
            )
        elif player.position in ['Centre', 'Rover', 'RuckRover']:  # Midfielders
            # Support ball carrier
            return (
                ball_position[0] + random.uniform(-10, 10),
                ball_position[1] + random.uniform(-10, 10)
            )
            
        return base_pos

    def _calculate_defensive_position(
        self,
        player: Player,
        ball_position: Tuple[float, float]
    ) -> Tuple[float, float]:
        """Calculate position when defending"""
        # Get base position from tactics
        base_pos = self.tactics_engine.get_position_for_formation(
            player.position,
            'general_play',
            player.team_side
        )
        
        # Modify based on ball position and player role
        if player.position in ['FB', 'CHB', 'LB', 'RB']:  # Defenders
            # Stay deep but track ball
            return (
                base_pos[0],
                ball_position[1] + random.uniform(-5, 5)
            )
        elif player.position in ['Centre', 'Rover', 'RuckRover']:  # Midfielders
            # Press ball carrier
            return (
                ball_position[0] + random.uniform(-5, 5),
                ball_position[1] + random.uniform(-5, 5)
            )
            
        return base_pos
        
    def _move_player(self, player: Player, target_pos: Tuple[float, float], movement_type: str = 'run') -> None:
        """Initiate player movement to new position"""
        self.movement_engine.update_movement_state(player, target_pos, movement_type)
        
    def _move_ball(self, target_pos: Tuple[float, float], movement_type: str = 'ground', height: float = 0.0) -> None:
        """Move ball to new position with specified movement type"""
        if self.game_state.ball_position:
            new_pos = self.movement_engine.calculate_ball_movement(
                self.game_state.ball_position,
                target_pos,
                movement_type,
                height
            )
            self.game_state.ball_position = (new_pos[0], new_pos[1])

class ScoringEngine:
    """Handles scoring calculations and outcomes"""
    def __init__(self, ground: Ground):
        self.ground = ground
        
    def process_shot(
        self,
        player: Player,
        position: Tuple[float, float],
        game_state: Any
    ) -> Dict[str, Any]:
        """Process a shot at goal"""
        # Calculate base accuracy
        base_accuracy = 0.3 + (player.ability_stats.goal_kicking / 20)
        
        # Apply distance modifier
        distance = self.ground.get_distance_to_goal(position, player.team_side)
        distance_mod = max(0.2, 1 - (distance / 60))  # Harder from further out
        
        # Apply angle modifier
        angle = self._calculate_angle(position, player.team_side)
        angle_mod = max(0.2, 1 - (angle / 45))  # Harder from wider angles
        
        # Calculate final accuracy
        accuracy = base_accuracy * distance_mod * angle_mod
        
        # Determine outcome
        if random.random() < accuracy:
            return {'result': 'goal', 'player': player}
        elif random.random() < 0.7:  # 70% chance of behind if not goal
            return {'result': 'behind', 'player': player}
        else:
            return {'result': 'miss', 'player': player}
            
    def _calculate_angle(
        self,
        position: Tuple[float, float],
        team_side: str
    ) -> float:
        """Calculate angle to goal"""
        x, y = position
        if team_side == 'home':
            goal_x = self.ground.length
        else:
            goal_x = 0
        goal_y = self.ground.width / 2
        
        dx = abs(goal_x - x)
        dy = abs(goal_y - y)
        
        if dx == 0:
            return 90
        return math.degrees(math.atan(dy/dx))

class StatsManager:
    """Manages game statistics"""
    def __init__(self):
        self.player_stats = {}
        self.team_stats = {
            'home': self._initialize_team_stats(),
            'away': self._initialize_team_stats()
        }
        self.quarter_stats = {
            1: {'home': self._initialize_team_stats(), 'away': self._initialize_team_stats()},
            2: {'home': self._initialize_team_stats(), 'away': self._initialize_team_stats()},
            3: {'home': self._initialize_team_stats(), 'away': self._initialize_team_stats()},
            4: {'home': self._initialize_team_stats(), 'away': self._initialize_team_stats()}
        }
        
    def _initialize_team_stats(self) -> Dict[str, int]:
        """Initialize team statistics"""
        return {
            'kick': 0,
            'handball': 0,
            'mark': 0,
            'tackle': 0,
            'hitout': 0,
            'clearance': 0,
            'inside_50': 0,
            'rebound_50': 0,
            'contested_possession': 0,
            'uncontested_possession': 0,
            'goal': 0,
            'behind': 0,
            'disposal': 0,
            'effective_disposal': 0
        }
        
    def _initialize_player_stats(self, player: Player) -> Dict[str, Any]:
        """Initialize individual player statistics"""
        return {
            'player': player,
            'kick': 0,
            'handball': 0,
            'mark': 0,
            'tackle': 0,
            'hitout': 0,
            'clearance': 0,
            'inside_50': 0,
            'rebound_50': 0,
            'contested_possession': 0,
            'uncontested_possession': 0,
            'goal': 0,
            'behind': 0,
            'disposal': 0,
            'effective_disposal': 0,
            'clanger': 0,
            'free_kick_for': 0,
            'free_kick_against': 0
        }
        
    def update_stats(
        self,
        event_type: str,
        player: Player,
        team_side: str,
        quarter: int
    ) -> None:
        """Update statistics based on event"""
        # Initialize player stats if needed
        if player.id not in self.player_stats:
            self.player_stats[player.id] = self._initialize_player_stats(player)
            
        # Update player stats
        player_stats = self.player_stats[player.id]
        if event_type in player_stats:
            player_stats[event_type] += 1
            
        # Update team stats
        self.team_stats[team_side][event_type] += 1
        self.quarter_stats[quarter][team_side][event_type] += 1
        
        # Update derived stats
        if event_type in ['kick', 'handball']:
            player_stats['disposal'] += 1
            self.team_stats[team_side]['disposal'] = (
                self.team_stats[team_side]['kick'] +
                self.team_stats[team_side]['handball']
            )
            
    def collect_player_stats(
        self,
        team1_players: Dict[str, Player],
        team2_players: Dict[str, Player]
    ) -> List[Dict[str, Any]]:
        """Collect all player statistics"""
        all_stats = []
        for player_dict in [team1_players, team2_players]:
            for player in player_dict.values():
                if player.id in self.player_stats:
                    stats = self.player_stats[player.id]
                    formatted_stats = {
                        'player_name': player.name,
                        'team': player.team.name,
                        'kick': stats['kick'],
                        'handball': stats['handball'],
                        'mark': stats['mark'],
                        'tackle': stats['tackle'],
                        'hitout': stats['hitout'],
                        'clearance': stats['clearance'],
                        'inside_50': stats['inside_50'],
                        'rebound_50': stats['rebound_50'],
                        'contested_possession': stats['contested_possession'],
                        'uncontested_possession': stats['uncontested_possession'],
                        'goal': stats['goal'],
                        'behind': stats['behind'],
                        'disposal': stats['disposal'],
                        'effective_disposal': stats['effective_disposal']
                    }
                    all_stats.append(formatted_stats)
        return all_stats

    def initialize_player_stats(self, team1_players: Dict[str, Any], team2_players: Dict[str, Any]) -> None:
        """Initialize statistics for all players"""
        # Initialize home team players
        for position, player in team1_players.items():
            self.player_stats[player.id] = self._initialize_player_stats(player)

        # Initialize away team players
        for position, player in team2_players.items():
            self.player_stats[player.id] = self._initialize_player_stats(player)

class ContestEngine:
    """Handles physical contests between players"""
    def __init__(self, ground: Ground, action_engine: ActionEngine):
        self.ground = ground
        self.action_engine = action_engine
        
    def process_contest(
        self,
        players: List[Player],
        game_state: Any,
        contest_type: str = 'ground_ball'
    ) -> Dict[str, Any]:
        """Process a contest between multiple players"""
        if not players:
            return {
                'result': 'spill',
                'commentary': "Ball spills free!"
            }
            
        # Filter out ineligible players
        eligible_players = [p for p in players if self._is_eligible_for_contest(p)]
        
        if not eligible_players:
            return {
                'result': 'spill',
                'commentary': "No players can contest, ball spills!"
            }
            
        # Calculate contest ratings
        rated_players = []
        for player in eligible_players:
            rating = self._calculate_contest_rating(player, game_state, contest_type)
            rated_players.append((player, rating))
            
        # Sort by rating
        rated_players.sort(key=lambda x: x[1], reverse=True)
        
        # Determine winner
        winner = rated_players[0][0]
        win_chance = min(0.8, rated_players[0][1])  # Cap at 80%
        
        if random.random() < win_chance:
            return self._process_contest_win(winner, game_state, contest_type)
        else:
            return self._process_contest_spill(game_state)
            
    def process_marking_contest(
        self,
        target: Player,
        opponents: List[Player],
        game_state: Any,
        ball_position: Tuple[float, float]
    ) -> Dict[str, Any]:
        """Process a marking contest"""
        if not target.current_position:
            return {
                'result': 'spill',
                'commentary': "Ball spills from the contest!"
            }
            
        # Calculate marking rating for target
        target_rating = self._calculate_marking_rating(target, game_state)
        
        # Get best opponent rating
        best_opponent_rating = 0
        best_opponent = None
        
        for opponent in opponents:
            if not opponent.current_position:
                continue
                
            rating = self._calculate_marking_rating(opponent, game_state)
            if rating > best_opponent_rating:
                best_opponent_rating = rating
                best_opponent = opponent
                
        # Uncontested mark
        if not best_opponent:
            mark_chance = target_rating
            if random.random() < mark_chance:
                return {
                    'result': 'mark',
                    'player': target,
                    'commentary': f"Clean mark taken by {target.name}!"
                }
                
        # Contested mark
        else:
            # Need to win convincingly
            if target_rating > best_opponent_rating * 1.2:
                return {
                    'result': 'mark',
                    'player': target,
                    'commentary': f"Strong contested mark by {target.name}!"
                }
                
        # No mark taken
        return self._process_contest_spill(game_state)
        
    def _calculate_contest_rating(
        self,
        player: Player,
        game_state: Any,
        contest_type: str
    ) -> float:
        """Calculate player's contest rating"""
        if contest_type == 'ground_ball':
            # Ground ball contests favor agility and speed
            base_rating = (
                player.physical_stats.agility * 0.4 +
                player.physical_stats.speed * 0.3 +
                player.ability_stats.tackling * 0.2 +
                player.physical_stats.strength * 0.1
            ) / 20
        elif contest_type == 'marking':
            # Marking contests favor height and marking ability
            base_rating = (
                player.ability_stats.marking * 0.4 +
                player.physical_stats.height * 0.3 +
                player.physical_stats.strength * 0.2 +
                player.physical_stats.agility * 0.1
            ) / 20
        else:  # general contest
            # General contests balance all attributes
            base_rating = (
                player.physical_stats.strength * 0.3 +
                player.physical_stats.agility * 0.3 +
                player.ability_stats.tackling * 0.2 +
                player.ability_stats.marking * 0.2
            ) / 20
            
        # Apply fatigue modifier
        fatigue_mod = max(0.6, 1 - (player.fatigue / 100))
        
        # Apply weather effects
        weather_mod = 1.0
        if game_state.weather_system:
            mods = game_state.weather_system.get_modifiers()
            if mods['condition'] in ['wet_ground', 'heavy_rain']:
                if contest_type == 'marking':
                    weather_mod = 0.8  # Harder to mark in wet
                else:
                    weather_mod = 0.9  # Slightly harder for other contests
                    
        # Add some randomness
        random_factor = random.uniform(0.8, 1.2)
        
        return base_rating * fatigue_mod * weather_mod * random_factor
        
    def _calculate_marking_rating(self, player: Player, game_state: Any) -> float:
        """Calculate player's marking rating"""
        # Base rating from marking ability and height
        base_rating = (
            player.ability_stats.marking * 0.5 +
            player.physical_stats.height * 0.3 +
            player.physical_stats.strength * 0.2
        ) / 20
        
        # Apply fatigue modifier
        fatigue_mod = max(0.6, 1 - (player.fatigue / 100))
        
        # Apply weather effects
        weather_mod = 1.0
        if game_state.weather_system:
            mods = game_state.weather_system.get_modifiers()
            if mods['condition'] in ['wet_ground', 'heavy_rain']:
                weather_mod = 0.8  # Harder to mark in wet
                
        return base_rating * fatigue_mod * weather_mod
        
    def _is_eligible_for_contest(self, player: Player) -> bool:
        """Check if player is eligible to contest"""
        if not player.current_position:
            return False
            
        # Check if player has been in recent contest
        for action in player.recent_actions:
            if action['type'] in ['contest_win', 'contest_lose']:
                if time.time() - action['time'] < 1:  # Within last second
                    return False
                    
        return True
        
    def _process_contest_win(
        self,
        winner: Player,
        game_state: Any,
        contest_type: str
    ) -> Dict[str, Any]:
        """Process a successful contest win"""
        # Update player state
        winner.add_action('contest_win')
        winner.fatigue += 5
        
        # Update game state
        game_state.ball_carrier = winner
        game_state.ball_position = winner.current_position
        game_state.phase = 'open_play'
        
        # Generate commentary
        if contest_type == 'ground_ball':
            commentary = random.choice([
                f"{winner.name} emerges with the ball!",
                f"Clean gather by {winner.name}!",
                f"{winner.name} wins the ground ball!",
                f"Good hands in close from {winner.name}!"
            ])
        else:
            commentary = random.choice([
                f"{winner.name} wins the contest!",
                f"Strong work from {winner.name} to win possession!",
                f"{winner.name} comes away with it!",
                f"Brilliant contest work from {winner.name}!"
            ])
            
        return {
            'result': 'win',
            'player': winner,
            'commentary': commentary
        }
        
    def _process_contest_spill(self, game_state: Any) -> Dict[str, Any]:
        """Process a contest resulting in spilled ball"""
        if not game_state.ball_position:
            return {
                'result': 'error',
                'commentary': "Invalid ball position"
            }
            
        # Calculate spill position (1-3m deviation)
        spill_x = game_state.ball_position[0] + random.uniform(-3, 3)
        spill_y = game_state.ball_position[1] + random.uniform(-3, 3)
        
        # Ensure position is in bounds
        spill_x = max(0, min(spill_x, self.ground.length))
        spill_y = max(0, min(spill_y, self.ground.width))
        
        # Update game state
        game_state.ball_carrier = None
        game_state.ball_position = (spill_x, spill_y)
        game_state.phase = 'contest'
        
        return {
            'result': 'spill',
            'commentary': random.choice([
                "Ball spills free from the contest!",
                "No clean possession taken!",
                "It'll be another contest!",
                "Players scramble for the loose ball!"
            ])
        }

class PhaseEngine:
    """Handles game phase transitions and logic"""
    def __init__(
        self,
        ground: Ground,
        action_engine: ActionEngine,
        contest_engine: ContestEngine
    ):
        self.ground = ground
        self.action_engine = action_engine
        self.contest_engine = contest_engine
        self.ruck_engine = RuckEngine(ground)  # Initialize RuckEngine
        self.event_manager = EventManager(None, None)  # For creating events
        self.decision_engine = DecisionEngine(ground, action_engine)  # Pass action_engine
        self.disposal_engine = DisposalEngine(ground, action_engine)  # Pass action_engine
        
    async def process_phase(
        self,
        game_state: Any
    ) -> Optional[Dict[str, Any]]:
        """Process the current phase of play"""
        if game_state.phase == 'center_bounce':
            return await self._process_center_bounce(game_state)
        elif game_state.phase == 'contest':
            return await self._process_contest_phase(game_state)
        elif game_state.phase == 'open_play':
            return await self._process_open_play_phase(game_state)
        elif game_state.phase == 'set_shot':
            return await self._process_set_shot_phase(game_state)
        elif game_state.phase == 'throw_in':
            return await self._process_throw_in_phase(game_state)
        elif game_state.phase == 'kick_in':
            return await self._process_kick_in_phase(game_state)
        elif game_state.phase == 'loose_ball':
            return await self._process_loose_ball_phase(game_state)
        return None
        
    async def _process_center_bounce(self, game_state: Any) -> Dict[str, Any]:
        """Process center bounce"""
        # Process center bounce using RuckEngine
        ruck_result = self.ruck_engine.process_center_bounce(game_state)
        print(f"Result from Ruck Contest {ruck_result}")
        
        # After center bounce, transition to contest or open play based on result
        if ruck_result.get('target'):
            game_state.phase = 'open_play'
        else:
            game_state.phase = 'contest'
        
        # Format the result as a proper event
        return self.event_manager.create_event(
            'center_bounce',
            game_state.quarter,
            'match_event',
            ruck1=ruck_result['winner'].name,  # Use player names instead of objects
            ruck2=ruck_result.get('loser', 'Unknown').name if ruck_result.get('loser') else 'Unknown',
            winner=ruck_result['winner'].name,
            direction=ruck_result['direction'],
            target=ruck_result['target'].name if ruck_result.get('target') else None,
            landing_pos=ruck_result['landing_pos'],
            commentary=ruck_result['commentary']
        )
        
    async def _process_contest_phase(self, game_state: Any) -> Dict[str, Any]:
        """Process contest phase"""
        print("Hit Contest Phase")
        nearby_players = self._get_players_near_ball(game_state)
        
        if not nearby_players:
            game_state.phase = 'loose_ball'
            return self.event_manager.create_event(
                'loose_ball',
                game_state.quarter,
                'match_event',
                commentary="Ball spills into space!"
            )
            
        contest_result = self.contest_engine.process_contest(
            nearby_players,
            game_state
        )
        print(f"Contest Result {contest_result}")
        if contest_result['result'] == 'gather':
            game_state.ball_carrier = contest_result['player']
            game_state.phase = 'open_play'
            
        return self.event_manager.create_event(
            contest_result['result'],
            game_state.quarter,
            'match_event',
            player=contest_result['player'].name if contest_result.get('player') else None,
            commentary=contest_result['commentary']
        )
        
    async def _process_open_play_phase(self, game_state: Any) -> Dict[str, Any]:
        """Process open play phase"""
        print("Open Play Phase")

        if not game_state.ball_carrier:
            game_state.phase = 'contest'
            return None
            
        # Store ball carrier name before processing action
        ball_carrier_name = game_state.ball_carrier.name
            
        # Process player action
        action = self._decide_player_action(game_state.ball_carrier, game_state)
        action_result = await self._process_action(action, game_state)
        print (f"Action Result {action_result}")
        if action_result:
            return self.event_manager.create_event(
                action_result['type'],
                game_state.quarter,
                'match_event',
                player=action_result.get('player', ball_carrier_name),
                target=action_result.get('target').name if action_result.get('target') else None,
                result=action_result.get('result'),
                commentary=action_result.get('commentary')
            )
        return None
        
    async def _process_set_shot_phase(self, game_state: Any) -> Dict[str, Any]:
        """Process set shot phase"""
        if not game_state.ball_carrier:
            game_state.phase = 'contest'
            return None
            
        # Process the set shot attempt
        shot_result = self.scoring_engine.process_shot(
            game_state.ball_carrier,
            game_state.ball_carrier.current_position,
            game_state
        )
        
        # Reset phase after shot
        game_state.phase = 'center_bounce' if shot_result['type'] in ['goal', 'behind'] else 'contest'
        
        return self.event_manager.create_event(
            shot_result['type'],
            game_state.quarter,
            'match_event',
            player=game_state.ball_carrier.name,
            result=shot_result['result'],
            commentary=shot_result['commentary']
        )
        
    async def _process_throw_in_phase(self, game_state: Any) -> Dict[str, Any]:
        """Process throw in phase"""
        # Get rucks from each team
        team1_ruck = self._get_nearest_ruck(game_state.ball_position, game_state.team1_players)
        team2_ruck = self._get_nearest_ruck(game_state.ball_position, game_state.team2_players)
        
        if not team1_ruck or not team2_ruck:
            game_state.phase = 'contest'
            return None
            
        # Process the ruck contest
        ruck_result = self.ruck_engine.process_ball_up(
            game_state.ball_position,
            game_state
        )
        
        # Update phase based on result
        game_state.phase = 'open_play' if ruck_result['result'] == 'clean_tap' else 'contest'
        
        return self.event_manager.create_event(
            'throw_in',
            game_state.quarter,
            'match_event',
            player=ruck_result['winner'].name if ruck_result.get('winner') else None,
            result=ruck_result['result'],
            commentary=ruck_result['commentary']
        )
        
    async def _process_kick_in_phase(self, game_state: Any) -> Dict[str, Any]:
        """Process kick in phase"""
        if not game_state.ball_carrier:
            game_state.phase = 'contest'
            return None
            
        # Get the kicker's team side
        kicker_team_side = game_state.ball_carrier.team.side
        
        # Find a target player for the kick in
        disposal_options = self.decision_engine._evaluate_disposal_options(
            game_state.ball_carrier,
            game_state
        )
        
        # Process the kick in
        if disposal_options:
            best_option = max(disposal_options, key=lambda x: x['rating'])
            kick_result = self.disposal_engine.process_disposal(
                game_state.ball_carrier,
                'kick',
                best_option['target'],
                game_state
            )
        else:
            # If no good options, kick to space
            kick_result = self.disposal_engine._process_clearing_kick(
                game_state.ball_carrier,
                game_state
            )
            
        # Update phase
        game_state.phase = 'contest'
        
        return self.event_manager.create_event(
            'kick_in',
            game_state.quarter,
            'match_event',
            player=game_state.ball_carrier.name,
            target=kick_result.get('target').name if kick_result.get('target') else None,
            result=kick_result['result'],
            commentary=kick_result['commentary']
        )
        
    async def _process_loose_ball_phase(self, game_state: Any) -> Dict[str, Any]:
        """Process loose ball phase"""
        # Get players near the ball
        nearby_players = self._get_players_near_ball(game_state, radius=8.0)  # Larger radius for loose ball
        
        if not nearby_players:
            # Ball continues to spill, update its position slightly
            spill_distance = 2.0  # Meters
            angle = random.uniform(0, 2 * math.pi)
            new_x = game_state.ball_position[0] + spill_distance * math.cos(angle)
            new_y = game_state.ball_position[1] + spill_distance * math.sin(angle)
            
            # Ensure new position is in bounds
            if self.ground.is_in_bounds((new_x, new_y)):
                game_state.ball_position = (new_x, new_y)
                
            return self.event_manager.create_event(
                'loose_ball',
                game_state.quarter,
                'match_event',
                commentary="Ball continues to spill..."
            )
            
        # Rate each player's chance to gather the ball
        player_ratings = []
        for player in nearby_players:
            # Calculate base rating from player stats
            rating = (
                player.physical_stats.agility * 0.4 +
                player.physical_stats.speed * 0.3 +
                player.ability_stats.tackling * 0.3
            )
            
            # Apply distance modifier
            distance = math.sqrt(
                (player.current_position[0] - game_state.ball_position[0])**2 +
                (player.current_position[1] - game_state.ball_position[1])**2
            )
            distance_modifier = max(0, 1 - (distance / 8.0))  # Linear falloff with distance
            rating *= distance_modifier
            
            player_ratings.append({
                'player': player,
                'rating': rating
            })
            
        # Sort by rating and get the best player
        player_ratings.sort(key=lambda x: x['rating'], reverse=True)
        best_player = player_ratings[0]['player']
        
        # 70% chance for best player to gather the ball
        if random.random() < 0.7:
            game_state.ball_carrier = best_player
            game_state.phase = 'open_play'
            
            return self.event_manager.create_event(
                'gather',
                game_state.quarter,
                'match_event',
                player=best_player.name,
                commentary=f"{best_player.name} gathers the loose ball!"
            )
        else:
            # Failed to gather, stays in loose_ball phase
            return self.event_manager.create_event(
                'loose_ball',
                game_state.quarter,
                'match_event',
                player=best_player.name,
                commentary=f"{best_player.name} fumbles the gather attempt!"
            )
        
    def _get_players_near_ball(self, game_state: Any, radius: float = 5.0) -> List[Player]:
        """Get all players within radius of ball"""
        if not game_state.ball_position:
            return []
            
        nearby_players = []
        for team_players in [game_state.team1_players, game_state.team2_players]:
            for player in team_players.values():
                if not player.current_position:
                    continue
                    
                distance = math.sqrt(
                    (player.current_position[0] - game_state.ball_position[0])**2 +
                    (player.current_position[1] - game_state.ball_position[1])**2
                )
                
                if distance <= radius:
                    nearby_players.append(player)
                    
        return nearby_players
        
    def _decide_player_action(self, player: Player, game_state: Any) -> Dict[str, Any]:
        """Decide what action a player should take"""
        # Calculate pressure on the player using action_engine
        pressure = self.action_engine.calculate_pressure(player, game_state)
        
        # Get distance to goal
        distance_to_goal = self.ground.get_distance_to_goal(
            player.current_position,
            player.team.side
        )
        
        # Check if in scoring position
        if distance_to_goal < 45 and self.disposal_engine._is_scoring_position(player, game_state):
            return {
                'type': 'shot_at_goal',
                'player': player,
                'pressure': pressure
            }
            
        # If under high pressure, make a quick disposal
        if pressure > 0.7:
            disposal_options = self.decision_engine._evaluate_disposal_options(player, game_state)
            if disposal_options:
                best_option = max(disposal_options, key=lambda x: x['rating'])
                return {
                    'type': best_option['disposal_type'],
                    'player': player,
                    'target': best_option['target'],
                    'pressure': pressure
                }
            else:
                return {
                    'type': 'blind_handball',
                    'player': player,
                    'pressure': pressure
                }
                
        # Normal play - evaluate disposal options
        disposal_options = self.decision_engine._evaluate_disposal_options(player, game_state)
        if disposal_options:
            best_option = max(disposal_options, key=lambda x: x['rating'])
            return {
                'type': best_option['disposal_type'],
                'player': player,
                'target': best_option['target'],
                'pressure': pressure
            }
            
        # If no good options, kick to space
        return {
            'type': 'clearing_kick',
            'player': player,
            'pressure': pressure
        }
        
    async def _process_action(self, action: Dict[str, Any], game_state: Any) -> Dict[str, Any]:
        """Process a player action"""
        if not action:
            print("No action")
            return None
            
        action_type = action['type']
        print(f"Processing action: {action_type}")
        player = action['player']
        
        # Process different action types
        if action_type == 'shot_at_goal':
            return self.scoring_engine.process_shot(
                player,
                player.current_position,
                game_state
            )
            
        elif action_type in ['kick', 'handball']:
            return self.disposal_engine.process_disposal(
                player,
                action_type,
                action.get('target'),
                game_state
            )
            
        elif action_type == 'blind_handball':
            return self.disposal_engine._process_handball(
                player,
                None,  # No target for blind handball
                game_state
            )
            
        elif action_type == 'clearing_kick':
            return self.disposal_engine._process_clearing_kick(
                player,
                game_state
            )
            
        # If action type not recognized, return None
        return None

class DecisionEngine:
    """Handles player decision making"""
    def __init__(self, ground: Ground, action_engine: ActionEngine):
        self.ground = ground
        self.action_engine = action_engine  # Store reference to action_engine
        
    def _calculate_pressure(self, player: Player, game_state: Any) -> float:
        """Use action_engine's pressure calculation"""
        return self.action_engine.calculate_pressure(player, game_state)
        
    def decide_player_action(
        self,
        player: Player,
        game_state: Any
    ) -> Dict[str, Any]:
        """Decide what action a player should take"""
        # Calculate base decision threshold based on mental stats
        decision_threshold = 0.5 - (player.ability_stats.tactical / 20)  # Better decision making = more likely to make good choices
        
        # Calculate pressure
        pressure = self._calculate_pressure(player, game_state)
        
        # Get scoring opportunity rating if in range
        scoring_chance = self._evaluate_scoring_chance(player, game_state)
        
        # Get disposal options
        disposal_options = self._evaluate_disposal_options(player, game_state)
        
        # Decide action based on context
        if pressure > 0.8:  # Very high pressure
            return self._decide_under_pressure(player, game_state, pressure)
        elif scoring_chance > 0.6:  # Good scoring opportunity
            return self._decide_scoring_opportunity(player, game_state, scoring_chance)
        elif disposal_options:  # Has good disposal options
            return self._decide_disposal(player, game_state, disposal_options)
        else:  # Default to clearing kick
            return self._decide_clearing_action(player, game_state)
            
    def _evaluate_scoring_chance(self, player: Player, game_state: Any) -> float:
        """Evaluate chance of scoring from current position"""
        if not player.current_position:
            return 0.0
            
        # Calculate distance to goal
        distance = self.ground.get_distance_to_goal(player.current_position, player.team_side)
        
        # Too far out
        if distance > 60:
            return 0.0
            
        # Calculate angle
        x, y = player.current_position
        goal_x = self.ground.length if player.team_side == 'home' else 0
        goal_y = self.ground.width / 2
        dx = abs(goal_x - x)
        dy = abs(goal_y - y)
        angle = math.degrees(math.atan2(dy, dx))
        
        # Base chance from goal kicking ability
        base_chance = player.ability_stats.goal_kicking / 20
        
        # Apply modifiers
        distance_mod = max(0.2, 1 - (distance / 60))  # Further = harder
        angle_mod = max(0.2, 1 - (angle / 45))  # Wider = harder
        
        return base_chance * distance_mod * angle_mod
        
    def _evaluate_disposal_options(
        self,
        player: Player,
        game_state: Any
    ) -> List[Dict[str, Any]]:
        """Evaluate possible disposal options"""
        if not player.current_position:
            return []
            
        options = []
        teammates = game_state.team1_players if player.team_side == 'home' else game_state.team2_players
        
        for teammate in teammates.values():
            if teammate.id == player.id or not teammate.current_position:
                continue
                
            # Calculate distance
            distance = math.sqrt(
                (player.current_position[0] - teammate.current_position[0])**2 +
                (player.current_position[1] - teammate.current_position[1])**2
            )
            
            # Skip if too close or too far
            if distance < 5 or distance > 50:
                continue
                
            # Calculate option rating
            rating = self._rate_disposal_option(player, teammate, game_state)
            
            if rating > 0.3:  # Minimum threshold for viable option
                options.append({
                    'player': teammate,
                    'distance': distance,
                    'rating': rating,
                    'type': 'kick' if distance > 15 else 'handball'
                })
                
        # Sort by rating
        options.sort(key=lambda x: x['rating'], reverse=True)
        return options
        
    def _rate_disposal_option(
        self,
        disposer: Player,
        target: Player,
        game_state: Any
    ) -> float:
        """Rate a disposal option"""
        if not disposer.current_position or not target.current_position:
            return 0.0
            
        # Base rating from relevant stats
        if self._is_long_kick_distance(disposer.current_position, target.current_position):
            base_rating = (disposer.ability_stats.kicking + target.ability_stats.marking) / 40
        else:
            base_rating = (disposer.ability_stats.handball + target.ability_stats.handball) / 40
            
        # Adjust for target's space
        target_pressure = self._calculate_pressure(target, game_state)
        space_mod = 1 - target_pressure
        
        # Adjust for forward progress
        progress_mod = self._calculate_forward_progress(disposer, target)
        
        # Combine ratings
        return base_rating * space_mod * progress_mod
        
    def _is_long_kick_distance(
        self,
        pos1: Tuple[float, float],
        pos2: Tuple[float, float]
    ) -> bool:
        """Check if distance requires a kick rather than handball"""
        dx = pos2[0] - pos1[0]
        dy = pos2[1] - pos1[1]
        distance = math.sqrt(dx*dx + dy*dy)
        return distance > 15  # More than 15 meters needs a kick
        
    def _calculate_forward_progress(self, disposer: Player, target: Player) -> float:
        """Calculate how much forward progress the disposal makes"""
        if not disposer.current_position or not target.current_position:
            return 0.5  # Neutral if positions unknown
            
        # Calculate progress towards goal
        if disposer.team_side == 'home':
            disposer_to_goal = self.ground.length - disposer.current_position[0]
            target_to_goal = self.ground.length - target.current_position[0]
        else:
            disposer_to_goal = disposer.current_position[0]
            target_to_goal = target.current_position[0]
            
        progress = disposer_to_goal - target_to_goal
        
        # Normalize to 0-1 range
        return max(0.3, min(1.0, 0.7 + progress/30))
        
    def _decide_under_pressure(
        self,
        player: Player,
        game_state: Any,
        pressure: float
    ) -> Dict[str, Any]:
        """Decide action when under high pressure"""
        # Check if snap shot at goal is viable
        if self._evaluate_scoring_chance(player, game_state) > 0.4:
            return {
                'action': 'snap_shot',
                'target': None,
                'commentary': f"{player.name} goes for a quick snap under pressure!"
            }
            
        # Look for close handball option
        disposal_options = self._evaluate_disposal_options(player, game_state)
        close_options = [opt for opt in disposal_options if opt['distance'] < 10]
        
        if close_options:
            best_option = close_options[0]
            return {
                'action': 'handball',
                'target': best_option['player'],
                'commentary': f"{player.name} gets a quick handball away!"
            }
            
        # Default to clearing kick under pressure
        return {
            'action': 'clearing_kick',
            'target': None,
            'commentary': f"{player.name} kicks under pressure!"
        }
        
    def _decide_scoring_opportunity(
        self,
        player: Player,
        game_state: Any,
        scoring_chance: float
    ) -> Dict[str, Any]:
        """Decide action for scoring opportunity"""
        if scoring_chance > 0.7:  # Very good chance
            return {
                'action': 'set_shot',
                'target': None,
                'commentary': f"{player.name} lines up for goal!"
            }
        else:  # Moderate chance
            # Check if better option available
            disposal_options = self._evaluate_disposal_options(player, game_state)
            better_options = [
                opt for opt in disposal_options
                if self._evaluate_scoring_chance(opt['player'], game_state) > scoring_chance + 0.2
            ]
            
            if better_options:
                best_option = better_options[0]
                return {
                    'action': best_option['type'],
                    'target': best_option['player'],
                    'commentary': f"{player.name} looks for a better option!"
                }
                
            # Take the shot if no better options
            return {
                'action': 'set_shot',
                'target': None,
                'commentary': f"{player.name} decides to have a shot!"
            }
            
    def _decide_disposal(
        self,
        player: Player,
        game_state: Any,
        disposal_options: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Decide best disposal option"""
        best_option = disposal_options[0]
        
        return {
            'action': best_option['type'],
            'target': best_option['player'],
            'commentary': f"{player.name} looks to {best_option['type']} to {best_option['player'].name}!"
        }
        
    def _decide_clearing_action(
        self,
        player: Player,
        game_state: Any
    ) -> Dict[str, Any]:
        """Decide action when no good options available"""
        # Check if bounce is viable
        if self._calculate_pressure(player, game_state) < 0.4:
            return {
                'action': 'bounce',
                'target': None,
                'commentary': f"{player.name} takes on the defender!"
            }
            
        # Default to clearing kick
        return {
            'action': 'clearing_kick',
            'target': None,
            'commentary': f"{player.name} kicks clear!"
        }

class TackleEngine:
    """Handles tackling and physical contests"""
    def __init__(self, ground: Ground, action_engine: ActionEngine):
        self.ground = ground
        self.action_engine = action_engine
        
    def process_tackle_attempt(
        self,
        tackler: Player,
        target: Player,
        game_state: Any
    ) -> Dict[str, Any]:
        """Process a tackle attempt between two players"""
        if not tackler.current_position or not target.current_position:
            return {
                'result': 'miss',
                'commentary': f"{tackler.name} misses the tackle!"
            }
            
        # Calculate distance between players
        distance = self._calculate_distance(tackler.current_position, target.current_position)
        
        # Only allow tackles within 2 meters
        if distance > 2:
            return {
                'result': 'miss',
                'commentary': f"{tackler.name} can't quite reach {target.name}!"
            }
            
        # Calculate base tackle success chance
        base_chance = self._calculate_base_chance(tackler, target)
        
        # Apply modifiers
        modified_chance = self._apply_modifiers(base_chance, tackler, target, game_state)
        
        # Determine outcome
        if random.random() < modified_chance:
            # Successful tackle
            return self._process_successful_tackle(tackler, target, game_state)
        else:
            # Failed tackle
            return self._process_failed_tackle(tackler, target, game_state)
            
    def should_attempt_tackle(
        self,
        player: Player,
        opponent: Player,
        game_state: Any
    ) -> bool:
        """Determine if player should attempt a tackle"""
        if not player.current_position or not opponent.current_position:
            return False
            
        # Calculate distance
        distance = self._calculate_distance(player.current_position, opponent.current_position)
        
        # Must be within tackle range
        if distance > 2:
            return False
            
        # More likely to tackle if opponent has ball
        if opponent == game_state.ball_carrier:
            base_chance = 0.8
        else:
            base_chance = 0.2
            
        # Modify based on player attributes
        tackle_rating = player.ability_stats.tackling / 20
        aggression_mod = player.ability_stats.aggression / 20 if hasattr(player.ability_stats, 'aggression') else 0.5
        
        # Fatigue reduces tackle attempts
        fatigue_mod = max(0.3, 1 - (player.fatigue / 100))
        
        # Calculate final chance
        attempt_chance = base_chance * tackle_rating * aggression_mod * fatigue_mod
        
        return random.random() < attempt_chance
        
    def _calculate_distance(
        self,
        pos1: Tuple[float, float],
        pos2: Tuple[float, float]
    ) -> float:
        """Calculate distance between two positions"""
        dx = pos2[0] - pos1[0]
        dy = pos2[1] - pos1[1]
        return math.sqrt(dx*dx + dy*dy)
        
    def _calculate_base_chance(self, tackler: Player, target: Player) -> float:
        """Calculate base tackle success chance"""
        # Base chance from tackling ability
        base_chance = tackler.ability_stats.tackling / 20
        
        # Modify based on strength differential
        strength_diff = tackler.physical_stats.strength - target.physical_stats.strength
        strength_mod = max(0.5, 1 + (strength_diff / 40))  # Max 50% bonus/penalty
        
        return base_chance * strength_mod
        
    def _apply_modifiers(
        self,
        base_chance: float,
        tackler: Player,
        target: Player,
        game_state: Any
    ) -> float:
        """Apply modifiers to tackle success chance"""
        modified_chance = base_chance
        
        # Speed differential - faster players harder to tackle
        speed_diff = target.physical_stats.speed - tackler.physical_stats.speed
        speed_mod = max(0.5, 1 - (speed_diff / 40))  # Max 50% reduction
        modified_chance *= speed_mod
        
        # Fatigue factors
        tackler_fatigue_mod = max(0.6, 1 - (tackler.fatigue / 100))
        target_fatigue_mod = max(0.6, 1 - (target.fatigue / 100))
        modified_chance *= (tackler_fatigue_mod / target_fatigue_mod)
        
        # Agility factor - more agile players better at avoiding tackles
        agility_diff = target.physical_stats.agility - tackler.physical_stats.agility
        agility_mod = max(0.5, 1 - (agility_diff / 40))
        modified_chance *= agility_mod
        
        # Weather effects
        if game_state.weather_system:
            weather_mods = game_state.weather_system.get_modifiers()
            if weather_mods['condition'] in ['wet_ground', 'heavy_rain']:
                modified_chance *= 1.2  # Easier to tackle in wet conditions
                
        return min(modified_chance, 0.95)  # Cap at 95% chance
        
    def _process_successful_tackle(
        self,
        tackler: Player,
        target: Player,
        game_state: Any
    ) -> Dict[str, Any]:
        """Handle successful tackle outcome"""
        # Update stats
        tackler.add_action('tackle')
        target.add_action('tackled')
        
        # Increase fatigue
        tackler.fatigue += 5
        target.fatigue += 8
        
        # Ball spills free
        if game_state.ball_carrier == target:
            game_state.ball_carrier = None
            game_state.phase = 'loose_ball'
            
            # Calculate spill position
            spill_x = target.current_position[0] + random.uniform(-2, 2)
            spill_y = target.current_position[1] + random.uniform(-2, 2)
            game_state.ball_position = (spill_x, spill_y)
            
        return {
            'result': 'success',
            'commentary': random.choice([
                f"Brilliant tackle by {tackler.name}!",
                f"{tackler.name} wraps up {target.name} in a perfect tackle!",
                f"Strong tackle from {tackler.name}!",
                f"{target.name} is caught in a bone-crunching tackle by {tackler.name}!"
            ])
        }
        
    def _process_failed_tackle(
        self,
        tackler: Player,
        target: Player,
        game_state: Any
    ) -> Dict[str, Any]:
        """Handle failed tackle outcome"""
        # Update stats
        tackler.add_action('missed_tackle')
        
        # Increase fatigue
        tackler.fatigue += 8  # More fatiguing to miss
        target.fatigue += 3
        
        # Determine if holding the ball
        if game_state.ball_carrier == target:
            # Check for holding the ball
            if self._is_holding_the_ball(target, game_state):
                return {
                    'result': 'holding_the_ball',
                    'commentary': f"Holding the ball against {target.name}!"
                }
                
        return {
            'result': 'miss',
            'commentary': random.choice([
                f"{target.name} breaks free from {tackler.name}'s tackle attempt!",
                f"{tackler.name} misses the tackle on {target.name}!",
                f"Great evasion by {target.name} to avoid {tackler.name}'s tackle!",
                f"{target.name} slips through {tackler.name}'s grasp!"
            ])
        }
        
    def _is_holding_the_ball(self, player: Player, game_state: Any) -> bool:
        """Determine if player should be called for holding the ball"""
        if not player.current_position or player != game_state.ball_carrier:
            return False
            
        # Check prior opportunity
        had_prior = any(
            action['type'] == 'receive' and
            time.time() - action['time'] > 2.0
            for action in player.recent_actions
        )
        
        if not had_prior:
            return False
            
        # Check if player tried to dispose
        attempted_disposal = any(
            action['type'] in ['kick', 'handball'] and
            time.time() - action['time'] < 0.5
            for action in player.recent_actions
        )
        
        return not attempted_disposal 

class DisposalEngine:
    """Handles all ball disposal mechanics (kicks, handballs, marking)"""
    def __init__(self, ground: Ground, action_engine: ActionEngine):
        self.ground = ground
        self.action_engine = action_engine  # Store reference to action_engine
        
    def process_disposal(
        self,
        disposer: Player,
        disposal_type: str,
        target: Optional[Player] = None,
        game_state: Any = None
    ) -> Dict[str, Any]:
        """Process a disposal attempt"""
        if disposal_type == 'kick':
            return self._process_kick(disposer, target, game_state)
        elif disposal_type == 'handball':
            return self._process_handball(disposer, target, game_state)
        elif disposal_type == 'snap_shot':
            return self._process_snap_shot(disposer, game_state)
        elif disposal_type == 'clearing_kick':
            return self._process_clearing_kick(disposer, game_state)
        else:
            return {
                'result': 'error',
                'commentary': f"Unknown disposal type: {disposal_type}"
            }
            
    def _process_kick(
        self,
        kicker: Player,
        target: Optional[Player],
        game_state: Any
    ) -> Dict[str, Any]:
        """Process a kick to a target player"""
        if not kicker.current_position:
            return {'result': 'error', 'commentary': "Invalid kicker position"}
            
        # Calculate base accuracy
        base_accuracy = kicker.ability_stats.kicking / 20
        
        # Apply modifiers
        pressure = self._calculate_pressure(kicker, game_state)
        weather_mod = self._get_weather_modifier(game_state, 'kicking')
        fatigue_mod = max(0.6, 1 - (kicker.fatigue / 100))
        
        # Calculate final accuracy
        accuracy = base_accuracy * (1 - pressure * 0.5) * weather_mod * fatigue_mod
        
        # If no target, kick to space
        if not target or not target.current_position:
            return self._kick_to_space(kicker, accuracy, game_state)
            
        # Calculate kick destination with deviation based on accuracy
        kick_distance = self._calculate_distance(kicker.current_position, target.current_position)
        if kick_distance > self._get_max_kick_distance(kicker):
            return {
                'result': 'error',
                'commentary': f"{kicker.name} tries to kick beyond their range!"
            }
            
        landing_pos = self._calculate_landing_position(
            kicker.current_position,
            target.current_position,
            accuracy
        )
        
        # Update game state
        game_state.ball_carrier = None
        game_state.ball_position = landing_pos
        
        # Process marking contest if applicable
        if self._is_marking_distance(kick_distance):
            mark_result = self._process_marking_contest(target, landing_pos, game_state)
            if mark_result['result'] == 'mark':
                game_state.ball_carrier = target
                game_state.phase = 'set_shot' if self._is_scoring_position(target, game_state) else 'open_play'
                return mark_result
                
        # No mark, ball becomes contested
        game_state.phase = 'contest'
        
        return {
            'result': 'kick',
            'landing_pos': landing_pos,
            'commentary': random.choice([
                f"{kicker.name} sends it long towards {target.name}!",
                f"Good kick from {kicker.name} looking for {target.name}!",
                f"{kicker.name} launches it in {target.name}'s direction!"
            ])
        }
        
    def _process_handball(
        self,
        handballer: Player,
        target: Optional[Player],
        game_state: Any
    ) -> Dict[str, Any]:
        """Process a handball attempt"""
        if not handballer.current_position or not target or not target.current_position:
            return {'result': 'error', 'commentary': "Invalid handball positions"}
            
        # Calculate base accuracy
        base_accuracy = handballer.ability_stats.handball / 20
        
        # Apply modifiers
        pressure = self._calculate_pressure(handballer, game_state)
        weather_mod = self._get_weather_modifier(game_state, 'handling')
        fatigue_mod = max(0.6, 1 - (handballer.fatigue / 100))
        
        # Calculate final accuracy
        accuracy = base_accuracy * (1 - pressure * 0.7) * weather_mod * fatigue_mod
        
        # Check distance
        distance = self._calculate_distance(handballer.current_position, target.current_position)
        if distance > 10:  # Max handball distance 10m
            return {
                'result': 'error',
                'commentary': f"{handballer.name}'s handball falls short!"
            }
            
        # Calculate landing position with deviation
        landing_pos = self._calculate_landing_position(
            handballer.current_position,
            target.current_position,
            accuracy,
            is_handball=True
        )
        
        # Determine if target receives cleanly
        receive_chance = accuracy * (target.ability_stats.handball / 20)
        if random.random() < receive_chance:
            game_state.ball_carrier = target
            game_state.phase = 'open_play'
            return {
                'result': 'clean_receive',
                'commentary': random.choice([
                    f"Clean hands from {target.name}!",
                    f"Slick handball received by {target.name}!",
                    f"Good grab by {target.name}!"
                ])
            }
        else:
            game_state.ball_carrier = None
            game_state.ball_position = landing_pos
            game_state.phase = 'contest'
            return {
                'result': 'spill',
                'commentary': random.choice([
                    f"{target.name} fumbles the handball!",
                    f"Handball goes to ground!",
                    f"Messy handball leads to a contest!"
                ])
            }
            
    def _process_snap_shot(
        self,
        shooter: Player,
        game_state: Any
    ) -> Dict[str, Any]:
        """Process a snap shot at goal"""
        if not shooter.current_position:
            return {'result': 'error', 'commentary': "Invalid shooter position"}
            
        # Calculate base accuracy
        base_accuracy = shooter.ability_stats.goal_kicking / 20
        
        # Apply modifiers
        pressure = self._calculate_pressure(shooter, game_state)
        weather_mod = self._get_weather_modifier(game_state, 'kicking')
        fatigue_mod = max(0.6, 1 - (shooter.fatigue / 100))
        
        # Snaps are harder but less affected by pressure
        accuracy = (base_accuracy * 0.8) * (1 - pressure * 0.3) * weather_mod * fatigue_mod
        
        # Calculate goal probability
        distance = self.ground.get_distance_to_goal(shooter.current_position, shooter.team_side)
        angle = self._calculate_shot_angle(shooter.current_position, shooter.team_side)
        
        goal_chance = accuracy * (1 - distance/60) * (1 - angle/90)
        
        # Determine outcome
        if random.random() < goal_chance:
            game_state.update_score(shooter.team_side, 'goal')
            return {
                'result': 'goal',
                'commentary': random.choice([
                    f"GOAL! Brilliant snap from {shooter.name}!",
                    f"{shooter.name} threads it through from the snap!",
                    f"What a finish by {shooter.name}!"
                ])
            }
        elif random.random() < 0.7:  # 70% chance of behind if not goal
            game_state.update_score(shooter.team_side, 'behind')
            return {
                'result': 'behind',
                'commentary': random.choice([
                    f"Just a behind from the snap shot.",
                    f"{shooter.name}'s snap drifts wide for a behind.",
                    f"Close but not quite from {shooter.name}."
                ])
            }
        else:
            return {
                'result': 'miss',
                'commentary': random.choice([
                    f"Complete miss from {shooter.name}!",
                    f"That snap shot was never close.",
                    f"{shooter.name}'s snap goes out on the full."
                ])
            }
            
    def _process_clearing_kick(
        self,
        kicker: Player,
        game_state: Any
    ) -> Dict[str, Any]:
        """Process a clearing kick"""
        if not kicker.current_position:
            print("Invalid kicker position, should not happen")
            sys.exit()
            
        # Calculate base accuracy
        base_accuracy = kicker.ability_stats.kicking / 20
        
        # Apply modifiers
        pressure = self.action_engine.calculate_pressure(kicker, game_state)  # Use public method
        weather_mod = self._get_weather_modifier(game_state, 'kicking')
        fatigue_mod = max(0.6, 1 - (kicker.fatigue / 100))
        
        # Clearing kicks are less accurate
        accuracy = (base_accuracy * 0.7) * (1 - pressure * 0.5) * weather_mod * fatigue_mod
        
        # Calculate clearing direction (towards sideline and forward)
        clearing_pos = self._calculate_clearing_position(kicker, game_state)
        
        # Calculate landing position with deviation
        landing_pos = self._calculate_landing_position(
            kicker.current_position,
            clearing_pos,
            accuracy
        )
        
        # Store kicker's name before clearing game state
        kicker_name = kicker.name
        
        # Create return dictionary before updating game state
        result = {
            'type': 'match_event',  # Add type for event creation
            'player': kicker.name,    # Add player name
            'result': 'clearing_kick',
            'landing_pos': landing_pos,
            'commentary': random.choice([
                f"{kicker.name} sends it long to clear the danger!",
                f"Big clearing kick from {kicker.name}!",
                f"{kicker.name} boots it clear of the pack!"
            ])
        }
        
        # Update game state after creating return dictionary
        game_state.ball_carrier = None
        game_state.ball_position = landing_pos
        game_state.phase = 'contest'
        
        return result
        
    def _calculate_pressure(self, player: Player, game_state: Any) -> float:
        """Use action_engine's pressure calculation"""
        return self.action_engine.calculate_pressure(player, game_state)  # Use public method
        
    def _get_weather_modifier(self, game_state: Any, action_type: str) -> float:
        """Get weather modifier for an action"""
        if not game_state.weather_system:
            return 1.0
            
        mods = game_state.weather_system.get_modifiers()
        return mods.get(action_type, 1.0)
        
    def _calculate_distance(self, pos1: Tuple[float, float], pos2: Tuple[float, float]) -> float:
        """Calculate distance between two positions"""
        dx = pos2[0] - pos1[0]
        dy = pos2[1] - pos1[1]
        return math.sqrt(dx*dx + dy*dy)
        
    def _get_max_kick_distance(self, player: Player) -> float:
        """Get maximum kick distance for a player"""
        base_distance = 45  # Base 45m kick
        kicking_bonus = (player.ability_stats.kicking / 20) * 15  # Up to 15m bonus
        return base_distance + kicking_bonus
        
    def _calculate_landing_position(
        self,
        start_pos: Tuple[float, float],
        target_pos: Tuple[float, float],
        accuracy: float,
        is_handball: bool = False
    ) -> Tuple[float, float]:
        """Calculate where the ball will land with deviation"""
        # Calculate direction vector
        dx = target_pos[0] - start_pos[0]
        dy = target_pos[1] - start_pos[1]
        distance = math.sqrt(dx*dx + dy*dy)
        
        if distance == 0:
            return start_pos
            
        # Calculate deviation based on accuracy
        max_deviation = (1 - accuracy) * (3 if is_handball else 10)  # Handballs more accurate
        deviation_x = random.uniform(-max_deviation, max_deviation)
        deviation_y = random.uniform(-max_deviation, max_deviation)
        
        # Calculate landing position
        landing_x = target_pos[0] + deviation_x
        landing_y = target_pos[1] + deviation_y
        
        # Ensure position is in bounds
        landing_x = max(0, min(landing_x, self.ground.length))
        landing_y = max(0, min(landing_y, self.ground.width))
        
        return (landing_x, landing_y)
        
    def _is_marking_distance(self, distance: float) -> bool:
        """Check if distance is suitable for marking"""
        return 15 <= distance <= 50  # Marks typically taken between 15-50m
        
    def _process_marking_contest(
        self,
        target: Player,
        landing_pos: Tuple[float, float],
        game_state: Any
    ) -> Dict[str, Any]:
        """Process a marking contest"""
        if not target.current_position:
            return {'result': 'spill', 'commentary': "Ball spills free!"}
            
        # Get nearby opponents
        opponents = []
        opponent_team = game_state.team2_players if target.team_side == 'home' else game_state.team1_players
        for opponent in opponent_team.values():
            if not opponent.current_position:
                continue
            distance = self._calculate_distance(landing_pos, opponent.current_position)
            if distance < 3:  # Within 3m of landing spot
                opponents.append(opponent)
                
        # Uncontested mark
        if not opponents:
            mark_chance = target.ability_stats.marking / 20
            if random.random() < mark_chance:
                return {
                    'result': 'mark',
                    'commentary': random.choice([
                        f"Clean mark taken by {target.name}!",
                        f"{target.name} marks unopposed!",
                        f"Easy mark for {target.name}!"
                    ])
                }
                
        # Contested mark
        else:
            # Calculate marking contest rating
            target_rating = (
                target.ability_stats.marking * 0.4 +
                target.physical_stats.strength * 0.3 +
                target.physical_stats.height * 0.3
            ) / 20
            
            best_opponent = max(
                opponents,
                key=lambda o: (
                    o.ability_stats.marking * 0.4 +
                    o.physical_stats.strength * 0.3 +
                    o.physical_stats.height * 0.3
                ) / 20
            )
            
            opponent_rating = (
                best_opponent.ability_stats.marking * 0.4 +
                best_opponent.physical_stats.strength * 0.3 +
                best_opponent.physical_stats.height * 0.3
            ) / 20
            
            # Target needs to win convincingly
            if target_rating > opponent_rating * 1.2:
                return {
                    'result': 'mark',
                    'commentary': random.choice([
                        f"Brilliant contested mark by {target.name}!",
                        f"{target.name} climbs high to take a strong mark!",
                        f"What a grab by {target.name} in traffic!"
                    ])
                }
                
        # No mark taken
        return {
            'result': 'spill',
            'commentary': random.choice([
                "Ball spills from the contest!",
                "No clean possession taken!",
                "It'll be a ground ball!"
            ])
        }
        
    def _is_scoring_position(self, player: Player, game_state: Any) -> bool:
        """Check if player is in scoring position"""
        if not player.current_position:
            return False
            
        distance = self.ground.get_distance_to_goal(player.current_position, player.team_side)
        angle = self._calculate_shot_angle(player.current_position, player.team_side)
        
        return distance <= 50 and angle <= 45
        
    def _calculate_shot_angle(
        self,
        position: Tuple[float, float],
        team_side: str
    ) -> float:
        """Calculate angle to goal"""
        x, y = position
        goal_x = self.ground.length if team_side == 'home' else 0
        goal_y = self.ground.width / 2
        
        dx = abs(goal_x - x)
        dy = abs(goal_y - y)
        
        if dx == 0:
            return 90
        return math.degrees(math.atan(dy/dx))
        
    def _calculate_clearing_position(
        self,
        kicker: Player,
        game_state: Any
    ) -> Tuple[float, float]:
        """Calculate position to kick to when clearing"""
        if not kicker.current_position:
            return self.ground.get_center()
            
        x, y = kicker.current_position
        
        # Determine which direction to clear
        if kicker.team_side == 'home':
            clear_x = min(x + 40, self.ground.length)  # Clear forward
        else:
            clear_x = max(x - 40, 0)  # Clear forward
            
        # Clear towards nearest sideline
        if y < self.ground.width / 2:
            clear_y = max(y - 20, 0)  # Clear towards left sideline
        else:
            clear_y = min(y + 20, self.ground.width)  # Clear towards right sideline
            
        return (clear_x, clear_y)
        
    def _kick_to_space(
        self,
        kicker: Player,
        accuracy: float,
        game_state: Any
    ) -> Dict[str, Any]:
        """Process a kick to space"""
        if not kicker.current_position:
            return {'result': 'error', 'commentary': "Invalid kicker position"}
            
        # Calculate kick distance based on kicking ability
        max_distance = self._get_max_kick_distance(kicker)
        actual_distance = max_distance * (0.7 + random.random() * 0.3)  # 70-100% of max
        
        # Calculate direction (forward and slightly wide)
        if kicker.team_side == 'home':
            target_x = min(kicker.current_position[0] + actual_distance, self.ground.length)
        else:
            target_x = max(kicker.current_position[0] - actual_distance, 0)
            
        # Add some sideways movement
        side_movement = random.uniform(-20, 20)
        target_y = max(0, min(kicker.current_position[1] + side_movement, self.ground.width))
        
        # Calculate landing position with deviation
        landing_pos = self._calculate_landing_position(
            kicker.current_position,
            (target_x, target_y),
            accuracy
        )
        
        # Update game state
        game_state.ball_carrier = None
        game_state.ball_position = landing_pos
        game_state.phase = 'contest'
        
        return {
            'result': 'kick_to_space',
            'landing_pos': landing_pos,
            'commentary': random.choice([
                f"{kicker.name} kicks into space!",
                f"{kicker.name} sends it long!",
                f"Big kick forward from {kicker.name}!"
            ])
        } 

class RuckEngine:
    """Handles ruck contests, ball-ups, and center bounces"""
    def __init__(self, ground: Ground):
        self.ground = ground
        
    def process_ruck_contest(
        self,
        ruck1: Player,
        ruck2: Player,
        game_state: Any,
        is_center_bounce: bool = False
    ) -> Dict[str, Any]:
        """Process a ruck contest between two players"""
        if not ruck1.current_position or not ruck2.current_position:
            return {
                'result': 'error',
                'commentary': "Invalid ruck positions"
            }
            
        # Calculate base ratings for each ruckman
        ruck1_rating = self._calculate_ruck_rating(ruck1, game_state)
        ruck2_rating = self._calculate_ruck_rating(ruck2, game_state)
        
        # Determine winner
        total_rating = ruck1_rating + ruck2_rating
        ruck1_chance = ruck1_rating / total_rating if total_rating > 0 else 0.5
        
        # Add some randomness
        if random.random() < ruck1_chance:
            winner = ruck1
            loser = ruck2
        else:
            winner = ruck2
            loser = ruck1
            
        # Determine tap direction and target
        tap_result = self._determine_tap_direction(winner, game_state, is_center_bounce)
        
        # Update stats
        winner.add_action('hitout')
        if tap_result['target']:
            tap_result['target'].add_action('receive')
            
        # Update game state
        game_state.ball_carrier = tap_result.get('target')
        game_state.ball_position = tap_result['landing_pos']
        game_state.phase = 'open_play' if tap_result.get('target') else 'contest'
        print(f"Hitout Winner: {winner}")
        return {
            'result': 'hitout',
            'winner': winner,
            'direction': tap_result['direction'],
            'target': tap_result.get('target'),
            'landing_pos': tap_result['landing_pos'],
            'commentary': self._get_ruck_commentary(winner, tap_result)
        }
        
    def process_ball_up(
        self,
        position: Tuple[float, float],
        game_state: Any
    ) -> Dict[str, Any]:
        """Process a ball-up at a specific position"""
        # Find nearest ruckmen
        home_ruck = self._get_nearest_ruck(position, game_state.team1_players)
        away_ruck = self._get_nearest_ruck(position, game_state.team2_players)
        
        if not home_ruck or not away_ruck:
            return {
                'result': 'error',
                'commentary': "Unable to find ruckmen for ball-up"
            }
            
        # Position ruckmen for ball-up
        self._position_for_ball_up(home_ruck, away_ruck, position)
        
        # Process the ruck contest
        return self.process_ruck_contest(home_ruck, away_ruck, game_state)
        
    def process_center_bounce(
        self,
        game_state: Any
    ) -> Dict[str, Any]:
        """Process a center bounce"""
        # Get primary ruckmen
        home_ruck = self._get_primary_ruck(game_state.team1_players)
        away_ruck = self._get_primary_ruck(game_state.team2_players)
        
        if not home_ruck or not away_ruck:
            return {
                'result': 'error',
                'commentary': "Unable to find ruckmen for center bounce"
            }
            
        # Position ruckmen at center
        center_pos = self.ground.get_center()
        self._position_for_ball_up(home_ruck, away_ruck, center_pos)
        
        # Process the ruck contest
        return self.process_ruck_contest(home_ruck, away_ruck, game_state, is_center_bounce=True)
        
    def _calculate_ruck_rating(self, player: Player, game_state: Any) -> float:
        """Calculate ruck contest rating for a player"""
        # Base rating from relevant attributes
        base_rating = (
            player.physical_stats.height * 0.4 +    # Height is most important
            player.physical_stats.strength * 0.3 +  # Strength helps in contests
            #player.ability_stats.ruck_work * 0.2 +  # Ruck skill
            player.ability_stats.tactical * 0.1     # Tactical awareness
        ) / 20
        
        # Apply fatigue modifier
        fatigue_mod = max(0.6, 1 - (player.fatigue / 100))
        
        # Apply weather effects
        weather_mod = 1.0
        if game_state.weather_system:
            mods = game_state.weather_system.get_modifiers()
            print(f"Weather Mods {mods}")
            if mods['condition'] in ['wet_ground', 'heavy_rain']:
                weather_mod = 0.9  # Harder in wet conditions
                
        # Add some randomness
        random_factor = random.uniform(0.8, 1.2)
        
        return base_rating * fatigue_mod * weather_mod * random_factor
        
    def _determine_tap_direction(
        self,
        winner: Player,
        game_state: Any,
        is_center_bounce: bool
    ) -> Dict[str, Any]:
        """Determine direction and target for ruck tap"""
        # Get potential targets (teammates within range)
        targets = self._get_potential_tap_targets(winner, game_state)
        
        # Calculate tap zones based on game situation
        tap_zones = self._calculate_tap_zones(winner, is_center_bounce)
        
        # Rate each zone and target combination
        best_option = None
        best_rating = -1
        
        for zone in tap_zones:
            for target in targets:
                if not target.current_position:
                    continue
                    
                # Calculate rating for this option
                rating = self._rate_tap_option(winner, target, zone, game_state)
                
                if rating > best_rating:
                    best_rating = rating
                    best_option = {
                        'target': target,
                        'zone': zone
                    }
                    
        # If no good options, tap to space
        if not best_option or best_rating < 0.3:
            return self._tap_to_space(winner, game_state, is_center_bounce)
            
        # Calculate landing position with some deviation
        landing_pos = self._calculate_tap_landing(
            winner.current_position,
            best_option['target'].current_position,
            best_option['zone']
        )
        
        return {
            'direction': best_option['zone']['name'],
            'target': best_option['target'],
            'landing_pos': landing_pos
        }
        
    def _get_potential_tap_targets(
        self,
        ruckman: Player,
        game_state: Any,
        max_distance: float = 10.0
    ) -> List[Player]:
        """Get potential targets for a ruck tap"""
        targets = []
        teammates = game_state.team1_players if ruckman.team_side == 'home' else game_state.team2_players
        
        for player in teammates.values():
            if player.id == ruckman.id or not player.current_position:
                continue
                
            # Calculate distance
            dx = player.current_position[0] - ruckman.current_position[0]
            dy = player.current_position[1] - ruckman.current_position[1]
            distance = math.sqrt(dx*dx + dy*dy)
            
            if distance <= max_distance:
                targets.append(player)
                
        return targets
        
    def _calculate_tap_zones(
        self,
        ruckman: Player,
        is_center_bounce: bool
    ) -> List[Dict[str, Any]]:
        """Calculate possible tap zones"""
        if not ruckman.current_position:
            return []
            
        x, y = ruckman.current_position
        
        # Define base zones
        zones = [
            {
                'name': 'forward',
                'dx': 5 if ruckman.team_side == 'home' else -5,
                'dy': 0,
                'weight': 1.0
            },
            {
                'name': 'backward',
                'dx': -5 if ruckman.team_side == 'home' else 5,
                'dy': 0,
                'weight': 0.7
            },
            {
                'name': 'left',
                'dx': 0,
                'dy': -5,
                'weight': 0.8
            },
            {
                'name': 'right',
                'dx': 0,
                'dy': 5,
                'weight': 0.8
            }
        ]
        
        # Adjust weights based on game situation
        if is_center_bounce:
            # Prefer forward taps in center bounces
            for zone in zones:
                if zone['name'] == 'forward':
                    zone['weight'] *= 1.2
                    zone['dx'] *= 1.5  # Longer taps from center
                    
        return zones
        
    def _rate_tap_option(
        self,
        ruckman: Player,
        target: Player,
        zone: Dict[str, Any],
        game_state: Any
    ) -> float:
        """Rate a potential tap option"""
        if not target.current_position:
            return 0.0
            
        # Base rating from zone weight
        rating = zone['weight']
        
        # Calculate tap position
        tap_x = ruckman.current_position[0] + zone['dx']
        tap_y = ruckman.current_position[1] + zone['dy']
        
        # Rate based on target's position and attributes
        dx = tap_x - target.current_position[0]
        dy = tap_y - target.current_position[1]
        distance = math.sqrt(dx*dx + dy*dy)
        
        # Distance factor - closer is better
        distance_mod = max(0.2, 1 - (distance / 10))
        rating *= distance_mod
        
        # Target's ability to receive
        receive_rating = (target.ability_stats.marking + target.ability_stats.handball) / 40
        rating *= receive_rating
        
        # Space factor - prefer targets in space
        space_mod = 1.0
        opponent_team = game_state.team2_players if ruckman.team_side == 'home' else game_state.team1_players
        for opponent in opponent_team.values():
            if not opponent.current_position:
                continue
            opp_dx = opponent.current_position[0] - tap_x
            opp_dy = opponent.current_position[1] - tap_y
            opp_distance = math.sqrt(opp_dx*opp_dx + opp_dy*opp_dy)
            if opp_distance < 3:  # Within 3m
                space_mod *= 0.7
                
        rating *= space_mod
        
        return rating
        
    def _tap_to_space(
        self,
        ruckman: Player,
        game_state: Any,
        is_center_bounce: bool
    ) -> Dict[str, Any]:
        """Calculate tap to space when no good target available"""
        if not ruckman.current_position:
            return {
                'direction': 'forward',
                'target': None,
                'landing_pos': self.ground.get_center()
            }
            
        # Base tap distance
        tap_distance = 8.0  # 8m base tap
        if is_center_bounce:
            tap_distance *= 1.5  # Longer taps from center
            
        # Calculate tap direction (prefer forward)
        if ruckman.team_side == 'home':
            tap_x = min(ruckman.current_position[0] + tap_distance, self.ground.length)
        else:
            tap_x = max(ruckman.current_position[0] - tap_distance, 0)
            
        # Add some sideways movement
        tap_y = ruckman.current_position[1] + random.uniform(-3, 3)
        
        # Ensure position is in bounds
        tap_y = max(0, min(tap_y, self.ground.width))
        
        return {
            'direction': 'forward',
            'target': None,
            'landing_pos': (tap_x, tap_y)
        }
        
    def _calculate_tap_landing(
        self,
        ruck_pos: Tuple[float, float],
        target_pos: Tuple[float, float],
        zone: Dict[str, Any]
    ) -> Tuple[float, float]:
        """Calculate actual landing position for a tap"""
        # Start with perfect tap position
        landing_x = ruck_pos[0] + zone['dx']
        landing_y = ruck_pos[1] + zone['dy']
        
        # Add some deviation based on ruck skill
        deviation = random.uniform(-1, 1)  # 1m max deviation
        landing_x += deviation
        landing_y += deviation
        
        # Ensure position is in bounds
        landing_x = max(0, min(landing_x, self.ground.length))
        landing_y = max(0, min(landing_y, self.ground.width))
        
        return (landing_x, landing_y)
        
    def _get_nearest_ruck(
        self,
        position: Tuple[float, float],
        team_players: Dict[str, Player]
    ) -> Optional[Player]:
        """Get nearest ruck-capable player to a position"""
        best_ruck = None
        best_distance = float('inf')
        
        for player in team_players.values():
            if not player.current_position:
                continue
                
            # Calculate distance
            dx = player.current_position[0] - position[0]
            dy = player.current_position[1] - position[1]
            distance = math.sqrt(dx*dx + dy*dy)
            
            # Calculate ruck capability
            ruck_rating = (
                player.physical_stats.height * 0.5 +
                player.physical_stats.strength * 0.3 #+
                #player.ability_stats.ruck_work * 0.2
            ) / 20
            
            # Adjust distance by ruck rating (better rucks preferred even if slightly further)
            adjusted_distance = distance * (1.5 - ruck_rating)
            
            if adjusted_distance < best_distance:
                best_distance = adjusted_distance
                best_ruck = player
                
        return best_ruck
        
    def _get_primary_ruck(self, team_players: Dict[str, Player]) -> Optional[Player]:
        """Get team's primary ruckman"""
        best_ruck = None
        best_rating = -1
        
        for player in team_players.values():
            # Calculate ruck rating
            ruck_rating = (
                player.physical_stats.height * 0.4 +
                player.physical_stats.strength * 0.3 +
                #player.ability_stats.ruck_work * 0.2 +
                player.ability_stats.tactical * 0.1
            ) / 20
            
            if ruck_rating > best_rating:
                best_rating = ruck_rating
                best_ruck = player
                
        return best_ruck
        
    def _position_for_ball_up(
        self,
        ruck1: Player,
        ruck2: Player,
        position: Tuple[float, float]
    ) -> None:
        """Position ruckmen for a ball-up"""
        if not position:
            return
            
        # Position ruckmen on opposite sides
        ruck1.current_position = (
            position[0],
            position[1] - 1  # 1m to the left
        )
        
        ruck2.current_position = (
            position[0],
            position[1] + 1  # 1m to the right
        )
        
    def _get_ruck_commentary(
        self,
        winner: Player,
        tap_result: Dict[str, Any]
    ) -> str:
        """Generate commentary for ruck contest"""
        if tap_result.get('target'):
            return random.choice([
                f"{winner.name} taps it perfectly to {tap_result['target'].name}!",
                f"Beautiful tap work from {winner.name} to find {tap_result['target'].name}!",
                f"Clinical tap by {winner.name}, straight to {tap_result['target'].name}!"
            ])
        else:
            return random.choice([
                f"{winner.name} taps it forward into space!",
                f"{winner.name} gets first hands to it!",
                f"Tap forward from {winner.name}!"
            ])

class ClearanceEngine:
    """Handles clearances from stoppages and congestion"""
    def __init__(self, ground: Ground, action_engine: ActionEngine):
        self.ground = ground
        self.action_engine = action_engine
        
    def process_clearance(
        self,
        game_state: Any,
        stoppage_position: Tuple[float, float],
        stoppage_type: str = 'ruck_contest'
    ) -> Dict[str, Any]:
        """Process a clearance attempt from a stoppage"""
        # Get players in clearance zone
        clearance_players = self._get_clearance_players(game_state, stoppage_position)
        
        if not clearance_players:
            return {
                'result': 'error',
                'commentary': "No players in clearance zone"
            }
            
        # Rate each player's clearance chance
        rated_players = []
        for player in clearance_players:
            rating = self._calculate_clearance_rating(player, game_state, stoppage_type)
            rated_players.append((player, rating))
            
        # Sort by rating
        rated_players.sort(key=lambda x: x[1], reverse=True)
        
        # Determine winner
        winner = rated_players[0][0]
        win_chance = min(0.8, rated_players[0][1])  # Cap at 80%
        
        if random.random() < win_chance:
            # Successful clearance
            clearance_result = self._process_successful_clearance(winner, game_state)
            
            # Update stats
            winner.add_action('clearance')
            
            return {
                'result': 'success',
                'player': winner,
                'next_action': clearance_result['next_action'],
                'target': clearance_result.get('target'),
                'position': clearance_result.get('position'),
                'commentary': self._get_clearance_commentary(winner, clearance_result)
            }
        else:
            # Failed clearance - ball spills
            spill_pos = self._calculate_spill_position(stoppage_position)
            game_state.ball_position = spill_pos
            game_state.ball_carrier = None
            game_state.phase = 'contest'
            
            return {
                'result': 'spill',
                'position': spill_pos,
                'commentary': "Ball spills from the stoppage!"
            }
            
    def _get_clearance_players(
        self,
        game_state: Any,
        position: Tuple[float, float],
        radius: float = 8.0
    ) -> List[Player]:
        """Get players in position for clearance"""
        clearance_players = []
        
        # Check all players from both teams
        for team_players in [game_state.team1_players, game_state.team2_players]:
            for player in team_players.values():
                if not player.current_position:
                    continue
                    
                # Calculate distance to stoppage
                dx = player.current_position[0] - position[0]
                dy = player.current_position[1] - position[1]
                distance = math.sqrt(dx*dx + dy*dy)
                
                # Include players within radius
                if distance <= radius:
                    clearance_players.append(player)
                    
        return clearance_players
        
    def _calculate_clearance_rating(
        self,
        player: Player,
        game_state: Any,
        stoppage_type: str
    ) -> float:
        """Calculate player's clearance rating"""
        # Base rating from relevant attributes
        base_rating = (
            player.ability_stats.clearance * 0.3 +     # Clearance ability
            player.physical_stats.strength * 0.2 +     # Strength for contests
            player.physical_stats.agility * 0.2 +      # Agility to evade
            player.ability_stats.tackling * 0.15 +     # Tackling for ball winning
            player.ability_stats.tactical * 0.15       # Decision making
        ) / 20
        
        # Modify based on stoppage type
        if stoppage_type == 'ruck_contest':
            # Midfielders better at ruck clearances
            if player.position in ['RuckRover', 'Rover', 'Centre']:
                base_rating *= 1.2
        elif stoppage_type == 'ball_up':
            # Smaller players better at ground balls
            if player.physical_stats.height < 185:  # Under 185cm
                base_rating *= 1.1
                
        # Apply fatigue modifier
        fatigue_mod = max(0.6, 1 - (player.fatigue / 100))
        
        # Apply congestion modifier
        congestion = self._calculate_congestion(player, game_state)
        congestion_mod = max(0.5, 1 - (congestion * 0.3))
        
        # Apply weather effects
        weather_mod = 1.0
        if game_state.weather_system:
            mods = game_state.weather_system.get_modifiers()
            if mods['condition'] in ['wet_ground', 'heavy_rain']:
                weather_mod = 0.9  # Harder in wet conditions
                
        # Add some randomness
        random_factor = random.uniform(0.8, 1.2)
        
        return base_rating * fatigue_mod * congestion_mod * weather_mod * random_factor
        
    def _calculate_congestion(
        self,
        player: Player,
        game_state: Any,
        radius: float = 5.0
    ) -> float:
        """Calculate congestion around a player"""
        if not player.current_position:
            return 0.0
            
        nearby_players = 0
        
        # Count players from both teams
        for team_players in [game_state.team1_players, game_state.team2_players]:
            for other in team_players.values():
                if other.id == player.id or not other.current_position:
                    continue
                    
                # Calculate distance
                dx = other.current_position[0] - player.current_position[0]
                dy = other.current_position[1] - player.current_position[1]
                distance = math.sqrt(dx*dx + dy*dy)
                
                if distance <= radius:
                    nearby_players += 1
                    
        # Convert to 0-1 scale (0 = no congestion, 1 = very congested)
        return min(1.0, nearby_players / 8)  # Cap at 8 nearby players
        
    def _process_successful_clearance(
        self,
        player: Player,
        game_state: Any
    ) -> Dict[str, Any]:
        """Process a successful clearance"""
        # Update game state
        game_state.ball_carrier = player
        game_state.ball_position = player.current_position
        
        # Evaluate disposal options
        disposal_options = self._evaluate_clearance_options(player, game_state)
        
        if not disposal_options:
            # No good options - burst through
            return {
                'next_action': 'burst',
                'commentary': f"{player.name} bursts clear of the pack!"
            }
            
        # Choose best option
        best_option = max(disposal_options, key=lambda x: x['rating'])
        
        if best_option['type'] == 'handball':
            return {
                'next_action': 'handball',
                'target': best_option['target'],
                'position': player.current_position,
                'commentary': f"{player.name} looks to handball clear!"
            }
        else:  # kick
            return {
                'next_action': 'kick',
                'target': best_option['target'],
                'position': player.current_position,
                'commentary': f"{player.name} tries to kick clear!"
            }
            
    def _evaluate_clearance_options(
        self,
        player: Player,
        game_state: Any
    ) -> List[Dict[str, Any]]:
        """Evaluate disposal options for clearance"""
        options = []
        teammates = game_state.team1_players if player.team_side == 'home' else game_state.team2_players
        
        for teammate in teammates.values():
            if teammate.id == player.id or not teammate.current_position:
                continue
                
            # Calculate distance
            dx = teammate.current_position[0] - player.current_position[0]
            dy = teammate.current_position[1] - player.current_position[1]
            distance = math.sqrt(dx*dx + dy*dy)
            
            # Skip if too far
            if distance > 30:  # Max 30m clearance
                continue
                
            # Determine disposal type
            disposal_type = 'handball' if distance < 10 else 'kick'
            
            # Calculate option rating
            rating = self._rate_clearance_option(
                player,
                teammate,
                disposal_type,
                game_state
            )
            
            if rating > 0.3:  # Minimum threshold
                options.append({
                    'target': teammate,
                    'type': disposal_type,
                    'distance': distance,
                    'rating': rating
                })
                
        return options
        
    def _rate_clearance_option(
        self,
        player: Player,
        target: Player,
        disposal_type: str,
        game_state: Any
    ) -> float:
        """Rate a clearance disposal option"""
        # Base rating from relevant skills
        if disposal_type == 'handball':
            base_rating = (player.ability_stats.handball + target.ability_stats.handball) / 40
        else:  # kick
            base_rating = (player.ability_stats.kicking + target.ability_stats.marking) / 40
            
        # Adjust for target's space
        target_congestion = self._calculate_congestion(target, game_state)
        space_mod = 1 - target_congestion
        
        # Adjust for forward progress
        progress_mod = self._calculate_forward_progress(player, target)
        
        # Combine ratings
        return base_rating * space_mod * progress_mod
        
    def _calculate_forward_progress(
        self,
        player: Player,
        target: Player
    ) -> float:
        """Calculate how much forward progress the disposal makes"""
        if not player.current_position or not target.current_position:
            return 0.5  # Neutral if positions unknown
            
        # Calculate progress towards goal
        if player.team_side == 'home':
            player_to_goal = self.ground.length - player.current_position[0]
            target_to_goal = self.ground.length - target.current_position[0]
        else:
            player_to_goal = player.current_position[0]
            target_to_goal = target.current_position[0]
            
        progress = player_to_goal - target_to_goal
        
        # Normalize to 0-1 range
        return max(0.3, min(1.0, 0.7 + progress/30))
        
    def _calculate_spill_position(
        self,
        position: Tuple[float, float]
    ) -> Tuple[float, float]:
        """Calculate where ball spills to on failed clearance"""
        # Add random deviation (1-3m)
        deviation_x = random.uniform(-3, 3)
        deviation_y = random.uniform(-3, 3)
        
        spill_x = position[0] + deviation_x
        spill_y = position[1] + deviation_y
        
        # Ensure position is in bounds
        spill_x = max(0, min(spill_x, self.ground.length))
        spill_y = max(0, min(spill_y, self.ground.width))
        
        return (spill_x, spill_y)
        
    def _get_clearance_commentary(
        self,
        player: Player,
        clearance_result: Dict[str, Any]
    ) -> str:
        """Generate commentary for clearance"""
        if clearance_result['next_action'] == 'burst':
            return random.choice([
                f"{player.name} bursts clear of the stoppage!",
                f"Brilliant clearance work from {player.name}!",
                f"{player.name} extracts it from the congestion!",
                f"Clean hands in traffic from {player.name}!",
                f"{player.name} breaks through the pack!"
            ])
        elif clearance_result['next_action'] == 'handball':
            target = clearance_result['target']
            return random.choice([
                f"{player.name} gets a quick handball to {target.name}!",
                f"Slick hands from {player.name} to find {target.name}!",
                f"{player.name} feeds it out to {target.name}!",
                f"Clean handball from {player.name} to {target.name}!"
            ])
        else:  # kick
            target = clearance_result['target']
            return random.choice([
                f"{player.name} kicks clear towards {target.name}!",
                f"{player.name} gets boot to ball looking for {target.name}!",
                f"Long kick from {player.name} to {target.name}!",
                f"{player.name} sends it forward to {target.name}!"
            ])