

.dynamic-link {
    text-decoration: none; /* Removes underline from links */
    color: inherit; /* Ensures link inherits the text color */
}

.matches-container {
    background-color: #f7f7f7;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    width: 800px; /* Increase width for better layout */
    margin: 20px;
    padding: 15px;
    transition: transform 0.2s;
    flex-direction: row;
}

.matches-header {
    text-align: center;
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 20px;
    color: #003f87;
}

.match-card {
    background-color: #e8f0fa;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    margin: 20px auto;
    width: 700px;
    padding: 15px;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.match-card:hover {
    transform: scale(1.02);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.match-date {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    font-size: 1.2em;
    color: #003f87;
    font-weight: bold;
}

.match-day {
    font-size: 1.5em;
}

.match-info {
    display: flex;
    align-items: center;
    justify-content: space-around;
    margin-bottom: 10px;
}

.team {
    text-align: center;
}

.team-logo {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    margin-bottom: 5px;
}

.team-name {
    display: block;
    font-weight: bold;
    color: #333;
}

.vs {
    font-size: 1.5em;
    font-weight: bold;
    color: #333;
}

.match-extra {
    display: flex;
    justify-content: space-between;
    font-size: 1em;
    color: #555;
}

.match-location {
    display: flex;
    align-items: center;
}

.match-location i {
    margin-right: 5px;
}

.match-score {
    font-size: 1.2em;
    font-weight: bold;
}

.orders{
    background-color: #aacaf1;
    text-align: center;
    border-radius: 10px;
    margin-bottom: -12px;
   
}
