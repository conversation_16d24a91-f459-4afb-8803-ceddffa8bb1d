"""
URL configuration for afl_game project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.0/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.conf import settings
from django.contrib import admin
from django.urls import path, include
from users.views import home_view

#def debug_view(request):
#    from django.http import HttpResponse
#    return HttpResponse("Debug view: URL configuration is working")

urlpatterns = [
    path('admin/ajax/', include('custom_admin.urls')),  # Include custom admin paths first
    #path('admin/ajax/debug/', debug_view),  # Temporary debug view to test URL configuration
    path('admin/', admin.site.urls),  # Default Django admin paths
    path('users/', include('users.urls')),
    path('teams/', include('teams.urls')),
    path('', home_view, name='home'),
]

if settings.DEBUG:
    import debug_toolbar
    urlpatterns = [
        path('__debug__/', include(debug_toolbar.urls)),
    ] + urlpatterns

