from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from teams.models import Team, Player
from matches.models import Match
from matchengine.models import simulate_match
import sys
from django.http import JsonResponse

@login_required
def team_view(request, team_id):
    print("team_view called")
    team = get_object_or_404(Team, id=team_id, user=request.user)
    players = Player.objects.filter(team=team)
    return render(request, 'teams/team.html', {'team': team, 'players': players})

def load_players(request):
    team_id = request.GET.get('team_id')
    team = get_object_or_404(Team, id=team_id)
    players = Player.objects.filter(team=team).values('id', 'name')
    return JsonResponse(list(players), safe=False)

@login_required
def simulate_match_view(request):
    print("simulate_match_view called")
    teams = Team.objects.all()
    print(f"Teams: {teams}")  # This should print a queryset or a list of teams
    return render(request, 'admin/simulate_match.html', {'teams': teams})

@login_required
def matches_list_view(request):
    matches = Match.objects.all()
    #print("hit match list")
    return render(request, 'admin/matches_list.html', {'matches': matches})

@login_required
def force_simulate_match(request, match_id):
    match = get_object_or_404(Match, id=match_id)
    return render(request, 'matches/live_match.html', {'match_id': match_id, 'match': match})
