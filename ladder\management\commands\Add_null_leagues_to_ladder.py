from django.core.management.base import BaseCommand
from leagues.models import League
from teams.models import Team
from ladder.models import Ladder

class Command(BaseCommand):
# Adjust the logic here based on how you want to map Ladder entries to leagues.
    def handle(*args, **kwargs):
        # Loop through all ladder entries
        ladders = Ladder.objects.filter(league__isnull=True)
        
        for ladder in ladders:
            # Fetch the team linked to this ladder entry
            team = ladder.team
            
            if team and team.league_id:
                # Assuming 'League' has a field like 'division_number' to match against
                try:
                    # Retrieve the correct league based on division or other criteria
                    league = League.objects.get(id=team.league_id)
                    ladder.league = league
                    ladder.save()
                    print(f"Updated {team.name} in division {team.league_id} to league {league.name}")
                except League.DoesNotExist:
                    print(f"No matching league found for division {team.league_id}")
            else:
                print(f"Skipping team {team.name} due to missing division or team data")

        print("League IDs updated for all ladder entries.")

    # Run the update function
    handle()
