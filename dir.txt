 Volume in drive C has no label.
 Volume Serial Number is 844D-9C15

 Directory of C:\MAMP\htdocs\AFL_Game

01/08/2024  12:49 PM    <DIR>          .
26/07/2024  09:54 AM    <DIR>          ..
31/07/2024  02:23 PM    <DIR>          afl_game
26/07/2024  10:01 AM           155,648 db.sqlite3
01/08/2024  12:49 PM                 0 dir.txt
26/07/2024  09:51 AM               686 manage.py
31/07/2024  02:16 PM    <DIR>          static
01/08/2024  08:42 AM    <DIR>          teams
01/08/2024  08:41 AM    <DIR>          templates
26/07/2024  03:35 PM    <DIR>          users
               3 File(s)        156,334 bytes

 Directory of C:\MAMP\htdocs\AFL_Game\afl_game

31/07/2024  02:23 PM    <DIR>          .
01/08/2024  12:49 PM    <DIR>          ..
26/07/2024  09:51 AM               409 asgi.py
31/07/2024  02:35 PM             3,741 settings.py
31/07/2024  02:51 PM             1,010 urls.py
26/07/2024  09:51 AM               409 wsgi.py
26/07/2024  09:51 AM                 0 __init__.py
31/07/2024  02:51 PM    <DIR>          __pycache__
               5 File(s)          5,569 bytes

 Directory of C:\MAMP\htdocs\AFL_Game\afl_game\__pycache__

31/07/2024  02:51 PM    <DIR>          .
31/07/2024  02:23 PM    <DIR>          ..
31/07/2024  02:35 PM             2,807 settings.cpython-312.pyc
31/07/2024  02:51 PM             1,303 urls.cpython-312.pyc
26/07/2024  10:01 AM               631 wsgi.cpython-312.pyc
26/07/2024  09:55 AM               141 __init__.cpython-312.pyc
               4 File(s)          4,882 bytes

 Directory of C:\MAMP\htdocs\AFL_Game\static

31/07/2024  02:16 PM    <DIR>          .
01/08/2024  12:49 PM    <DIR>          ..
31/07/2024  02:16 PM    <DIR>          admin
               0 File(s)              0 bytes

 Directory of C:\MAMP\htdocs\AFL_Game\static\admin

31/07/2024  02:16 PM    <DIR>          .
31/07/2024  02:16 PM    <DIR>          ..
31/07/2024  02:16 PM    <DIR>          js
               0 File(s)              0 bytes

 Directory of C:\MAMP\htdocs\AFL_Game\static\admin\js

31/07/2024  02:16 PM    <DIR>          .
31/07/2024  02:16 PM    <DIR>          ..
01/08/2024  11:52 AM             2,255 match_simulation.js
               1 File(s)          2,255 bytes

 Directory of C:\MAMP\htdocs\AFL_Game\teams

01/08/2024  08:42 AM    <DIR>          .
01/08/2024  12:49 PM    <DIR>          ..
01/08/2024  12:08 PM             2,335 admin.py
30/07/2024  08:47 AM               334 admin.py.bak
26/07/2024  09:55 AM               148 apps.py
31/07/2024  02:32 PM               520 forms.py
29/07/2024  09:48 AM    <DIR>          management
31/07/2024  02:08 PM    <DIR>          match_engine
31/07/2024  12:55 PM    <DIR>          migrations
31/07/2024  12:55 PM             1,910 models.py
26/07/2024  12:16 PM             1,224 models.py.bak
26/07/2024  09:55 AM                63 tests.py
01/08/2024  12:43 PM               379 urls.py
26/07/2024  12:15 PM               546 utils.py
01/08/2024  12:15 PM               926 views.py
26/07/2024  09:55 AM                 0 __init__.py
01/08/2024  12:43 PM    <DIR>          __pycache__
              11 File(s)          8,385 bytes

 Directory of C:\MAMP\htdocs\AFL_Game\teams\management

29/07/2024  09:48 AM    <DIR>          .
01/08/2024  08:42 AM    <DIR>          ..
30/07/2024  09:53 AM    <DIR>          commands
               0 File(s)              0 bytes

 Directory of C:\MAMP\htdocs\AFL_Game\teams\management\commands

30/07/2024  09:53 AM    <DIR>          .
29/07/2024  09:48 AM    <DIR>          ..
31/07/2024  01:29 PM             1,664 simulate_match.py
29/07/2024  10:17 AM             1,286 simulate_match.py.bak
31/07/2024  01:33 PM    <DIR>          __pycache__
               2 File(s)          2,950 bytes

 Directory of C:\MAMP\htdocs\AFL_Game\teams\management\commands\__pycache__

31/07/2024  01:33 PM    <DIR>          .
30/07/2024  09:53 AM    <DIR>          ..
31/07/2024  01:33 PM             2,872 simulate_match.cpython-312.pyc
               1 File(s)          2,872 bytes

 Directory of C:\MAMP\htdocs\AFL_Game\teams\match_engine

31/07/2024  02:08 PM    <DIR>          .
01/08/2024  08:42 AM    <DIR>          ..
31/07/2024  02:08 PM             4,628 match_engine.py
29/07/2024  10:16 AM             1,930 match_engine.py.bak
31/07/2024  01:05 PM             5,084 match_engine.py.bak2
31/07/2024  01:39 PM             4,638 match_engine.py.bak3
31/07/2024  02:10 PM    <DIR>          __pycache__
               4 File(s)         16,280 bytes

 Directory of C:\MAMP\htdocs\AFL_Game\teams\match_engine\__pycache__

31/07/2024  02:10 PM    <DIR>          .
31/07/2024  02:08 PM    <DIR>          ..
31/07/2024  02:10 PM             7,362 match_engine.cpython-312.pyc
               1 File(s)          7,362 bytes

 Directory of C:\MAMP\htdocs\AFL_Game\teams\migrations

31/07/2024  12:55 PM    <DIR>          .
01/08/2024  08:42 AM    <DIR>          ..
26/07/2024  09:58 AM             1,574 0001_initial.py
26/07/2024  12:20 PM             3,300 0002_playerstatsability_playerstatsphysical_and_more.py
26/07/2024  12:20 PM             1,175 0003_team_user_player_ability_stats_player_physical_stats.py
26/07/2024  03:23 PM               746 0004_alter_player_ability_stats_alter_player_team.py
30/07/2024  08:44 AM               872 0005_alter_player_team_alter_team_user.py
30/07/2024  11:58 AM               464 0006_playerstatsability_marking.py
31/07/2024  09:48 AM               422 0007_alter_playerstatsability_marking.py
31/07/2024  09:55 AM               419 0008_alter_playerstatsability_marking.py
31/07/2024  12:55 PM               428 0009_alter_playerstatsability_marking.py
26/07/2024  09:55 AM                 0 __init__.py
31/07/2024  12:55 PM    <DIR>          __pycache__
              10 File(s)          9,400 bytes

 Directory of C:\MAMP\htdocs\AFL_Game\teams\migrations\__pycache__

31/07/2024  12:55 PM    <DIR>          .
31/07/2024  12:55 PM    <DIR>          ..
26/07/2024  09:58 AM             2,036 0001_initial.cpython-312.pyc
26/07/2024  12:21 PM             3,268 0002_playerstatsability_playerstatsphysical_and_more.cpython-312.pyc
26/07/2024  12:21 PM             1,760 0003_team_user_player_ability_stats_player_physical_stats.cpython-312.pyc
26/07/2024  03:23 PM             1,257 0004_alter_player_ability_stats_alter_player_team.cpython-312.pyc
30/07/2024  08:44 AM             1,438 0005_alter_player_team_alter_team_user.cpython-312.pyc
30/07/2024  11:59 AM               787 0006_playerstatsability_marking.cpython-312.pyc
31/07/2024  09:48 AM               764 0007_alter_playerstatsability_marking.cpython-312.pyc
31/07/2024  09:55 AM               748 0008_alter_playerstatsability_marking.cpython-312.pyc
31/07/2024  12:55 PM               770 0009_alter_playerstatsability_marking.cpython-312.pyc
26/07/2024  09:58 AM               149 __init__.cpython-312.pyc
              10 File(s)         12,977 bytes

 Directory of C:\MAMP\htdocs\AFL_Game\teams\__pycache__

01/08/2024  12:43 PM    <DIR>          .
01/08/2024  08:42 AM    <DIR>          ..
01/08/2024  12:08 PM             3,655 admin.cpython-312.pyc
26/07/2024  09:58 AM               442 apps.cpython-312.pyc
31/07/2024  02:32 PM             1,296 forms.cpython-312.pyc
31/07/2024  12:55 PM             4,133 models.cpython-312.pyc
01/08/2024  12:43 PM               513 urls.cpython-312.pyc
26/07/2024  12:16 PM             1,112 utils.cpython-312.pyc
01/08/2024  12:15 PM             1,839 views.cpython-312.pyc
26/07/2024  09:58 AM               138 __init__.cpython-312.pyc
               8 File(s)         13,128 bytes

 Directory of C:\MAMP\htdocs\AFL_Game\templates

01/08/2024  08:41 AM    <DIR>          .
01/08/2024  12:49 PM    <DIR>          ..
01/08/2024  12:48 PM    <DIR>          admin
29/07/2024  09:11 AM               632 home.html
26/07/2024  02:54 PM               635 home.html.bak
26/07/2024  11:43 AM    <DIR>          registration
26/07/2024  02:00 PM    <DIR>          teams
               2 File(s)          1,267 bytes

 Directory of C:\MAMP\htdocs\AFL_Game\templates\admin

01/08/2024  12:48 PM    <DIR>          .
01/08/2024  08:41 AM    <DIR>          ..
01/08/2024  10:54 AM             1,289 simulate_match.html
01/08/2024  12:48 PM    <DIR>          teams
               1 File(s)          1,289 bytes

 Directory of C:\MAMP\htdocs\AFL_Game\templates\admin\teams

01/08/2024  12:48 PM    <DIR>          .
01/08/2024  12:48 PM    <DIR>          ..
01/08/2024  09:02 AM               331 change_list.html
               1 File(s)            331 bytes

 Directory of C:\MAMP\htdocs\AFL_Game\templates\registration

26/07/2024  11:43 AM    <DIR>          .
01/08/2024  08:41 AM    <DIR>          ..
26/07/2024  11:43 AM               259 register.html
               1 File(s)            259 bytes

 Directory of C:\MAMP\htdocs\AFL_Game\templates\teams

26/07/2024  02:00 PM    <DIR>          .
01/08/2024  08:41 AM    <DIR>          ..
30/07/2024  12:41 PM               982 team.html
               1 File(s)            982 bytes

 Directory of C:\MAMP\htdocs\AFL_Game\users

26/07/2024  03:35 PM    <DIR>          .
01/08/2024  12:49 PM    <DIR>          ..
30/07/2024  08:47 AM               562 admin.py
26/07/2024  11:52 AM               140 apps.py
29/07/2024  08:19 AM               346 forms.py
26/07/2024  01:55 PM               403 forms.py.bak
30/07/2024  11:58 AM    <DIR>          migrations
30/07/2024  08:46 AM               921 models.py
26/07/2024  11:52 AM             1,564 signals.py
29/07/2024  08:27 AM    <DIR>          templates
26/07/2024  11:37 AM                63 tests.py
29/07/2024  09:31 AM               291 urls.py
01/08/2024  12:14 PM             3,139 views.py
26/07/2024  02:59 PM             1,116 views.py.ak
26/07/2024  11:37 AM                 0 __init__.py
01/08/2024  12:14 PM    <DIR>          __pycache__
              11 File(s)          8,545 bytes

 Directory of C:\MAMP\htdocs\AFL_Game\users\migrations

30/07/2024  11:58 AM    <DIR>          .
26/07/2024  03:35 PM    <DIR>          ..
26/07/2024  12:20 PM             2,891 0001_initial.py
30/07/2024  08:44 AM               463 0002_customuser_team_name.py
30/07/2024  11:58 AM               441 0003_alter_customuser_team_name.py
26/07/2024  11:37 AM                 0 __init__.py
30/07/2024  11:59 AM    <DIR>          __pycache__
               4 File(s)          3,795 bytes

 Directory of C:\MAMP\htdocs\AFL_Game\users\migrations\__pycache__

30/07/2024  11:59 AM    <DIR>          .
30/07/2024  11:58 AM    <DIR>          ..
26/07/2024  12:21 PM             3,575 0001_initial.cpython-312.pyc
30/07/2024  08:44 AM               782 0002_customuser_team_name.cpython-312.pyc
30/07/2024  11:59 AM               779 0003_alter_customuser_team_name.cpython-312.pyc
26/07/2024  11:58 AM               149 __init__.cpython-312.pyc
               4 File(s)          5,285 bytes

 Directory of C:\MAMP\htdocs\AFL_Game\users\templates

29/07/2024  08:27 AM    <DIR>          .
26/07/2024  03:35 PM    <DIR>          ..
               0 File(s)              0 bytes

 Directory of C:\MAMP\htdocs\AFL_Game\users\__pycache__

01/08/2024  12:14 PM    <DIR>          .
26/07/2024  03:35 PM    <DIR>          ..
30/07/2024  08:47 AM             1,193 admin.cpython-312.pyc
26/07/2024  11:55 AM               530 apps.cpython-312.pyc
29/07/2024  08:25 AM               842 forms.cpython-312.pyc
30/07/2024  08:46 AM             1,188 models.cpython-312.pyc
26/07/2024  11:55 AM             2,349 signals.cpython-312.pyc
29/07/2024  09:31 AM               519 urls.cpython-312.pyc
01/08/2024  12:14 PM             4,064 views.cpython-312.pyc
26/07/2024  11:44 AM               138 __init__.cpython-312.pyc
               8 File(s)         10,823 bytes

     Total Files Listed:
              93 File(s)        274,970 bytes
              74 Dir(s)  763,902,754,816 bytes free
