"""
Refactored Match Engine V2
Based on working AI version with improved organization and movement
"""
from typing import Any, Dict, List, Optional, Tuple, Generator, Set
import random
import math
import asyncio
import time
import sys

class Ground:
    """
    Represents the AFL ground with dimensions and zones.
    Uses the working AI's simpler zone system for compatibility.
    """
    def __init__(self):
        self.length = 160  # AFL ground length in meters
        self.width = 130   # AFL ground width in meters
        self.grid_length = int(self.length / 10)  # 16 cells
        self.grid_width = int(self.width / 10)    # 13 cells
        self.positions_zones = self.define_dynamic_zones(self.grid_width, self.grid_length)
        
    def get_grid_map(self) -> Dict[str, List[Tuple[int, int]]]:
        """Return the grid map with dynamic zones."""
        return self.positions_zones
    
    def get_center(self) -> Tuple[float, float]:
        """Calculate the center point of the ground."""
        center_x = (self.grid_width - 1) / 2
        center_y = (self.grid_length - 1) / 2
        return (round(center_x), round(center_y))
        
    def define_dynamic_zones(self, grid_width: int, grid_length: int) -> Dict[str, List[Tuple[int, int]]]:
        """Define zones for each position with dynamic sizing."""
        # Calculate zone widths
        left_width = (grid_width - 1) // 3
        right_width = left_width
        center_width = grid_width - (left_width * 2)
        
        # Calculate section length with overlap
        section_length = grid_length // 5
        overlap = max(1, int(section_length * 0.2))  # 20% overlap
        
        positions = [
            ["LB", "FB", "RB"],      # Back line
            ["LHB", "CHB", "RHB"],   # Half-back line
            ["LWing", "Centre", "RWing"],  # Centre line
            ["LHF", "CHF", "RHF"],   # Half-forward line
            ["LF", "FF", "RF"]       # Forward line
        ]
        
        positions_zones = {}
        
        # Assign zones with proportional overlap
        for i, row in enumerate(positions):
            for j, position in enumerate(row):
                if position in ['Ruck', 'Rover', 'RuckRover']:
                    continue
                    
                # Determine x range (width)
                if j == 0:  # Left positions
                    x_range = range(0, left_width + overlap)
                elif j == 1:  # Center positions
                    x_range = range(left_width - overlap, left_width + center_width + overlap)
                else:  # Right positions
                    x_range = range(left_width + center_width - overlap, grid_width)
                
                # Determine y range (length)
                start_y = max(0, i * section_length - overlap)
                end_y = min(grid_length, (i + 1) * section_length + overlap)
                y_range = range(start_y, end_y)
                
                # Create zone with overlapping areas
                zone = [(x, y) for x in x_range for y in y_range]
                positions_zones[position] = zone
                
        return positions_zones
    
    def is_in_bounds(self, position: Tuple[float, float]) -> bool:
        """Check if position is within ground boundaries."""
        x, y = position
        return (0 <= x < self.grid_width and 0 <= y < self.grid_length)
    
    def get_zone(self, position: Tuple[float, float]) -> str:
        """Get the zone name for a position."""
        x, y = position
        for pos_name, zone in self.positions_zones.items():
            if (int(x), int(y)) in zone:
                return pos_name
        return "Centre"  # Default to Centre if not in any zone
    
    def get_distance_to_goal(self, position: Tuple[float, float], team_side: str) -> float:
        """Calculate distance to goal."""
        x, y = position
        goal_y = self.grid_width / 2
        goal_x = self.grid_length if team_side == "home" else 0
        dx = goal_x - x
        dy = goal_y - y
        return math.sqrt(dx * dx + dy * dy) * 10  # Convert grid cells to meters
    
    def _get_nearest_zone(self, position: Tuple[float, float]) -> str:
        """Find nearest zone to position."""
        x, y = position
        min_dist = float('inf')
        nearest_zone = None
        
        for pos_name, zone in self.positions_zones.items():
            for zx, zy in zone:
                dist = (zx - x) ** 2 + (zy - y) ** 2
                if dist < min_dist:
                    min_dist = dist
                    nearest_zone = pos_name
                    
        return nearest_zone or "Centre"  # Default to Centre if no zones found
    
    def get_meters_per_cell(self) -> Tuple[float, float]:
        """Get meters per cell for both dimensions."""
        return (self.meters_per_cell_width, self.meters_per_cell_length)
    
    def get_thirds(self) -> Tuple[int, int]:
        """Get y-coordinates for defensive and forward thirds."""
        return (self.defensive_third, self.forward_third)
    
    def get_corridor_boundaries(self) -> Tuple[int, int]:
        """Get x-coordinates for corridor (middle third of width)."""
        grid_third = self.grid_width // 3
        return grid_third, grid_third * 2

    def get_forward_zones(self, team_side: str) -> List[str]:
        """Get forward zones for team side"""
        if team_side == 'home':
            return ['forward_50', 'forward_flank']
        else:
            return ['back_50', 'back_flank']

    def get_defensive_zones(self, team_side: str) -> List[str]:
        """Get defensive zones for team side"""
        if team_side == 'home':
            return ['back_50', 'back_flank'] 
        else:
            return ['forward_50', 'forward_flank']

    def get_midfield_zones(self) -> List[str]:
        """Get midfield zones"""
        return ['center']

    def get_zone_center(self, zone: str) -> Tuple[float, float]:
        """Get center coordinates of zone."""
        zone_coords = self.positions_zones[zone]
        x_coords = [x for x, _ in zone_coords]
        y_coords = [y for _, y in zone_coords]
        center_x = sum(x_coords) / len(x_coords)
        center_y = sum(y_coords) / len(y_coords)
        return (center_x, center_y)

    def get_zone_boundaries(self, zone: str) -> Tuple[float, float, float, float]:
        """Get boundaries of zone."""
        zone_coords = self.positions_zones[zone]
        x_coords = [x for x, _ in zone_coords]
        y_coords = [y for _, y in zone_coords]
        return (
            min(x_coords),
            max(x_coords),
            min(y_coords),
            max(y_coords)
        )

    def get_random_position_in_zone(self, zone: str) -> Tuple[float, float]:
        """Get random valid position within zone."""
        zone_coords = self.positions_zones[zone]
        return random.choice(zone_coords)

    def get_nearest_position_in_zone(self, current_pos: Tuple[float, float], zone: str) -> Tuple[float, float]:
        """Get nearest valid position in zone to current position."""
        zone_coords = self.positions_zones[zone]
        x, y = current_pos
        min_dist = float('inf')
        nearest_pos = None
        
        for zx, zy in zone_coords:
            dist = (zx - x) ** 2 + (zy - y) ** 2
            if dist < min_dist:
                min_dist = dist
                nearest_pos = (zx, zy)
                
        return nearest_pos or self.get_zone_center(zone)  # Fallback to center if no position found

# TODO: Implement these classes with proper functionality later
class WeatherSystem:
    """
    Weather system that affects gameplay.
    Potential implementation:
    - Different weather conditions (rain, wind, etc.)
    - Impact on player performance
    - Dynamic weather changes
    - Condition-specific commentary
    - Weather-based modifiers for actions
    """
    pass

class GameClock:
    """
    Handles match timing and quarters.
    Maintains compatibility with working AI version while adding improvements.
    """
    def __init__(self):
        # Standard quarter length in seconds (20 minutes)
        self.quarter_length = 20 * 60
        
        # Current state
        self.current_quarter = 1
        self.time_elapsed = 0  # Used by working AI version
        self.quarter_time = 0.0  # More precise tracking for new version
        self.total_time = 0.0
        
        # Break lengths
        self.quarter_break_length = 5 * 60  # 5 minutes
        self.half_time_break_length = 20 * 60  # 20 minutes
        
        # Break state
        self.in_break = False
        self.break_time = 0.0
        
    def update(self, delta_time: float) -> None:
        """Update clock state"""
        if not self.in_break:
            # Update both time trackers
            self.time_elapsed += delta_time
            self.quarter_time += delta_time
            self.total_time += delta_time
        else:
            # Update break time
            self.break_time += delta_time
            
    def start_quarter(self) -> None:
        """Start a new quarter"""
        self.time_elapsed = 0
        self.quarter_time = 0.0
        self.in_break = False
        self.break_time = 0.0
        
    def end_quarter(self) -> None:
        """End current quarter"""
        self.in_break = True
        self.break_time = 0.0
        
    def start_break(self) -> None:
        """Start quarter/half time break"""
        self.in_break = True
        self.break_time = 0.0
        
    def end_break(self) -> None:
        """End quarter/half time break"""
        self.in_break = False
        self.next_quarter()
        
    def next_quarter(self) -> None:
        """Move to next quarter"""
        self.current_quarter += 1
        self.start_quarter()
        
    def get_quarter_time_str(self) -> str:
        """Get formatted quarter time string"""
        minutes = int(self.quarter_time // 60)
        seconds = int(self.quarter_time % 60)
        return f"{minutes}:{seconds:02d}"
            
    def get_break_time_str(self) -> str:
        """Get formatted break time string"""
        if not self.in_break:
            return ""
            
        break_length = (
            self.half_time_break_length if self.current_quarter == 2
            else self.quarter_break_length
        )
        
        time_remaining = max(0, break_length - self.break_time)
        minutes = int(time_remaining // 60)
        seconds = int(time_remaining % 60)
        
        return f"{minutes}:{seconds:02d}"
        
    def should_end_quarter(self) -> bool:
        """Check if quarter should end"""
        return self.time_elapsed >= self.quarter_length
            
    def should_end_break(self) -> bool:
        """Check if break should end"""
        if not self.in_break:
            return False
            
        break_length = (
            self.half_time_break_length if self.current_quarter == 2
            else self.quarter_break_length
        )
        
        return self.break_time >= break_length
        
    def is_game_over(self) -> bool:
        """Check if game is over"""
        return self.current_quarter > 4 and self.should_end_quarter()
        
    def get_quarter_name(self) -> str:
        """Get current quarter name"""
        if self.current_quarter == 1:
            return "First Quarter"
        elif self.current_quarter == 2:
            return "Second Quarter"
        elif self.current_quarter == 3:
            return "Third Quarter"
        elif self.current_quarter == 4:
            return "Final Quarter"
        else:
            return f"Quarter {self.current_quarter}"
            
    def get_break_name(self) -> str:
        """Get current break name"""
        if not self.in_break:
            return ""
            
        if self.current_quarter == 1:
            return "Quarter Time"
        elif self.current_quarter == 2:
            return "Half Time"
        elif self.current_quarter == 3:
            return "Three Quarter Time"
        else:
            return "Break"

class GameState:
    """
    Tracks current state of the match.
    Integrates GameClock functionality and maintains compatibility with working AI version.
    Handles:
    - Game timing and quarters
    - Ball and player states
    - Score tracking
    - Statistics
    - Phase management
    - Movement interpolation support
    """
    def __init__(
        self,
        ground: Ground,
        team1_players: Dict[str, Player],
        team2_players: Dict[str, Player]
    ):
        self.ground = ground
        self.team1_players = team1_players
        self.team2_players = team2_players
        
        # Game timing (integrated from GameClock)
        self.quarter = 1
        self.quarter_length = 20 * 60  # 20 minutes in seconds
        self.quarter_time = 0.0  # Time in current quarter
        self.total_time = 0.0  # Total game time
        self.in_break = False
        self.break_time = 0.0
        self.quarter_break_length = 5 * 60  # 5 minutes
        self.half_time_break_length = 20 * 60  # 20 minutes
        
        # Ball state
        self.ball_carrier = None
        self.ball_position = self.ground.get_center()
        self.ball_height = 0.0  # Height in meters
        self.ball_velocity = (0.0, 0.0)  # Velocity vector (x, y)
        
        # Game phase
        self.phase = "center_bounce"  # Current game phase
        self.last_phase = None  # Previous game phase
        self.phase_time = 0.0  # Time in current phase
        
        # Possession
        self.team_in_possession = None
        self.possession_time = 0.0
        self.last_disposal = None
        self.last_disposal_time = 0.0
        
        # Contest
        self.contest_location = None
        self.players_in_contest = []
        self.contest_type = None
        self.contest_start_time = 0.0
        
        # Score
        self.team1_score = {"goals": 0, "behinds": 0, "total": 0}
        self.team2_score = {"goals": 0, "behinds": 0, "total": 0}
        
        # Statistics
        self.stats = {
            "disposals": {"team1": 0, "team2": 0},
            "marks": {"team1": 0, "team2": 0},
            "tackles": {"team1": 0, "team2": 0},
            "inside_50s": {"team1": 0, "team2": 0},
            "clearances": {"team1": 0, "team2": 0},
            "contested_possessions": {"team1": 0, "team2": 0},
            "uncontested_possessions": {"team1": 0, "team2": 0}
        }
        
        # Movement interpolation state
        self.movement_states = {}  # player_name -> movement_state
        
    def update(self, delta_time: float) -> None:
        """Update game state"""
        if not self.in_break:
            # Update quarter time
            self.quarter_time += delta_time
            self.total_time += delta_time
            
            # Update phase time
            self.phase_time += delta_time
            
            # Update possession time
            if self.team_in_possession:
                self.possession_time += delta_time
                
            # Update ball position if in flight
            if not self.ball_carrier and self.ball_velocity != (0.0, 0.0):
                self._update_ball_physics(delta_time)
                
            # Update movement interpolation
            self._update_movement_states(delta_time)
        else:
            # Update break time
            self.break_time += delta_time
            
    def start_quarter(self) -> None:
        """Start a new quarter"""
        self.quarter_time = 0.0
        self.in_break = False
        self.break_time = 0.0
        self.phase = "center_bounce"
        self.ball_position = self.ground.get_center()
        self.ball_height = 0.0
        self.ball_velocity = (0.0, 0.0)
        
    def end_quarter(self) -> None:
        """End current quarter"""
        self.in_break = True
        self.break_time = 0.0
        
    def start_break(self) -> None:
        """Start quarter/half time break"""
        self.in_break = True
        self.break_time = 0.0
        
    def end_break(self) -> None:
        """End quarter/half time break"""
        self.in_break = False
        self.next_quarter()
        
    def next_quarter(self) -> None:
        """Move to next quarter"""
        self.quarter += 1
        self.start_quarter()
        
    def should_end_quarter(self) -> bool:
        """Check if quarter should end"""
        return self.quarter_time >= self.quarter_length
        
    def should_end_break(self) -> bool:
        """Check if break should end"""
        if not self.in_break:
            return False
            
        break_length = (
            self.half_time_break_length if self.quarter == 2
            else self.quarter_break_length
        )
        
        return self.break_time >= break_length
        
    def is_game_over(self) -> bool:
        """Check if game is over"""
        return self.quarter > 4 and self.should_end_quarter()
        
    def get_quarter_name(self) -> str:
        """Get current quarter name"""
        if self.quarter == 1:
            return "First Quarter"
        elif self.quarter == 2:
            return "Second Quarter"
        elif self.quarter == 3:
            return "Third Quarter"
        elif self.quarter == 4:
            return "Final Quarter"
        else:
            return f"Quarter {self.quarter}"
            
    def get_break_name(self) -> str:
        """Get current break name"""
        if not self.in_break:
            return ""
            
        if self.quarter == 1:
            return "Quarter Time"
        elif self.quarter == 2:
            return "Half Time"
        elif self.quarter == 3:
            return "Three Quarter Time"
        else:
            return "Break"
            
    def get_quarter_time_str(self) -> str:
        """Get formatted quarter time string"""
        minutes = int(self.quarter_time // 60)
        seconds = int(self.quarter_time % 60)
        return f"{minutes}:{seconds:02d}"
            
    def get_break_time_str(self) -> str:
        """Get formatted break time string"""
        if not self.in_break:
            return ""
            
        break_length = (
            self.half_time_break_length if self.quarter == 2
            else self.quarter_break_length
        )
        
        time_remaining = max(0, break_length - self.break_time)
        minutes = int(time_remaining // 60)
        seconds = int(time_remaining % 60)
        
        return f"{minutes}:{seconds:02d}"
            
    def change_phase(self, new_phase: str) -> None:
        """Change game phase"""
        self.last_phase = self.phase
        self.phase = new_phase
        self.phase_time = 0.0
        
        # Reset phase-specific state
        if new_phase == "contest":
            self.contest_location = self.ball_position
            self.contest_start_time = time.time()
        elif new_phase == "center_bounce":
            self.ball_position = self.ground.get_center()
            self.ball_height = 0.0
            self.ball_velocity = (0.0, 0.0)
            
    def set_ball_carrier(self, player: Optional[Player]) -> None:
        """Set ball carrier"""
        self.ball_carrier = player
        
        if player:
            # Update possession
            self.team_in_possession = (
                "team1" if player.team == self.team1_players["Ruck"].team
                else "team2"
            )
            self.possession_time = 0.0
            
            # Update ball state
            self.ball_position = player.current_position
            self.ball_height = 0.0
            self.ball_velocity = (0.0, 0.0)
        else:
            self.team_in_possession = None
            
    def record_disposal(
        self,
        player: Player,
        disposal_type: str,
        was_effective: bool
    ) -> None:
        """Record a disposal"""
        self.last_disposal = {
            'player': player,
            'type': disposal_type,
            'effective': was_effective,
            'position': player.current_position,
            'time': time.time()
        }
        self.last_disposal_time = time.time()
        
        # Update stats
        team_key = "team1" if player.team == self.team1_players["Ruck"].team else "team2"
        self.stats["disposals"][team_key] += 1
        
        # Check for inside 50
        if self._is_inside_50(player.current_position, player.team.side):
            self.stats["inside_50s"][team_key] += 1
            
    def record_mark(self, player: Player) -> None:
        """Record a mark"""
        team_key = "team1" if player.team == self.team1_players["Ruck"].team else "team2"
        self.stats["marks"][team_key] += 1
        
    def record_tackle(self, player: Player) -> None:
        """Record a tackle"""
        team_key = "team1" if player.team == self.team1_players["Ruck"].team else "team2"
        self.stats["tackles"][team_key] += 1
        
    def record_clearance(self, player: Player) -> None:
        """Record a clearance"""
        team_key = "team1" if player.team == self.team1_players["Ruck"].team else "team2"
        self.stats["clearances"][team_key] += 1
        
    def record_contested_possession(self, player: Player) -> None:
        """Record a contested possession"""
        team_key = "team1" if player.team == self.team1_players["Ruck"].team else "team2"
        self.stats["contested_possessions"][team_key] += 1
        
    def record_uncontested_possession(self, player: Player) -> None:
        """Record an uncontested possession"""
        team_key = "team1" if player.team == self.team1_players["Ruck"].team else "team2"
        self.stats["uncontested_possessions"][team_key] += 1
        
    def update_score(
        self,
        team_side: str,
        score_type: str
    ) -> None:
        """Update team score"""
        score = self.team1_score if team_side == "home" else self.team2_score
        
        if score_type == "goal":
            score["goals"] += 1
            score["total"] += 6
        elif score_type == "behind":
            score["behinds"] += 1
            score["total"] += 1
            
    def get_score_string(self) -> str:
        """Get formatted score string"""
        return (
            f"{self.team1_score['goals']}.{self.team1_score['behinds']}"
            f" ({self.team1_score['total']}) vs "
            f"{self.team2_score['goals']}.{self.team2_score['behinds']}"
            f" ({self.team2_score['total']})"
        )
        
    def _update_ball_physics(self, delta_time: float) -> None:
        """Update ball position based on physics"""
        # Update position
        self.ball_position = (
            self.ball_position[0] + self.ball_velocity[0] * delta_time,
            self.ball_position[1] + self.ball_velocity[1] * delta_time
        )
        
        # Apply gravity to height
        gravity = -9.81  # m/s^2
        self.ball_height = max(0.0, self.ball_height + gravity * delta_time)
        
        # Check if ball has landed
        if self.ball_height <= 0.0:
            self.ball_height = 0.0
            self.ball_velocity = (0.0, 0.0)
            
        # Ensure ball stays in bounds
        self.ball_position = (
            max(0, min(self.ball_position[0], self.ground.length)),
            max(0, min(self.ball_position[1], self.ground.width))
        )
        
    def _update_movement_states(self, delta_time: float) -> None:
        """Update movement interpolation states"""
        for player_name, state in list(self.movement_states.items()):
            # Calculate progress
            elapsed = time.time() - state['start_time']
            distance = self._calculate_distance(state['start_pos'], state['target_pos'])
            time_needed = distance / state['speed']
            progress = min(1.0, elapsed / time_needed)
            
            # Get player
            player = (
                self.team1_players.get(player_name) or 
                self.team2_players.get(player_name)
            )
            if not player:
                continue
                
            # Update position
            if progress < 1.0:
                player.current_position = self._interpolate_position(
                    state['start_pos'],
                    state['target_pos'],
                    progress
                )
            else:
                player.current_position = state['target_pos']
                del self.movement_states[player_name]
                
    def _interpolate_position(
        self,
        start_pos: Tuple[float, float],
        target_pos: Tuple[float, float],
        progress: float
    ) -> Tuple[float, float]:
        """Interpolate between positions"""
        x1, y1 = start_pos
        x2, y2 = target_pos
        x = x1 + (x2 - x1) * progress
        y = y1 + (y2 - y1) * progress
        return (x, y)
        
    def _calculate_distance(
        self,
        pos1: Tuple[float, float],
        pos2: Tuple[float, float]
    ) -> float:
        """Calculate distance between positions"""
        x1, y1 = pos1
        x2, y2 = pos2
        return math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)
        
    def _is_inside_50(
        self,
        position: Tuple[float, float],
        team_side: str
    ) -> bool:
        """Check if position is inside team's forward 50"""
        x = position[0]
        
        if team_side == "home":
            return x >= self.ground.length - 50.0
        else:
            return x <= 50.0

class MovementEngine:
    """Handles player movement and positioning, maintaining compatibility with original AI version."""
    
    def __init__(self, ground: Ground):
        self.ground = ground
        self.movement_history = {}  # Track player movements
        self.base_positions = {}  # Store base positions for each team
        self.team_side = None
        
    def update_movement_state(self, player: Any, target_pos: Tuple[float, float], movement_type: str = 'run') -> None:
        """Update player's movement state based on target position."""
        current_pos = player.current_position
        if not current_pos or not target_pos:
            return
            
        # Calculate movement speed based on player attributes and type
        base_speed = self._calculate_base_speed(player, movement_type)
        speed = base_speed * self._get_speed_modifiers(player)
        
        # Store movement state
        player.movement_state = {
            'start_pos': current_pos,
            'target_pos': target_pos,
            'speed': speed,
            'progress': 0.0,
        }
        
        # Update movement history
        if player not in self.movement_history:
            self.movement_history[player] = []
        self.movement_history[player].append({
            'timestamp': time.time(),
            'from': current_pos,
            'to': target_pos,
            'type': movement_type
        })

    def _calculate_base_speed(self, player: Any, movement_type: str) -> float:
        """Calculate base movement speed based on player attributes and movement type."""
        base_speed = 1.0
        
        if movement_type == 'sprint':
            base_speed = 1.5 + (player.physical_stats.speed / 20) * 0.5
        elif movement_type == 'jog':
            base_speed = 0.7 + (player.physical_stats.speed / 20) * 0.3
        elif movement_type == 'walk':
            base_speed = 0.4
            
        return base_speed
        
    def _get_speed_modifiers(self, player: Any) -> float:
        """Calculate speed modifiers based on player state and attributes."""
        modifier = 1.0
        
        # Fatigue impact
        fatigue_penalty = max(0.6, 1.0 - (player.fatigue * 0.05))
        modifier *= fatigue_penalty
        
        # Agility bonus for quick movements
        agility_bonus = 1.0 + (player.physical_stats.agility / 20) * 0.2
        modifier *= agility_bonus
        
        # Stamina impact on sustained movement
        stamina_factor = 1.0 + (player.physical_stats.stamina / 20) * 0.1
        modifier *= stamina_factor
        
        return modifier
        
    def calculate_new_position(
        self,
        player: Any,
        target_pos: Optional[Tuple[float, float]] = None,
        phase: str = 'open_play',
        ball_pos: Optional[Tuple[float, float]] = None
    ) -> Tuple[float, float]:
        """Calculate new position for player based on current state and target."""
        if not player.current_position:
            return self._get_role_based_position(player, phase, ball_pos)
            
        current_x, current_y = player.current_position
        
        if target_pos:
            target_x, target_y = target_pos
        else:
            role_pos = self._get_role_based_position(player, phase, ball_pos)
            target_x, target_y = self._determine_target_position(player, role_pos, phase, ball_pos)
            
        # Calculate movement vector
        dx = target_x - current_x
        dy = target_y - current_y
        
        # Normalize vector
        distance = math.sqrt(dx*dx + dy*dy)
        if distance > 0:
            dx /= distance
            dy /= distance
            
        # Apply player attributes
        speed = player.physical_stats.speed / 20.0
        agility = player.physical_stats.agility / 20.0
        endurance = player.physical_stats.stamina / 20.0
        
        # Calculate speed factors
        speed_factor = 0.5 + speed * 0.5
        agility_factor = 0.3 + agility * 0.7
        endurance_factor = 0.7 + endurance * 0.3
        
        # Combine factors
        actual_speed = speed * speed_factor * (1 + agility_factor * 0.3) * endurance_factor
        
        # Calculate new position
        new_x = current_x + dx * actual_speed
        new_y = current_y + dy * actual_speed
        
        # Ensure position is valid and apply tactical adjustments
        new_pos = self._get_valid_position((new_x, new_y))
        return self._apply_tactical_adjustments(new_pos, player.team.tactics, phase)
        
    def _get_role_based_position(
        self,
        player: Any,
        phase: str,
        ball_pos: Optional[Tuple[float, float]]
    ) -> Tuple[float, float]:
        """Get ideal position based on player's role and current phase."""
        base_pos = self._get_base_position(player.position, player.team_side)
        
        if not ball_pos:
            return base_pos
            
        ball_x, ball_y = ball_pos
        base_x, base_y = base_pos
        
        # Adjust based on role
        if player.position in ['FF', 'CHF']:
            # Forward positions push up
            return (base_x + 10, base_y)
        elif player.position in ['FB', 'CHB']:
            # Defensive positions stay back
            return (base_x - 5, base_y)
        elif player.position in ['Centre', 'Rover', 'RuckRover']:
            # Midfield positions follow ball
            return (ball_x + (base_x - ball_x) * 0.3, ball_y + (base_y - ball_y) * 0.3)
            
        return base_pos
        
    def _determine_target_position(
        self,
        player: Any,
        role_pos: Tuple[float, float],
        phase: str,
        ball_pos: Optional[Tuple[float, float]]
    ) -> Tuple[float, float]:
        """Determine where the player should move to based on game state."""
        if not ball_pos:
            return role_pos
            
        ball_x, ball_y = ball_pos
        role_x, role_y = role_pos
        
        if phase == 'contest':
            if self._should_contest(player, ball_pos):
                # Move to contest but maintain some structure
                return (
                    ball_x + (role_x - ball_x) * 0.2,
                    ball_y + (role_y - ball_y) * 0.2
                )
        elif phase == 'open_play':
            if player.team.tactics['mentality'] == 'attacking':
                # More aggressive positioning
                return (
                    role_x + (ball_x - role_x) * 0.3,
                    role_y + (ball_y - role_y) * 0.3
                )
            elif player.team.tactics['mentality'] == 'defensive':
                # More conservative positioning
                return (
                    role_x + (ball_x - role_x) * 0.1,
                    role_y + (ball_y - role_y) * 0.1
                )
                
        return role_pos
        
    def _should_contest(self, player: Any, ball_pos: Tuple[float, float]) -> bool:
        """Determine if player should contest for the ball."""
        if not player.current_position:
            return False
            
        distance = math.sqrt(
            (player.current_position[0] - ball_pos[0])**2 +
            (player.current_position[1] - ball_pos[1])**2
        )
        
        # Base contest radius
        contest_radius = 10.0
        
        # Adjust for player attributes
        contest_radius *= (1.0 + player.physical_stats.speed / 20.0)
        contest_radius *= (1.0 + player.ability_stats.tackling / 20.0)
        
        return distance <= contest_radius
        
    def _get_base_position(self, position: str, team_side: str) -> Tuple[float, float]:
        """Get base position for player role."""
        # Cache base positions for each team
        if team_side not in self.base_positions:
            self.base_positions[team_side] = self._generate_base_positions(team_side)
            
        return self.base_positions[team_side].get(position, (0, 0))
        
    def _generate_base_positions(self, team_side: str) -> Dict[str, Tuple[float, float]]:
        """Generate base positions for all roles."""
        positions = {}
        field_length = self.ground.length
        field_width = self.ground.width
        
        # Adjust coordinates based on team side
        multiplier = 1 if team_side == "home" else -1
        
        # Forward positions
        positions['FF'] = (field_length * 0.9 * multiplier, field_width * 0.5)
        positions['CHF'] = (field_length * 0.75 * multiplier, field_width * 0.5)
        positions['LHF'] = (field_length * 0.7 * multiplier, field_width * 0.25)
        positions['RHF'] = (field_length * 0.7 * multiplier, field_width * 0.75)
        
        # Midfield positions
        positions['Centre'] = (0, field_width * 0.5)
        positions['Rover'] = (field_length * 0.1 * multiplier, field_width * 0.4)
        positions['RuckRover'] = (field_length * 0.1 * multiplier, field_width * 0.6)
        positions['LWing'] = (0, field_width * 0.2)
        positions['RWing'] = (0, field_width * 0.8)
        
        # Defensive positions
        positions['FB'] = (field_length * -0.9 * multiplier, field_width * 0.5)
        positions['CHB'] = (field_length * -0.75 * multiplier, field_width * 0.5)
        positions['LHB'] = (field_length * -0.7 * multiplier, field_width * 0.25)
        positions['RHB'] = (field_length * -0.7 * multiplier, field_width * 0.75)
        
        return positions
        
    def _get_valid_position(self, position: Tuple[float, float]) -> Tuple[float, float]:
        """Ensure position is within field boundaries."""
        x, y = position
        x = max(0, min(self.ground.length, x))
        y = max(0, min(self.ground.width, y))
        return (x, y)
        
    def _apply_tactical_adjustments(
        self,
        position: Tuple[float, float],
        tactics: Dict[str, Any],
        phase: str
    ) -> Tuple[float, float]:
        """Apply team tactical adjustments to position."""
        x, y = position
        
        # Apply tactical adjustments based on team strategy
        if tactics['mentality'] == 'attacking':
            if phase == 'open_play':
                x += random.uniform(1, 3)  # Push up more aggressively
        elif tactics['mentality'] == 'defensive':
            if phase == 'open_play':
                x -= random.uniform(1, 2)  # Sit back but maintain some attack
                
        # Apply width adjustments
        if tactics.get('offense_strategy') == 'stay_wide':
            if y < self.ground.width / 2:
                y -= random.uniform(0.5, 1.5)  # Wider on the left
            else:
                y += random.uniform(0.5, 1.5)  # Wider on the right
                
        return self._get_valid_position((x, y))

    def calculate_ball_movement(
        self,
        current_pos: Tuple[float, float],
        target_pos: Tuple[float, float],
        movement_type: str = 'kick',
        height: float = 0.0
    ) -> Tuple[float, float]:
        """Calculate ball movement with trajectory."""
        if movement_type == 'kick':
            # Add arc to kicked balls
            max_height = height if height > 0 else 20.0  # Default max height
            progress = self._calculate_progress(current_pos, target_pos)
            current_height = self._calculate_height(progress, max_height)
            
            # Calculate new position with arc
            new_pos = self._interpolate_position(current_pos, target_pos, progress)
            return new_pos[0], new_pos[1], current_height
            
        else:  # Ground ball
            # Linear movement for ground balls
            new_pos = self._interpolate_position(current_pos, target_pos, 0.1)
            return new_pos[0], new_pos[1], 0.0
            
    def _calculate_progress(
        self,
        current_pos: Tuple[float, float],
        target_pos: Tuple[float, float]
    ) -> float:
        """Calculate movement progress between positions."""
        total_distance = self._calculate_distance(current_pos, target_pos)
        if total_distance == 0:
            return 1.0
            
        current_distance = self._calculate_distance(current_pos, target_pos)
        return 1.0 - (current_distance / total_distance)
        
    def _calculate_distance(
        self,
        pos1: Tuple[float, float],
        pos2: Tuple[float, float]
    ) -> float:
        """Calculate distance between two positions."""
        return ((pos2[0] - pos1[0]) ** 2 + (pos2[1] - pos1[1]) ** 2) ** 0.5
        
    def record_movement(self, player: Any, from_pos: Tuple[float, float], to_pos: Tuple[float, float], timestamp: float, quarter: int) -> None:
        """Record player movement for analysis and visualization."""
        if player.id not in self.movement_history:
            self.movement_history[player.id] = []
            
        self.movement_history[player.id].append({
            'from': {'x': from_pos[0], 'y': from_pos[1]},
            'to': {'x': to_pos[0], 'y': to_pos[1]},
            'time': timestamp,
            'quarter': quarter
        })
        
    def get_player_movement_data(self, player_id: str, quarter: Optional[int] = None) -> List[Dict[str, Any]]:
        """Get movement data for visualization."""
        movements = self.movement_history.get(player_id, [])
        if quarter is not None:
            movements = [m for m in movements if m['quarter'] == quarter]
        return movements

# TODO: Implement these classes with proper functionality later
class CommentaryEngine:
    """
    Generates match commentary.
    Potential implementation:
    - Different commentary styles
    - Context-aware comments
    - Player-specific remarks
    - Excitement levels
    - Historical context
    - Multiple commentators
    - Special event commentary
    """
    pass

class EventManager:
    """
    Manages match events and broadcasts.
    Potential implementation:
    - Event types (goals, marks, etc.)
    - Event history
    - Broadcasting to clients
    - Stats tracking
    - Replay system
    - Event filtering
    - Real-time updates
    """
    pass

class MatchEngine:
    """Main match simulation engine"""
    def __init__(
        self,
        ground: Ground,
        team1_players: Dict[str, Player],
        team2_players: Dict[str, Player],
        channel_layer: Any = None,
        match_group_name: str = None
    ):
        self.ground = ground
        self.team1_players = team1_players
        self.team2_players = team2_players
        
        # Initialize engines
        self.game_clock = GameClock()
        self.game_state = GameState(ground, team1_players, team2_players)
        self.movement_engine = MovementEngine(ground)
        self.weather_system = WeatherSystem()
        self.event_manager = EventManager(channel_layer, match_group_name)
        
        # Track last update time
        self.last_update = time.time()
        
    async def run_match(self) -> Generator[Dict[str, Any], None, None]:
        """Run the match simulation"""
        # Initialize positions
        self._initialize_positions()
        
        # Main game loop
        while not self.game_clock.is_game_over():
            # Get time delta
            current_time = time.time()
            delta_time = current_time - self.last_update
            self.last_update = current_time
            
            # Update game clock
            self.game_clock.update(delta_time)
            
            # Update player states
            for players in [self.team1_players.values(), self.team2_players.values()]:
                for player in players:
                    player.update(delta_time)
            
            # Update positions
            position_event = self._update_positions()
            if position_event:
                yield position_event
                
            # Process current phase
            phase_event = await self.phase_engine.process_phase(self.game_state)
            if phase_event:
                yield phase_event
                
            # Small delay between updates
            await asyncio.sleep(0.1)
            
        # Quarter end event
        yield self.event_manager.create_event(
            'quarter_end',
            self.game_clock.current_quarter,
            'match_event',
            commentary=f"End of {self._get_quarter_name(self.game_clock.current_quarter)}"
        )
        
    def _initialize_positions(self) -> None:
        """Initialize player starting positions"""
        # Get default positions from tactics engine
        for team_players in [self.team1_players, self.team2_players]:
            for player in team_players.values():
                default_pos = self.tactics_engine.get_default_position(
                    player.position,
                    player.team.side
                )
                player.current_position = default_pos
                
    def _setup_center_bounce(self) -> None:
        """Setup players for center bounce"""
        # Position ruckmen
        center_pos = self.ground.get_center()
        
        # Get primary rucks
        ruck1 = self.team1_players["Ruck"]
        ruck2 = self.team2_players["Ruck"]
        
        if ruck1 and ruck2:
            # Position rucks slightly offset from center
            ruck1.current_position = (center_pos[0] - 1, center_pos[1])
            ruck2.current_position = (center_pos[0] + 1, center_pos[1])
            
        # Position other players in formation
        for team_players in [self.team1_players, self.team2_players]:
            for player in team_players.values():
                if player.position != "Ruck":
                    pos = self.tactics_engine.get_default_position(
                        player.position,
                        player.team.side
                    )
                    player.current_position = pos
                    
    async def _process_quarter_break(self) -> None:
        """Process quarter break"""
        # Update stats
        stats = self.stats_manager.collect_player_stats(
            self.team1_players,
            self.team2_players
        )
        
        # Create stats event
        await self.event_manager.broadcast_event({
            'type': 'player_stats',
            'quarter': self.game_clock.current_quarter,
            'data': {
                'player_stats': stats
            }
        })
        
        # Short delay
        await asyncio.sleep(1.0)
        
    def _get_quarter_name(self, quarter: int) -> str:
        """Get quarter name"""
        names = {
            1: "First Quarter",
            2: "Second Quarter",
            3: "Third Quarter",
            4: "Final Quarter"
        }
        return names.get(quarter, f"Quarter {quarter}")
        
    def _update_positions(self) -> Optional[Dict[str, Any]]:
        """Update and broadcast position changes"""
        # Get delta time since last update
        current_time = time.time()
        delta_time = current_time - self.last_update
        
        # Update movement states
        position_updates = self.movement_engine.update_positions(delta_time)
        
        # Add ball position
        if self.game_state.ball_position:
            position_updates.append({
                'player_name': 'ball',
                'team': None,
                'position': {
                    'x': self.game_state.ball_position[0],
                    'y': self.game_state.ball_position[1]
                }
            })
            
        # Create position update event if we have updates
        if position_updates:
            return self.event_manager.create_event(
                'position_update',
                self.game_clock.current_quarter,
                'position_update',
                positions=position_updates
            )
            
        return None 

class PhaseEngine:
    """Handles different phases of play"""
    def __init__(
        self,
        ground: Ground,
        action_engine: Any,
        contest_engine: Any,
        event_manager: EventManager
    ):
        self.ground = ground
        self.action_engine = action_engine
        self.contest_engine = contest_engine
        self.event_manager = event_manager
        
        # Phase timings
        self.min_contest_duration = 1.0  # Minimum seconds for contest
        self.max_contest_duration = 3.0  # Maximum seconds for contest
        self.min_open_play_duration = 2.0  # Minimum seconds for open play
        
    async def process_phase(self, game_state: Any) -> Optional[Dict[str, Any]]:
        """Process current game phase"""
        if game_state.phase == "center_bounce":
            return await self._process_center_bounce(game_state)
        elif game_state.phase == "contest":
            return await self._process_contest_phase(game_state)
        elif game_state.phase == "open_play":
            return await self._process_open_play_phase(game_state)
        elif game_state.phase == "set_shot":
            return await self._process_set_shot_phase(game_state)
        elif game_state.phase == "throw_in":
            return await self._process_throw_in_phase(game_state)
        elif game_state.phase == "kick_in":
            return await self._process_kick_in_phase(game_state)
        elif game_state.phase == "loose_ball":
            return await self._process_loose_ball_phase(game_state)
            
        return None
        
    async def _process_center_bounce(self, game_state: Any) -> Dict[str, Any]:
        """Process center bounce phase"""
        # Get ruckmen
        ruck1 = self._get_primary_ruck(game_state.team1_players)
        ruck2 = self._get_primary_ruck(game_state.team2_players)
        
        if not ruck1 or not ruck2:
            # Fallback to ball up if no rucks
            game_state.change_phase("ball_up")
            return None
            
        # Process ruck contest
        contest_result = self.contest_engine.process_ruck_contest(
            ruck1,
            ruck2,
            game_state,
            is_center_bounce=True
        )
        
        # Create event
        event = self.event_manager.create_event(
            'center_bounce',
            game_state.quarter,
            'match_event',
            ruck1=ruck1.name,
            ruck2=ruck2.name,
            winner=contest_result['winner'].name,
            commentary=contest_result['commentary']
        )
        
        # Change phase based on result
        if contest_result['next_phase'] == 'contest':
            game_state.change_phase('contest')
        else:
            game_state.change_phase('open_play')
            
        return event
        
    async def _process_contest_phase(self, game_state: Any) -> Optional[Dict[str, Any]]:
        """Process contest phase"""
        # Get players in contest
        players = self._get_players_near_ball(game_state)
        
        if not players:
            game_state.change_phase("loose_ball")
            return None
            
        # Process contest
        contest_result = self.contest_engine.process_contest(
            players,
            game_state,
            contest_type=game_state.contest_type or 'ground_ball'
        )
        
        # Create event
        event = self.event_manager.create_event(
            'contest',
            game_state.quarter,
            'match_event',
            players=[p.name for p in players],
            winner=contest_result['winner'].name if contest_result.get('winner') else None,
            contest_type=contest_result['type'],
            commentary=contest_result['commentary']
        )
        
        # Change phase based on result
        game_state.change_phase(contest_result['next_phase'])
        
        return event
        
    async def _process_open_play_phase(self, game_state: Any) -> Optional[Dict[str, Any]]:
        """Process open play phase"""
        # Check if ball carrier exists
        if not game_state.ball_carrier:
            game_state.change_phase("loose_ball")
            return None
            
        # Get ball carrier's action
        action = self._decide_player_action(game_state.ball_carrier, game_state)
        
        # Process action
        action_result = await self._process_action(action, game_state)
        
        # Create event
        event = self.event_manager.create_event(
            'open_play',
            game_state.quarter,
            'match_event',
            player=game_state.ball_carrier.name,
            action=action['type'],
            commentary=action_result['commentary']
        )
        
        # Change phase based on result
        game_state.change_phase(action_result['next_phase'])
        
        return event
        
    async def _process_set_shot_phase(self, game_state: Any) -> Optional[Dict[str, Any]]:
        """Process set shot phase"""
        if not game_state.ball_carrier:
            game_state.change_phase("loose_ball")
            return None
            
        # Process shot
        shot_result = self.action_engine.process_shot(
            game_state.ball_carrier,
            game_state.ball_carrier.current_position,
            game_state
        )
        
        # Create event
        event = self.event_manager.create_event(
            'set_shot',
            game_state.quarter,
            'match_event',
            player=game_state.ball_carrier.name,
            result=shot_result['outcome'],
            commentary=shot_result['commentary']
        )
        
        # Update score if goal/behind
        if shot_result['outcome'] in ['goal', 'behind']:
            game_state.update_score(
                game_state.ball_carrier.team.side,
                shot_result['outcome']
            )
            
        # Change phase
        game_state.change_phase(shot_result['next_phase'])
        
        return event
        
    def _get_players_near_ball(self, game_state: Any, radius: float = 5.0) -> List[Any]:
        """Get players near the ball"""
        nearby_players = []
        
        for players in [game_state.team1_players.values(), game_state.team2_players.values()]:
            for player in players:
                distance = self._calculate_distance(
                    player.current_position,
                    game_state.ball_position
                )
                if distance <= radius:
                    nearby_players.append(player)
                    
        return nearby_players
        
    def _calculate_distance(
        self,
        pos1: Tuple[float, float],
        pos2: Tuple[float, float]
    ) -> float:
        """Calculate distance between positions"""
        x1, y1 = pos1
        x2, y2 = pos2
        return math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)
        
    def _get_primary_ruck(self, team_players: Dict[str, Any]) -> Optional[Any]:
        """Get team's primary ruck"""
        for player in team_players.values():
            if player.position == "Ruck":
                return player
        return None
        
    def _decide_player_action(self, player: Any, game_state: Any) -> Dict[str, Any]:
        """Decide action for player"""
        # Get pressure on player
        pressure = self.action_engine.calculate_pressure(player, game_state)
        
        # Get possible actions based on situation
        possible_actions = self._get_possible_actions(player, game_state, pressure)
        
        # Rate each action
        rated_actions = []
        for action in possible_actions:
            rating = self._rate_action(action, player, game_state, pressure)
            rated_actions.append((rating, action))
            
        # Choose best action
        rated_actions.sort(reverse=True)
        if rated_actions:
            return rated_actions[0][1]
            
        # Default to clearing kick if no good options
        return {
            'type': 'clearing_kick',
            'target': None
        }
        
    def _get_possible_actions(
        self,
        player: Any,
        game_state: Any,
        pressure: float
    ) -> List[Dict[str, Any]]:
        """Get possible actions for player"""
        actions = []
        
        # Always consider handball and kick
        actions.extend([
            {'type': 'handball', 'target': None},
            {'type': 'kick', 'target': None}
        ])
        
        # Add shot if in range
        if self._is_scoring_position(player, game_state):
            actions.append({'type': 'shot', 'target': None})
            
        # Add bounce if not under pressure
        if pressure < 0.3:
            actions.append({'type': 'bounce', 'target': None})
            
        return actions
        
    def _rate_action(
        self,
        action: Dict[str, Any],
        player: Any,
        game_state: Any,
        pressure: float
    ) -> float:
        """Rate suitability of action"""
        if action['type'] == 'shot':
            return self._rate_shot(player, game_state)
        elif action['type'] == 'handball':
            return self._rate_handball(player, game_state, pressure)
        elif action['type'] == 'kick':
            return self._rate_kick(player, game_state, pressure)
        elif action['type'] == 'bounce':
            return self._rate_bounce(player, game_state, pressure)
            
        return 0.0
        
    def _is_scoring_position(self, player: Any, game_state: Any) -> bool:
        """Check if player is in scoring position"""
        # Get distance to goal
        distance = self.ground.get_distance_to_goal(
            player.current_position,
            player.team.side
        )
        
        # Check if within 50m
        return distance <= 50.0
        
    async def _process_throw_in_phase(self, game_state: Any) -> Optional[Dict[str, Any]]:
        """Process throw in phase"""
        # Get nearby players
        players = self._get_players_near_ball(game_state, radius=3.0)
        
        if not players:
            game_state.change_phase("loose_ball")
            return None
            
        # Process throw in contest
        contest_result = self.contest_engine.process_contest(
            players,
            game_state,
            contest_type='throw_in'
        )
        
        # Create event
        event = self.event_manager.create_event(
            'throw_in',
            game_state.quarter,
            'match_event',
            players=[p.name for p in players],
            winner=contest_result['winner'].name if contest_result.get('winner') else None,
            commentary=contest_result['commentary']
        )
        
        # Change phase based on result
        game_state.change_phase(contest_result['next_phase'])
        
        return event
        
    async def _process_kick_in_phase(self, game_state: Any) -> Optional[Dict[str, Any]]:
        """Process kick in phase"""
        if not game_state.ball_carrier:
            game_state.change_phase("loose_ball")
            return None
            
        # Get kick target
        action = self._decide_player_action(game_state.ball_carrier, game_state)
        
        # Process kick
        kick_result = await self._process_action(action, game_state)
        
        # Create event
        event = self.event_manager.create_event(
            'kick_in',
            game_state.quarter,
            'match_event',
            player=game_state.ball_carrier.name,
            target=kick_result.get('target', {}).get('name'),
            commentary=kick_result['commentary']
        )
        
        # Change phase
        game_state.change_phase(kick_result['next_phase'])
        
        return event
        
    async def _process_loose_ball_phase(self, game_state: Any) -> Optional[Dict[str, Any]]:
        """Process loose ball phase"""
        # Get nearby players
        players = self._get_players_near_ball(game_state)
        
        if not players:
            # Ball rolls to new position
            new_pos = self._calculate_loose_ball_movement(game_state)
            game_state.ball_position = new_pos
            return None
            
        # Process contest for loose ball
        contest_result = self.contest_engine.process_contest(
            players,
            game_state,
            contest_type='ground_ball'
        )
        
        # Create event
        event = self.event_manager.create_event(
            'loose_ball',
            game_state.quarter,
            'match_event',
            players=[p.name for p in players],
            winner=contest_result['winner'].name if contest_result.get('winner') else None,
            commentary=contest_result['commentary']
        )
        
        # Change phase based on result
        game_state.change_phase(contest_result['next_phase'])
        
        return event
        
    async def _process_action(
        self,
        action: Dict[str, Any],
        game_state: Any
    ) -> Dict[str, Any]:
        """Process player action"""
        if action['type'] == 'shot':
            return self.action_engine.process_shot(
                game_state.ball_carrier,
                game_state.ball_carrier.current_position,
                game_state
            )
        elif action['type'] in ['kick', 'handball']:
            return self.action_engine.process_disposal(
                game_state.ball_carrier,
                action['type'],
                action.get('target'),
                game_state
            )
        elif action['type'] == 'bounce':
            return self.action_engine.process_bounce(
                game_state.ball_carrier,
                game_state
            )
        elif action['type'] == 'clearing_kick':
            return self.action_engine.process_clearing_kick(
                game_state.ball_carrier,
                game_state
            )
            
        return {
            'success': False,
            'next_phase': 'loose_ball',
            'commentary': f"{game_state.ball_carrier.name} loses possession"
        }
        
    def _rate_shot(self, player: Any, game_state: Any) -> float:
        """Rate shot opportunity"""
        # Base rating on distance and angle
        distance = self.ground.get_distance_to_goal(
            player.current_position,
            player.team.side
        )
        
        # Get angle to goal (0 is straight in front)
        angle = abs(self._calculate_shot_angle(
            player.current_position,
            player.team.side
        ))
        
        # Rate based on distance (higher is better)
        distance_rating = max(0, 1 - (distance / 60))
        
        # Rate based on angle (higher is better)
        angle_factor = max(0, 1 - (angle / 45))  # Max angle 45 degrees
        
        # Combine ratings
        base_chance = (distance_rating + angle_factor) / 2
        return base_chance * player.ability_stats.get('goal_kicking', 0.5)
        
    def _rate_handball(
        self,
        player: Any,
        game_state: Any,
        pressure: float
    ) -> float:
        """Rate handball opportunity"""
        # Base rating on pressure (lower pressure is better)
        base_rating = 1 - pressure
        
        # Adjust based on nearby teammates
        nearby_teammates = self._get_nearby_teammates(player, game_state)
        if not nearby_teammates:
            return 0.0
            
        # Rate higher if good handball targets available
        target_ratings = []
        for teammate in nearby_teammates:
            target_rating = self._rate_handball_target(
                player,
                teammate,
                game_state
            )
            target_ratings.append(target_rating)
            
        if target_ratings:
            return base_rating * max(target_ratings)
            
        return 0.0
        
    def _rate_kick(
        self,
        player: Any,
        game_state: Any,
        pressure: float
    ) -> float:
        """Rate kick opportunity"""
        # Base rating on pressure (lower pressure is better)
        base_rating = 1 - pressure
        
        # Get potential targets
        targets = self._get_kick_targets(player, game_state)
        if not targets:
            return base_rating * 0.5  # Can still kick to space
            
        # Rate each target
        target_ratings = []
        for target in targets:
            rating = self._rate_kick_target(player, target, game_state)
            target_ratings.append(rating)
            
        if target_ratings:
            return base_rating * max(target_ratings)
            
        return base_rating * 0.5
        
    def _rate_bounce(
        self,
        player: Any,
        game_state: Any,
        pressure: float
    ) -> float:
        """Rate bounce opportunity"""
        # Only bounce if low pressure and space ahead
        if pressure >= 0.3:
            return 0.0
            
        # Check if space ahead
        space_rating = self._rate_space_ahead(player, game_state)
        
        # Combine with pressure rating
        return (1 - pressure) * space_rating
        
    def _calculate_shot_angle(
        self,
        position: Tuple[float, float],
        team_side: str
    ) -> float:
        """Calculate angle to goal (degrees)"""
        # Get goal position
        goal_pos = self.ground.get_goal_line_position(team_side)
        
        # Calculate angle
        dx = goal_pos[0] - position[0]
        dy = goal_pos[1] - position[1]
        
        return math.degrees(math.atan2(dy, dx))
        
    def _get_nearby_teammates(
        self,
        player: Any,
        game_state: Any,
        radius: float = 10.0
    ) -> List[Any]:
        """Get nearby teammates"""
        nearby = []
        team_players = (
            game_state.team1_players if player in game_state.team1_players.values()
            else game_state.team2_players
        )
        
        for teammate in team_players.values():
            if teammate != player:
                distance = self._calculate_distance(
                    player.current_position,
                    teammate.current_position
                )
                if distance <= radius:
                    nearby.append(teammate)
                    
        return nearby
        
    def _rate_handball_target(
        self,
        player: Any,
        target: Any,
        game_state: Any
    ) -> float:
        """Rate handball target"""
        # Get distance
        distance = self._calculate_distance(
            player.current_position,
            target.current_position
        )
        
        # Too far for handball
        if distance > 5.0:
            return 0.0
            
        # Check if target under pressure
        target_pressure = self.action_engine.calculate_pressure(
            target,
            game_state
        )
        
        # Rate based on distance and pressure
        distance_rating = 1 - (distance / 5.0)
        pressure_rating = 1 - target_pressure
        
        return (distance_rating + pressure_rating) / 2
        
    def _get_kick_targets(
        self,
        player: Any,
        game_state: Any
    ) -> List[Any]:
        """Get potential kick targets"""
        team_players = (
            game_state.team1_players if player in game_state.team1_players.values()
            else game_state.team2_players
        )
        
        targets = []
        for teammate in team_players.values():
            if teammate != player:
                # Check if in range
                distance = self._calculate_distance(
                    player.current_position,
                    teammate.current_position
                )
                if distance <= 50.0:  # Max kick distance
                    targets.append(teammate)
                    
        return targets
        
    def _rate_kick_target(
        self,
        player: Any,
        target: Any,
        game_state: Any
    ) -> float:
        """Rate kick target"""
        # Get distance
        distance = self._calculate_distance(
            player.current_position,
            target.current_position
        )
        
        # Check if target under pressure
        target_pressure = self.action_engine.calculate_pressure(
            target,
            game_state
        )
        
        # Rate based on distance and pressure
        distance_rating = 1 - (distance / 50.0)
        pressure_rating = 1 - target_pressure
        
        # Bonus for targets further up the ground
        forward_progress = self._calculate_forward_progress(
            player,
            target
        )
        
        return (distance_rating + pressure_rating + forward_progress) / 3
        
    def _rate_space_ahead(
        self,
        player: Any,
        game_state: Any,
        check_distance: float = 10.0
    ) -> float:
        """Rate space ahead of player"""
        # Get point ahead of player
        angle = 0.0  # Straight ahead
        if player.team.side == "away":
            angle = 180.0
            
        dx = check_distance * math.cos(math.radians(angle))
        dy = check_distance * math.sin(math.radians(angle))
        
        check_pos = (
            player.current_position[0] + dx,
            player.current_position[1] + dy
        )
        
        # Count opponents in the space
        opponents = 0
        for opp in self._get_opponents(player, game_state):
            distance = self._calculate_distance(
                check_pos,
                opp.current_position
            )
            if distance < check_distance:
                opponents += 1
                
        # Rate based on number of opponents
        return max(0, 1 - (opponents * 0.25))
        
    def _calculate_forward_progress(
        self,
        player: Any,
        target: Any
    ) -> float:
        """Calculate forward progress of target"""
        if player.team.side == "home":
            progress = target.current_position[0] - player.current_position[0]
        else:
            progress = player.current_position[0] - target.current_position[0]
            
        # Normalize to 0-1
        max_progress = 50.0  # Max expected progress
        return max(0, min(1, progress / max_progress))
        
    def _get_opponents(self, player: Any, game_state: Any) -> List[Any]:
        """Get opponent players"""
        if player in game_state.team1_players.values():
            return list(game_state.team2_players.values())
        else:
            return list(game_state.team1_players.values())
            
    def _calculate_loose_ball_movement(
        self,
        game_state: Any
    ) -> Tuple[float, float]:
        """Calculate new position for loose ball"""
        # Get current velocity
        vx, vy = game_state.ball_velocity
        
        # Apply friction
        friction = 0.8
        vx *= friction
        vy *= friction
        
        # Update position
        x = game_state.ball_position[0] + vx
        y = game_state.ball_position[1] + vy
        
        # Ensure in bounds
        x = max(0, min(x, self.ground.length))
        y = max(0, min(y, self.ground.width))
        
        return (x, y)

    def calculate_tactical_position(
        self,
        player: Any,
        phase: str,
        has_possession: bool,
        ball_position: Tuple[float, float]
    ) -> Optional[Tuple[float, float]]:
        """Calculate tactical position based on game phase and context."""
        if phase == 'contest':
            return self._calculate_contest_position(player, ball_position)
        elif phase == 'attacking':
            return self._calculate_attacking_position(player, ball_position)
        elif phase == 'defensive':
            return self._calculate_defensive_position(player, ball_position)
        return None

    def _calculate_contest_position(
        self,
        player: Any,
        ball_position: Tuple[float, float]
    ) -> Tuple[float, float]:
        """Calculate position during contested situations."""
        if player.position in ['Ruck', 'RuckRover']:
            return ball_position
            
        # Get optimal contest position based on role
        contest_positions = self._get_contest_positions(ball_position)
        if not contest_positions:
            return ball_position
            
        # Find best position based on player attributes
        best_pos = None
        best_rating = -1
        
        for pos in contest_positions:
            rating = self._rate_contest_position(player, pos, ball_position)
            if rating > best_rating:
                best_rating = rating
                best_pos = pos
                
        return best_pos if best_pos else ball_position

    def _calculate_attacking_position(
        self,
        player: Any,
        ball_position: Tuple[float, float]
    ) -> Tuple[float, float]:
        """Calculate position during attacking play."""
        # Define attacking zones based on position
        attacking_roles = {
            "FF": {"preferred_distance": 15, "preferred_side": 0},    # Central
            "CHF": {"preferred_distance": 25, "preferred_side": 0},   # Central
            "LF": {"preferred_distance": 20, "preferred_side": -10},  # Wide left
            "RF": {"preferred_distance": 20, "preferred_side": 10},   # Wide right
            "LHF": {"preferred_distance": 30, "preferred_side": -15}, # Very wide left
            "RHF": {"preferred_distance": 30, "preferred_side": 15},  # Very wide right
            "Centre": {"preferred_distance": 35, "preferred_side": 0},
            "Wing": {"preferred_distance": 40, "preferred_side": 0}
        }
        
        role_prefs = attacking_roles.get(player.position, {"preferred_distance": 30, "preferred_side": 0})
        
        # Calculate base position
        x = ball_position[0] + role_prefs["preferred_side"]
        y = ball_position[1] + role_prefs["preferred_distance"]
        
        # Adjust for boundaries
        x = max(0, min(x, self.ground.width))
        y = max(0, min(y, self.ground.length))
        
        return (x, y)

    def _calculate_defensive_position(
        self,
        player: Any,
        ball_position: Tuple[float, float]
    ) -> Tuple[float, float]:
        """Calculate position during defensive play."""
        # Define defensive roles and their preferred positions
        defensive_roles = {
            "FB": {"preferred_distance": 10, "preferred_side": 0},    # Stay central and close
            "CHB": {"preferred_distance": 15, "preferred_side": 0},   # Central
            "LB": {"preferred_distance": 12, "preferred_side": -10},  # Wide left
            "RB": {"preferred_distance": 12, "preferred_side": 10},   # Wide right
            "LHB": {"preferred_distance": 18, "preferred_side": -15}, # Very wide left
            "RHB": {"preferred_distance": 18, "preferred_side": 15},  # Very wide right
        }
        
        role_prefs = defensive_roles.get(player.position, {"preferred_distance": 15, "preferred_side": 0})
        
        # Calculate base position
        x = ball_position[0] + role_prefs["preferred_side"]
        y = ball_position[1] - role_prefs["preferred_distance"]  # Move towards defensive goal
        
        # Adjust for boundaries
        x = max(0, min(x, self.ground.width))
        y = max(0, min(y, self.ground.length))
        
        return (x, y)

    def _get_contest_positions(
        self,
        ball_position: Tuple[float, float],
        radius: float = 5.0
    ) -> List[Tuple[float, float]]:
        """Get valid positions around contest point."""
        positions = []
        x, y = ball_position
        
        for dx in [-radius, 0, radius]:
            for dy in [-radius, 0, radius]:
                new_x = x + dx
                new_y = y + dy
                if self.ground.is_in_bounds((new_x, new_y)):
                    positions.append((new_x, new_y))
                    
        return positions

    def _rate_contest_position(
        self,
        player: Any,
        position: Tuple[float, float],
        ball_position: Tuple[float, float]
    ) -> float:
        """Rate how good a contest position is for a player."""
        # Base rating starts at 1.0
        rating = 1.0
        
        # Distance to ball factor (closer is better)
        distance = self._calculate_distance(position, ball_position)
        rating *= max(0.1, 1.0 - (distance / 10.0))
        
        # Player attribute factors
        rating *= (0.5 + (player.physical_stats.speed / 100) * 0.5)
        rating *= (0.5 + (player.physical_stats.agility / 100) * 0.5)
        
        # Add some randomness
        rating *= random.uniform(0.9, 1.1)
        
        return rating

class ActionEngine:
    """Handles player actions and decisions, maintaining compatibility with original AI version."""
    
    def __init__(self, ground: Ground):
        self.ground = ground
        
    def process_action(
        self,
        player: Any,
        action_type: str,
        target: Optional[Any] = None,
        game_state: Optional[Any] = None
    ) -> Dict[str, Any]:
        """Process a player action."""
        # Get base chance of success
        base_chance = self._get_base_chance(player, action_type)
        
        # Apply modifiers
        final_chance = self._apply_modifiers(base_chance, player, action_type, target, game_state)
        
        # Determine success
        success = random.random() < final_chance
        
        # Process result
        result = {
            'success': success,
            'player': player,
            'action': action_type,
            'chance': final_chance
        }
        
        if target:
            result['target'] = target
            
        return result
        
    def _get_base_chance(self, player: Any, action_type: str) -> float:
        """Get base success chance for an action."""
        # Base chances from original AI
        base_chances = {
            'kick': 0.75,
            'handball': 0.8,
            'mark': 0.6,
            'tackle': 0.5,
            'bounce': 0.85,
            'pickup': 0.7
        }
        
        base = base_chances.get(action_type, 0.5)
        
        # Adjust based on relevant attributes
        if action_type == 'kick':
            base *= (0.5 + (player.ability_stats.kicking / 100) * 0.5)
        elif action_type == 'handball':
            base *= (0.5 + (player.ability_stats.handballing / 100) * 0.5)
        elif action_type == 'mark':
            base *= (0.5 + (player.ability_stats.marking / 100) * 0.5)
        elif action_type == 'tackle':
            base *= (0.5 + (player.ability_stats.tackling / 100) * 0.5)
            
        return base
        
    def _apply_modifiers(
        self,
        base_chance: float,
        player: Any,
        action_type: str,
        target: Optional[Any],
        game_state: Optional[Any]
    ) -> float:
        """Apply situational modifiers to base chance."""
        chance = base_chance
        
        # Pressure modifier
        if game_state:
            pressure = self._calculate_pressure(player, game_state)
            chance *= max(0.5, 1.0 - pressure)
            
        # Fatigue modifier
        if hasattr(player, 'fatigue'):
            chance *= max(0.6, 1.0 - (player.fatigue / 200))
            
        # Weather modifier (if available)
        if game_state and hasattr(game_state, 'weather'):
            weather_mod = game_state.weather.get_modifiers().get(action_type, 1.0)
            chance *= weather_mod
            
        # Distance modifier for kicks
        if action_type == 'kick' and target:
            distance = self._calculate_distance(player.current_position, target.current_position)
            max_range = 50.0  # Maximum effective kicking range
            if distance > max_range:
                chance *= max(0.5, 1.0 - ((distance - max_range) / max_range))
                
        return max(0.1, min(0.95, chance))  # Keep between 10% and 95%
        
    def _calculate_pressure(self, player: Any, game_state: Any) -> float:
        """Calculate pressure on a player."""
        if not player.current_position:
            return 0.0
            
        pressure = 0.0
        opponent_team = game_state.team2_players if player in game_state.team1_players.values() else game_state.team1_players
        
        for opponent in opponent_team.values():
            if not opponent.current_position:
                continue
                
            distance = self._calculate_distance(player.current_position, opponent.current_position)
            if distance < 10.0:  # Pressure radius
                # Closer opponents apply more pressure
                pressure += (1.0 - (distance / 10.0)) * (opponent.ability_stats.tackling / 100)
                
        return min(1.0, pressure)
        
    def _calculate_distance(
        self,
        pos1: Tuple[float, float],
        pos2: Tuple[float, float]
    ) -> float:
        """Calculate distance between two positions."""
        return ((pos2[0] - pos1[0]) ** 2 + (pos2[1] - pos1[1]) ** 2) ** 0.5
        
    def handle_disposal(
        self,
        player: Any,
        current_position: Tuple[float, float],
        team_players: Dict[str, Any],
        opponent_players: Dict[str, Any],
        team_tactics: Dict[str, Any]
    ) -> Tuple[Any, Any, str]:
        """Handle player disposal decisions."""
        # Find disposal options
        options, disposer_pressure = self.find_disposal_options(
            player,
            current_position,
            team_players,
            opponent_players,
            team_tactics
        )
        
        # Choose best option
        return self.choose_disposal_option(player, options, team_tactics, disposer_pressure)
        
    def find_disposal_options(
        self,
        player: Any,
        current_position: Tuple[float, float],
        team_players: Dict[str, Any],
        opponent_players: Dict[str, Any],
        team_tactics: Dict[str, Any]
    ) -> Tuple[List[Dict[str, Any]], float]:
        """Find available disposal options."""
        options = []
        disposer_pressure = 0.0
        
        # Calculate pressure on disposer
        for opponent in opponent_players.values():
            if not opponent.current_position:
                continue
                
            distance = self._calculate_distance(current_position, opponent.current_position)
            if distance < 10.0:
                disposer_pressure += (1.0 - (distance / 10.0))
                
        # Find potential targets
        for teammate in team_players.values():
            if teammate == player or not teammate.current_position:
                continue
                
            distance = self._calculate_distance(current_position, teammate.current_position)
            if distance > 50.0:  # Maximum disposal distance
                continue
                
            # Calculate target rating
            target_pressure = 0.0
            for opponent in opponent_players.values():
                if not opponent.current_position:
                    continue
                    
                opp_distance = self._calculate_distance(teammate.current_position, opponent.current_position)
                if opp_distance < 10.0:
                    target_pressure += (1.0 - (opp_distance / 10.0))
                    
            options.append({
                'target': teammate,
                'distance': distance,
                'pressure': target_pressure
            })
            
        return options, min(1.0, disposer_pressure)
        
    def choose_disposal_option(
        self,
        player: Any,
        options: List[Dict[str, Any]],
        team_tactics: Dict[str, Any],
        disposer_pressure: float
    ) -> Tuple[Any, Any, str]:
        """Choose best disposal option."""
        if not options:
            if disposer_pressure > 0.7:
                return None, None, 'blind_handball'
            else:
                return None, None, 'kick_to_space'
                
        # Rate each option
        rated_options = []
        for option in options:
            rating = self._rate_disposal_option(player, option, team_tactics, disposer_pressure)
            rated_options.append((rating, option))
            
        # Choose best option
        rated_options.sort(reverse=True, key=lambda x: x[0])
        best_option = rated_options[0][1]
        
        # Decide disposal type
        if best_option['distance'] < 15.0:
            return best_option['target'], None, 'handball'
        else:
            return best_option['target'], None, 'kick'
            
    def _rate_disposal_option(
        self,
        player: Any,
        option: Dict[str, Any],
        team_tactics: Dict[str, Any],
        disposer_pressure: float
    ) -> float:
        """Rate a disposal option based on situation and tactics."""
        # Calculate tactical vision and visibility
        tactical_vision = player.ability_stats.tactical
        effective_vision = tactical_vision - disposer_pressure
        
        # Base visibility chance
        visibility_chance = effective_vision
        visibility_chance *= (1 + option['space_rating'])
        distance_factor = max(0.2, 1 - (option['distance'] / 10))
        visibility_chance *= distance_factor
        
        # If player can't "see" this option, rate it very low
        if random.random() >= visibility_chance:
            return 0.1
            
        # Base weight
        weight = 1.0
        
        # Prefer options that align with team tactics
        weight *= (1 + option['tactical_rating'])
        
        # Prefer open players
        weight *= (1 + option['space_rating'] * 2)
        
        # Adjust for risk/reward based on mentality
        if team_tactics['mentality'] == 'attacking':
            # Reward forward movement more
            if option['target'].position in ["FF", "CHF", "LF", "RF", 'LHF', 'RHF']:
                weight *= 1.3
        elif team_tactics['mentality'] == 'defensive':
            # Reward safer options
            weight *= (1 + option['space_rating'])
            
        # Disposal type preference based on skills and situation
        if option['can_kick'] and option['can_handball']:
            kick_pref = player.ability_stats.kicking / 100
            handball_pref = player.ability_stats.handball / 100
            
            # Under pressure, prefer handballs
            if disposer_pressure > 0.5:
                handball_pref *= 1.5
            
            # For longer distances, prefer kicks
            if option['distance'] > 2:
                kick_pref *= 1.3
                
            # For open targets, prefer kicks
            if option['space_rating'] > 0.7:
                kick_pref *= 1.2
                
            # Add appropriate skill weighting
            weight *= max(kick_pref, handball_pref)
            
        return weight

class ContestEngine:
    """Handles physical contests between players, maintaining exact compatibility with working AI version."""
    
    def __init__(self, ground: Ground, action_engine: ActionEngine):
        self.ground = ground
        self.action_engine = action_engine
        self.stats_manager = StatsManager()
        
        # Required instance variables from working AI
        self.ball_position = None
        self.play_state = None
        self.field_zones = None
        self.disposer = None
        self.team1 = None
        self.team2 = None
        self.team1_players = None
        self.team2_players = None
        self.team1_tactics = None
        self.team2_tactics = None

    def initialize_teams(self, team1: Any, team2: Any, team1_players: Dict[str, Any], team2_players: Dict[str, Any], team1_tactics: Dict[str, Any], team2_tactics: Dict[str, Any]) -> None:
        """Initialize team-related instance variables."""
        self.team1 = team1
        self.team2 = team2
        self.team1_players = team1_players
        self.team2_players = team2_players
        self.team1_tactics = team1_tactics
        self.team2_tactics = team2_tactics

    def get_play_state(self, team_in_possession: Any, goal: Optional[bool] = None) -> str:
        """Direct migration of original get_play_state from working AI."""
        if goal:
            return "goal"
            
        if not team_in_possession:
            return "contest"
            
        # Get ball position
        if not self.ball_position:
            return "contest"
            
        x, y = self.ball_position
        
        # Check if in scoring range
        if team_in_possession == self.team1:
            if x > 120:  # Deep forward
                return "scoring_opportunity"
        else:
            if x < 40:  # Deep forward for away team
                return "scoring_opportunity"
                
        # Check for congestion
        nearby_players = 0
        all_players = list(self.team1_players.values()) + list(self.team2_players.values())
        for player in all_players:
            if not player.current_position:
                continue
            dx = player.current_position[0] - x
            dy = player.current_position[1] - y
            if math.sqrt(dx*dx + dy*dy) < 10:  # Within 10 meters
                nearby_players += 1
                
        if nearby_players > 4:  # High congestion
            return "contest"
            
        return "open_play"

    def calculate_field_zones(self, ball_position: Tuple[float, float], play_state: str) -> Dict[str, List[Tuple[float, float]]]:
        """Direct migration of original calculate_field_zones from working AI."""
        zones = {}
        
        # Forward zones (attacking team)
        zones['forward'] = [(x, y) for x in range(int(self.ground.length * 0.7), self.ground.length)
                          for y in range(self.ground.width)]
                          
        # Midfield zones
        zones['midfield'] = [(x, y) for x in range(int(self.ground.length * 0.3), int(self.ground.length * 0.7))
                           for y in range(self.ground.width)]
                           
        # Defensive zones
        zones['defensive'] = [(x, y) for x in range(int(self.ground.length * 0.3))
                            for y in range(self.ground.width)]
                            
        return zones

    def update_individual_player_position(self, player: Any, is_possession_team: bool, play_state: str, field_zones: Dict[str, List[Tuple[float, float]]], ball_position: Tuple[float, float]) -> None:
        """Direct migration of original update_individual_player_position from working AI."""
        if not player.current_position:
            return

        # Get distance to ball
        distance = math.sqrt(
            (player.current_position[0] - ball_position[0])**2 +
            (player.current_position[1] - ball_position[1])**2
        )

        # Handle different movement scenarios
        if distance < 10:  # Close to ball
            if is_possession_team:
                self.handle_close_teammate_movement(player, ball_position, field_zones)
            else:
                self.handle_close_opponent_movement(player, ball_position, field_zones)
        else:  # Further from ball
            self.handle_distant_player_movement(player, ball_position, field_zones, is_possession_team, play_state)

    def handle_close_teammate_movement(self, player: Any, ball_position: Tuple[float, float], field_zones: Dict[str, List[Tuple[float, float]]]) -> None:
        """Direct migration of original handle_close_teammate_movement from working AI."""
        # Get optimal support position
        support_pos = self.calculate_support_positions(ball_position, player.position)
        
        # Move towards support position
        if support_pos:
            dx = support_pos[0] - player.current_position[0]
            dy = support_pos[1] - player.current_position[1]
            
            # Normalize movement vector
            distance = math.sqrt(dx*dx + dy*dy)
            if distance > 0:
                dx = dx / distance
                dy = dy / distance
                
            # Apply movement
            player.current_position = (
                player.current_position[0] + dx,
                player.current_position[1] + dy
            )

    def handle_close_opponent_movement(self, player: Any, ball_position: Tuple[float, float], field_zones: Dict[str, List[Tuple[float, float]]]) -> None:
        """Direct migration of original handle_close_opponent_movement from working AI."""
        # Move to apply pressure
        dx = ball_position[0] - player.current_position[0]
        dy = ball_position[1] - player.current_position[1]
        
        # Normalize movement vector
        distance = math.sqrt(dx*dx + dy*dy)
        if distance > 0:
            dx = dx / distance
            dy = dy / distance
            
        # Apply movement
        player.current_position = (
            player.current_position[0] + dx,
            player.current_position[1] + dy
        )

    def handle_distant_player_movement(self, player: Any, ball_position: Tuple[float, float], field_zones: Dict[str, List[Tuple[float, float]]], is_possession_team: bool, play_state: str) -> None:
        """Direct migration of original handle_distant_player_movement from working AI."""
        # Get strategic position based on play state
        strategic_pos = self.calculate_strategic_positions(player, play_state, is_possession_team, ball_position)
        
        if strategic_pos:
            dx = strategic_pos[0] - player.current_position[0]
            dy = strategic_pos[1] - player.current_position[1]
            
            # Normalize movement vector
            distance = math.sqrt(dx*dx + dy*dy)
            if distance > 0:
                dx = dx / distance
                dy = dy / distance
                
            # Apply movement
            player.current_position = (
                player.current_position[0] + dx,
                player.current_position[1] + dy
            )

    def calculate_support_positions(self, ball_position: Tuple[float, float], player_position: str) -> Optional[Tuple[float, float]]:
        """Direct migration of original calculate_support_positions from working AI."""
        # Define support positions based on player role
        if player_position in ['FF', 'CHF']:
            # Forward positions - stay ahead of ball
            return (ball_position[0] + 10, ball_position[1])
        elif player_position in ['FB', 'CHB']:
            # Defensive positions - stay behind ball
            return (ball_position[0] - 10, ball_position[1])
        elif player_position in ['Centre', 'Rover', 'RuckRover']:
            # Midfield positions - stay close to ball
            return (ball_position[0], ball_position[1] + random.uniform(-5, 5))
        return None

    def calculate_strategic_positions(self, player: Any, play_state: str, is_possession_team: bool, ball_position: Tuple[float, float]) -> Optional[Tuple[float, float]]:
        """Direct migration of original calculate_strategic_positions from working AI."""
        if play_state == 'possession':
            if is_possession_team:
                # Attacking team spreads out
                if player.position in ['FF', 'CHF']:
                    return (ball_position[0] + 20, ball_position[1])
                elif player.position in ['FB', 'CHB']:
                    return (ball_position[0] - 10, ball_position[1])
            else:
                # Defending team compresses
                if player.position in ['FF', 'CHF']:
                    return (ball_position[0] - 5, ball_position[1])
                elif player.position in ['FB', 'CHB']:
                    return (ball_position[0] - 15, ball_position[1])
        return None

    def ruck_contest(self, ruck1: Any, ruck2: Any) -> Any:
        """Direct migration of original ruck_contest from working AI."""
        print(f"Ruck contest between {ruck1.name} and {ruck2.name}")
        ruck1_score = self.calculate_ruck_performance(ruck1)
        ruck2_score = self.calculate_ruck_performance(ruck2)
        print(f"{ruck1.name} score: {ruck1_score}, {ruck2.name} score: {ruck2_score}")
        return ruck1 if ruck1_score > ruck2_score else ruck2

    def calculate_ruck_performance(self, ruck: Any) -> float:
        """Direct migration of original calculate_ruck_performance from working AI."""
        height_weight = 1.5
        strength_weight = 1.4
        agility_weight = 1.3
        mental_weight = 1.2
        tactical_weight = 1.1
        consistency_weight = 1.0
        age_weight = 0.9
        stamina_weight = 0.8

        performance = (
            (ruck.physical_stats.height * height_weight) +
            (ruck.physical_stats.strength * strength_weight) +
            (ruck.physical_stats.agility * agility_weight) +
            (ruck.ability_stats.mental * mental_weight) +
            (ruck.ability_stats.tactical * tactical_weight) +
            (ruck.ability_stats.consistency * consistency_weight) +
            (ruck.physical_stats.age * age_weight) +
            (ruck.physical_stats.stamina * stamina_weight)
        )

        # Add a random element to simulate the unpredictability of sports
        random_factor = random.uniform(0.8, 1.2)  # Random factor between 0.8 and 1.2
        performance *= random_factor

        # Print the calculated performance for debugging
        print(f"Calculated performance for {ruck.name}: {performance:.2f}")

        return performance

    def clearance(self, team1: Any, team2: Any, team1_players: Dict[str, Any], team2_players: Dict[str, Any], ruck_winner: Any) -> Tuple[Any, Dict[str, Any]]:
        """Direct migration of original clearance from working AI."""
        print("Simulating clearance")
        midfielders_team1 = [team1_players["Rover"], team1_players["RuckRover"], team1_players["Centre"]]
        midfielders_team2 = [team2_players["Rover"], team2_players["RuckRover"], team2_players["Centre"]]

        team1_score = sum(self.calculate_midfielder_performance(player) for player in midfielders_team1)
        team2_score = sum(self.calculate_midfielder_performance(player) for player in midfielders_team2)

        # Add extra weight to the team whose ruck won the center bounce
        if ruck_winner == team1:
            team1_score *= 1.1
        else:
            team2_score *= 1.1

        print(f"Team 1 clearance score: {team1_score:.2f}, Team 2 clearance score: {team2_score:.2f}")

        if team1_score > team2_score:
            winner_team = team1
            winner_players = midfielders_team1
        else:
            winner_team = team2
            winner_players = midfielders_team2

        # Determine the player who gets the ball
        player_with_ball = random.choices(
            winner_players,
            weights=[self.calculate_midfielder_performance(player) for player in winner_players],
            k=1
        )[0]

        self.stats_manager.record_stat(player_with_ball.name, 'clearances')
        self.disposer = player_with_ball
        print(f"{player_with_ball.name} from {winner_team.name} gets the ball after clearance")

        if player_with_ball.team == team1:
            team_players = team1_players
        else:
            team_players = team2_players

        return winner_team, team_players

    def calculate_midfielder_performance(self, player: Any) -> float:
        """Direct migration of original calculate_midfielder_performance from working AI."""
        speed_weight = 1.5
        agility_weight = 1.4
        stamina_weight = 1.3
        strength_weight = 1.2
        mental_weight = 1.1
        tactical_weight = 1.0
        consistency_weight = 0.9
        age_weight = 0.8

        performance = (
            (player.physical_stats.speed * speed_weight) +
            (player.physical_stats.agility * agility_weight) +
            (player.physical_stats.stamina * stamina_weight) +
            (player.physical_stats.strength * strength_weight) +
            (player.ability_stats.mental * mental_weight) +
            (player.ability_stats.tactical * tactical_weight) +
            (player.ability_stats.consistency * consistency_weight) +
            (player.physical_stats.age * age_weight)
        )

        # Add a random element to simulate the unpredictability of sports
        random_factor = random.uniform(0.8, 1.2)  # Random factor between 0.8 and 1.2
        performance *= random_factor

        # Print the calculated performance for debugging
        print(f"Calculated performance for {player.name}: {performance:.2f}")

        return performance

    def possession_contest(self, receiver: Any, opponent: Any) -> Tuple[float, float]:
        """Direct migration of original possession_contest from working AI."""
        print("Simulating possession contest")

        receiver_weights = {
            'marking': 1.5,
            'strength': 1.4,
            'consistency': 1.3,
            'agility': 1.2,
            'age': 1.1,
            'stamina': 1.0
        }

        opponent_weights = {
            'tackling': 1.5,
            'strength': 1.4,
            'consistency': 1.3,
            'speed': 1.2,
            'age': 1.1,
            'stamina': 1.0
        }

        receiver_performance = self.calculate_performance(receiver, receiver_weights)
        opponent_performance = self.calculate_performance(opponent, opponent_weights)

        print(f"{receiver.name} performance: {receiver_performance:.2f}")
        print(f"{opponent.name} performance: {opponent_performance:.2f}")

        return receiver_performance, opponent_performance

    def calculate_performance(self, player: Any, weights: Dict[str, float]) -> float:
        """Direct migration of original calculate_performance from working AI."""
        performance = 0
        for attribute, weight in weights.items():
            performance += getattr(player.ability_stats, attribute, 0) * weight
            performance += getattr(player.physical_stats, attribute, 0) * weight

        random_factor = random.uniform(0.8, 1.2)  # Random factor between 0.8 and 1.2
        performance *= random_factor

        print(f"Calculated performance for {player.name}: {performance:.2f}")
        return performance

    def simulate_center_bounce(self, team1: Any, team2: Any, team1_players: Dict[str, Any], team2_players: Dict[str, Any]) -> Dict[str, Any]:
        """Direct migration of original simulate_center_bounce from working AI."""
        print("Simulating center bounce")
        ruck1 = team1_players["Ruck"]
        ruck2 = team2_players["Ruck"]
        winner = self.ruck_contest(ruck1, ruck2)
        
        if winner == ruck1:
            print(f"{team1.name} wins the center bounce")
            self.stats_manager.record_stat(ruck1.name, 'hitouts')
            # Continue with clearance but store result for next contest
            winner_team, team_players = self.clearance(team1, team2, team1_players, team2_players, ruck1)
            return {
                "result": f"{winner_team.name} wins center bounce through {self.disposer.name}",
                "next_action": "handle_possession",
                "team": winner_team,
                "team_players": team_players
            }
        else:
            print(f"{team2.name} wins the center bounce")
            self.stats_manager.record_stat(ruck2.name, 'hitouts')
            # Continue with clearance but store result for next contest
            winner_team, team_players = self.clearance(team1, team2, team1_players, team2_players, ruck2)
            return {
                "result": f"{winner_team.name} wins center bounce through {self.disposer.name}",
                "next_action": "handle_possession",
                "team": winner_team,
                "team_players": team_players
            }

    def handle_possession(self, team: Any, team_players: Dict[str, Any]) -> Dict[str, Any]:
        """Direct migration of original handle_possession from working AI."""
        print(f"{team.name} handling possession")
        
        # Get ball position and update game state
        self.ball_position_name = self.get_ball_position()
        print(f"Ball position: {self.ball_position} ({self.ball_position_name})")
        self.update_game_state(team)
        
        # Get the player in position
        player = self.disposer
        print(f"Got Player {player} Position: {player.current_position}")
        
        # Check if player is in position
        if not player or not player.is_in_position(self.ball_position_name):
            print(f"Player {player.name} not in position {self.ball_position_name}")
            all_players = list(self.team1_players.values()) + list(self.team2_players.values())
            closest_player = Player.determine_closest_player_to_ball(self, self.ball_position, all_players, exclude_player=player)
            print(f"{closest_player.name} gets to the ball based on proximity and speed/agility")
            team = self.team1 if closest_player.team == self.team1 else self.team2
            player = closest_player
            print(f'Team: {team}')
            return {
                "result": f"{closest_player.name} gets to the ball based on proximity and speed/agility",
                "next_action": "advance_ball",
                "team": closest_player.team,
                "team_players": team_players,
                "position": self.ball_position,
                "receiver": closest_player
            }
            
        # Handle scoring positions
        if self.ball_position_name in ["LF", "RF", "FF", "CHF", "RHF", "LHF"]:
            print("Attempt Goal")
            result = self.attempt_goal(player, self.ball_position_name)
            
            if result == "goal":
                self.ball_position_name = self.get_ball_position()
                self.update_game_state(team, "goal")
                return {
                    "result": result,
                    "next_action": "center_bounce"
                }
            elif result in ["kick_in", "behind"]:
                self.ball_position_name = self.get_ball_position()
                team = self.team2 if team == self.team1 else self.team1
                team_players = self.team2_players if team == self.team1 else self.team1_players
                opponent_players = self.team2_players if team == self.team1 else self.team1_players
                self.update_game_state(team)
                player = next((player for player in team_players.values() if player.position == self.ball_position_name))
                team_tactics = self.team1_tactics if team == self.team1 else self.team2_tactics
                return {
                    "result": f"{result}",
                    "next_action": "kick_in",
                    "player": player,
                    "position": self.ball_position,
                    "team_players": team_players,
                    "opponent_players": opponent_players,
                    "team_tactics": team_tactics
                }
            elif result == "out_on_full":
                self.ball_position_name = self.get_ball_position()
                team = self.team2 if team == self.team1 else self.team1
                team_players = self.team2_players if team == self.team1 else self.team1_players
                self.update_game_state(team)
                player = next((player for player in team_players.values() if player.position == self.ball_position_name))
                opponent_players = self.team2_players if team == self.team1 else self.team1_players
                team_tactics = self.team1_tactics if team == self.team1 else self.team2_tactics
                return {
                    "result": result,
                    "next_action": "out_on_full",
                    "player": player,
                    "position": self.ball_position_name,
                    "team_players": team_players,
                    "opponent_players": opponent_players,
                    "team_tactics": team_tactics
                }
            elif result.get("next_action") == "consider_pass":
                team_tactics = self.team1_tactics if team == self.team1 else self.team2_tactics
                team_players = self.team2_players if team == self.team1 else self.team1_players
                opponent_players = self.team2_players if team == self.team1 else self.team1_players
                return {
                    "result": result.get('result'),
                    "next_action": result.get('next_action'),
                    "player": result.get('player'),
                    "position": result.get('position'),
                    "team_players": team_players,
                    "opponent_players": opponent_players,
                    "team_tactics": team_tactics
                }
                
        # Handle defensive/midfield positions
        if self.ball_position_name in ["RBP", "LBP", "FB", "LHB", "RHB", "CHB", "LWing", "RWing", "Centre"]:
            team_tactics = self.team1_tactics if team == self.team1 else self.team2_tactics
            opponent_players = self.team2_players if team == self.team1 else self.team1_players
            print(f"Player {player.name} Position {self.ball_position_name}")
            
            next_position, receiver, action = self.handle_disposal(player, self.ball_position, team_players, opponent_players, team_tactics)
            
            if next_position and receiver and action != "play_on":
                team_players = self.team2_players if receiver.team == self.team1 else self.team1_players
                return {
                    "result": f"{receiver.name} Chosen From {action}",
                    "next_action": "advance_ball",
                    "team": receiver.team,
                    "team_players": team_players,
                    "position": self.ball_position,
                    "receiver": receiver,
                    "next_position": next_position,
                    "disp_type": action
                }
            elif action == "goal":
                self.ball_position_name = self.get_ball_position()
                self.update_game_state(team, "goal")
                return {
                    "result": result,
                    "next_action": "center_bounce"
                }
            elif action in ["kick_in", "behind"]:
                self.ball_position_name = self.get_ball_position()
                team = self.team2 if team == self.team1 else self.team1
                team_players = self.team2_players if team == self.team1 else self.team1_players
                opponent_players = self.team2_players if team == self.team1 else self.team1_players
                self.update_game_state(team)
                player = next((player for player in team_players.values() if player.position == "FB"))
                team_tactics = self.team1_tactics if team == self.team1 else self.team2_tactics
                return {
                    "result": f"{result}",
                    "next_action": "kick_in",
                    "player": player,
                    "position": self.ball_position,
                    "team_players": team_players,
                    "opponent_players": opponent_players,
                    "team_tactics": team_tactics
                }
            else:
                team_players = self.team2_players if self.disposer.team == self.team1 else self.team1_players
                self.update_game_state(self.disposer.team)
                return {
                    "result": f"{self.disposer.name} Chosen From {action}",
                    "next_action": "handle_possession",
                    "team": self.disposer.team,
                    "team_players": team_players
                }
                
        # Check for boundary
        is_near_boundary, _ = self.is_position_near_boundary(self.ball_position)
        if is_near_boundary:
            self.out_of_bounds_count_possession += 1
            if self.out_of_bounds_count_possession >= 2:
                self.out_of_bounds_count_possession = 0
                return {
                    "result": "Ball out of bounds",
                    "next_action": "throw_in",
                    "position": self.ball_position
                }
                
        # Default case - advance ball
        print("Hit Return")
        return {
            "result": f"{player.name} looks to advance the ball",
            "next_action": "advance_ball",
            "team": team,
            "team_players": team_players,
            "position": self.ball_position
        }

    def get_ball_position(self) -> Tuple[float, float]:
        """Direct migration of original get_ball_position from working AI."""
        return self.ball_position

    def update_game_state(self, team_in_possession: Any, goal: Optional[bool] = None) -> None:
        """Direct migration of original update_game_state from working AI."""
        # Update play state
        self.play_state = self.get_play_state(team_in_possession, goal)
        
        # Calculate field zones based on current state
        self.field_zones = self.calculate_field_zones(self.ball_position, self.play_state)
        
        # Update player positions
        for player in list(self.team1_players.values()) + list(self.team2_players.values()):
            is_possession_team = player.team == team_in_possession
            self.update_individual_player_position(
                player,
                is_possession_team,
                self.play_state,
                self.field_zones,
                self.ball_position
            )

    def handle_disposal(self, player: Any, current_position: Tuple[float, float], team_players: Dict[str, Any], opponent_players: Dict[str, Any], team_tactics: Dict[str, Any]) -> Dict[str, Any]:
        """Direct migration of original handle_disposal from working AI."""
        print(f"Handling disposal for {player.name}")
        
        # Find disposal options and calculate pressure
        options, disposer_pressure = self.find_disposal_options(player, current_position, team_players, opponent_players, team_tactics)
        
        if not options:
            print("No disposal options found")
            return {
                "result": "no_options",
                "next_action": "handle_no_options",
                "player": player
            }
            
        # Choose best disposal option
        target, landing_pos, disposal_type = self.choose_disposal_option(player, options, team_tactics, disposer_pressure)
        
        # Record the disposal
        self.stats_manager.record_stat(player.name, disposal_type)
        
        # Determine disposal success chance based on type
        disposal_result = self.decide_disposal_type(player, disposal_type, disposer_pressure)
        
        if disposal_result["success"]:
            return {
                "result": "successful_disposal",
                "next_action": "handle_possession",
                "player": target,
                "disposal_type": disposal_type,
                "landing_pos": landing_pos
            }
        else:
            return {
                "result": "turnover",
                "next_action": "handle_turnover",
                "position": current_position
            }

    def find_disposal_options(self, player: Any, current_position: Tuple[float, float], team_players: Dict[str, Any], opponent_players: Dict[str, Any], team_tactics: Dict[str, Any]) -> Tuple[List[Dict[str, Any]], float]:
        """Direct migration of original find_disposal_options from working AI."""
        print(f"Finding disposal options for {player.name}")
        
        options = []
        total_pressure = 0
        num_pressuring = 0
        
        # Calculate pressure from nearby opponents
        for opponent in opponent_players.values():
            if not opponent.current_position:
                continue
                
            distance = math.sqrt(
                (opponent.current_position[0] - current_position[0])**2 +
                (opponent.current_position[1] - current_position[1])**2
            )
            
            if distance < 10:  # Within pressure range
                pressure = (10 - distance) / 10  # More pressure when closer
                pressure *= opponent.ability_stats.tackling / 20  # Better tacklers apply more pressure
                total_pressure += pressure
                num_pressuring += 1
                
        # Calculate average pressure (0-1 range)
        disposer_pressure = min(1.0, total_pressure / max(1, num_pressuring))
        
        # Find potential targets
        for teammate in team_players.values():
            if teammate == player or not teammate.current_position:
                continue
                
            # Calculate distance to teammate
            distance = math.sqrt(
                (teammate.current_position[0] - current_position[0])**2 +
                (teammate.current_position[1] - current_position[1])**2
            )
            
            # Skip if too close or too far
            if distance < 5 or distance > 50:
                continue
                
            # Calculate space rating for target
            space_rating = self._calculate_space_rating(teammate, opponent_players)
            
            # Calculate forward progress
            forward_progress = self._calculate_forward_progress(current_position, teammate.current_position, player.team_side)
            
            options.append({
                "target": teammate,
                "distance": distance,
                "space_rating": space_rating,
                "forward_progress": forward_progress
            })
            
        return options, disposer_pressure

    def _calculate_space_rating(self, player: Any, opponent_players: Dict[str, Any]) -> float:
        """Helper function to calculate space around a player."""
        if not player.current_position:
            return 0.0
            
        space_rating = 1.0
        
        for opponent in opponent_players.values():
            if not opponent.current_position:
                continue
                
            distance = math.sqrt(
                (opponent.current_position[0] - player.current_position[0])**2 +
                (opponent.current_position[1] - player.current_position[1])**2
            )
            
            if distance < 10:
                space_rating -= (10 - distance) / 10
                
        return max(0.0, space_rating)

    def _calculate_forward_progress(self, current_pos: Tuple[float, float], target_pos: Tuple[float, float], team_side: str) -> float:
        """Helper function to calculate forward progress of a disposal."""
        if team_side == 'home':
            return (target_pos[0] - current_pos[0]) / self.ground.length
        else:
            return (current_pos[0] - target_pos[0]) / self.ground.length

    def decide_disposal_type(self, player: Any, disposal_type: str, pressure: float) -> Dict[str, Any]:
        """Direct migration of original decide_disposal_type from working AI."""
        print(f"Deciding disposal type for {player.name}: {disposal_type}")
        
        # Base success chance based on disposal type
        if disposal_type == 'kick':
            base_chance = player.ability_stats.kicking / 20
        else:  # handball
            base_chance = player.ability_stats.handball / 20
            
        # Modify based on pressure
        chance = base_chance * (1 - pressure * 0.7)  # Pressure reduces effectiveness by up to 70%
        
        # Add some randomness
        chance = min(0.95, chance + random.uniform(-0.1, 0.1))
        
        # Determine success
        success = random.random() < chance
        
        return {
            "success": success,
            "type": disposal_type
        }

    def choose_disposal_option(self, player: Any, options: List[Dict[str, Any]], team_tactics: Dict[str, Any], disposer_pressure: float) -> Tuple[Any, Tuple[float, float], str]:
        """Direct migration of original choose_disposal_option from working AI."""
        print(f"Choosing disposal option for {player.name}")
        
        # Rate each option
        rated_options = []
        for option in options:
            rating = self._rate_disposal_option(player, option, team_tactics, disposer_pressure)
            rated_options.append((rating, option))
            
        # Sort by rating
        rated_options.sort(reverse=True, key=lambda x: x[0])
        
        if not rated_options:
            return None, None, 'kick'  # Default to kick if no options
            
        best_option = rated_options[0][1]
        target = best_option['target']
        
        # Decide disposal type based on distance and pressure
        if best_option['distance'] < 15 and disposer_pressure > 0.7:
            disposal_type = 'handball'
        else:
            disposal_type = 'kick'
            
        # Calculate landing position (slightly in front of target)
        if target.current_position:
            landing_pos = (
                target.current_position[0] + (2 if player.team_side == 'home' else -2),
                target.current_position[1]
            )
        else:
            landing_pos = player.current_position
            
        return target, landing_pos, disposal_type

    def _rate_disposal_option(self, player: Any, option: Dict[str, Any], team_tactics: Dict[str, Any], disposer_pressure: float) -> float:
        """Helper function to rate a disposal option."""
        rating = 0.0
        
        # Base rating from space
        rating += option['space_rating'] * 2.0
        
        # Forward progress bonus
        rating += option['forward_progress'] * 1.5
        
        # Distance factor (prefer medium-range options)
        distance_rating = 1.0 - abs(option['distance'] - 25) / 25  # Best at 25m
        rating += distance_rating
        
        # Tactical adjustments
        if team_tactics['mentality'] == 'attacking':
            rating += option['forward_progress'] * 0.5  # Extra weight to forward movement
        elif team_tactics['mentality'] == 'defensive':
            rating += option['space_rating'] * 0.5  # Extra weight to safe options
            
        # Pressure adjustment
        if disposer_pressure > 0.7:
            # Under high pressure, prefer closer options
            rating *= (1.0 - option['distance'] / 50)
            
        return rating

    def attempt_goal(self, player: Any, position: str) -> str:
        """Direct migration of original attempt_goal from working AI."""
        print(f"{player.name} attempting goal from {position}")
        
        # Calculate base goal kicking performance
        goal_kicking_performance = self.calculate_goal_kicking_performance(player)
        
        # Get distance to goal
        distance = self.ground.get_distance_to_goal(player.current_position, player.team.side)
        print(f"Distance to goal: {distance}m")
        
        # Adjust for distance
        if distance < 15:  # Too close, harder to score
            goal_kicking_performance *= 0.8
        elif distance > 50:  # Long range
            goal_kicking_performance *= 0.6
            
        # Adjust for angle
        angle = abs(player.current_position[1] - (self.ground.width / 2))
        angle_factor = max(0.5, 1 - (angle / 40))  # Harder from wider angles
        goal_kicking_performance *= angle_factor
        
        print(f"Final goal kicking performance: {goal_kicking_performance}")
        
        # Determine outcome
        if goal_kicking_performance > 0.7:  # High chance of goal
            if random.random() < 0.8:  # 80% chance of goal
                print("GOAL!")
                return "goal"
            else:
                print("Behind!")
                return "behind"
        elif goal_kicking_performance > 0.4:  # Moderate chance
            if random.random() < 0.4:  # 40% chance of goal
                print("GOAL!")
                return "goal"
            elif random.random() < 0.7:  # 70% chance of behind if not goal
                print("Behind!")
                return "behind"
            else:
                print("Out on the full!")
                return "out_on_full"
        else:  # Low chance
            if random.random() < 0.2:  # 20% chance of goal
                print("GOAL!")
                return "goal"
            elif random.random() < 0.5:  # 50% chance of behind if not goal
                print("Behind!")
                return "behind"
            else:
                print("Out on the full!")
                return "out_on_full"

    def calculate_goal_kicking_performance(self, player: Any) -> float:
        """Direct migration of original calculate_goal_kicking_performance from working AI."""
        goal_kicking_weight = 1.5
        mental_weight = 1.4
        strength_weight = 1.3
        consistency_weight = 1.2
        age_weight = 1.1
        stamina_weight = 1.0

        performance = (
            (player.ability_stats.goal_kicking * goal_kicking_weight) +
            (player.ability_stats.mental * mental_weight) +
            (player.physical_stats.strength * strength_weight) +
            (player.ability_stats.consistency * consistency_weight) +
            (player.physical_stats.age * age_weight) +
            (player.physical_stats.stamina * stamina_weight)
        )

        # Add a small random factor to simulate unpredictability
        random_factor = random.uniform(0.9, 1.1)
        performance *= random_factor

        print(f"Calculated goal kicking performance for {player.name}: {performance:.2f}")

        return performance

class Player:
    """Represents a player in the match, maintaining exact compatibility with original AI version."""
    
    def __init__(
        self,
        name: str,
        team: Any,
        position: str,
        ability_stats: Any,
        physical_stats: Any
    ):
        self.name = name
        self.team = team
        self.position = position
        self.ability_stats = ability_stats
        self.physical_stats = physical_stats
        self.current_position = None
        self.fatigue = 0.0
        self.movement_state = None
        self.marked_opponent = None
        
    def __str__(self) -> str:
        """String representation of player."""
        return f"{self.name} ({self.position})"
        
    def __repr__(self) -> str:
        """Detailed string representation of player."""
        return f"Player({self.name}, {self.team.name}, {self.position})"
        
    def determine_closest_player_to_ball(self, players: List[Any], ball_position: Tuple[float, float]) -> Any:
        """Find closest player to ball from a list of players."""
        closest_player = None
        min_distance = float('inf')
        equal_distance_players = []
        
        for player in players:
            if not player.current_position:
                continue
                
            distance = self.calculate_distance(ball_position)
            
            if distance < min_distance:
                min_distance = distance
                closest_player = player
                equal_distance_players = [player]
            elif distance == min_distance:
                equal_distance_players.append(player)
                
        # If multiple players at equal distance, choose based on speed and agility
        if len(equal_distance_players) > 1:
            best_player = None
            best_score = -1
            
            for player in equal_distance_players:
                score = (player.physical_stats.speed + player.physical_stats.agility) / 2
                if score > best_score:
                    best_score = score
                    best_player = player
                    
            return best_player
            
        return closest_player
        
    def determine_closest_opponent_player_to_ball(
        self,
        opponent_players: List[Any],
        ball_position: Tuple[float, float]
    ) -> Any:
        """Find closest opponent to ball."""
        return self.determine_closest_player_to_ball(opponent_players, ball_position)
        
    def is_in_position(self, influence_radius: float = 5.0) -> bool:
        """Check if player is in their designated position."""
        if not self.current_position:
            return False
            
        # Mobile positions have larger influence radius
        mobile_positions = ['Centre', 'Rover', 'RuckRover', 'LWing', 'RWing']
        if self.position in mobile_positions:
            influence_radius *= 1.5
            
        distance = self.calculate_distance(self.get_coordinates(self.position))
        return distance <= influence_radius
        
    def calculate_distance(
        self,
        target_pos: Tuple[float, float],
        from_pos: Optional[Tuple[float, float]] = None
    ) -> float:
        """Calculate Manhattan distance between positions."""
        if from_pos is None:
            if not self.current_position:
                return float('inf')
            from_pos = self.current_position
            
        # Handle both coordinate tuples and named positions
        if isinstance(target_pos, str):
            target_pos = self.get_coordinates(target_pos)
            
        return abs(target_pos[0] - from_pos[0]) + abs(target_pos[1] - from_pos[1])
        
    def get_coordinates(self, position: str) -> Tuple[float, float]:
        """Get coordinates for a named position."""
        # Position coordinates from original AI
        position_coords = {
            'FF': (0.9, 0.5),
            'CHF': (0.75, 0.5),
            'LHF': (0.7, 0.25),
            'RHF': (0.7, 0.75),
            'Centre': (0.5, 0.5),
            'Rover': (0.5, 0.4),
            'RuckRover': (0.5, 0.6),
            'LWing': (0.5, 0.2),
            'RWing': (0.5, 0.8),
            'FB': (0.1, 0.5),
            'CHB': (0.25, 0.5),
            'LHB': (0.3, 0.25),
            'RHB': (0.3, 0.75)
        }
        
        if position not in position_coords:
            return (0.5, 0.5)  # Default to center
            
        return position_coords[position]
        
    def update_fatigue(self, intensity: float) -> None:
        """Update player's fatigue based on action intensity."""
        # Base fatigue increase
        fatigue_increase = intensity * 0.1
        
        # Modify based on stamina
        stamina_factor = 1.0 - (self.physical_stats.stamina / 100)
        fatigue_increase *= (1.0 + stamina_factor)
        
        # Add fatigue
        self.fatigue = min(100.0, self.fatigue + fatigue_increase)
        
    def recover_fatigue(self, amount: float) -> None:
        """Recover fatigue during breaks."""
        # Base recovery
        recovery = amount
        
        # Modify based on stamina
        stamina_factor = self.physical_stats.stamina / 100
        recovery *= (1.0 + stamina_factor)
        
        # Reduce fatigue
        self.fatigue = max(0.0, self.fatigue - recovery)
        
    def get_effective_stats(self) -> Dict[str, float]:
        """Get stats adjusted for fatigue."""
        fatigue_factor = 1.0 - (self.fatigue / 100)
        
        return {
            'speed': self.physical_stats.speed * fatigue_factor,
            'agility': self.physical_stats.agility * fatigue_factor,
            'strength': self.physical_stats.strength * fatigue_factor,
            'stamina': self.physical_stats.stamina,
            'mental': self.ability_stats.mental * fatigue_factor,
            'tactical': self.ability_stats.tactical * fatigue_factor,
            'kicking': self.ability_stats.kicking * fatigue_factor,
            'handballing': self.ability_stats.handballing * fatigue_factor,
            'marking': self.ability_stats.marking * fatigue_factor,
            'tackling': self.ability_stats.tackling * fatigue_factor
        }
        
    def get_influence_radius(self) -> float:
        """Calculate player's area of influence."""
        base_radius = 5.0
        speed_bonus = self.physical_stats.speed * 0.1
        agility_bonus = self.physical_stats.agility * 0.05
        fatigue_penalty = self.fatigue * 0.02
        return max(2.0, base_radius + speed_bonus + agility_bonus - fatigue_penalty)
        
    def return_to_natural_position(self) -> None:
        """Return player to their natural position."""
        self.current_position = self.position
        
    def determine_position_based_on_tactics(
        self,
        ball_position: Tuple[float, float],
        play_state: str,
        tactics: Dict[str, Any]
    ) -> List[str]:
        """Determine possible positions based on tactics and play state."""
        possible_positions = []
        
        if play_state == 'attacking':
            if tactics['offense_strategy'] == 'direct':
                # More central positions for direct play
                if self.position in ['FF', 'CHF']:
                    possible_positions = ['FF', 'CHF']
                elif self.position in ['LHF', 'RHF']:
                    possible_positions = ['CHF', 'LHF', 'RHF']
                elif self.position in ['Centre', 'Rover', 'RuckRover']:
                    possible_positions = ['Centre', 'CHF']
            elif tactics['offense_strategy'] == 'stay_wide':
                # Wider positions for possession play
                if self.position in ['FF', 'CHF']:
                    possible_positions = ['CHF', 'LHF', 'RHF']
                elif self.position in ['LHF', 'RHF']:
                    possible_positions = ['LHF', 'RHF', 'LWing', 'RWing']
                elif self.position in ['Centre', 'Rover', 'RuckRover']:
                    possible_positions = ['LWing', 'RWing', 'Centre']
        elif play_state == 'defensive':
            if tactics['defense_strategy'] == 'man_mark':
                # Follow opponents more closely
                if self.position in ['FB', 'CHB']:
                    possible_positions = ['FB', 'CHB']
                elif self.position in ['LHB', 'RHB']:
                    possible_positions = ['LHB', 'RHB', 'CHB']
                elif self.position in ['Centre', 'Rover', 'RuckRover']:
                    possible_positions = ['Centre', 'CHB']
            elif tactics['defense_strategy'] == 'zone_mark':
                # Stay in zones
                if self.position in ['FB', 'CHB']:
                    possible_positions = ['FB', 'CHB', 'LHB', 'RHB']
                elif self.position in ['LHB', 'RHB']:
                    possible_positions = ['LHB', 'RHB']
                elif self.position in ['Centre', 'Rover', 'RuckRover']:
                    possible_positions = ['Centre', 'LWing', 'RWing']
                    
        # If no specific positions found, stay in current position
        if not possible_positions:
            possible_positions = [self.position]
            
        return possible_positions
        
    def choose_position_based_on_stats(
        self,
        ball_position: Tuple[float, float],
        possible_positions: List[str]
    ) -> str:
        """Choose best position based on player stats."""
        best_position = None
        best_score = float('-inf')
        
        for position in possible_positions:
            # Calculate distance to ball
            target_coords = self.get_coordinates(position)
            distance = self.calculate_distance(ball_position, target_coords)
            
            # Calculate anticipation score
            anticipation = (self.ability_stats.mental + self.ability_stats.tactical) / 2
            
            # Calculate ability to contest
            contest_ability = 1 / (1 + distance) * anticipation
            
            # Apply fatigue penalty
            fatigue_penalty = max(0.5, 1 - (self.fatigue * 0.05))
            final_score = contest_ability * fatigue_penalty
            
            if final_score > best_score:
                best_score = final_score
                best_position = position
                
        return best_position or random.choice(possible_positions)
        
    def get_coordinates(self, position: str) -> Tuple[float, float]:
        """Get coordinates for a named position."""
        # Position coordinates from original AI
        position_coords = {
            'FF': (0.9, 0.5),
            'CHF': (0.75, 0.5),
            'LHF': (0.7, 0.25),
            'RHF': (0.7, 0.75),
            'Centre': (0.5, 0.5),
            'Rover': (0.5, 0.4),
            'RuckRover': (0.5, 0.6),
            'LWing': (0.5, 0.2),
            'RWing': (0.5, 0.8),
            'FB': (0.1, 0.5),
            'CHB': (0.25, 0.5),
            'LHB': (0.3, 0.25),
            'RHB': (0.3, 0.75)
        }
        
        if position not in position_coords:
            return (0.5, 0.5)  # Default to center
            
        return position_coords[position]
        
    def calculate_distance(
        self,
        target_pos: Tuple[float, float],
        from_pos: Optional[Tuple[float, float]] = None
    ) -> float:
        """Calculate Manhattan distance between positions."""
        if from_pos is None:
            if not self.current_position:
                return float('inf')
            from_pos = self.current_position
            
        # Handle both coordinate tuples and named positions
        if isinstance(target_pos, str):
            target_pos = self.get_coordinates(target_pos)
            
        return abs(target_pos[0] - from_pos[0]) + abs(target_pos[1] - from_pos[1])
        
    def get_zone(self, position: Tuple[float, float]) -> str:
        """Get zone name for a position."""
        x, y = position
        
        # Forward zones
        if x >= 0.7:
            if y <= 0.33:
                return "forward_left"
            elif y >= 0.67:
                return "forward_right"
            else:
                return "forward_center"
        # Midfield zones
        elif x >= 0.3:
            if y <= 0.33:
                return "midfield_left"
            elif y >= 0.67:
                return "midfield_right"
            else:
                return "midfield_center"
        # Defensive zones
        else:
            if y <= 0.33:
                return "defensive_left"
            elif y >= 0.67:
                return "defensive_right"
            else:
                return "defensive_center"

class Team:
    """Represents a team in the match, maintaining exact compatibility with original AI version."""
    
    def __init__(self, name: str):
        self.name = name
        self.score = 0
        self.behinds = 0
        self.total_score = 0
        self.tactics = {
            'mentality': 'balanced',
            'offense_strategy': 'balanced',
            'defense_strategy': 'balanced',
            'push_factor': 1.0
        }
        self.players = {}
        
    def __str__(self) -> str:
        """String representation of team."""
        return f"{self.name}: {self.score}.{self.behinds} ({self.total_score})"
        
    def update_score(self, points: int) -> None:
        """Update team score."""
        if points == 6:
            self.score += 1
        elif points == 1:
            self.behinds += 1
        self.total_score = self.score * 6 + self.behinds
        
    def add_player(self, player: Any) -> None:
        """Add player to team."""
        self.players[player.name] = player
        player.team = self
        
    def get_players_by_position(self, position: str) -> List[Any]:
        """Get list of players in a position."""
        return [p for p in self.players.values() if p.position == position]
        
    def get_players_in_zone(self, zone: str) -> List[Any]:
        """Get list of players in a zone."""
        return [p for p in self.players.values() if p.current_position and zone in self.get_zone(p.current_position)]
        
    def get_zone(self, position: Tuple[float, float]) -> str:
        """Get zone name for a position."""
        x, y = position
        
        # Forward zones
        if x >= 0.7:
            if y <= 0.33:
                return "forward_left"
            elif y >= 0.67:
                return "forward_right"
            else:
                return "forward_center"
        # Midfield zones
        elif x >= 0.3:
            if y <= 0.33:
                return "midfield_left"
            elif y >= 0.67:
                return "midfield_right"
            else:
                return "midfield_center"
        # Defensive zones
        else:
            if y <= 0.33:
                return "defensive_left"
            elif y >= 0.67:
                return "defensive_right"
            else:
                return "defensive_center"
                
    def Tactics_handler(
        self,
        position: str,
        receiver: Any,
        opponent_team: Any,
        receiver_performance: float,
        opponent_performance: float,
        Current_team_tactics: Dict[str, Any],
        opponent_team_tactics: Dict[str, Any]
    ) -> float:
        """Handle team tactics and their effect on player performance."""
        tactics = Current_team_tactics
        opponent_tactics = opponent_team_tactics
        
        if tactics['mentality'] == 'attacking':
            if position in ["LF", "FF", "RF"]:
                receiver_performance *= 1.1
            turnover_risk = 0.1
            if random.random() < turnover_risk:
                print(f"Turnover risk due to attacking mentality!")
                
        elif tactics['mentality'] == 'defensive':
            if opponent_tactics['mentality'] == 'attacking':
                opponent_performance *= 0.9
                
            if position in ["LF", "FF", "RF"]:
                receiver_performance *= 0.9
                
        return receiver_performance
        
    def update_tactics(self, new_tactics: Dict[str, Any]) -> None:
        """Update team tactics."""
        self.tactics.update(new_tactics)
        
    def get_team_structure(self) -> Dict[str, List[Any]]:
        """Get team structure by lines."""
        structure = {
            'forwards': [],
            'midfielders': [],
            'defenders': []
        }
        
        for player in self.players.values():
            if player.position in ['FF', 'CHF', 'LHF', 'RHF', 'LF', 'RF']:
                structure['forwards'].append(player)
            elif player.position in ['Centre', 'Rover', 'RuckRover', 'LWing', 'RWing']:
                structure['midfielders'].append(player)
            elif player.position in ['FB', 'CHB', 'LHB', 'RHB', 'LB', 'RB']:
                structure['defenders'].append(player)
                
        return structure
        
    def get_team_balance(self) -> Dict[str, float]:
        """Get team balance metrics."""
        structure = self.get_team_structure()
        
        return {
            'forward_strength': sum(p.ability_stats.kicking for p in structure['forwards']) / len(structure['forwards']),
            'midfield_strength': sum(p.ability_stats.tactical for p in structure['midfielders']) / len(structure['midfielders']),
            'defense_strength': sum(p.ability_stats.marking for p in structure['defenders']) / len(structure['defenders'])
        }
        
    def get_team_fatigue(self) -> float:
        """Get average team fatigue."""
        if not self.players:
            return 0.0
            
        total_fatigue = sum(p.fatigue for p in self.players.values())
        return total_fatigue / len(self.players)
        
    def get_team_morale(self) -> float:
        """Get team morale based on score difference and fatigue."""
        if not hasattr(self, 'opponent'):
            return 1.0
            
        score_diff = self.total_score - self.opponent.total_score
        fatigue_penalty = self.get_team_fatigue() / 200  # Max 50% reduction
        
        if score_diff > 0:
            morale = 1.0 + min(0.3, score_diff * 0.02)  # Max 30% boost
        else:
            morale = max(0.7, 1.0 + score_diff * 0.02)  # Max 30% reduction
            
        return max(0.5, morale - fatigue_penalty)
        
    def update_player_positions(self, play_state: str) -> None:
        """Update all player positions based on play state."""
        for player in self.players.values():
            possible_positions = player.determine_position_based_on_tactics(
                None,  # ball_position will be added when needed
                play_state,
                self.tactics
            )
            
            if possible_positions:
                player.current_position = random.choice(possible_positions)
                
    def reset_positions(self) -> None:
        """Reset all players to their natural positions."""
        for player in self.players.values():
            player.return_to_natural_position()
            
    def get_team_pressure(self) -> float:
        """Calculate team's overall pressure level."""
        structure = self.get_team_structure()
        
        # Calculate pressure from each line
        forward_pressure = sum(p.ability_stats.tackling for p in structure['forwards']) / len(structure['forwards'])
        midfield_pressure = sum(p.ability_stats.tackling for p in structure['midfielders']) / len(structure['midfielders'])
        defense_pressure = sum(p.ability_stats.tackling for p in structure['defenders']) / len(structure['defenders'])
        
        # Weight the pressure (midfield pressure most important)
        weighted_pressure = (
            forward_pressure * 0.3 +
            midfield_pressure * 0.5 +
            defense_pressure * 0.2
        )
        
        # Adjust for tactics
        if self.tactics['defense_strategy'] == 'high_press':
            weighted_pressure *= 1.2
        elif self.tactics['defense_strategy'] == 'deep_block':
            weighted_pressure *= 0.8
            
        return weighted_pressure
        
    def get_team_possession_style(self) -> Dict[str, float]:
        """Calculate team's possession style metrics."""
        structure = self.get_team_structure()
        
        # Calculate style metrics
        short_game = sum(p.ability_stats.handballing for p in self.players.values()) / len(self.players)
        long_game = sum(p.ability_stats.kicking for p in self.players.values()) / len(self.players)
        contested = sum(p.physical_stats.strength for p in self.players.values()) / len(self.players)
        uncontested = sum(p.physical_stats.speed for p in self.players.values()) / len(self.players)
        
        return {
            'short_game': short_game,
            'long_game': long_game,
            'contested': contested,
            'uncontested': uncontested
        }
        
    def get_team_formation(self) -> Dict[str, List[Tuple[float, float]]]:
        """Get team's current formation."""
        formation = {
            'forwards': [],
            'midfielders': [],
            'defenders': []
        }
        
        for player in self.players.values():
            if not player.current_position:
                continue
                
            if player.position in ['FF', 'CHF', 'LHF', 'RHF', 'LF', 'RF']:
                formation['forwards'].append(player.current_position)
            elif player.position in ['Centre', 'Rover', 'RuckRover', 'LWing', 'RWing']:
                formation['midfielders'].append(player.current_position)
            elif player.position in ['FB', 'CHB', 'LHB', 'RHB', 'LB', 'RB']:
                formation['defenders'].append(player.current_position)
                
        return formation
        
    def get_formation_balance(self) -> Dict[str, float]:
        """Calculate team's formation balance metrics."""
        formation = self.get_team_formation()
        
        # Calculate average positions
        forward_x = sum(pos[0] for pos in formation['forwards']) / len(formation['forwards']) if formation['forwards'] else 0
        midfield_x = sum(pos[0] for pos in formation['midfielders']) / len(formation['midfielders']) if formation['midfielders'] else 0
        defense_x = sum(pos[0] for pos in formation['defenders']) / len(formation['defenders']) if formation['defenders'] else 0
        
        # Calculate width metrics
        forward_width = max(pos[1] for pos in formation['forwards']) - min(pos[1] for pos in formation['forwards']) if formation['forwards'] else 0
        midfield_width = max(pos[1] for pos in formation['midfielders']) - min(pos[1] for pos in formation['midfielders']) if formation['midfielders'] else 0
        defense_width = max(pos[1] for pos in formation['defenders']) - min(pos[1] for pos in formation['defenders']) if formation['defenders'] else 0
        
        return {
            'forward_depth': forward_x,
            'midfield_depth': midfield_x,
            'defense_depth': defense_x,
            'forward_width': forward_width,
            'midfield_width': midfield_width,
            'defense_width': defense_width
        }
        
    def get_team_stats(self) -> Dict[str, Any]:
        """Get comprehensive team statistics."""
        return {
            'score': {
                'goals': self.score,
                'behinds': self.behinds,
                'total': self.total_score
            },
            'structure': self.get_team_structure(),
            'balance': self.get_team_balance(),
            'fatigue': self.get_team_fatigue(),
            'morale': self.get_team_morale(),
            'pressure': self.get_team_pressure(),
            'possession_style': self.get_team_possession_style(),
            'formation': self.get_team_formation(),
            'formation_balance': self.get_formation_balance()
        }
        
    def get_team_performance(self) -> float:
        """Calculate team's overall performance rating."""
        stats = self.get_team_stats()
        
        # Base rating from score
        rating = self.total_score * 0.5
        
        # Add ratings from other metrics
        rating += stats['balance']['forward_strength'] * 10
        rating += stats['balance']['midfield_strength'] * 10
        rating += stats['balance']['defense_strength'] * 10
        
        # Adjust for fatigue and morale
        rating *= (1.0 - stats['fatigue'] / 200)  # Max 50% reduction from fatigue
        rating *= stats['morale']
        
        # Scale to 0-10 range
        return min(10.0, rating / 20)
        
    def get_team_momentum(self) -> float:
        """Calculate team's current momentum."""
        if not hasattr(self, 'opponent'):
            return 1.0
            
        # Base momentum from morale
        momentum = self.get_team_morale()
        
        # Adjust for recent scoring (if available)
        if hasattr(self, 'recent_scores'):
            recent_diff = sum(1 if score > 0 else -1 for score in self.recent_scores[-5:])
            momentum *= (1.0 + recent_diff * 0.1)  # ±10% per recent score
            
        # Adjust for field position (if available)
        if hasattr(self, 'field_position'):
            if self.field_position > 0.7:  # In forward 50
                momentum *= 1.2
            elif self.field_position < 0.3:  # In defensive 50
                momentum *= 0.8
                
        return max(0.5, min(2.0, momentum))
        
    def get_team_tactics_effectiveness(self) -> Dict[str, float]:
        """Calculate effectiveness of current tactics."""
        stats = self.get_team_stats()
        effectiveness = {}
        
        # Mentality effectiveness
        if self.tactics['mentality'] == 'attacking':
            effectiveness['mentality'] = stats['balance']['forward_strength'] / 10
        elif self.tactics['mentality'] == 'defensive':
            effectiveness['mentality'] = stats['balance']['defense_strength'] / 10
        else:
            effectiveness['mentality'] = (stats['balance']['midfield_strength'] / 10)
            
        # Offense strategy effectiveness
        if self.tactics['offense_strategy'] == 'direct':
            effectiveness['offense'] = stats['possession_style']['long_game'] / 10
        elif self.tactics['offense_strategy'] == 'stay_wide':
            effectiveness['offense'] = (
                stats['formation_balance']['forward_width'] +
                stats['formation_balance']['midfield_width']
            ) / 20
        else:
            effectiveness['offense'] = stats['possession_style']['short_game'] / 10
            
        # Defense strategy effectiveness
        if self.tactics['defense_strategy'] == 'high_press':
            effectiveness['defense'] = stats['pressure'] / 10
        elif self.tactics['defense_strategy'] == 'deep_block':
            effectiveness['defense'] = (1.0 - stats['formation_balance']['defense_depth'])
        else:
            effectiveness['defense'] = stats['balance']['defense_strength'] / 10
            
        return effectiveness
        
    def adjust_tactics_based_on_performance(self) -> None:
        """Adjust team tactics based on performance."""
        effectiveness = self.get_team_tactics_effectiveness()
        
        # Adjust mentality
        if effectiveness['mentality'] < 0.5:
            if self.tactics['mentality'] == 'attacking':
                self.tactics['mentality'] = 'balanced'
            elif self.tactics['mentality'] == 'defensive':
                self.tactics['mentality'] = 'balanced'
                
        # Adjust offense strategy
        if effectiveness['offense'] < 0.5:
            if self.tactics['offense_strategy'] == 'direct':
                self.tactics['offense_strategy'] = 'balanced'
            elif self.tactics['offense_strategy'] == 'stay_wide':
                self.tactics['offense_strategy'] = 'balanced'
                
        # Adjust defense strategy
        if effectiveness['defense'] < 0.5:
            if self.tactics['defense_strategy'] == 'high_press':
                self.tactics['defense_strategy'] = 'balanced'
            elif self.tactics['defense_strategy'] == 'deep_block':
                self.tactics['defense_strategy'] = 'balanced'
                
    def get_optimal_tactics(self) -> Dict[str, str]:
        """Calculate optimal tactics based on team attributes."""
        stats = self.get_team_stats()
        
        # Determine optimal mentality
        if stats['balance']['forward_strength'] > stats['balance']['defense_strength']:
            mentality = 'attacking'
        elif stats['balance']['defense_strength'] > stats['balance']['forward_strength']:
            mentality = 'defensive'
        else:
            mentality = 'balanced'
            
        # Determine optimal offense strategy
        if stats['possession_style']['long_game'] > stats['possession_style']['short_game']:
            offense = 'direct'
        elif stats['formation_balance']['forward_width'] > 0.7:
            offense = 'stay_wide'
        else:
            offense = 'balanced'
            
        # Determine optimal defense strategy
        if stats['pressure'] > 7.0:
            defense = 'high_press'
        elif stats['balance']['defense_strength'] > 7.0:
            defense = 'deep_block'
        else:
            defense = 'balanced'
            
        return {
            'mentality': mentality,
            'offense_strategy': offense,
            'defense_strategy': defense
        }
        
    def get_team_weaknesses(self) -> Dict[str, float]:
        """Identify team weaknesses based on stats."""
        stats = self.get_team_stats()
        weaknesses = {}
        
        # Forward line weaknesses
        if stats['balance']['forward_strength'] < 6.0:
            weaknesses['forward_strength'] = 6.0 - stats['balance']['forward_strength']
            
        # Midfield weaknesses
        if stats['balance']['midfield_strength'] < 6.0:
            weaknesses['midfield_strength'] = 6.0 - stats['balance']['midfield_strength']
            
        # Defense weaknesses
        if stats['balance']['defense_strength'] < 6.0:
            weaknesses['defense_strength'] = 6.0 - stats['balance']['defense_strength']
            
        # Formation weaknesses
        if stats['formation_balance']['forward_width'] < 0.4:
            weaknesses['forward_width'] = 0.4 - stats['formation_balance']['forward_width']
            
        if stats['formation_balance']['defense_width'] < 0.4:
            weaknesses['defense_width'] = 0.4 - stats['formation_balance']['defense_width']
            
        # Style weaknesses
        if stats['possession_style']['short_game'] < 5.0 and stats['possession_style']['long_game'] < 5.0:
            weaknesses['ball_movement'] = 5.0 - max(stats['possession_style']['short_game'], stats['possession_style']['long_game'])
            
        return weaknesses
        
    def get_team_strengths(self) -> Dict[str, float]:
        """Identify team strengths based on stats."""
        stats = self.get_team_stats()
        strengths = {}
        
        # Forward line strengths
        if stats['balance']['forward_strength'] > 7.0:
            strengths['forward_strength'] = stats['balance']['forward_strength'] - 7.0
            
        # Midfield strengths
        if stats['balance']['midfield_strength'] > 7.0:
            strengths['midfield_strength'] = stats['balance']['midfield_strength'] - 7.0
            
        # Defense strengths
        if stats['balance']['defense_strength'] > 7.0:
            strengths['defense_strength'] = stats['balance']['defense_strength'] - 7.0
            
        # Formation strengths
        if stats['formation_balance']['forward_width'] > 0.7:
            strengths['forward_width'] = stats['formation_balance']['forward_width'] - 0.7
            
        if stats['formation_balance']['defense_width'] > 0.7:
            strengths['defense_width'] = stats['formation_balance']['defense_width'] - 0.7
            
        # Style strengths
        if stats['possession_style']['short_game'] > 7.0:
            strengths['short_game'] = stats['possession_style']['short_game'] - 7.0
            
        if stats['possession_style']['long_game'] > 7.0:
            strengths['long_game'] = stats['possession_style']['long_game'] - 7.0
            
        return strengths
        
    def get_team_matchups(self, opponent: Any) -> Dict[str, float]:
        """Calculate matchup ratings against opponent."""
        our_stats = self.get_team_stats()
        opp_stats = opponent.get_team_stats()
        
        matchups = {}
        
        # Forward line matchup
        matchups['forward_vs_defense'] = (
            our_stats['balance']['forward_strength'] /
            opp_stats['balance']['defense_strength']
        )
        
        # Midfield matchup
        matchups['midfield'] = (
            our_stats['balance']['midfield_strength'] /
            opp_stats['balance']['midfield_strength']
        )
        
        # Defense matchup
        matchups['defense_vs_forward'] = (
            our_stats['balance']['defense_strength'] /
            opp_stats['balance']['forward_strength']
        )
        
        # Style matchups
        if our_stats['possession_style']['short_game'] > our_stats['possession_style']['long_game']:
            matchups['ball_movement'] = (
                our_stats['possession_style']['short_game'] /
                opp_stats['pressure']
            )
        else:
            matchups['ball_movement'] = (
                our_stats['possession_style']['long_game'] /
                opp_stats['balance']['defense_strength']
            )
            
        return matchups
        
    def adjust_tactics_for_matchup(self, opponent: Any) -> None:
        """Adjust tactics based on matchup against opponent."""
        matchups = self.get_team_matchups(opponent)
        
        # Adjust mentality based on matchups
        if matchups['forward_vs_defense'] > 1.2:
            self.tactics['mentality'] = 'attacking'
        elif matchups['defense_vs_forward'] < 0.8:
            self.tactics['mentality'] = 'defensive'
            
        # Adjust offense strategy based on matchups
        if matchups['ball_movement'] > 1.2:
            if self.get_team_stats()['possession_style']['short_game'] > self.get_team_stats()['possession_style']['long_game']:
                self.tactics['offense_strategy'] = 'possession'
            else:
                self.tactics['offense_strategy'] = 'direct'
        else:
            self.tactics['offense_strategy'] = 'balanced'
            
        # Adjust defense strategy based on matchups
        if matchups['defense_vs_forward'] > 1.2:
            self.tactics['defense_strategy'] = 'high_press'
        elif matchups['defense_vs_forward'] < 0.8:
            self.tactics['defense_strategy'] = 'deep_block'
            
    def get_team_depth(self) -> Dict[str, List[Any]]:
        """Get team depth chart by position."""
        depth_chart = {}
        
        # Forward positions
        for pos in ['FF', 'CHF', 'LHF', 'RHF', 'LF', 'RF']:
            depth_chart[pos] = sorted(
                [p for p in self.players.values() if p.can_play_position(pos)],
                key=lambda x: x.get_position_rating(pos),
                reverse=True
            )
            
        # Midfield positions
        for pos in ['Centre', 'Rover', 'RuckRover', 'LWing', 'RWing']:
            depth_chart[pos] = sorted(
                [p for p in self.players.values() if p.can_play_position(pos)],
                key=lambda x: x.get_position_rating(pos),
                reverse=True
            )
            
        # Defensive positions
        for pos in ['FB', 'CHB', 'LHB', 'RHB', 'LB', 'RB']:
            depth_chart[pos] = sorted(
                [p for p in self.players.values() if p.can_play_position(pos)],
                key=lambda x: x.get_position_rating(pos),
                reverse=True
            )
            
        return depth_chart
        
    def optimize_lineup(self) -> None:
        """Optimize team lineup based on player ratings and fatigue."""
        depth_chart = self.get_team_depth()
        
        # For each position, put the best available player there
        for position, players in depth_chart.items():
            # Filter out too fatigued players
            available = [p for p in players if p.fatigue < 80.0]
            if not available:
                continue
                
            # Get best player for position
            best_player = max(available, key=lambda p: p.get_position_rating(position))
            
            # Update player's position
            best_player.position = position
            best_player.current_position = position
            
    def rotate_squad(self) -> None:
        """Rotate squad to manage fatigue."""
        depth_chart = self.get_team_depth()
        
        for position, players in depth_chart.items():
            current = next((p for p in players if p.position == position), None)
            if not current:
                continue
                
            # If current player is too fatigued, replace them
            if current.fatigue > 70.0:
                # Find fresh replacement
                replacement = next(
                    (p for p in players if p != current and p.fatigue < 50.0),
                    None
                )
                
                if replacement:
                    # Swap positions
                    old_pos = replacement.position
                    replacement.position = position
                    replacement.current_position = position
                    current.position = old_pos
                    current.current_position = old_pos

class StatsManager:
    """Manages player and team statistics."""
    
    def __init__(self):
        self.player_stats = {}
        self.team_stats = {}
        
    def record_stat(self, player_name: str, stat_type: str) -> None:
        """Record a statistic for a player."""
        if player_name not in self.player_stats:
            self.player_stats[player_name] = {
                'disposals': 0,
                'marks': 0,
                'tackles': 0,
                'goals': 0,
                'behinds': 0,
                'clearances': 0,
                'inside_50s': 0,
                'rebound_50s': 0
            }
            
        if stat_type in self.player_stats[player_name]:
            self.player_stats[player_name][stat_type] += 1
            
    def get_player_stats(self, player_name: str) -> Dict[str, int]:
        """Get statistics for a player."""
        return self.player_stats.get(player_name, {})
        
    def record_team_stat(self, team_name: str, stat_type: str) -> None:
        """Record a statistic for a team."""
        if team_name not in self.team_stats:
            self.team_stats[team_name] = {
                'goals': 0,
                'behinds': 0,
                'total_score': 0,
                'inside_50s': 0,
                'clearances': 0,
                'tackles': 0
            }
            
        if stat_type in self.team_stats[team_name]:
            self.team_stats[team_name][stat_type] += 1
            
    def get_team_stats(self, team_name: str) -> Dict[str, int]:
        """Get statistics for a team."""
        return self.team_stats.get(team_name, {})
        
    def get_match_stats(self) -> Dict[str, Dict[str, int]]:
        """Get all match statistics."""
        return {
            'players': self.player_stats,
            'teams': self.team_stats
        }
        
    def clear_stats(self) -> None:
        """Clear all statistics."""
        self.player_stats = {}
        self.team_stats = {}
        
    def get_player_rankings(self) -> Dict[str, List[Tuple[str, int]]]:
        """Get player rankings for each statistic."""
        rankings = {
            'disposals': [],
            'marks': [],
            'tackles': [],
            'goals': [],
            'behinds': [],
            'clearances': [],
            'inside_50s': [],
            'rebound_50s': []
        }
        
        for player_name, stats in self.player_stats.items():
            for stat_type in rankings:
                rankings[stat_type].append((player_name, stats.get(stat_type, 0)))
                
        # Sort each ranking list
        for stat_type in rankings:
            rankings[stat_type].sort(key=lambda x: x[1], reverse=True)
            
        return rankings
        
    def get_team_rankings(self) -> Dict[str, List[Tuple[str, int]]]:
        """Get team rankings for each statistic."""
        rankings = {
            'goals': [],
            'behinds': [],
            'total_score': [],
            'inside_50s': [],
            'clearances': [],
            'tackles': []
        }
        
        for team_name, stats in self.team_stats.items():
            for stat_type in rankings:
                rankings[stat_type].append((team_name, stats.get(stat_type, 0)))
                
        # Sort each ranking list
        for stat_type in rankings:
            rankings[stat_type].sort(key=lambda x: x[1], reverse=True)
            
        return rankings
        
    def get_player_performance_rating(self, player_name: str) -> float:
        """Calculate overall performance rating for a player."""
        stats = self.get_player_stats(player_name)
        
        # Base rating from stats
        rating = (
            stats.get('disposals', 0) * 3 +
            stats.get('marks', 0) * 4 +
            stats.get('tackles', 0) * 4 +
            stats.get('goals', 0) * 6 +
            stats.get('behinds', 0) * 1 +
            stats.get('clearances', 0) * 5 +
            stats.get('inside_50s', 0) * 3 +
            stats.get('rebound_50s', 0) * 3
        )
        
        # Scale to 0-10 range
        return min(10.0, rating / 50)
        
    def get_team_performance_rating(self, team_name: str) -> float:
        """Calculate overall performance rating for a team."""
        stats = self.get_team_stats(team_name)
        
        # Base rating from score
        rating = stats.get('total_score', 0) * 0.5
        
        # Add ratings from other stats
        rating += stats.get('inside_50s', 0) * 0.3
        rating += stats.get('clearances', 0) * 0.3
        rating += stats.get('tackles', 0) * 0.2
        
        # Scale to 0-10 range
        return min(10.0, rating / 50)

class TeamMovementCoordinator:
    """Coordinates movement patterns between teammates"""
    
    def __init__(self, ground: Ground, team_tactics: Dict[str, Any]):
        self.ground = ground
        self.team_tactics = team_tactics
        self.team_side = None
        
    def _get_position_priority(self, position: Tuple[float, float], state: Dict[str, Any]) -> float:
        """Calculate priority score for a support position"""
        score = 0.0
        
    def get_weather_description(self) -> str:
        """Get human-readable weather description."""
        description = []
        
        # Temperature
        temp = self.conditions['temperature']
        if temp < 10:
            description.append("Cold")
        elif temp > 30:
            description.append("Hot")
        else:
            description.append("Mild")
            
        # Rain
        rain = self.conditions['rain']
        if rain > 0.7:
            description.append("Heavy Rain")
        elif rain > 0.3:
            description.append("Light Rain")
            
        # Wind
        wind = self.conditions['wind']
        if wind > 0.7:
            description.append("Strong Wind")
        elif wind > 0.3:
            description.append("Breezy")
            
        # Humidity
        humidity = self.conditions['humidity']
        if humidity > 0.7:
            description.append("Humid")
            
        return ", ".join(description)

class GameState:
    """Manages the current state of the game."""
    
    def __init__(self):
        self.quarter = 1
        self.time_remaining = 20 * 60  # 20 minutes in seconds
        self.ball_position = (0.5, 0.5)  # Center of ground
        self.ball_height = 0.0
        self.ball_carrier = None
        self.last_possession = None
        self.play_state = 'center_bounce'
        self.score_history = []
        
    def update(self, delta_time: float) -> None:
        """Update game state."""
        # Update time
        self.time_remaining -= delta_time
        
        # Check for quarter end
        if self.time_remaining <= 0:
            self.end_quarter()
            
    def end_quarter(self) -> None:
        """Handle end of quarter."""
        self.quarter += 1
        if self.quarter <= 4:
            self.time_remaining = 20 * 60
            self.ball_position = (0.5, 0.5)
            self.ball_height = 0.0
            self.ball_carrier = None
            self.play_state = 'center_bounce'
            
    def record_score(self, team: str, points: int) -> None:
        """Record a score."""
        self.score_history.append({
            'team': team,
            'points': points,
            'quarter': self.quarter,
            'time': self.time_remaining
        })
        
    def get_score_differential(self) -> Dict[str, int]:
        """Get current score differential for each team."""
        differentials = {}
        
        for score in self.score_history:
            team = score['team']
            points = score['points']
            if team not in differentials:
                differentials[team] = 0
            differentials[team] += points
            
        return differentials
        
    def get_momentum(self) -> Dict[str, float]:
        """Calculate current momentum for each team."""
        momentum = {}
        recent_scores = self.score_history[-5:]  # Last 5 scores
        
        for score in recent_scores:
            team = score['team']
            if team not in momentum:
                momentum[team] = 1.0
                
            # More recent scores have more impact
            recency_factor = recent_scores.index(score) / len(recent_scores)
            momentum[team] *= (1.0 + recency_factor * 0.2)
            
        return momentum
        
    def get_field_position_stats(self) -> Dict[str, float]:
        """Get field position statistics."""
        if not hasattr(self, 'position_history'):
            return {}
            
        stats = {
            'inside_50s': 0,
            'rebound_50s': 0,
            'center_clearances': 0,
            'stoppages': 0
        }
        
        for pos in self.position_history[-50:]:  # Last 50 positions
            x, y = pos
            if x > 0.7:  # Inside 50
                stats['inside_50s'] += 1
            elif x < 0.3:  # Defensive 50
                stats['rebound_50s'] += 1
            elif 0.45 < x < 0.55:  # Center
                stats['center_clearances'] += 1
                
        return stats
        
    def get_play_style_stats(self) -> Dict[str, float]:
        """Get play style statistics."""
        if not hasattr(self, 'action_history'):
            return {}
            
        stats = {
            'contested_possessions': 0,
            'uncontested_possessions': 0,
            'tackles': 0,
            'marks': 0
        }
        
        for action in self.action_history[-50:]:  # Last 50 actions
            action_type = action['type']
            stats[action_type] = stats.get(action_type, 0) + 1
            
        return stats
        
    def get_game_flow(self) -> str:
        """Get description of current game flow."""
        momentum = self.get_momentum()
        field_stats = self.get_field_position_stats()
        play_stats = self.get_play_style_stats()
        
        # Determine game flow characteristics
        flow = []
        
        # Momentum-based flow
        max_momentum = max(momentum.values()) if momentum else 1.0
        if max_momentum > 1.5:
            flow.append("One-sided")
        elif max_momentum > 1.2:
            flow.append("Building Momentum")
        else:
            flow.append("Even Contest")
            
        # Field position-based flow
        if field_stats.get('inside_50s', 0) > 10:
            flow.append("Forward Press")
        elif field_stats.get('center_clearances', 0) > 10:
            flow.append("Midfield Battle")
        elif field_stats.get('rebound_50s', 0) > 10:
            flow.append("Defensive Struggle")
            
        # Play style-based flow
        contested = play_stats.get('contested_possessions', 0)
        uncontested = play_stats.get('uncontested_possessions', 0)
        if contested > uncontested * 1.5:
            flow.append("Contested")
        elif uncontested > contested * 1.5:
            flow.append("Free-flowing")
            
        return ", ".join(flow)

class MatchEngine:
    """Main match simulation engine."""
    
    def __init__(
        self,
        team1: Any,
        team2: Any,
        team1_players: Dict[str, Any],
        team2_players: Dict[str, Any],
        team1_tactics: Dict[str, Any],
        team2_tactics: Dict[str, Any],
        match_group_name: str,
        channel_layer: Any
    ):
        self.team1 = team1
        self.team2 = team2
        self.team1_players = team1_players
        self.team2_players = team2_players
        self.team1_tactics = team1_tactics
        self.team2_tactics = team2_tactics
        self.match_group_name = match_group_name
        self.channel_layer = channel_layer
        self.stats_manager = StatsManager()
        self.weather = WeatherSystem()
        self.game_clock = GameClock()
        self.game_state = GameState()
        self.movement_engine = MovementEngine(self.ground)
        self.action_engine = ActionEngine(self.ground)
        self.contest_engine = ContestEngine(self.ground)

    def get_quarter_summary(self) -> Dict[str, Any]:
        """Generate a summary of the current quarter."""
        quarter_totals = {
            self.team1.name: self.team1.score,
            self.team2.name: self.team2.score
        }
        return {
            'quarter': self.game_clock.current_quarter,
            'scores': quarter_totals,
            'highlights': [
                highlight for highlight in self.get_match_highlights()
                if highlight.get('quarter') == self.game_clock.current_quarter
            ]
        }
        
    def get_play_by_play(self) -> List[Dict[str, Any]]:
        return {
            'summary': self.get_match_summary(),
            'statistics': self.get_match_statistics(),
            'progression': self.get_match_progression(),
            'play_by_play': self.get_play_by_play(),
            'visualization': self.get_match_visualization_data(),
            'analytics': self.get_match_analytics(),
            'report': self.get_match_report()
        }
        
    async def save_match_data(self) -> None:
        """Save match data for later analysis."""
        match_data = {
            'match_id': f"{self.team1.name}_vs_{self.team2.name}_{int(time.time())}",
            'teams': {
                self.team1.name: {
                    'score': {
                        'goals': self.team1.score,
                        'behinds': self.team1.behinds,
                        'total': self.team1.total_score
                    },
                    'tactics': self.team1_tactics,
                    'players': {
                        name: {
                            'position': player.position,
                            'stats': self.stats_manager.get_player_stats(name)
                        }
                        for name, player in self.team1_players.items()
                    }
                },
                self.team2.name: {
                    'score': {
                        'goals': self.team2.score,
                        'behinds': self.team2.behinds,
                        'total': self.team2.total_score
                    },
                    'tactics': self.team2_tactics,
                    'players': {
                        name: {
                            'position': player.position,
                            'stats': self.stats_manager.get_player_stats(name)
                        }
                        for name, player in self.team2_players.items()
                    }
                }
            },
            'score_history': self.game_state.score_history,
            'weather': self.weather.conditions,
            'analysis': self.get_match_analysis()
        }
        
        # Send match data to storage
        await self.channel_layer.group_send(
            'match_storage',
            {
                'type': 'store.match',
                'match_data': match_data
            }
        )
        
    async def cleanup(self) -> None:
        """Clean up match resources."""
        # Save match data
        await self.save_match_data()
        
        # Clear game state
        self.game_state = None
        
        # Clear player states
        for player in self.team1_players.values():
            player.movement_state = None
            player.current_position = None
            player.fatigue = 0.0
            
        for player in self.team2_players.values():
            player.movement_state = None
            player.current_position = None
            player.fatigue = 0.0
            
        # Clear stats
        self.stats_manager.clear_stats()
        
        # Send cleanup notification
        await self.channel_layer.group_send(
            self.match_group_name,
            {
                'type': 'match.cleanup',
                'message': 'Match resources cleaned up'
            }
        )

    def set_match_state(self, state: Dict[str, Any]) -> None:
        """Set match state from saved state."""
        # Set game state
        self.game_state.quarter = state['game_state']['quarter']
        self.game_state.time_remaining = state['game_state']['time_remaining']
        self.game_state.ball_position = state['game_state']['ball_position']
        self.game_state.play_state = state['game_state']['play_state']
        
        # Set ball carrier
        if state['game_state']['ball_carrier']:
            self.game_state.ball_carrier = (
                self.team1_players.get(state['game_state']['ball_carrier']) or
                self.team2_players.get(state['game_state']['ball_carrier'])
            )
        else:
            self.game_state.ball_carrier = None
            
        # Set scores
        self.team1.score = state['scores'][self.team1.name]['goals']
        self.team1.behinds = state['scores'][self.team1.name]['behinds']
        self.team1.total_score = state['scores'][self.team1.name]['total']
        self.team2.score = state['scores'][self.team2.name]['goals']
        self.team2.behinds = state['scores'][self.team2.name]['behinds']
        self.team2.total_score = state['scores'][self.team2.name]['total']
        
        # Set player positions
        for name, position in state['player_positions'][self.team1.name].items():
            if name in self.team1_players:
                self.team1_players[name].current_position = position
                
        for name, position in state['player_positions'][self.team2.name].items():
            if name in self.team2_players:
                self.team2_players[name].current_position = position
                
    def serialize(self) -> Dict[str, Any]:
        """Serialize match engine state."""
        return {
            'match_id': f"{self.team1.name}_vs_{self.team2.name}_{int(time.time())}",
            'teams': {
                self.team1.name: {
                    'score': {
                        'goals': self.team1.score,
                        'behinds': self.team1.behinds,
                        'total': self.team1.total_score
                    },
                    'tactics': self.team1_tactics,
                    'players': {
                        name: {
                            'position': player.position,
                            'current_position': player.current_position,
                            'fatigue': player.fatigue,
                            'stats': self.stats_manager.get_player_stats(name)
                        }
                        for name, player in self.team1_players.items()
                    }
                },
                self.team2.name: {
                    'score': {
                        'goals': self.team2.score,
                        'behinds': self.team2.behinds,
                        'total': self.team2.total_score
                    },
                    'tactics': self.team2_tactics,
                    'players': {
                        name: {
                            'position': player.position,
                            'current_position': player.current_position,
                            'fatigue': player.fatigue,
                            'stats': self.stats_manager.get_player_stats(name)
                        }
                        for name, player in self.team2_players.items()
                    }
                }
            },
            'game_state': {
                'quarter': self.game_state.quarter,
                'time_remaining': self.game_state.time_remaining,
                'ball_position': self.game_state.ball_position,
                'ball_carrier': self.game_state.ball_carrier.name if self.game_state.ball_carrier else None,
                'play_state': self.game_state.play_state,
                'score_history': self.game_state.score_history
            },
            'weather': self.weather.conditions
        }
        
    @classmethod
    def deserialize(
        cls,
        data: Dict[str, Any],
        channel_layer: Any
    ) -> 'MatchEngine':
        """Create match engine from serialized state."""
        # Create teams
        team1 = Team(list(data['teams'].keys())[0])
        team2 = Team(list(data['teams'].keys())[1])
        
        # Create players
        team1_players = {}
        team2_players = {}
        
        for name, player_data in data['teams'][team1.name]['players'].items():
            player = Player(
                name=name,
                team=team1,
                position=player_data['position'],
                ability_stats=player_data.get('ability_stats', {}),
                physical_stats=player_data.get('physical_stats', {})
            )
            player.current_position = player_data['current_position']
            player.fatigue = player_data['fatigue']
            team1_players[name] = player
            
        for name, player_data in data['teams'][team2.name]['players'].items():
            player = Player(
                name=name,
                team=team2,
                position=player_data['position'],
                ability_stats=player_data.get('ability_stats', {}),
                physical_stats=player_data.get('physical_stats', {})
            )
            player.current_position = player_data['current_position']
            player.fatigue = player_data['fatigue']
            team2_players[name] = player
            
        return cls(
            team1,
            team2,
            team1_players,
            team2_players,
            data['teams'][team1.name]['tactics'],
            data['teams'][team2.name]['tactics'],
            data['match_group_name'],
            data['channel_layer']
        )
        
    @classmethod
    def create_from_config(
        cls,
        config: Dict[str, Any],
        channel_layer: Any
    ) -> 'MatchEngine':
        """Create a match from a configuration dictionary."""
        # Extract team names
        team1_name = config['team1']['name']
        team2_name = config['team2']['name']
        
        # Create teams
        team1 = Team(team1_name)
        team2 = Team(team2_name)
        
        # Add players to teams
        for team_name, team_data in config.items():
            if team_name != 'team1' and team_name != 'team2':
                for player_name, player_data in team_data['players'].items():
                    player = Player(
                        name=player_name,
                        team=team1 if team_name == team1_name else team2,
                        position=player_data['position'],
                        ability_stats=player_data['ability_stats'],
                        physical_stats=player_data['physical_stats']
                    )
                    team1.add_player(player) if team_name == team1_name else team2.add_player(player)
        
        # Create match engine
        return cls(
            team1,
            team2,
            team1.players,
            team2.players,
            team1.tactics,
            team2.tactics,
            config['match_group_name'],
            channel_layer
        )
        
    @classmethod
    def create_random_match(
        cls,
        channel_layer: Any,
        team1_name: str = "Team 1",
        team2_name: str = "Team 2"
    ) -> 'MatchEngine':
        """Create a match with random player attributes."""