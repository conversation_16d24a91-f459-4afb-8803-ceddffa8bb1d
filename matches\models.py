from django.db import models
from leagues.models import League
from teams.models import Team
#from seasons.models import Season
from django.db.models import <PERSON><PERSON><PERSON><PERSON>  # Useful for flexible data storage
from datetime import timedelta

class Match(models.Model):
    league = models.ForeignKey('leagues.League', on_delete=models.CASCADE)
    season = models.ForeignKey('seasons.Season', on_delete=models.CASCADE)
    home_team = models.ForeignKey('teams.Team', related_name='home_matches', on_delete=models.CASCADE)
    away_team = models.ForeignKey('teams.Team', related_name='away_matches', on_delete=models.CASCADE)
    date = models.DateField()
    played = models.Bo<PERSON>anField(default=False)
    home_team_score = models.IntegerField(null=True, blank=True)
    away_team_score = models.IntegerField(null=True, blank=True)
    
    # Lineups and Tactics
    home_team_lineup = J<PERSON><PERSON>ield(null=True, blank=True)  # {"position": "player_id", ...}
    away_team_lineup = J<PERSON><PERSON>ield(null=True, blank=True)  # {"position": "player_id", ...}
    home_team_tactics = J<PERSON>NField(null=True, blank=True)  # {"mentality": "attacking", "push_factor": 2, ...}
    away_team_tactics = JSONField(null=True, blank=True)  # {"mentality": "defensive", "push_factor": 3, ...}

    # Quarter-wise scores
    home_team_q1_score = models.IntegerField(default=0)
    home_team_q2_score = models.IntegerField(default=0)
    home_team_q3_score = models.IntegerField(default=0)
    home_team_q4_score = models.IntegerField(default=0)
    away_team_q1_score = models.IntegerField(default=0)
    away_team_q2_score = models.IntegerField(default=0)
    away_team_q3_score = models.IntegerField(default=0)
    away_team_q4_score = models.IntegerField(default=0)

    # Match events as a JSON field to store details of events (e.g., goals, tackles, fouls)
    match_events = JSONField(null=True, blank=True)  # E.g., {"events": [{"time": "00:15", "type": "goal", "player": "John Doe", "team": "home"}]}

    # Player statistics for this match (key-value storage of stats per player per quarter)
    player_stats = JSONField(null=True, blank=True)  # E.g., {"player_id": {"Q1": {"kicks": 5, "goals": 1}, "Q2": {...}}}
    
    def set_lineup_and_tactics(self, team, lineup, tactics):
        """
        Save lineup and tactics for a team.
        :param team: Team instance (home or away)
        :param lineup: Dictionary of positions to player IDs
        :param tactics: Dictionary of tactics
        """
        if team == self.home_team:
            self.home_team_lineup = lineup
            self.home_team_tactics = tactics
        elif team == self.away_team:
            self.away_team_lineup = lineup
            self.away_team_tactics = tactics
        else:
            raise ValueError("Team must be either the home or away team of this match.")
        self.save()

    def record_event(self, time, event_type, player, team):
        """Record a single match event, e.g., goal, tackle, etc."""
        if self.match_events is None:
            self.match_events = {"events": []}
        self.match_events["events"].append({
            "time": time,
            "type": event_type,
            "player": player.name,
            "team": "home" if team == self.home_team else "away"
        })
        self.save()

    def record_player_stat(self, player, quarter, stat_type, value):
        """Record a stat for a player in a specific quarter, e.g., kicks, goals."""
        if self.player_stats is None:
            self.player_stats = {}
        if player.id not in self.player_stats:
            self.player_stats[player.id] = {"Q1": {}, "Q2": {}, "Q3": {}, "Q4": {}}
        self.player_stats[player.id][quarter][stat_type] = value
        self.save()

    def calculate_total_scores(self):
        """Calculate the total score for each team based on the quarter scores."""
        self.home_team_score = (self.home_team_q1_score + self.home_team_q2_score +
                                self.home_team_q3_score + self.home_team_q4_score)
        self.away_team_score = (self.away_team_q1_score + self.away_team_q2_score +
                                self.away_team_q3_score + self.away_team_q4_score)
        self.save()

    def __str__(self):
        return f"{self.home_team} vs {self.away_team} on {self.date}"



