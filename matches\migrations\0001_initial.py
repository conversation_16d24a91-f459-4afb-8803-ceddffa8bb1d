# Generated by Django 5.0.7 on 2024-11-18 02:13

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('leagues', '0002_league_country'),
        ('teams', '0023_alter_team_league'),
    ]

    operations = [
        migrations.CreateModel(
            name='Match',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('played', models.BooleanField(default=False)),
                ('home_team_score', models.IntegerField(blank=True, null=True)),
                ('away_team_score', models.IntegerField(blank=True, null=True)),
                ('home_team_q1_score', models.IntegerField(default=0)),
                ('home_team_q2_score', models.IntegerField(default=0)),
                ('home_team_q3_score', models.IntegerField(default=0)),
                ('home_team_q4_score', models.IntegerField(default=0)),
                ('away_team_q1_score', models.IntegerField(default=0)),
                ('away_team_q2_score', models.IntegerField(default=0)),
                ('away_team_q3_score', models.IntegerField(default=0)),
                ('away_team_q4_score', models.IntegerField(default=0)),
                ('match_events', models.JSONField(blank=True, null=True)),
                ('player_stats', models.JSONField(blank=True, null=True)),
                ('away_team', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='away_matches', to='teams.team')),
                ('home_team', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='home_matches', to='teams.team')),
                ('league', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='leagues.league')),
            ],
        ),
    ]
