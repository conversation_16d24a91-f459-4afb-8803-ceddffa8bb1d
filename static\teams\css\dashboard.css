/* Sidebar styling */
.sidebar {
    position: absolute; /* Changed to absolute for normal page scroll */
    top: 100px; /* Adjusts to leave room for the banner */
    left: 200px; /* Indented the sidebar */
    width: 220px; /* Sidebar width */
    background-color: #FFFFFF; /* Background color */
    border-radius: 20px; /* Rounded corners */
    padding: 20px;
    box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.2); /* Subtle shadow */
    z-index: 500; /* Lowered z-index so it stays below the banner */
}

/* Sidebar items styling */
.sidebar ul {
    list-style-type: none;
    padding: 0;
}

.sidebar li {
    margin: 15px 0;
}

.sidebar a {
    text-decoration: none;
    color: white;
    font-size: 18px;
    display: block;
    padding: 10px 15px;
    background-color: #003f87; /* Slightly lighter blue */
    border-radius: 15px;
    transition: background-color 0.3s ease;
}

.sidebar a:hover {
    background-color: #004f9e; /* Changes on hover */
}

.sidebar a i {
    margin-right: 10px;
}

/* Adjust main content to leave space for the sidebar */
.main-content {
    margin-left: 450px; /* Adjust this based on sidebar width */
    padding: 20px;
    margin-top: 100px; /* Leaves space for the banner */
	
}


/* Banner styling */
.banner {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 80px;
    background-color: #003f87;
    border-radius: 20px;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
	z-index: 1000; /* Raised z-index to make sure it's on top of everything */
}

.banner img {
    height: 100%;
    padding: 10px;
}

/* Wrapper to control the full page layout */
.wrapper {
    display: flex;
    flex-direction: column;
    min-height: 130vh;
}

/* Footer styling */
.footer {
    background-color: #003f87;
    color: white;
    text-align: center;
    padding: 10px 0;
    width: 100%;
    margin-top: auto;
}

.footer p {
    margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .content {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        min-height: auto;
    }

    .main-content {
        padding: 20px;
    }
}



