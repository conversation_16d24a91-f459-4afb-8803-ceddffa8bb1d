
// Check if D<PERSON> is already loaded
if (document.readyState === "loading") {
    // DOM is still loading, attach the event listener
    document.addEventListener("DOMContentLoaded", () => {
        //console.log("DOMContentLoaded triggered in matches.js");
        initializeOrdersScript();
    });
} else {
    // DOM is already loaded
    //console.log("DOM already loaded, executing directly");
    initializeOrdersScript();
}


function initializeOrdersScript() {
        // Select all orders containers
        const ordersContainers = document.querySelectorAll('.orders');

        //console.log("initializeOrders triggered in matches.js");
    
        ordersContainers.forEach((container) => {
            // Check if the container contains the "not-submitted" text
            const statusText = container.querySelector('.not-submitted');
            if (statusText) {
                container.style.backgroundColor = '#f5cbcb'; // Set to red
            }
        });
  

}    