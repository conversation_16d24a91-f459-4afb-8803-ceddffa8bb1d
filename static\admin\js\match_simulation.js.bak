document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM fully loaded and parsed');

    const team1Select = document.getElementById('id_team1');
    const team2Select = document.getElementById('id_team2');
    const team1RuckSelect = document.getElementById('id_team1_ruck');
    const team2RuckSelect = document.getElementById('id_team2_ruck');
	

    function fetchPlayers(teamId, ruckSelect) {
        if (teamId) {
            fetch(`/admin/ajax/load-players/?team_id=${teamId}`)
                .then(response => response.json())
                .then(players => {
                    ruckSelect.options.length = 0;
                    players.forEach(player => {
                        const option = document.createElement('option');
                        option.value = player.id;
                        option.textContent = player.name;
                        ruckSelect.appendChild(option);
                    });
                })
                .catch(error => console.error('Error fetching players:', error));
        }
    }

    team1Select.addEventListener('change', function() {
        fetchPlayers(this.value, team1RuckSelect);
    });

    team2Select.addEventListener('change', function() {
        fetchPlayers(this.value, team2RuckSelect);
    });
});
