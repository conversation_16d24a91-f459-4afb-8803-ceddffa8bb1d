from django.db.models.signals import post_save
from django.dispatch import receiver
from .models import Season
from teams.models import Team
from django.core.mail import send_mail

@receiver(post_save, sender=Season)
def notify_new_season(sender, instance, created, **kwargs):
    if created:
        league = instance.league
        teams = Team
        print(f"Teams: {teams}")
        """
        for team in teams:
            manager_email = team.manager.email
            send_mail(
                subject=f"New Season Created for {league.name}",
                message=f"The new season for {league.name} starts on {instance.start_date}.",
                from_email="<EMAIL>",
                recipient_list=[manager_email]
            )
        """    

@receiver(post_save, sender=Team)
def notify_team_promotion_relegation(sender, instance, **kwargs):
    # Check if the league has changed (indicating promotion/relegation)
    if instance.league_id != instance._original_league_id:
        old_league = instance._original_league
        new_league = instance.league
        """
        send_mail(
            subject=f"Team {instance.name} Moved Leagues",
            message=f"Your team has been moved from {old_league.name} to {new_league.name}.",
            from_email="<EMAIL>",
            recipient_list=[instance.manager.email]
        )
        """