from django.db import models
from django.conf import settings
from leagues.models import League
from users.models import CustomUser

class Team(models.Model):
    name = models.CharField(max_length=100)
    user = models.OneToOneField(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='team')
    league = models.ForeignKey(League, on_delete=models.SET_NULL, null=True)

    def __str__(self):
        return self.name

class PlayerStatsPhysical(models.Model):
    height = models.IntegerField()
    agility = models.IntegerField()
    speed = models.IntegerField()
    strength = models.IntegerField()
    stamina = models.IntegerField()
    age = models.IntegerField()

    def __str__(self):
        return f"Height: {self.height}, Agility: {self.agility}, Speed: {self.speed}, Strength: {self.strength}, Stamina: {self.stamina}, Age: {self.age}"

class PlayerStatsAbility(models.Model):
    kicking = models.IntegerField()
    goal_kicking = models.IntegerField()
    handball = models.IntegerField()
    tackling = models.IntegerField()
    mental = models.IntegerField()
    tactical = models.IntegerField()
    versatility = models.IntegerField()
    consistency = models.IntegerField()
    marking = models.IntegerField(default=5)

    def __str__(self):
        return (f"Kicking: {self.kicking}, Goal Kicking: {self.goal_kicking}, Handball: {self.handball}, "
                f"Tackling: {self.tackling}, Mental: {self.mental}, Tactical: {self.tactical}, "
                f"Versatility: {self.versatility}, Consistency: {self.consistency}, Marking: {self.marking}")
                
class PlayerStatsAttributes(models.Model):
    experience = models.IntegerField()
    leadership = models.IntegerField()
    loyalty = models.IntegerField()
    wage = models.DecimalField(max_digits=10, decimal_places=2)
    
    def __str__(self):
        return f"Experience: {self.experience}, leadership: {self.leadership}, Loyalty: {self.loyalty}, Wage: {self.wage}"

class Player(models.Model):
    team = models.ForeignKey(Team, on_delete=models.CASCADE, related_name='players')
    physical_stats = models.OneToOneField(PlayerStatsPhysical, on_delete=models.CASCADE)
    ability_stats = models.OneToOneField(PlayerStatsAbility, on_delete=models.CASCADE)
    attributes_stats = models.OneToOneField(PlayerStatsAttributes, on_delete=models.CASCADE)
    name = models.CharField(max_length=100)

    def __str__(self):
        return self.name

class PlayerStatsLvls(models.Model):
    # This table has columns '0', '1', ..., '20'
    level_0 = models.CharField(max_length=50, db_column='0')
    level_1 = models.CharField(max_length=50, db_column='1')
    level_2 = models.CharField(max_length=50, db_column='2')
    level_3 = models.CharField(max_length=50, db_column='3')
    level_4 = models.CharField(max_length=50, db_column='4')
    level_5 = models.CharField(max_length=50, db_column='5')
    level_6 = models.CharField(max_length=50, db_column='6')
    level_7 = models.CharField(max_length=50, db_column='7')
    level_8 = models.CharField(max_length=50, db_column='8')
    level_9 = models.CharField(max_length=50, db_column='9')
    level_10 = models.CharField(max_length=50, db_column='10')
    level_11 = models.CharField(max_length=50, db_column='11')
    level_12 = models.CharField(max_length=50, db_column='12')
    level_13 = models.CharField(max_length=50, db_column='13')    
    level_14 = models.CharField(max_length=50, db_column='14')
    level_15 = models.CharField(max_length=50, db_column='15')
    level_16 = models.CharField(max_length=50, db_column='16')
    level_17 = models.CharField(max_length=50, db_column='17')
    level_18 = models.CharField(max_length=50, db_column='18')
    level_19 = models.CharField(max_length=50, db_column='19')
    level_20 = models.CharField(max_length=50, db_column='20')
    
    class Meta:
        db_table = 'player_stats_lvls'  # Use the existing table name
        managed = False  # Prevent Django from managing (creating or deleting) this table
        auto_created = True  # Skip auto-adding an 'id' field
    
    def __str__(self):
        return "Player Stats Levels"