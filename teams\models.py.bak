from django.db import models
from .utils import create_random_ability_stats

class PlayerStatsPhysical(models.Model):
    height = models.IntegerField()
    agility = models.IntegerField()
    speed = models.IntegerField()
    strength = models.IntegerField()
    stamina = models.IntegerField()
    age = models.IntegerField()

class PlayerStatsAbility(models.Model):
    kicking = models.IntegerField()
    goal_kicking = models.IntegerField()
    handball = models.IntegerField()
    tackling = models.IntegerField()
    mental = models.IntegerField()
    tactical = models.IntegerField()
    versatility = models.IntegerField()
    consistency = models.IntegerField()

class Player(models.Model):
    name = models.CharField(max_length=100)
    physical_stats = models.OneToOneField(PlayerStatsPhysical, on_delete=models.CASCADE)
    ability_stats = models.OneToOneField(PlayerStatsAbility, on_delete=models.CASCADE, default=create_random_ability_stats)
    team = models.ForeignKey('Team', on_delete=models.CASCADE, related_name='players')

class Team(models.Model):
    name = models.CharField(max_length=100)
    user = models.OneToOneField('users.CustomUser', on_delete=models.CASCADE)
