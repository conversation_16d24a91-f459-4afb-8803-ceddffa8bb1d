
.player-card {
    background-color: #f7f7f7;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    width: 700px; /* Increase width for better layout */
    margin: 20px;
    padding: 15px;
    transition: transform 0.2s;
    display: flex;
    flex-direction: row;
}

.player-card:hover {
    transform: scale(1.05);
}

.player-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    margin-right: 20px;
}

.player-info {
    display: flex;
    justify-content: space-between;
    width: 100%;
}
.player-details {
    justify-content: space-between;
    margin-bottom: 5px;
}	

.player-details, .ability-stats {
    width: 45%; /* Allocate 45% of width to both details and stats */
}

.progress-bar-container {
    margin-bottom: 10px;
    position: relative; /* Enable positioning context for the label */
	transition: transform 0.2s ease;
}

.progress-bar {
    width: 100%;
    background-color: #003f87;
    border-radius: 10px;
    overflow: hidden;
    height: 20px;
}

.progress-bar-fill {
    height: 100%; 
	background-color: #3891f5;
    /* background-color: #003f87; */
	transition: transform 0.2s ease;
}

/* Center the label within the entire container */
.progress-label {
    position: absolute;
	top: 50%;
    left: 50%;
    transform: translate(-50%, +15%);
    color: #fff; /* Text color inside the progress bar */
    white-space: nowrap;
    z-index: 1; /* Ensure label is on top */
}


.stat-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
}

h3 {
    margin-top: 15px;
    font-weight: bold;
}
