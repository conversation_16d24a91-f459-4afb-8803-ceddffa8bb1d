{% load static %}
{% load custom_filters %}
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Set Orders</title>
  <link rel="stylesheet" href="{% static 'teams/css/match_orders/orders.css' %}?<?php echo date('l jS \of F Y h:i:s A'); ?>">
  <!--<script src="https://cdn.tailwindcss.com"></script>-->
  <script src="{% static 'teams/js/match_orders/orders.js' %}"></script>
</head>
<input type="hidden" id="match-id" value="{{ match.id }}">
<input type="hidden" id="team-id" value="{{ team.id }}">
<body class="p-6">

  <!-- Navigation Tabs -->
  <div class="tabs-container">
	<button class="tab-btn active" data-tab="lineup">Lineup</button>
	<button class="tab-btn" data-tab="tactics">Tactics</button>
	<button class="tab-btn" data-tab="review">Review</button>
    <button class="tab-btn" id="send-game-plan" disabled>Submit Game Plan</button>
  </div>
  <div id="lineup" class="tab-content active">
	<div class="set-orders-container">
		<div class="left-column">
  <div class="grid grid-cols-3 gap-4">
    <!-- Field Section -->
    <div class="col-span-2">
      <div class="field-container">
        <!-- Example player positions -->
        <div class="player-rect" style="top: 12%; left: 46.35%;" position="FB">FB</div>
        <div class="player-rect" style="top: 16%; left: 33%;" position="RBP">RBP</div>
        <div class="player-rect" style="top: 16%; left: 60%;" position="LBP">LBP</div>
		<div class="player-rect" style="top: 33%; left: 46.35%;" position="CHB">CHB</div>
		<div class="player-rect" style="top: 33%; left: 27%;" position="LHB">LHB</div>
        <div class="player-rect" style="top: 33%; left: 66%;" position="RHB">RHB</div>
		<div class="player-rect" style="top: 46.4%; left: 46.60%;" position="Center">Center</div>
		<div class="player-rect" style="top: 46.4%; left: 20%;" position="Lwing">Lwing</div>
        <div class="player-rect" style="top: 46.4%; left: 73%;" position="Rwing">Rwing</div>
		<div class="player-rect" style="top: 59.5%; left: 46.35%;" position="CHF">CHF</div>
		<div class="player-rect" style="top: 59.5%; left: 27%;" position="LHF">LHF</div>
        <div class="player-rect" style="top: 59.5%; left: 66%;" position="RHF">RHF</div>
		<div class="player-rect" style="top: 81%; left: 46.35%;" position="FF">FF</div>
        <div class="player-rect" style="top: 77%; left: 33%;" position="RFP">RFP</div>
        <div class="player-rect" style="top: 77%; left: 60%;" position="LFP">LFP</div>
		</div>
			<div class="followers-container">
			  <h3>Followers</h3>
			  <div class="player-rectf" position="Ruck">Ruck</div>
			  <div class="player-rectf" position="Rover">Rover</div>
			  <div class="player-rectf" position="Ruck_Rover">Ruck Rover</div>
			</div>
			<div class="Interchange-container">
			  <h3>Interchange</h3>
			  <div class="player-recti" position="1">1</div>
			  <div class="player-recti" position="2">2</div>
			  <div class="player-recti" position="3">3</div>
			  <div class="player-recti" position="4">4</div>
			  <div class="player-rects" position="Sub">Sub</div>
			</div>
    </div>
	</div>
	  </div>
<div class="right-column">
    <!-- Player List -->
    <div class="sort-container">
      <label for="sort-rules">Sort by:</label>
      <select id="sort-rules">
		<option value="" disabled selected>-----</option>
        <option value="forward">Forward</option>
        <option value="defender">Defender</option>
        <option value="midfielder">Midfielder</option>
        <option value="ruck">Ruck</option>
      </select>
    </div>
<div class="players-list">
  {% for player in players %}
  <div 
    class="player" 
    data-id="{{ player.id }}" 
 >
    <span class="player-name">{{ player.name }}</span>
    <div class="player-stats hidden">
      <h3>{{ player.name }}</h3>
      <p>Age: {{ player.physical_stats.age }}</p>
		<label style="margin-left: 20px;"id="Height-{{ player.id }}">Height: {{ player.physical_stats.height }}</label>
      <div class="card-body two-columns">
        <div class="player-column">
      
          
          <p>Ability Stats</p>

                <!-- Kicking Stat -->
				<div class="progress-bar-container">
					<div class="stat-row">
						<label id="Kicking-{{ player.id }}">Kicking: {{ player.ability_stats.kicking }}</label>
					</div>
					
					<!-- Label centered across the full width of the container -->
					<span class="progress-label">{{ player_stats_lvls|get_stat_label:player.ability_stats.kicking }}</span>

					<!-- Progress bar fill -->
					<div class="progress-bar">
						<div class="progress-bar-fill" style="width: {{ player.ability_stats.kicking|multiply:5 }}%;"></div>
					</div>
				</div>

				<!-- Goal Kicking Stat -->
				<div class="progress-bar-container">
					<div class="stat-row">
						<label id="Goal_Kicking-{{ player.id }}">Goal Kicking: {{ player.ability_stats.goal_kicking }}</label>
					</div>
					
					<!-- Label centered across the full width of the container -->
					<span class="progress-label">{{ player_stats_lvls|get_stat_label:player.ability_stats.goal_kicking }}</span>

					<!-- Progress bar fill -->
					<div class="progress-bar">
						<div class="progress-bar-fill" style="width: {{ player.ability_stats.goal_kicking|multiply:5 }}%;"></div>
					</div>
				</div>

                <!-- Handball Stat -->
                <div class="progress-bar-container">
                    <div class="stat-row">
                        <label id="Handball-{{ player.id }}">Handball: {{ player.ability_stats.handball }}</label>
                    </div>
					<!-- Label centered across the full width of the container -->
					<span class="progress-label">{{ player_stats_lvls|get_stat_label:player.ability_stats.handball }}</span>

					<!-- Progress bar fill -->
					<div class="progress-bar">
						<div class="progress-bar-fill" style="width: {{ player.ability_stats.handball|multiply:5 }}%;"></div>
					</div>
                </div>

                <!-- Marking Stat -->
                <div class="progress-bar-container">
                    <div class="stat-row">
                        <label id="Marking-{{ player.id }}">Marking: {{ player.ability_stats.marking }}</label>
                    </div>
					<!-- Label centered across the full width of the container -->
					<span class="progress-label">{{ player_stats_lvls|get_stat_label:player.ability_stats.marking }}</span>

					<!-- Progress bar fill -->
					<div class="progress-bar">
						<div class="progress-bar-fill" style="width: {{ player.ability_stats.marking|multiply:5 }}%;"></div>
					</div>
                </div>

                <!-- Tackling Stat -->
                <div class="progress-bar-container">
                    <div class="stat-row">
                        <label id="Tackling-{{ player.id }}">Tackling: {{ player.ability_stats.tackling }}</label>
                    </div>
					<!-- Label centered across the full width of the container -->
					<span class="progress-label">{{ player_stats_lvls|get_stat_label:player.ability_stats.tackling }}</span>

					<!-- Progress bar fill -->
					<div class="progress-bar">
						<div class="progress-bar-fill" style="width: {{ player.ability_stats.tackling|multiply:5 }}%;"></div>
					</div>
                </div>

                <!-- Mental Stat -->
                <div class="progress-bar-container">
                    <div class="stat-row">
                        <label id="Mental-{{ player.id }}">Mental: {{ player.ability_stats.mental }}</label>
                    </div>
					<!-- Label centered across the full width of the container -->
					<span class="progress-label">{{ player_stats_lvls|get_stat_label:player.ability_stats.mental }}</span>

					<!-- Progress bar fill -->
					<div class="progress-bar">
						<div class="progress-bar-fill" style="width: {{ player.ability_stats.mental|multiply:5 }}%;"></div>
					</div>
                </div>

                <!-- Tactical Stat -->
                <div class="progress-bar-container">
                    <div class="stat-row">
                        <label id="Tactical-{{ player.id }}">Tactical: {{ player.ability_stats.tactical }}</label>
                    </div>
					<!-- Label centered across the full width of the container -->
					<span class="progress-label">{{ player_stats_lvls|get_stat_label:player.ability_stats.tactical }}</span>

					<!-- Progress bar fill -->
					<div class="progress-bar">
						<div class="progress-bar-fill" style="width: {{ player.ability_stats.tactical|multiply:5 }}%;"></div>
					</div>
                </div>

                <!-- Versatility Stat -->
                <div class="progress-bar-container">
                    <div class="stat-row">
                        <label id="Versatility-{{ player.id }}">Versatility: {{ player.ability_stats.versatility }}</label>
                    </div>
					<!-- Label centered across the full width of the container -->
					<span class="progress-label">{{ player_stats_lvls|get_stat_label:player.ability_stats.versatility }}</span>

					<!-- Progress bar fill -->
					<div class="progress-bar">
						<div class="progress-bar-fill" style="width: {{ player.ability_stats.versatility|multiply:5 }}%;"></div>
					</div>
                </div>

                <!-- Consistency Stat -->
                <div class="progress-bar-container">
                    <div class="stat-row">
                        <label id="Consistency-{{ player.id }}">Consistency: {{ player.ability_stats.consistency }}</label>
                    </div>
					<!-- Label centered across the full width of the container -->
					<span class="progress-label">{{ player_stats_lvls|get_stat_label:player.ability_stats.consistency }}</span>

					<!-- Progress bar fill -->
					<div class="progress-bar">
						<div class="progress-bar-fill" style="width: {{ player.ability_stats.consistency|multiply:5 }}%;"></div>
					</div>
                </div>
              </div>  

      
      <div class="player-column">

        <p>Physical Stats</p>
 				<div class="progress-bar-container">
					<div class="stat-row">
						<label>Agility: {{ player.physical_stats.agility }}</label>
					</div>
					
					<!-- Label centered across the full width of the container -->
					<span class="progress-label">{{ player_stats_lvls|get_stat_label:player.physical_stats.agility }}</span>

					<!-- Progress bar fill -->
					<div class="progress-bar">
						<div class="progress-bar-fill" style="width: {{ player.physical_stats.agility|multiply:5 }}%;"></div>
					</div>
				</div>
				<div class="progress-bar-container">
					<div class="stat-row">
						<label id="Speed-{{ player.id }}">Speed: {{ player.physical_stats.speed }}</label>
					</div>
					
					<!-- Label centered across the full width of the container -->
					<span class="progress-label">{{ player_stats_lvls|get_stat_label:player.physical_stats.speed }}</span>

					<!-- Progress bar fill -->
					<div class="progress-bar">
						<div class="progress-bar-fill" style="width: {{ player.physical_stats.speed|multiply:5 }}%;"></div>
					</div>
				</div>
				<div class="progress-bar-container">
					<div class="stat-row">
						<label id="Strength-{{ player.id }}">Strength: {{ player.physical_stats.strength }}</label>
					</div>
					
					<!-- Label centered across the full width of the container -->
					<span class="progress-label">{{ player_stats_lvls|get_stat_label:player.physical_stats.strength }}</span>

					<!-- Progress bar fill -->
					<div class="progress-bar">
						<div class="progress-bar-fill" style="width: {{ player.physical_stats.strength|multiply:5 }}%;"></div>
					</div>
				</div>
				<div class="progress-bar-container">
					<div class="stat-row">
						<label id="Stamina-{{ player.id }}">Stamina: {{ player.physical_stats.stamina }}</label>
					</div>
					
					<!-- Label centered across the full width of the container -->
					<span class="progress-label">{{ player_stats_lvls|get_stat_label:player.physical_stats.stamina }}</span>

					<!-- Progress bar fill -->
					<div class="progress-bar">
						<div class="progress-bar-fill" style="width: {{ player.physical_stats.stamina|multiply:5 }}%;"></div>
					</div>
          </div>
          
    </div>
  </div>
  </div>
  </div>
  {% endfor %}
</div>
  </div>
</div>
</div>
<div id="tactics" class="tab-content">
	<h2>Tactics</h2>
	<div class="tactics-container">
		<label for="mentality">Mentality:</label>
		<select id="mentality" name="mentality">
			<option value="" disabled selected>Select Mentality</option>
			<option value="attacking">Attacking</option>
			<option value="defensive">Defensive</option>
		</select>

		<label for="push_factor">Attack/Defence, aggression level:</label>
		<select id="push_factor" name="push_factor">
		<option value="" disabled selected>Select aggression level</option>
		<option value="push_factor0">Low</option>
		<option value="push_factor1">Moderate</option>
		<option value="push_factor2">High</option>
		</select>
		<!--<input type="number" id="push_factor" name="push_factor" min="1" max="3" value="">-->

		<label for="defense_strategy">Defense Strategy:</label>
		<select id="defense_strategy" name="defense_strategy">
			<option value="" disabled selected>Select Defense Strategy</option>
			<option value="zone_mark">Zone Mark</option>
			<option value="man_mark">Man Mark</option>
		</select>

		<label for="offense_strategy">Offense Strategy:</label>
		<select id="offense_strategy" name="offense_strategy">
			<option value="" disabled selected>Select Offense Strategy</option>
			<option value="direct">Direct</option>
			<option value="stay_wide">Stay Wide</option>
		</select>
	</div>
  </div>
  
  <div id="review" class="tab-content">
	<h2>Game Plan Review</h2>
	<div id="review-content">
	</div>
	<p id="missing-info" style="color: red; display: none;"></p>
</div>
 <!--<script src="{% static 'teams/js/match_orders/orders.js' %}"></script>
<script>
</script> -->
</body>
</html>
