import json
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from teams.models import Team, Player
from teams.match_engine.match_engine import MatchEngine

class SimulateMatchConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        await self.accept()
        print("WebSocket connection accepted")

    async def disconnect(self, close_code):
        print(f"WebSocket connection closed: {close_code}")

    async def receive(self, text_data):
        print(f"Received data: {text_data}")
        try:
            text_data_json = json.loads(text_data)
            command = text_data_json.get('command')

            self.team1_id = text_data_json.get('team1_id')
            self.team2_id = text_data_json.get('team2_id')
            self.team1_ruck_id = text_data_json.get('team1_ruck_id')
            self.team2_ruck_id = text_data_json.get('team2_ruck_id')

            print(f"Command received: {command}")
            print(f"Team1 ID: {self.team1_id}")
            print(f"Team2 ID: {self.team2_id}")
            print(f"Team1 Ruck ID: {self.team1_ruck_id}")
            print(f"Team2 Ruck ID: {self.team2_ruck_id}")

            if command == 'start_simulation':
                await self.start_simulation()
        except Exception as e:
            print(f"Error receiving data: {e}")

    async def start_simulation(self):
        try:
            print("StartSimulation")
            team1 = await database_sync_to_async(self.get_team)(self.team1_id)
            print(f"Retrieved Team 1: {team1.name}")
            team2 = await database_sync_to_async(self.get_team)(self.team2_id)
            print(f"Retrieved Team 2: {team2.name}")
            ruck1 = await database_sync_to_async(self.get_player)(self.team1_ruck_id)
            print(f"Retrieved Ruck 1: {ruck1.name}")
            ruck2 = await database_sync_to_async(self.get_player)(self.team2_ruck_id)
            print(f"Retrieved Ruck 2: {ruck2.name}")

            engine = MatchEngine(team1, team2, ruck1, ruck2)
            team1_score, team2_score, quarter_results, event_log = await engine.simulate_match()

            match_result = {
                'team1_score': team1_score,
                'team2_score': team2_score,
                'quarter_results': quarter_results,
                'event_log': event_log,
            }

            await self.send(text_data=json.dumps({
                'command': 'simulation_result',
                'result': match_result,
            }))
        except Exception as e:
            print(f"Error during simulation: {e}")

    @staticmethod
    def get_team(team_id):
        print(f"Fetching team with ID: {team_id}")
        return Team.objects.get(id=team_id)

    @staticmethod
    def get_player(player_id):
        print(f"Fetching player with ID: {player_id}")
        return Player.objects.get(id=player_id)
