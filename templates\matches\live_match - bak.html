<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live Match</title>
    <script>
	//document.addEventListener('DOMContentLoaded', function() {
    //console.log('DOM fully loaded and parsed');
		const matchId = "{{ match_id }}";
		const socket = new WebSocket(`ws://${window.location.hostname}:8001/ws/match/${matchId}/`);
		
		//const button = document.createElement('button');
		//button.className = 'collapsible';
		//button.innerHTML = 'Quarter ' + ' Player Stats';

		//const contentDiv = document.createElement('div');
		//contentDiv.className = 'collapsible-content';
		
		

		socket.onopen = () => {
			console.log("WebSocket connected");
			socket.send(JSON.stringify({ command: "start_simulation" }));
		};

        socket.onmessage = function(e) {
    const data = JSON.parse(e.data);
    const resultsDiv = document.getElementById("results");
    if (data.type === "match_event") {
        console.log("match_event received:", data.data);
       const { quarter, event } = data.data;
       // Append new event without rebuilding entire content
       const eventP = document.createElement('p');
       eventP.textContent = `Quarter ${quarter}: ${event}`;
       resultsDiv.appendChild(eventP);
       // Auto-scroll to bottom to show latest events
       resultsDiv.scrollTop = resultsDiv.scrollHeight;
   } else if (data.type === "score_update") {
        console.log("message received:", data.data);
		const { quarter, team1_score, team2_score } = data.data;
		resultsDiv.innerHTML += `<h3>Score Update:</h3>`;
        resultsDiv.innerHTML += `<p> Quarter ${quarter} Team 1: ${team1_score} Team 2: ${team2_score}</p>`;

    } else if (data.type === "player_stats") {
        const { quarter, player_stats } = data.data; // Destructure only quarter
		console.log("Player stats received:", data.data.player_stats);
    if (Array.isArray(data.data.player_stats)) {
	
			var button = document.createElement('button');
            button.className = 'collapsible';
            button.innerHTML = 'Quarter ' + ' Player Stats';
            console.log('Button element created:', button); // Debugging line
           
			
			var contentDiv = document.createElement('div');
            contentDiv.className = 'collapsible-content';
            contentDiv.style.display = 'none'; // Ensure it starts closed
	
	
			  const playerStats = data.data.player_stats;
			  //const statsTable = createPlayerStatsTable(playerStats);
			  
			  var statsTable = '<table>';
                statsTable += '<tr>';
                statsTable += '<th onclick="sortTable(this)">Player</th>';
                statsTable += '<th onclick="sortTable(this)">Team</th>';
                statsTable += '<th onclick="sortTable(this)">Hitouts</th>';
                statsTable += '<th onclick="sortTable(this)">Kicks</th>';
                statsTable += '<th onclick="sortTable(this)">Handballs</th>';
                statsTable += '<th onclick="sortTable(this)">Tackles</th>';
                statsTable += '<th onclick="sortTable(this)">Clearances</th>';
                statsTable += '<th onclick="sortTable(this)">Marks</th>';
                statsTable += '<th onclick="sortTable(this)">Goals</th>';
                statsTable += '<th onclick="sortTable(this)">Behinds</th>';
                statsTable += '<th onclick="sortTable(this)">Disposals</th>';
                statsTable += '<th onclick="sortTable(this)">Disposal Efficiency</th>';
                statsTable += '<th onclick="sortTable(this)">Turnovers Won</th>';
                statsTable += '<th onclick="sortTable(this)">Interceptions Won</th>';
                statsTable += '</tr>';
                data.data.player_stats.forEach(function(stats) {
                    statsTable += '<tr>';
                    statsTable += '<td>' + stats.name + '</td>';
                    statsTable += '<td>' + stats.team + '</td>';
                    statsTable += '<td>' + stats.hitouts + '</td>';
                    statsTable += '<td>' + stats.kicks + '</td>';
                    statsTable += '<td>' + stats.handballs + '</td>';
                    statsTable += '<td>' + stats.tackles + '</td>';
                    statsTable += '<td>' + stats.clearances + '</td>';
                    statsTable += '<td>' + stats.marks + '</td>';
                    statsTable += '<td>' + stats.goals + '</td>';
                    statsTable += '<td>' + stats.behinds + '</td>';
                    statsTable += '<td>' + stats.disposals + '</td>';
                    statsTable += '<td>' + stats.disposal_efficiency + '%</td>';
                    statsTable += '<td>' + stats.turnovers_won + '</td>';
                    statsTable += '<td>' + stats.interceptions_won + '</td>';
                    statsTable += '</tr>';
                });
                statsTable += '</table>';
                contentDiv.innerHTML = statsTable;		  
				contentDiv.style.display = 'block';
                resultsDiv.appendChild(button);
			    resultsDiv.appendChild(contentDiv);

                button.addEventListener('click', function() {
                console.log('Button clicked for Quarter '); // Debugging line
                this.classList.toggle('active');
                if (contentDiv.style.display === 'block') {
                    contentDiv.style.display = 'none';
                } else {
                    contentDiv.style.display = 'block';
                }
                        });	


			  // ... update resultsDiv
    } else {
      console.warn("Unexpected player stats structure:", message.data);
    }
    } else if (data.event === "final_result") {
        const { result } = data.data;

        // Show final result
        resultsDiv.innerHTML += `<h3>Final Result:</h3>`;
        resultsDiv.innerHTML += `<p>${result}</p>`;
    }
};
			
			
		socket.onclose = (event) => console.log("WebSocket closed:", event);
		socket.onerror = (error) => console.error("WebSocket error:", error);
		
		
	function sortTable(header) {
    var table = header.parentElement.parentElement.parentElement;
    var rows = Array.from(table.querySelectorAll('tr:nth-child(n+2)')); // Skip the header row
    var index = Array.from(header.parentElement.children).indexOf(header);
    var isNumeric = !isNaN(rows[0].children[index].innerText.trim());
    var ascending = !header.classList.contains('asc');

    rows.sort(function(rowA, rowB) {
        var cellA = rowA.children[index].innerText.trim();
        var cellB = rowB.children[index].innerText.trim();

        if (isNumeric) {
            cellA = parseFloat(cellA);
            cellB = parseFloat(cellB);
        }

        if (ascending) {
            return cellA > cellB ? 1 : -1;
        } else {
            return cellA < cellB ? 1 : -1;
        }
    });

    rows.forEach(function(row) {
        table.appendChild(row);
    });

    // Update the class for sorting indication
    Array.from(header.parentElement.children).forEach(function(th) {
        th.classList.remove('asc', 'desc');
    });
    header.classList.add(ascending ? 'asc' : 'desc');
};
    //});

	
		
    </script>
</head>
<body>
    <h1>Live Match</h1>
    <div id="score">Loading scores...</div>
    <ul id="event-log"></ul>
    <div id="player-stats"></div>
    <div id="results"></div>
</body>
</html>
<style>
    .collapsible {
        cursor: pointer;
        padding: 10px;
        border: none;
        text-align: left;
        outline: none;
        font-size: 15px;
        background-color: #f1f1f1;
        margin-bottom: 5px;
        width: 100%;
    }

    .active, .collapsible:hover {
        background-color: #ddd;
    }

    .collapsible-content {
        padding: 0 18px;
        display: none;
        overflow: hidden;
        background-color: #f9f9f9;
    }

    table {
        width: 100%;
        border-collapse: collapse;
    }

    th, td {
        border: 1px solid #ddd;
        padding: 8px;
    }

    th {
        cursor: pointer;
    }

    th.asc::after {
        content: " \u25B2"; /* Up arrow */
    }

    th.desc::after {
        content: " \u25BC"; /* Down arrow */
    }
</style>
