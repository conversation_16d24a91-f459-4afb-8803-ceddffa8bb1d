# Generated by Django 5.0.7 on 2024-07-25 23:58

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Player',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('kicking', models.IntegerField()),
                ('goal_kicking', models.IntegerField()),
                ('handball', models.IntegerField()),
                ('tackling', models.IntegerField()),
                ('mental', models.IntegerField()),
                ('tactical', models.IntegerField()),
                ('versatility', models.IntegerField()),
                ('consistency', models.IntegerField()),
                ('height', models.IntegerField()),
                ('agility', models.IntegerField()),
                ('speed', models.IntegerField()),
                ('strength', models.Integer<PERSON>ield()),
                ('stamina', models.IntegerField()),
            ],
        ),
        migrations.CreateModel(
            name='Team',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('players', models.ManyToManyField(to='teams.player')),
            ],
        ),
    ]
