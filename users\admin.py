from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from .models import CustomUser
from teams.models import Team

@admin.action(description='Delete selected users and all associated data')
def delete_users_and_data(modeladmin, request, queryset):
    for user in queryset:
        try:
            user.team.delete()
        except Team.DoesNotExist:
            pass
        user.delete()

class CustomUserAdmin(UserAdmin):
    actions = [delete_users_and_data]

admin.site.register(CustomUser, CustomUserAdmin)

