"""
Refactored Match Engine V2
Based on working AI version with improved organization and movement
"""
from typing import Any, Dict, List, Optional, Tuple, Generator, Set, TYPE_CHECKING
import random
import math
import asyncio
import time
import sys
import traceback

if TYPE_CHECKING:
    from .match_engine_refactored_v3 import GameState

class Ground:
    """
    Represents an AFL ground with sophisticated zoning and positioning system.
    Handles dynamic zones, player channels, and tactical areas.
    """
    def __init__(self, ground_size):
        # Standard AFL ground dimensions (in meters)
        self.length = ground_size[0]  # Typically 160m
        self.width = ground_size[1]   # Typically 130m

        # Define the oval boundary
        self.is_oval = True  # Flag to enable/disable oval shape

        # Grid system for precise positioning
        self.grid_length = int(self.length / 2)  # 80 cells for more granular positioning
        self.grid_width = int(self.width / 2)    # 65 cells

        self.defensive_third = self.grid_length // 3
        self.forward_third = (self.grid_length * 2) // 3

        self.positions_zones = self.define_dynamic_zones(self.grid_width, self.grid_length)

        self.position_map = {}
        self._initialize_position_map()
        self.visualize_field()
        #print(self.position_map)
        #time.sleep(10)
        #self.positions_zones = self.position_map
        #print(self.positions_zones)
        #time.sleep(10)

        # Define key field markers
        self.fifty_meter_lines = {
            'home': self.grid_length * 0.7,  # 70% from home end
            'away': self.grid_length * 0.3   # 30% from home end
        }

        self.center_square = {
            'x_min': (self.grid_length / 2) - 25,  # 25m either side of center
            'x_max': (self.grid_length / 2) + 25,
            'y_min': (self.grid_width / 2) - 25,
            'y_max': (self.grid_width / 2) + 25
        }


        # Define tactical zones
        self.zones = self._define_tactical_zones()


        # Define running channels
        #self.channels = self._define_running_channels()

        # Define congestion areas
        self.congestion_map = self._initialize_congestion_map()


    def visualize_field(self):
        """Print a visual representation of the field with zones."""
        # Create a grid representation
        grid = [['.' for _ in range(self.grid_width)] for _ in range(self.grid_length)]

        if self.is_oval:
                    # Mark the oval boundary
            for x in range(self.grid_length):
                for y in range(self.grid_width):
                    if self.is_in_bounds((x, y)):
                        grid[x][y] = '.'  # Inside the oval
                    else:
                        grid[x][y] = ' '  # Outside the oval

        # Calculate field dimensions and center
        field_length = self.grid_length  # Should be 80
        field_width = self.grid_width    # Should be 65
        center_x = field_length // 2     # Should be 40
        center_y = field_width // 2      # Should be 32

        # Calculate field dimensions and center
        field_length = self.grid_length
        field_width = self.grid_width
        center_x = field_length // 2
        center_y = field_width // 2

        # Calculate zone sizes
        length_zone = field_length // 5
        width_zone = field_width // 3

        # Define position centers - adjusted to ensure they're inside the oval
        position_centers = {}

        # Calculate the oval's semi-axes
        a = field_length / 2 * 0.98  # Semi-major axis (x direction)
        b = field_width / 2 * 0.98   # Semi-minor axis (y direction)

        # Function to adjust a position to be inside the oval
        def adjust_to_oval(x, y):
            # If already in bounds, return as is
            if self.is_in_bounds((x, y)):
                return (x, y)

            # Calculate vector from center to position
            dx = x - center_x
            dy = y - center_y

            # Calculate the scaling factor needed to place point on the oval boundary
            # Using the ellipse equation: (x-h)²/a² + (y-k)²/b² = 1
            scale = math.sqrt(((dx/a)**2 + (dy/b)**2))

            # Scale down the vector to place it just inside the boundary
            adjusted_x = center_x + (dx / scale) * 0.80  # 80% of distance to boundary
            adjusted_y = center_y + (dy / scale) * 0.80

            return (int(adjusted_x), int(adjusted_y))

        # Back line - adjust positions to be inside oval
        position_centers["LB"] = adjust_to_oval(length_zone // 2, width_zone // 2)
        position_centers["FB"] = adjust_to_oval(length_zone // 2, center_y)
        position_centers["RB"] = adjust_to_oval(length_zone // 2, field_width - width_zone // 2 - 1)

        # Half-back line
        position_centers["LHB"] = adjust_to_oval(length_zone + length_zone // 2, width_zone // 2)
        position_centers["CHB"] = adjust_to_oval(length_zone + length_zone // 2, center_y)
        position_centers["RHB"] = adjust_to_oval(length_zone + length_zone // 2, field_width - width_zone // 2 - 1)

        # Center line
        position_centers["LWing"] = adjust_to_oval(center_x, width_zone // 2)
        position_centers["Centre"] = adjust_to_oval(center_x, center_y)
        position_centers["RWing"] = adjust_to_oval(center_x, field_width - width_zone // 2 - 1)

        # Half-forward line
        position_centers["LHF"] = adjust_to_oval(3*length_zone + length_zone // 2, width_zone // 2)
        position_centers["CHF"] = adjust_to_oval(3*length_zone + length_zone // 2, center_y)
        position_centers["RHF"] = adjust_to_oval(3*length_zone + length_zone // 2, field_width - width_zone // 2 - 1)

        # Forward line
        position_centers["LF"] = adjust_to_oval(4*length_zone + length_zone // 2, width_zone // 2)
        position_centers["FF"] = adjust_to_oval(4*length_zone + length_zone // 2, center_y)
        position_centers["RF"] = adjust_to_oval(4*length_zone + length_zone // 2, field_width - width_zone // 2 - 1)

        # Mark zone centers
        for pos, (x, y) in position_centers.items():
            if 0 <= x < self.grid_length and 0 <= y < self.grid_width:
                grid[x][y] = pos[0]  # First letter of position

            # Mark field center
            center_x, center_y = self.get_center()
            if 0 <= center_x < self.grid_length and 0 <= center_y < self.grid_width:
                grid[center_x][center_y] = 'X'  # Mark center with X

        # Print the grid
        for row in grid:
            print(''.join(row))

    def _initialize_position_map(self):
        """Create a direct mapping from every coordinate to its position."""
        for pos, coordinates_list in self.positions_zones.items():
            for coord in coordinates_list:
                self.position_map[coord] = pos

    def _define_tactical_zones(self) -> Dict[str, List[Tuple[int, int]]]:
        """Define sophisticated tactical zones across the ground."""
        zones = {}

        # Forward line zones (both deep and high)
        zones['deep_forward'] = self._create_zone(0.8, 1.0, 0.1, 0.9)
        zones['high_forward'] = self._create_zone(0.65, 0.8, 0.1, 0.9)

        # Midfield zones - ensure complete coverage
        zones['center_corridor'] = self._create_zone(0.35, 0.65, 0.3, 0.7)
        zones['wing_left'] = self._create_zone(0.35, 0.65, 0.0, 0.3)
        zones['wing_right'] = self._create_zone(0.35, 0.65, 0.7, 1.0)

        # Defensive zones
        zones['deep_defense'] = self._create_zone(0.0, 0.2, 0.1, 0.9)
        zones['high_defense'] = self._create_zone(0.2, 0.35, 0.1, 0.9)

        # Fill in the corner gaps
        zones['forward_left_flank'] = self._create_zone(0.65, 1.0, 0.0, 0.1)
        zones['forward_right_flank'] = self._create_zone(0.65, 1.0, 0.9, 1.0)
        zones['defensive_left_flank'] = self._create_zone(0.0, 0.35, 0.0, 0.1)
        zones['defensive_right_flank'] = self._create_zone(0.0, 0.35, 0.9, 1.0)

        # Specific tactical areas (these can overlap with the base zones)
        zones['forward_50'] = self._create_zone(0.7, 1.0, 0.0, 1.0)
        zones['defensive_50'] = self._create_zone(0.0, 0.3, 0.0, 1.0)
        zones['center_bounce'] = self._create_zone(0.45, 0.55, 0.45, 0.55)

        # Pocket areas (these can overlap with the base zones)
        zones['forward_pocket_left'] = self._create_zone(0.8, 1.0, 0.0, 0.2)
        zones['forward_pocket_right'] = self._create_zone(0.8, 1.0, 0.8, 1.0)
        zones['back_pocket_left'] = self._create_zone(0.0, 0.2, 0.0, 0.2)
        zones['back_pocket_right'] = self._create_zone(0.0, 0.2, 0.8, 1.0)

        # Filter out any coordinates that are out of bounds (for oval field)
        if self.is_oval:
            for zone_name in zones:
                zones[zone_name] = [pos for pos in zones[zone_name] if self.is_in_bounds(pos)]

        return zones

    def _initialize_congestion_map(self) -> List[List[float]]:
        """Initialize congestion map for the entire ground."""
        return [[0.0 for _ in range(self.grid_width)] for _ in range(self.grid_length)]

    def update_congestion(self, player_positions: List[Tuple[int, int]]) -> None:
        """Update congestion map based on player positions."""
        # Reset congestion map
        self.congestion_map = self._initialize_congestion_map()

        # Add congestion for each player
        for pos in player_positions:
            x, y = pos
            congestion_radius = 5  # 5-cell radius of influence

            for dx in range(-congestion_radius, congestion_radius + 1):
                for dy in range(-congestion_radius, congestion_radius + 1):
                    new_x = x + dx
                    new_y = y + dy

                    if (0 <= new_x < self.grid_length and
                        0 <= new_y < self.grid_width):
                        # Inverse square law for congestion
                        distance = math.sqrt(dx*dx + dy*dy)
                        if distance == 0:
                            self.congestion_map[new_x][new_y] += 1.0
                        else:
                            self.congestion_map[new_x][new_y] += 1.0 / (distance * distance)
    """
    def _define_running_channels(self) -> Dict[str, List[Tuple[int, int]]]:
        #Define running channels for player movement.
        channels = {}

        # Main corridor (central channel)
        channels['central'] = self._create_zone(0.0, 1.0, 0.4, 0.6)

        # Wing channels
        channels['left_wing'] = self._create_zone(0.0, 1.0, 0.15, 0.35)
        channels['right_wing'] = self._create_zone(0.0, 1.0, 0.65, 0.85)

        # Forward channels
        channels['forward_left'] = self._create_zone(0.6, 1.0, 0.2, 0.4)
        channels['forward_right'] = self._create_zone(0.6, 1.0, 0.6, 0.8)

        # Defensive channels
        channels['defensive_left'] = self._create_zone(0.0, 0.4, 0.2, 0.4)
        channels['defensive_right'] = self._create_zone(0.0, 0.4, 0.6, 0.8)

        return channels
    """

    def _create_zone(self, x_start: float, x_end: float, y_start: float, y_end: float) -> List[Tuple[int, int]]:
        #Create a zone based on relative coordinates.
        x_start_grid = int(x_start * self.grid_length)
        x_end_grid = int(x_end * self.grid_length)
        y_start_grid = int(y_start * self.grid_width)
        y_end_grid = int(y_end * self.grid_width)

        return [(x, y) for x in range(x_start_grid, x_end_grid)
                      for y in range(y_start_grid, y_end_grid)]

    """
    def get_zone_for_position(self, position: Tuple[int, int]) -> str:
        #Get the tactical zone for a given position.
        for zone_name, zone_positions in self.zones.items():
            if position in zone_positions:
                return zone_name
    """

    def get_zone_for_position(self, position: Tuple[int, int], team_side: str) -> str:
        """
        Get the tactical zone for a given position, from the perspective of the specified team.

        Args:
            position: The (x, y) coordinates to check
            team_side: 'home' or 'away' - determines the perspective

        Returns:
            The zone name from the team's perspective
        """
        # For away team, transform the coordinates to get the equivalent home team position
        if team_side == 'away':
            # Flip the x-coordinate (length axis)
            transformed_x = self.grid_length - 1 - position[0]
            # Flip the y-coordinate (width axis)
            transformed_y = self.grid_width - 1 - position[1]
            position_to_check = (transformed_x, transformed_y)
        else:
            position_to_check = position

        # Now get the zone from the home team's perspective
        home_zone = self._get_home_zone_for_position(position_to_check)

        # If we're using the away team's perspective, transform the zone name
        if team_side == 'away':
            return self._transform_zone_name_for_away_team(home_zone)
        else:
            return home_zone

    def _get_home_zone_for_position(self, position: Tuple[int, int]) -> str:
        """Get the tactical zone for a given position from home team's perspective."""
        # First check the primary zones (these should cover the entire field)
        primary_zones = [
            'deep_forward', 'high_forward', 'center_corridor',
            'wing_left', 'wing_right', 'deep_defense', 'high_defense',
            'forward_left_flank', 'forward_right_flank',
            'defensive_left_flank', 'defensive_right_flank'
        ]

        for zone_name in primary_zones:
            if position in self.zones[zone_name]:
                return zone_name

        # If not found in primary zones, check all zones
        for zone_name, zone_positions in self.zones.items():
            if position in zone_positions:
                return zone_name

        # If still not found, determine zone based on coordinates
        x, y = position

        # Get field dimensions
        length = self.grid_length
        width = self.grid_width

        # Determine zone based on position
        if x >= length * 0.8:
            if y <= width * 0.3:
                return 'forward_pocket_left'
            elif y >= width * 0.7:
                return 'forward_pocket_right'
            else:
                return 'deep_forward'
        elif x >= length * 0.65:
            return 'high_forward'
        elif x >= length * 0.35:
            if y <= width * 0.3:
                return 'wing_left'
            elif y >= width * 0.7:
                return 'wing_right'
            else:
                return 'center_corridor'
        elif x >= length * 0.2:
            return 'high_defense'
        else:
            if y <= width * 0.3:
                return 'back_pocket_left'
            elif y >= width * 0.7:
                return 'back_pocket_right'
            else:
                return 'deep_defense'

    def _transform_zone_name_for_away_team(self, home_zone: str) -> str:
        """Transform a zone name from home team perspective to away team perspective."""
        # Map of zone transformations
        zone_map = {
            # Forward becomes defense
            'deep_forward': 'deep_defense',
            'high_forward': 'high_defense',
            'forward_pocket_left': 'back_pocket_right',
            'forward_pocket_right': 'back_pocket_left',
            'forward_left_flank': 'defensive_right_flank',
            'forward_right_flank': 'defensive_left_flank',

            # Defense becomes forward
            'deep_defense': 'deep_forward',
            'high_defense': 'high_forward',
            'back_pocket_left': 'forward_pocket_right',
            'back_pocket_right': 'forward_pocket_left',
            'defensive_left_flank': 'forward_right_flank',
            'defensive_right_flank': 'forward_left_flank',

            # Wings swap sides
            'wing_left': 'wing_right',
            'wing_right': 'wing_left',

            # Center stays the same
            'center_corridor': 'center_corridor',
            'center_bounce': 'center_bounce',

            # 50s swap
            'forward_50': 'defensive_50',
            'defensive_50': 'forward_50'
        }

        return zone_map.get(home_zone, home_zone)

    def _calculate_distance(
        self,
        pos1: Tuple[float, float],
        pos2: Tuple[float, float]
    ) -> float:
        """Calculate distance between two positions in grid cells."""
        return math.sqrt((pos2[0] - pos1[0]) ** 2 + (pos2[1] - pos1[1]) ** 2)


    def is_position_legal(self, position: Tuple[int, int]) -> bool:
        """Check if a position is within legal bounds of the ground."""
        x, y = position
        return (0 <= x < self.grid_length and
                0 <= y < self.grid_width)

    def is_in_bounds(self, position: Tuple[float, float]) -> bool:
        """Check if position is within ground boundaries (oval shape)."""
        x, y = position

        # First check rectangular bounds (quick rejection)
        if not (0 <= x < self.grid_length and 0 <= y < self.grid_width):
            return False

        # If we're using rectangular field, we're done
        if not self.is_oval:
            return True

        # For oval field, check if point is inside the ellipse
        center_x = self.grid_length / 2
        center_y = self.grid_width / 2

        # Semi-major and semi-minor axes (slightly smaller than half dimensions to account for boundary)
        a = self.grid_length / 2 # Semi-major axis (x direction)
        b = self.grid_width / 2   # Semi-minor axis (y direction)

        # Ellipse equation: (x-h)²/a² + (y-k)²/b² <= 1
        normalized_distance = ((x - center_x)**2 / a**2) + ((y - center_y)**2 / b**2)


        return normalized_distance <= 1.0

    def attacking_direction(self, team_side):
        attacking_direction = 1 if team_side == 'home' else -1
        return attacking_direction

    def get_distance_to_goal(self, position: Tuple[int, int], team_side: str) -> float:
        """Calculate distance to goal posts in meters."""
        x, y = position
        goal_x = self.grid_length if team_side == 'home' else 0
        goal_y = self.grid_width / 2

        # Calculate distance in grid cells
        grid_distance = math.sqrt((x - goal_x)**2 + (y - goal_y)**2)

        # Convert to meters (each grid cell is 2 meters)
        meters_distance = grid_distance * 2

        return meters_distance


    def is_in_range_for_shot(self, position: Tuple[int, int], team_side: str) -> bool:
        """Determine if position is within reasonable range for shot at goal."""
        distance = self.get_distance_to_goal(position, team_side)
        angle = self._calculate_shot_angle(position, team_side)
        print(f"Distance: {distance}, Angle: {angle}")
        # Consider both distance and angle
        return distance < 60 and abs(angle) < 90  # 50m range, 60-degree angle

    def _calculate_shot_angle(self, position: Tuple[int, int], team_side: str) -> float:
        """Calculate angle to goal center in degrees.

        Returns:
            0 degrees when directly in front of goal
            90 degrees when side-on to goal (either side)
            180 degrees when directly behind goal

        Note: Always returns positive angle (0-180) representing deviation from direct shot
        """
        x, y = position
        goal_x = self.grid_length if team_side == 'home' else 0
        goal_y = self.grid_width / 2

        # Print debug information
        #print(f"Position: ({x}, {y})")
        #print(f"Goal position: ({goal_x}, {goal_y})")
        #print(f"Team side: {team_side}")

        # Calculate vector components from position to goal
        dx = goal_x - x
        dy = goal_y - y
        #print(f"Vector to goal: dx={dx}, dy={dy}")

        # Calculate raw angle using atan2
        raw_angle = math.degrees(math.atan2(dy, dx))
        #print(f"Raw angle: {raw_angle}")

        # Normalize to 0-360 range
        if raw_angle < 0:
            raw_angle += 360
        #print(f"Normalized angle (0-360): {raw_angle}")

        # Adjust based on team side
        if team_side == 'home':
            # Home team attacks right
            # 0 degrees is directly east (right)
            # For home team, the reference angle is 0 (directly east)
            reference_angle = 0
        else:
            # Away team attacks left
            # 180 degrees is directly west (left)
            # For away team, the reference angle is 180 (directly west)
            reference_angle = 180

        # Calculate the absolute angular difference from the reference angle
        angle_diff = abs((raw_angle - reference_angle + 180) % 360 - 180)
        #print(f"Angle difference from reference: {angle_diff}")

        # This gives us the shot angle directly:
        # - 0 when aligned with reference (directly toward goal)
        # - 90 when perpendicular (side-on)
        # - 180 when opposite (directly away from goal)
        shot_angle = angle_diff
        print(f"Final shot angle: {shot_angle}")

        return shot_angle

    def get_grid_map(self) -> Dict[str, List[Tuple[int, int]]]:
        """Return the grid map with dynamic zones."""
        return self.positions_zones

    def _flip_coordinates(self, coords, team_side='home'):
        """Flip coordinates based on team side."""
        if team_side == 'home':
            return coords

        # Flip x-coordinate for away team
        x, y = coords
        return (self.grid_length - 1 - x, y)


    def get_center(self) -> Tuple[float, float]:
        """Calculate the center point of the ground."""
        # For a 160x130 ground with grid of 80x65:
        # Center should be at (40, 32.5) in grid coordinates
        center_x = self.grid_length // 2  # 80/2 = 40
        center_y = self.grid_width // 2   # 65/2 = 32
        return (center_x, center_y)  # Should return (40, 32) for standard ground

    def define_dynamic_zones(self, grid_width: int, grid_length: int) -> Dict[str, List[Tuple[int, int]]]:
        """Define zones for each position with dynamic sizing based on field dimensions."""
        positions_zones = {}

        # FIXED: Correct coordinate system
        center_x = grid_length // 2   # Should be 40 for grid_length=80
        center_y = grid_width // 2    # Should be 32 for grid_width=65

        print(f"Field center: ({center_x}, {center_y})")

        # Define zone boundaries using actual grid coordinates for clarity
        # For a standard 80x65 grid:

        # Vertical zones (x-axis) - FIXED: these are along the length
        back_line_x = int(grid_length * 0.1)            # Around x=8
        half_back_line_x = int(grid_length * 0.3)       # Around x=24
        center_line_x = center_x                         # x=40
        half_forward_line_x = int(grid_length * 0.7)    # Around x=56
        forward_line_x = int(grid_length * 0.9)         # Around x=72

        # Horizontal zones (y-axis) - FIXED: these are along the width
        left_wing_y = int(grid_width * 0.25)            # Around y=16
        center_y_pos = center_y                          # y=32
        right_wing_y = int(grid_width * 0.75)           # Around y=48

        # Print zone line positions for verification
        print(f"Vertical zones: Back={back_line_x}, HB={half_back_line_x}, Center={center_line_x}, " +
            f"HF={half_forward_line_x}, Forward={forward_line_x}")
        print(f"Horizontal zones: Left={left_wing_y}, Center={center_y_pos}, Right={right_wing_y}")

        # Define zone widths
        zone_width_y = grid_width // 3
        zone_width_x = grid_length // 5

        # Now define each position zone explicitly with FIXED coordinate system
        # Back line
        positions_zones["LB"] = [(x, y) for x in range(0, back_line_x + zone_width_x//2)
                                        for y in range(0, left_wing_y + zone_width_y//2)]

        positions_zones["FB"] = [(x, y) for x in range(0, back_line_x + zone_width_x//2)
                                        for y in range(left_wing_y + zone_width_y//2, right_wing_y - zone_width_y//2)]

        positions_zones["RB"] = [(x, y) for x in range(0, back_line_x + zone_width_x//2)
                                        for y in range(right_wing_y - zone_width_y//2, grid_width)]

        # Half-back line
        positions_zones["LHB"] = [(x, y) for x in range(back_line_x + zone_width_x//2, half_back_line_x + zone_width_x//2)
                                        for y in range(0, left_wing_y + zone_width_y//2)]

        positions_zones["CHB"] = [(x, y) for x in range(back_line_x + zone_width_x//2, half_back_line_x + zone_width_x//2)
                                        for y in range(left_wing_y + zone_width_y//2, right_wing_y - zone_width_y//2)]

        positions_zones["RHB"] = [(x, y) for x in range(back_line_x + zone_width_x//2, half_back_line_x + zone_width_x//2)
                                        for y in range(right_wing_y - zone_width_y//2, grid_width)]

        # Center line
        positions_zones["LWing"] = [(x, y) for x in range(half_back_line_x + zone_width_x//2, half_forward_line_x - zone_width_x//2)
                                        for y in range(0, left_wing_y + zone_width_y//2)]

        positions_zones["Centre"] = [(x, y) for x in range(half_back_line_x + zone_width_x//2, half_forward_line_x - zone_width_x//2)
                                            for y in range(left_wing_y + zone_width_y//2, right_wing_y - zone_width_y//2)]

        positions_zones["RWing"] = [(x, y) for x in range(half_back_line_x + zone_width_x//2, half_forward_line_x - zone_width_x//2)
                                        for y in range(right_wing_y - zone_width_y//2, grid_width)]

        # Half-forward line
        positions_zones["LHF"] = [(x, y) for x in range(half_forward_line_x - zone_width_x//2, forward_line_x - zone_width_x//2)
                                        for y in range(0, left_wing_y + zone_width_y//2)]

        positions_zones["CHF"] = [(x, y) for x in range(half_forward_line_x - zone_width_x//2, forward_line_x - zone_width_x//2)
                                        for y in range(left_wing_y + zone_width_y//2, right_wing_y - zone_width_y//2)]

        positions_zones["RHF"] = [(x, y) for x in range(half_forward_line_x - zone_width_x//2, forward_line_x - zone_width_x//2)
                                        for y in range(right_wing_y - zone_width_y//2, grid_width)]

        # Forward line
        positions_zones["LF"] = [(x, y) for x in range(forward_line_x - zone_width_x//2, grid_length)
                                        for y in range(0, left_wing_y + zone_width_y//2)]

        positions_zones["FF"] = [(x, y) for x in range(forward_line_x - zone_width_x//2, grid_length)
                                        for y in range(left_wing_y + zone_width_y//2, right_wing_y - zone_width_y//2)]

        positions_zones["RF"] = [(x, y) for x in range(forward_line_x - zone_width_x//2, grid_length)
                                        for y in range(right_wing_y - zone_width_y//2, grid_width)]

        if self.is_oval:
            oval_zones = {}
            for pos, coords in positions_zones.items():
                oval_zones[pos] = [coord for coord in coords if self.is_in_bounds(coord)]

                # If the zone is now empty or too small (corner positions like LB, RB, LF, RF)
                # we need to ensure there are enough valid coordinates
                if len(oval_zones[pos]) < (len(coords) * 0.5):  # If we lost more than half the coordinates
                    # Calculate the ideal center for this position
                    if pos == "LB":
                        ideal_x, ideal_y = zone_width_x // 2, zone_width_y // 2
                    elif pos == "RB":
                        ideal_x, ideal_y = zone_width_x // 2, grid_width - zone_width_y // 2 - 1
                    elif pos == "LF":
                        ideal_x, ideal_y = grid_length - zone_width_x // 2 - 1, zone_width_y // 2
                    elif pos == "RF":
                        ideal_x, ideal_y = grid_length - zone_width_x // 2 - 1, grid_width - zone_width_y // 2 - 1
                    else:
                        continue  # Skip other positions

                    # Adjust the center to be inside the oval
                    adjusted_x, adjusted_y = self.adjust_to_oval(ideal_x, ideal_y)

                    # Create a new zone centered on the adjusted position
                    radius = min(zone_width_x, zone_width_y) // 2
                    new_coords = []
                    for dx in range(-radius, radius + 1):
                        for dy in range(-radius, radius + 1):
                            x, y = adjusted_x + dx, adjusted_y + dy
                            if self.is_in_bounds((x, y)):
                                new_coords.append((x, y))

                    oval_zones[pos] = new_coords

            return oval_zones

        # Print zone sizes and boundaries for verification
        for pos, zone in positions_zones.items():
            if zone:
                x_coords = [x for x, _ in zone]
                y_coords = [y for _, y in zone]
                print(f"Position {pos}: {len(zone)} cells, boundaries: " +
                    f"x=[{min(x_coords)}-{max(x_coords)}], y=[{min(y_coords)}-{max(y_coords)}]")

        return positions_zones


    def adjust_to_oval(self, x, y):
        """Adjust a position to ensure it's inside the oval boundary."""
        # If already in bounds, return as is
        if self.is_in_bounds((x, y)):
            return (x, y)

        # Calculate vector from center to position
        center_x = self.grid_length // 2
        center_y = self.grid_width // 2
        dx = x - center_x
        dy = y - center_y

        # Calculate the scaling factor needed to place point on the oval boundary
        a = self.grid_length / 2 * 0.99  # Semi-major axis (x direction)
        b = self.grid_width / 2 * 0.99   # Semi-minor axis (y direction)
        scale = math.sqrt(((dx/a)**2 + (dy/b)**2))

        # Scale down the vector to place it just inside the boundary
        adjusted_x = center_x + (dx / scale) * 0.95  # 95% of distance to boundary
        adjusted_y = center_y + (dy / scale) * 0.95

        return (int(adjusted_x), int(adjusted_y))

    """
    def get_position_from_coordinates(self, coordinates):
        #Get the most appropriate position name from (x, y) coordinates.
        if not coordinates:
            return "Out of Bounds"

        x, y = coordinates

        # First, check if these are grid coordinates or raw coordinates
        # If they're larger than our grid size, they're likely raw coordinates
        if x > self.grid_width or y > self.grid_length:
            # Convert raw coordinates to grid coordinates
            grid_x = int(x / 2)  # Convert to grid cells (2m per cell)
            grid_y = int(y / 2)
        else:
            grid_x, grid_y = x, y

        # Calculate relative position as percentages (0-100%)
        rel_x = (grid_x / self.grid_width) * 100   # x as percentage of width
        rel_y = (grid_y / self.grid_length) * 100  # y as percentage of length

        # Get center of ground
        center_x, center_y = self.get_center()

        if abs(grid_x - center_x) <= 12 and abs(grid_y - center_y) <= 12:
            print(f"Mapped coordinates ({x}, {y}) -> grid ({grid_x}, {grid_y}) -> " +
            f"relative ({(grid_x / self.grid_width) * 100:.1f}%, {(grid_y / self.grid_length) * 100:.1f}%) -> " +
            f"zone (center) -> position Centre")
            return "Centre"  # Expanded center zone

        # Define zones dynamically based on percentages
        # Vertical zones (5 zones)
        if rel_y < 20:
            vertical_zone = "deep_defense"
        elif rel_y < 40:
            vertical_zone = "half_back"
        elif rel_y < 60:
            vertical_zone = "midfield"
        elif rel_y < 80:
            vertical_zone = "half_forward"
        else:
            vertical_zone = "deep_forward"

        # Horizontal zones (3 zones)
        if rel_x < 33:
            horizontal_zone = "left"
        elif rel_x < 67:
            horizontal_zone = "center"
        else:
            horizontal_zone = "right"

        # Map zone combinations to position names
        position_map = {
            # Deep defense (back line)
            ("deep_defense", "left"): "LB",
            ("deep_defense", "center"): "FB",
            ("deep_defense", "right"): "RB",

            # Half-back line
            ("half_back", "left"): "LHB",
            ("half_back", "center"): "CHB",
            ("half_back", "right"): "RHB",

            # Midfield
            ("midfield", "left"): "LWing",
            ("midfield", "center"): "Centre",
            ("midfield", "right"): "RWing",

            # Half-forward line
            ("half_forward", "left"): "LHF",
            ("half_forward", "center"): "CHF",
            ("half_forward", "right"): "RHF",

            # Deep forward (forward line)
            ("deep_forward", "left"): "LF",
            ("deep_forward", "center"): "FF",
            ("deep_forward", "right"): "RF"
        }

        # Get position from map
        position = position_map.get((vertical_zone, horizontal_zone))

        print(f"Mapped coordinates ({x}, {y}) -> grid ({grid_x}, {grid_y}) -> " +
            f"relative ({rel_x:.1f}%, {rel_y:.1f}%) -> " +
            f"zone ({vertical_zone}, {horizontal_zone}) -> position {position}")

        return position
    """

    def get_position_from_coordinates(self, coordinates):
        #Get the position name from coordinates - efficient lookup.
        return self.position_map.get(coordinates, "Out of Bounds")


    def get_coordinates(self, position, home_team_players, away_team_players):
        """Get the (x, y) coordinates from a grid position name, or return the input if it's already coordinates."""
        # If position is already coordinates, return directly
        if isinstance(position, tuple):
            return position

        # Define mobile positions
        mobile_positions = ['Ruck', 'Rover', 'RuckRover']

        # If it's a mobile position, we need to find where that player actually is
        if position in mobile_positions:
            # Find the player with this position
            for team in [home_team_players, away_team_players]:
                for player in team.values():
                    if player.position == position:
                        print(f"Player {player.name} is at {player.current_position}")
                        # Check if current_position is also a mobile position to avoid loops
                        if player.current_position in mobile_positions:
                            print(f"Mobile player {player.name} defaulting to Centre")
                            return self.get_coordinates('Centre', home_team_players, away_team_players)
                        # Return coordinates of where this player currently is
                    return self.get_coordinates(player.current_position, home_team_players, away_team_players)
            print(f"Warning: Could not find player for mobile position {position}")
            return self.get_coordinates('Centre')  # Default to centre if player not found

        # For fixed positions, get coordinates from grid map
        grid_map = self.get_grid_map()
        for zone_name, coordinates_list in grid_map.items():
            if zone_name == position:
                if coordinates_list:
                    #print(f"Coordinates for {position}: {coordinates_list}")
                    return coordinates_list[0]  # Return the first coordinate for the named zone

        print(f"Error: Could not get coordinates for position {position}")
        sys.exit()

    def get_dynamic_possible_positions(self, position_coords, max_distance_meters, team_side='home', return_coordinates=False):
        """Get all possible field positions within the max_distance from the current position.

        Args:
            position_coords: Coordinates (x, y) of the player's current position in grid cells
            max_distance_meters: Maximum distance in meters (representing kick/handball range)
            team_side: 'home' or 'away' to determine field orientation
            return_coordinates: If True, returns all x,y coordinates within range instead of position names

        Returns:
            If return_coordinates=False: List of position names that are within the max_distance
            If return_coordinates=True: List of (x,y) coordinates that are within the max_distance
        """
        # Convert max distance to cells
        meters_per_cell = 2.0
        max_distance_cells = max_distance_meters / meters_per_cell

        print(f"Finding possible positions from coordinates {position_coords}")
        print(f"Max distance in meters: {max_distance_meters}m (= {max_distance_cells:.1f} cells)")
        print(f"Team side: {team_side}")

        # If we want all coordinates within range
        if return_coordinates:
            # Calculate field boundaries
            field_length = self.grid_length
            field_width = self.grid_width

            # Round up max_distance_cells to ensure we include all possible cells
            radius = math.ceil(max_distance_cells)

            # Get player position
            px, py = position_coords

            # Find all coordinates within the radius
            coordinates_in_range = []
            for x in range(max(0, px - radius), min(field_length, px + radius + 1)):
                for y in range(max(0, py - radius), min(field_width, py + radius + 1)):
                    # Calculate Euclidean distance
                    dx = x - px
                    dy = y - py
                    distance = math.sqrt(dx*dx + dy*dy)

                    # If within range, add to list
                    if distance <= max_distance_cells:
                        coordinates_in_range.append((x, y))
            if self.is_oval:
                coordinates_in_range = [coord for coord in coordinates_in_range
                                       if self.is_in_bounds(coord)]

                return coordinates_in_range

            print(f"Found {len(coordinates_in_range)} coordinates within {max_distance_meters}m")
            return coordinates_in_range

        # Otherwise, continue with position-based approach
        # Calculate field dimensions and center
        field_length = self.grid_length  # Should be 80
        field_width = self.grid_width    # Should be 65
        center_x = field_length // 2     # Should be 40
        center_y = field_width // 2      # Should be 32

        # Calculate zone sizes - divide field into 15 zones (5 vertical × 3 horizontal)
        length_zone = field_length // 5  # Should be 16 cells per zone vertically
        width_zone = field_width // 3    # Should be 21 cells per zone horizontally

        # Define zone boundaries for each position
        position_zones = {
            # Back line
            "LB": (0, length_zone, 0, width_zone),
            "FB": (0, length_zone, width_zone, 2*width_zone),
            "RB": (0, length_zone, 2*width_zone, field_width),

            # Half-back line
            "LHB": (length_zone, 2*length_zone, 0, width_zone),
            "CHB": (length_zone, 2*length_zone, width_zone, 2*width_zone),
            "RHB": (length_zone, 2*length_zone, 2*width_zone, field_width),

            # Center line
            "LWing": (2*length_zone, 3*length_zone, 0, width_zone),
            "Centre": (2*length_zone, 3*length_zone, width_zone, 2*width_zone),
            "RWing": (2*length_zone, 3*length_zone, 2*width_zone, field_width),

            # Half-forward line
            "LHF": (3*length_zone, 4*length_zone, 0, width_zone),
            "CHF": (3*length_zone, 4*length_zone, width_zone, 2*width_zone),
            "RHF": (3*length_zone, 4*length_zone, 2*width_zone, field_width),

            # Forward line
            "LF": (4*length_zone, field_length, 0, width_zone),
            "FF": (4*length_zone, field_length, width_zone, 2*width_zone),
            "RF": (4*length_zone, field_length, 2*width_zone, field_width),
        }

        # If team is away, flip zones horizontally
        if team_side == 'away':
            flipped_zones = {}
            for pos, (x_min, x_max, y_min, y_max) in position_zones.items():
                # Flip x-coordinates
                flipped_x_min = field_length - x_max
                flipped_x_max = field_length - x_min

                # Swap L/R positions
                if pos.startswith('L'):
                    new_pos = 'R' + pos[1:]
                elif pos.startswith('R'):
                    new_pos = 'L' + pos[1:]
                else:
                    new_pos = pos

                flipped_zones[new_pos] = (flipped_x_min, flipped_x_max, y_min, y_max)
            position_zones = flipped_zones

        # Print zone boundaries for debugging
        print(f"Field center: ({center_x}, {center_y})")
        print("Position zones:")
        for pos, (x_min, x_max, y_min, y_max) in position_zones.items():
            print(f"  {pos}: x=[{x_min}-{x_max}], y=[{y_min}-{y_max}]")

        # Find positions with any part within range
        possible_positions = []
        for pos, (x_min, x_max, y_min, y_max) in position_zones.items():
            # Find closest point in zone to player
            px, py = position_coords

            # Clamp player position to zone boundaries to find closest point
            closest_x = max(x_min, min(px, x_max - 1))
            closest_y = max(y_min, min(py, y_max - 1))

            # Calculate distance to closest point
            dx = closest_x - px
            dy = closest_y - py
            distance_cells = math.sqrt(dx*dx + dy*dy)
            distance_meters = distance_cells * meters_per_cell

            if distance_meters <= max_distance_meters:
                possible_positions.append(pos)
                print(f"Position {pos}: Closest point ({closest_x}, {closest_y}) is within range at {distance_cells:.1f} cells ({distance_meters:.1f}m)")
            else:
                print(f"Position {pos}: Closest point ({closest_x}, {closest_y}) at {distance_cells:.1f} cells ({distance_meters:.1f}m) - NOT in range")

        print(f"Possible positions from {position_coords} within {max_distance_meters}m: {possible_positions}")
        return possible_positions

    def get_zone(self, position: Tuple[float, float]) -> str:
        """Get the zone name for a position."""
        x, y = position
        for pos_name, zone in self.positions_zones.items():
            if (int(x), int(y)) in zone:
                return pos_name
        return None  # Default to Centre if not in any zone

    def _get_nearest_zone(self, position: Tuple[float, float]) -> str:
        """Find nearest zone to position."""
        x, y = position
        min_dist = float('inf')
        nearest_zone = None

        for pos_name, zone in self.positions_zones.items():
            for zx, zy in zone:
                dist = (zx - x) ** 2 + (zy - y) ** 2
                if dist < min_dist:
                    min_dist = dist
                    nearest_zone = pos_name

        return nearest_zone

    def get_meters_per_cell(self) -> Tuple[float, float]:
        """Get meters per cell for both dimensions."""
        return (self.meters_per_cell_width, self.meters_per_cell_length)

    def get_thirds(self) -> Tuple[int, int]:
        """Get y-coordinates for defensive and forward thirds."""
        return (self.defensive_third, self.forward_third)

    def get_corridor_boundaries(self) -> Tuple[int, int]:
        """Get x-coordinates for corridor (middle third of width)."""
        grid_third = self.grid_width // 3
        return grid_third, grid_third * 2

    def get_forward_zones(self, team_side: str) -> List[str]:
        """Get forward zones for team side"""
        if team_side == 'home':
            return ['forward_50', 'forward_flank']
        else:
            return ['back_50', 'back_flank']

    def get_defensive_zones(self, team_side: str) -> List[str]:
        """Get defensive zones for team side"""
        if team_side == 'home':
            return ['back_50', 'back_flank']
        else:
            return ['forward_50', 'forward_flank']

    def get_midfield_zones(self) -> List[str]:
        """Get midfield zones"""
        return ['center']

    def get_zone_center(self, zone: str) -> Tuple[float, float]:
        """Get center coordinates of zone."""
        print(f"Getting zone center for {zone}")
        print(self.positions_zones)
        zone_coords = self.positions_zones[zone]
        x_coords = [x for x, _ in zone_coords]
        y_coords = [y for _, y in zone_coords]
        center_x = sum(x_coords) / len(x_coords)
        center_y = sum(y_coords) / len(y_coords)
        return (center_x, center_y)

    def get_zone_boundaries(self, zone: str) -> Tuple[float, float, float, float]:
        """Get boundaries of zone."""
        zone_coords = self.positions_zones[zone]
        x_coords = [x for x, _ in zone_coords]
        y_coords = [y for _, y in zone_coords]
        return (
            min(x_coords),
            max(x_coords),
            min(y_coords),
            max(y_coords)
        )

    def get_random_position_in_zone(self, zone: str) -> Tuple[float, float]:
        """Get random valid position within zone."""
        zone_coords = self.positions_zones[zone]
        return random.choice(zone_coords)

    def get_nearest_position_in_zone(self, current_pos: Tuple[float, float], zone: str) -> Tuple[float, float]:
        """Get nearest valid position in zone to current position."""
        zone_coords = self.positions_zones[zone]
        x, y = current_pos
        min_dist = float('inf')
        nearest_pos = None

        for zx, zy in zone_coords:
            dist = (zx - x) ** 2 + (zy - y) ** 2
            if dist < min_dist:
                min_dist = dist
                nearest_pos = (zx, zy)

        return nearest_pos or self.get_zone_center(zone)  # Fallback to center if no position found


    def visualize_ground_with_position(self, position=None):
        """
        Visualize the ground with grid coordinates and mark a specific position.

        Args:
            position: Optional tuple (x, y) to mark on the grid
        """
        print(f"Visualizing ground with position: {position}")
        # Create a grid representation
        grid = [[' ' for _ in range(self.grid_width)] for _ in range(self.grid_length)]

        # Mark the oval boundary
        for x in range(self.grid_length):
            for y in range(self.grid_width):
                if self.is_in_bounds((x, y)):
                    grid[x][y] = '.'  # Inside the oval

        # Mark the center
        center_x, center_y = int(self.grid_length / 2), int(self.grid_width / 2)
        grid[center_x][center_y] = 'C'

        # Mark the position if provided
        if position:
            x, y = position
            if 0 <= x < self.grid_length and 0 <= y < self.grid_width:
                grid[x][y] = 'X'

        # Print the grid with coordinates
        print("Ground Visualization (. = in bounds, X = position, C = center)")
        print("   " + "".join(f"{y%10}" for y in range(self.grid_width)))
        for x in range(self.grid_length):
            print(f"{x:2d} " + "".join(grid[x]))

        # Check if position is in bounds
        if position:
            x, y = position
            is_in_bounds = self.is_in_bounds(position)
            print(f"Position ({x}, {y}) is {'IN' if is_in_bounds else 'OUT OF'} bounds")

            # Debug the calculation
            if self.is_oval:
                center_x = self.grid_length / 2
                center_y = self.grid_width / 2
                a = self.grid_length / 2 * 0.98
                b = self.grid_width / 2 * 0.98
                normalized_distance = ((x - center_x)**2 / a**2) + ((y - center_y)**2 / b**2)
                print(f"Normalized distance: {normalized_distance:.4f} (should be <= 1.0 to be in bounds)")
                print(f"Center: ({center_x}, {center_y}), Semi-axes: a={a:.2f}, b={b:.2f}")



class StatsManager:
    """Manages player and team statistics."""

    def __init__(self):
        self.player_stats = {}
        self.team_stats = {}


    def record_stat(self, player_name: str, stat_type: str) -> None:
        """Record a statistic for a player."""
        if player_name not in self.player_stats:
            self.player_stats[player_name] = {
                'disposals': 0,
                'marks': 0,
                'tackles': 0,
                'goals': 0,
                'behinds': 0,
                'clearances': 0,
                'inside_50s': 0,
                'rebound_50s': 0
            }

        if stat_type in self.player_stats[player_name]:
            self.player_stats[player_name][stat_type] += 1

    def get_player_stats(self, player_name: str) -> Dict[str, int]:
        """Get statistics for a player."""
        return self.player_stats.get(player_name, {})

    def record_team_stat(self, team_name: str, stat_type: str) -> None:
        """Record a statistic for a team."""
        if team_name not in self.team_stats:
            self.team_stats[team_name] = {
                'goals': 0,
                'behinds': 0,
                'total_score': 0,
                'inside_50s': 0,
                'clearances': 0,
                'tackles': 0
            }

        if stat_type in self.team_stats[team_name]:
            self.team_stats[team_name][stat_type] += 1

    def get_team_stats(self, team_name: str) -> Dict[str, int]:
        """Get statistics for a team."""
        return self.team_stats.get(team_name, {})

    def get_match_stats(self) -> Dict[str, Dict[str, int]]:
        """Get all match statistics."""
        return {
            'players': self.player_stats,
            'teams': self.team_stats
        }

    def clear_stats(self) -> None:
        """Clear all statistics."""
        self.player_stats = {}
        self.team_stats = {}

    def get_player_rankings(self) -> Dict[str, List[Tuple[str, int]]]:
        """Get player rankings for each statistic."""
        rankings = {
            'disposals': [],
            'marks': [],
            'tackles': [],
            'goals': [],
            'behinds': [],
            'clearances': [],
            'inside_50s': [],
            'rebound_50s': []
        }

        for player_name, stats in self.player_stats.items():
            for stat_type in rankings:
                rankings[stat_type].append((player_name, stats.get(stat_type, 0)))

        # Sort each ranking list
        for stat_type in rankings:
            rankings[stat_type].sort(key=lambda x: x[1], reverse=True)

        return rankings

    def get_team_rankings(self) -> Dict[str, List[Tuple[str, int]]]:
        """Get team rankings for each statistic."""
        rankings = {
            'goals': [],
            'behinds': [],
            'total_score': [],
            'inside_50s': [],
            'clearances': [],
            'tackles': []
        }

        for team_name, stats in self.team_stats.items():
            for stat_type in rankings:
                rankings[stat_type].append((team_name, stats.get(stat_type, 0)))

        # Sort each ranking list
        for stat_type in rankings:
            rankings[stat_type].sort(key=lambda x: x[1], reverse=True)

        return rankings

    def get_player_performance_rating(self, player_name: str) -> float:
        """Calculate overall performance rating for a player."""
        stats = self.get_player_stats(player_name)

        # Base rating from stats
        rating = (
            stats.get('disposals', 0) * 3 +
            stats.get('marks', 0) * 4 +
            stats.get('tackles', 0) * 4 +
            stats.get('goals', 0) * 6 +
            stats.get('behinds', 0) * 1 +
            stats.get('clearances', 0) * 5 +
            stats.get('inside_50s', 0) * 3 +
            stats.get('rebound_50s', 0) * 3
        )

        # Scale to 0-10 range
        return min(10.0, rating / 50)

    def get_team_performance_rating(self, team_name: str) -> float:
        """Calculate overall performance rating for a team."""
        stats = self.get_team_stats(team_name)

        # Base rating from score
        rating = stats.get('total_score', 0) * 0.5

        # Add ratings from other stats
        rating += stats.get('inside_50s', 0) * 0.3
        rating += stats.get('clearances', 0) * 0.3
        rating += stats.get('tackles', 0) * 0.2

        # Scale to 0-10 range
        return min(10.0, rating / 50)

class Player:
    """Represents a player in the match, maintaining exact compatibility with original AI version."""

    def __init__(
        self,
        name: str,
        team: Any,
        home_or_away: str,
        position: str,
        ability_stats: Any,
        physical_stats: Any,
        stats_slider: float = 1.0  # Default to no boost
    ):
        self.id = id(self)  # Unique ID for each player
        self.name = name
        self.team = team
        self.team_side = home_or_away
        self.position = position
        self.ability_stats = ability_stats
        self.physical_stats = physical_stats
        self.stats_slider = stats_slider
        self.current_position = None
        self.fatigue = 0.0
        self.movement_state = None
        self.marked_opponent = None

    def __str__(self) -> str:
        """String representation of player."""
        return f"{self.name} ({self.position})"

    def __repr__(self) -> str:
        """Detailed string representation of player."""
        return f"Player({self.name}, {self.team.name}, {self.position})"

    """
    def update(self, delta_time: float) -> None:
        #Update player state
        self.previous_position = self.current_position
        self.fatigue = max(0, self.fatigue - (delta_time * 0.1))
        self.recent_actions = [
            action for action in self.recent_actions
            if time.time() - action['time'] < 60
        ]
    """

    def determine_closest_player_to_ball(self, players: List[Any], ball_position: Tuple[float, float]) -> Any:
        """Find closest player to ball from a list of players."""
        closest_player = None
        min_distance = float('inf')
        equal_distance_players = []

        for player in players:
            if not player.current_position:
                continue

            distance = self.calculate_distance(ball_position)

            if distance < min_distance:
                min_distance = distance
                closest_player = player
                equal_distance_players = [player]
            elif distance == min_distance:
                equal_distance_players.append(player)

        # If multiple players at equal distance, choose based on speed and agility
        if len(equal_distance_players) > 1:
            best_player = None
            best_score = -1

            for player in equal_distance_players:
                score = (player.physical_stats.speed + player.physical_stats.agility) / 2
                if score > best_score:
                    best_score = score
                    best_player = player

            return best_player

        return closest_player

    def determine_closest_opponent_player_to_ball(
        self,
        opponent_players: List[Any],
        ball_position: Tuple[float, float]
    ) -> Any:
        """Find closest opponent to ball."""
        return self.determine_closest_player_to_ball(opponent_players, ball_position)

    def is_in_position(self, influence_radius: float = 5.0) -> bool:
        """Check if player is in their designated position."""
        if not self.current_position:
            return False

        # Mobile positions have larger influence radius
        mobile_positions = ['Centre', 'Rover', 'RuckRover', 'LWing', 'RWing']
        if self.position in mobile_positions:
            influence_radius *= 1.5

        distance = self.calculate_distance(self._get_default_position(self, self.team_side))
        return distance <= influence_radius

    """
    def calculate_distance(
        self,
        target_pos: Tuple[float, float],
        from_pos: Optional[Tuple[float, float]] = None
    ) -> float:
        #Calculate Manhattan distance between positions.
        if from_pos is None:
            if not self.current_position:
                return float('inf')
            from_pos = self.current_position

        # Handle both coordinate tuples and named positions
        if isinstance(target_pos, str):
            target_pos = self.get_coordinates(target_pos)

        return abs(target_pos[0] - from_pos[0]) + abs(target_pos[1] - from_pos[1])
     """

    def _get_default_position(self, player, team_side):
        """Get exact coordinates for player position during center bounce based on AFL rules."""
        """This mirrors MovementEngine Center Boune Function"""
        # Get center of ground
        center_x, center_y = self.ground.get_center()

        # Team direction factor (home team attacks right side, away team attacks left)
        team_factor = 1 if team_side == "home" else -1

        # Centre square size (approximately 50m x 50m in ground coordinates)
        square_radius = 10  # Grid cells from center to edge of center square

        # Position players according to their roles
        if player.position == "Ruck":
            if team_side == "home":
                #print(f"Positioning {player.name} for home team")
                return (center_x - random.randint(1, 2), center_y)  # Slightly offset
            else:
                #print(f"Positioning {player.name} for away team")
                return (center_x + random.randint(1, 2), center_y)  # Slightly offset

        elif player.position == "Rover":
            # Rover slightly offset in center square
            return (center_x + (random.randint(2, 4) * team_factor), center_y - random.randint(2, 4))

        elif player.position == "RuckRover":
            # Ruck Rover slightly offset in center square
            return (center_x + (random.randint(2, 4) * team_factor), center_y - random.randint(2, 4))

        elif player.position == "Centre":
            # Centre slightly back from center in square
            return (center_x + (random.randint(2, 4) * team_factor), center_y - random.randint(2, 4))

        elif player.position == "LWing":
            # Left Wing outside square on wing
            return (center_x - random.randint(-2, 2), center_y - 15)

        elif player.position == "RWing":
            # Right Wing outside square on wing
            return (center_x + random.randint(-2, 2), center_y + 15)

        elif player.position == "CHF":
            # Centre Half Forward
            return (center_x + (15 * team_factor), center_y)

        elif player.position == "CHB":
            # Centre Half Back
            return (center_x - (15 * team_factor), center_y)

        elif player.position == "LHF":
            # Left Half Forward
            return (center_x + (15 * team_factor), center_y - 10)

        elif player.position == "RHF":
            # Right Half Forward
            return (center_x + (15 * team_factor), center_y + 10)

        elif player.position == "LHB":
            # Left Half Back
            return (center_x - (15 * team_factor), center_y - 10)

        elif player.position == "RHB":
            # Right Half Back
            return (center_x - (15 * team_factor), center_y + 10)

        elif player.position == "FF":
            # Full Forward
            return (center_x + (25 * team_factor), center_y)

        elif player.position == "FB":
            # Full Back
            return (center_x - (25 * team_factor), center_y)

        elif player.position == "LF":
            # Left Forward Pocket
            return (center_x + (25 * team_factor), center_y - 12)

        elif player.position == "RF":
            # Right Forward Pocket
            return (center_x + (25 * team_factor), center_y + 12)

        elif player.position == "LB":
            # Left Back Pocket
            return (center_x - (25 * team_factor), center_y - 12)

        elif player.position == "RB":
            # Right Back Pocket
            return (center_x - (25 * team_factor), center_y + 12)

        # Default for unrecognized positions
        return (center_x, center_y)

    def update_fatigue(self, intensity: float) -> None:
        """Update player's fatigue based on action intensity."""
        # Base fatigue increase
        fatigue_increase = intensity * 0.1

        # Modify based on stamina
        stamina_factor = 1.0 - (self.physical_stats.stamina / 100)
        fatigue_increase *= (1.0 + stamina_factor)

        # Add fatigue
        self.fatigue = min(100.0, self.fatigue + fatigue_increase)

    def recover_fatigue(self, amount: float) -> None:
        """Recover fatigue during breaks."""
        # Base recovery
        recovery = amount

        # Modify based on stamina
        stamina_factor = self.physical_stats.stamina / 100
        recovery *= (1.0 + stamina_factor)

        # Reduce fatigue
        self.fatigue = max(0.0, self.fatigue - recovery)

    def get_effective_stats(self) -> Dict[str, float]:
        """Get stats adjusted for fatigue."""
        fatigue_factor = 1.0 - (self.fatigue / 200)

        # Apply stats_slider boost first, then fatigue reduction, then cap at 20
        base_stats = {
            'speed': min(20, self.physical_stats.speed * self.stats_slider),
            'agility': min(20, self.physical_stats.agility * self.stats_slider),
            'strength': min(20, self.physical_stats.strength * self.stats_slider),
            'stamina': min(20, self.physical_stats.stamina * self.stats_slider),
            'mental': min(20, self.ability_stats.mental * self.stats_slider),
            'tactical': min(20, self.ability_stats.tactical * self.stats_slider),
            'kicking': min(20, self.ability_stats.kicking * self.stats_slider),
            'handball': min(20, self.ability_stats.handball * self.stats_slider),
            'marking': min(20, self.ability_stats.marking * self.stats_slider),
            'tackling': min(20, self.ability_stats.tackling * self.stats_slider),
            'goal_kicking': min(20, self.ability_stats.goal_kicking * self.stats_slider),
            'versatility': min(20, self.ability_stats.versatility * self.stats_slider),
            'consistency': min(20, self.ability_stats.consistency * self.stats_slider)
        }

        # Apply fatigue factor to all stats except stamina, versatility, and consistency
        # These stats shouldn't be affected by fatigue
        return {
            'speed': base_stats['speed'] * fatigue_factor,
            'agility': base_stats['agility'] * fatigue_factor,
            'strength': base_stats['strength'] * fatigue_factor,
            'stamina': base_stats['stamina'],  # Stamina not affected by fatigue
            'mental': base_stats['mental'] * fatigue_factor,
            'tactical': base_stats['tactical'] * fatigue_factor,
            'kicking': base_stats['kicking'] * fatigue_factor,
            'handball': base_stats['handball'] * fatigue_factor,
            'marking': base_stats['marking'] * fatigue_factor,
            'tackling': base_stats['tackling'] * fatigue_factor,
            'goal_kicking': base_stats['goal_kicking'] * fatigue_factor,
            'versatility': base_stats['versatility'],  # Versatility not affected by fatigue
            'consistency': base_stats['consistency']   # Consistency not affected by fatigue
        }

    def get_influence_radius(self) -> float:
        """Calculate player's area of influence."""
        base_radius = 5.0
        speed_bonus = self.physical_stats.speed * 0.1
        agility_bonus = self.physical_stats.agility * 0.05
        fatigue_penalty = self.fatigue * 0.02
        return max(2.0, base_radius + speed_bonus + agility_bonus - fatigue_penalty)

    def return_to_natural_position(self) -> None:
        """Return player to their natural position."""
        self.current_position = self.position

    """
    def determine_position_based_on_tactics(
        self,
        ball_position: Tuple[float, float],
        play_state: str,
        tactics: Dict[str, Any]
    ) -> List[str]:
        #Determine possible positions based on tactics and play state.
        #TODO: move this to the MovementEngine class.
        possible_positions = []

        if play_state == 'attacking':
            if tactics['offense_strategy'] == 'direct':
                # More central positions for direct play
                if self.position in ['FF', 'CHF']:
                    possible_positions = ['FF', 'CHF']
                elif self.position in ['LHF', 'RHF']:
                    possible_positions = ['CHF', 'LHF', 'RHF']
                elif self.position in ['Centre', 'Rover', 'RuckRover']:
                    possible_positions = ['Centre', 'CHF']
            elif tactics['offense_strategy'] == 'stay_wide':
                # Wider positions for possession play
                if self.position in ['FF', 'CHF']:
                    possible_positions = ['CHF', 'LHF', 'RHF']
                elif self.position in ['LHF', 'RHF']:
                    possible_positions = ['LHF', 'RHF', 'LWing', 'RWing']
                elif self.position in ['Centre', 'Rover', 'RuckRover']:
                    possible_positions = ['LWing', 'RWing', 'Centre']
        elif play_state == 'defensive':
            if tactics['defense_strategy'] == 'man_mark':
                # Follow opponents more closely
                if self.position in ['FB', 'CHB']:
                    possible_positions = ['FB', 'CHB']
                elif self.position in ['LHB', 'RHB']:
                    possible_positions = ['LHB', 'RHB', 'CHB']
                elif self.position in ['Centre', 'Rover', 'RuckRover']:
                    possible_positions = ['Centre', 'CHB']
            elif tactics['defense_strategy'] == 'zone_mark':
                # Stay in zones
                if self.position in ['FB', 'CHB']:
                    possible_positions = ['FB', 'CHB', 'LHB', 'RHB']
                elif self.position in ['LHB', 'RHB']:
                    possible_positions = ['LHB', 'RHB']
                elif self.position in ['Centre', 'Rover', 'RuckRover']:
                    possible_positions = ['Centre', 'LWing', 'RWing']

        # If no specific positions found, stay in current position
        if not possible_positions:
            possible_positions = [self.position]

        return possible_positions
    """
    """
    def choose_position_based_on_stats(
        self,
        ball_position: Tuple[float, float],
        possible_positions: List[str]
    ) -> str:
        #Choose best position based on player stats.
        #TODO: move this to the MovementEngine class.
        best_position = None
        best_score = float('-inf')

        for position in possible_positions:
            # Calculate distance to ball
            target_coords = self.get_coordinates(position)
            distance = self.calculate_distance(ball_position, target_coords)

            # Calculate anticipation score
            anticipation = (self.ability_stats.mental + self.ability_stats.tactical) / 2

            # Calculate ability to contest
            contest_ability = 1 / (1 + distance) * anticipation

            # Apply fatigue penalty
            fatigue_penalty = max(0.5, 1 - (self.fatigue * 0.05))
            final_score = contest_ability * fatigue_penalty

            if final_score > best_score:
                best_score = final_score
                best_position = position

        return best_position or random.choice(possible_positions)
    """

    """
    def get_coordinates(self, position: str) -> Tuple[float, float]:
        #Get coordinates for a named position.
        # Position coordinates from original AI
        position_coords = {
            'FF': (0.9, 0.5),
            'CHF': (0.75, 0.5),
            'LHF': (0.7, 0.25),
            'RHF': (0.7, 0.75),
            'Centre': (0.5, 0.5),
            'Rover': (0.5, 0.4),
            'RuckRover': (0.5, 0.6),
            'LWing': (0.5, 0.2),
            'RWing': (0.5, 0.8),
            'FB': (0.1, 0.5),
            'CHB': (0.25, 0.5),
            'LHB': (0.3, 0.25),
            'RHB': (0.3, 0.75)
        }

        if position not in position_coords:
            return (0.5, 0.5)  # Default to center

        return position_coords[position]
    """

    """
    def calculate_distance(
        self,
        target_pos: Tuple[float, float],
        from_pos: Optional[Tuple[float, float]] = None
    ) -> float:
        #Calculate Manhattan distance between positions.
        if from_pos is None:
            if not self.current_position:
                return float('inf')
            from_pos = self.current_position

        # Handle both coordinate tuples and named positions
        if isinstance(target_pos, str):
            target_pos = self.get_coordinates(target_pos)

        return abs(target_pos[0] - from_pos[0]) + abs(target_pos[1] - from_pos[1])
    """



# TODO: Implement these classes with proper functionality later
class WeatherSystem:
    """
    Weather system that affects gameplay.
    Potential implementation:
    - Different weather conditions (rain, wind, etc.)
    - Impact on player performance
    - Dynamic weather changes
    - Condition-specific commentary
    - Weather-based modifiers for actions
    """
    pass

class MovementEngine:
    """
    Advanced AFL player movement engine with realistic physics and tactical awareness.
    Simulates authentic AFL player movement patterns, positioning, and decision-making.
    """
    def __init__(self, ground: Ground):
        self.ground = ground
        self.movement_states = {}  # Track player movement states
        self.player_paths = {}     # Track player movement paths
        self.movement_history = {} # Track historical movements

        # AFL-specific movement constants
        self.AFL_MAX_SPRINT_SPEED = 9.0  # m/s (top AFL players reach ~9 m/s)
        self.AFL_MAX_ACCELERATION = 3.0  # m/s² (elite acceleration)
        self.AFL_MAX_DECELERATION = 4.0  # m/s² (faster to stop than accelerate)
        self.AFL_AGILITY_TURN_RATE = 45.0  # degrees/s (turning speed)

        # Position-specific movement tendencies
        self.position_movement_profiles = {
            # Forwards - explosive, good at leading
            'FF': {'acceleration_bonus': 0.2, 'leading_tendency': 0.8, 'defensive_discipline': 0.3},
            'CHF': {'acceleration_bonus': 0.15, 'leading_tendency': 0.7, 'defensive_discipline': 0.4},
            'LF': {'acceleration_bonus': 0.2, 'leading_tendency': 0.7, 'defensive_discipline': 0.3},
            'RF': {'acceleration_bonus': 0.2, 'leading_tendency': 0.7, 'defensive_discipline': 0.3},
            'LHF': {'acceleration_bonus': 0.1, 'leading_tendency': 0.6, 'defensive_discipline': 0.5},
            'RHF': {'acceleration_bonus': 0.1, 'leading_tendency': 0.6, 'defensive_discipline': 0.5},

            # Midfielders - endurance, good at finding space
            'Centre': {'acceleration_bonus': 0.1, 'leading_tendency': 0.5, 'defensive_discipline': 0.6},
            'Rover': {'acceleration_bonus': 0.15, 'leading_tendency': 0.5, 'defensive_discipline': 0.6},
            'RuckRover': {'acceleration_bonus': 0.1, 'leading_tendency': 0.5, 'defensive_discipline': 0.6},
            'LWing': {'acceleration_bonus': 0.1, 'leading_tendency': 0.5, 'defensive_discipline': 0.5},
            'RWing': {'acceleration_bonus': 0.1, 'leading_tendency': 0.5, 'defensive_discipline': 0.5},

            # Defenders - disciplined, good at positioning
            'FB': {'acceleration_bonus': 0.05, 'leading_tendency': 0.3, 'defensive_discipline': 0.8},
            'CHB': {'acceleration_bonus': 0.05, 'leading_tendency': 0.4, 'defensive_discipline': 0.7},
            'LB': {'acceleration_bonus': 0.1, 'leading_tendency': 0.3, 'defensive_discipline': 0.8},
            'RB': {'acceleration_bonus': 0.1, 'leading_tendency': 0.3, 'defensive_discipline': 0.8},
            'LHB': {'acceleration_bonus': 0.1, 'leading_tendency': 0.4, 'defensive_discipline': 0.7},
            'RHB': {'acceleration_bonus': 0.1, 'leading_tendency': 0.4, 'defensive_discipline': 0.7},

            # Ruck - unique movement profile
            'Ruck': {'acceleration_bonus': 0.0, 'leading_tendency': 0.4, 'defensive_discipline': 0.6}
        }

        # Tactical movement patterns
        self.tactical_patterns = {
            'leading': self._calculate_leading_pattern,
            'defensive_cover': self._calculate_defensive_cover_pattern,
            'space_creation': self._calculate_space_creation_pattern,
            'corridor_run': self._calculate_corridor_run_pattern,
            'wing_run': self._calculate_wing_run_pattern,
            'marking_contest': self._calculate_marking_contest_pattern
        }

    def _initialize_player_movement_state(self, player: Player) -> None:
        """Initialize movement state for a player with realistic physics properties."""
        if player.id not in self.movement_states:
            # Calculate player's base physical capabilities
            stats = player.get_effective_stats() if hasattr(player, 'get_effective_stats') else {
                'speed': getattr(player.physical_stats, 'speed', 10),
                'agility': getattr(player.physical_stats, 'agility', 10),
                'stamina': getattr(player.physical_stats, 'stamina', 10)
            }

            # Get position-specific movement profile
            position_profile = self.position_movement_profiles.get(
                player.position,
                {'acceleration_bonus': 0.1, 'leading_tendency': 0.5, 'defensive_discipline': 0.6}
            )

            # Calculate max speed based on player's speed attribute (0-20 scale)
            # Top speed ranges from 6-9 m/s based on player's speed attribute
            speed_factor = stats.get('speed', 10) / 20.0
            max_speed = 6.0 + (speed_factor * 3.0)

            # Calculate acceleration based on player's agility and position profile
            agility_factor = stats.get('agility', 10) / 20.0
            acceleration = (1.5 + (agility_factor * 1.5)) * (1 + position_profile['acceleration_bonus'])

            # Calculate deceleration (usually faster than acceleration)
            deceleration = acceleration * 1.3

            # Calculate turning rate based on agility
            turning_rate = self.AFL_AGILITY_TURN_RATE * (0.7 + (agility_factor * 0.6))

            self.movement_states[player.id] = {
                'momentum': (0, 0),  # x, y momentum vector
                'speed': 0,          # current speed
                'max_speed': max_speed,  # maximum speed in m/s
                'acceleration': acceleration,  # acceleration in m/s²
                'deceleration': deceleration,  # deceleration in m/s²
                'turning_rate': turning_rate,  # turning rate in degrees/s
                'path': [],          # current movement path
                'path_index': 0,     # current position in path
                'target_position': player.current_position,  # where player is heading
                'is_moving': False,   # whether player is currently moving
                'movement_style': position_profile,  # position-specific movement tendencies
                'fatigue_factor': 1.0,  # fatigue factor (1.0 = fresh, 0.0 = exhausted)
                'recovery_rate': stats.get('stamina', 10) / 20.0,  # stamina-based recovery rate
                'last_direction_change': 0.0,  # time since last significant direction change
                'current_tactical_pattern': None  # current tactical movement pattern
            }

    def _get_player_movement_state(self, player: Player) -> Dict[str, Any]:
        """Get movement state for a player, initializing if needed."""
        if player.id not in self.movement_states:
            self._initialize_player_movement_state(player)
        return self.movement_states[player.id]

    def update_player_position(self, player: Player, game_state: 'GameState',
                            include_ball_carrier: bool = False, ball_carrier_intended_position: Tuple[float, float] = None) -> Tuple[int, int]:
        """
        Update player position with improved tactical awareness and realistic movement patterns.

        Args:
            player: The player to update position for
            game_state: Current game state with all relevant info
            include_ball_carrier: Whether to include ball carrier in updates
            ball_carrier_intended_position: Where the ball carrier intends to move

        Returns:
            Tuple[int, int]: New position coordinates
        """
        # Skip ball carrier unless explicitly included
        if game_state.ball_carrier == player and not include_ball_carrier:
            return player.current_position

        # Handle special game phases
        if game_state.phase == "center_bounce":
            return self._get_center_bounce_position(player, player.team_side)
        elif game_state.phase == "ball_up":
            return self._get_ball_up_position(player, game_state)
        elif game_state.phase == "boundary_throw_in":
            return self._get_boundary_throw_in_position(player, game_state)
        elif game_state.phase == "goal_kick":
            return self._get_goal_kick_position(player, game_state)

        # Get team tactics
        team_tactics = game_state.home_team_tactics.tactics if player.team_side == 'home' else game_state.away_team_tactics.tactics

        # Get the play state (e.g., "attacking_forward", "defending_back")
        play_state = self._get_play_state(player, game_state, ball_carrier_intended_position)

        # Calculate field zones based on current tactical situation
        attacking_direction = self.ground.attacking_direction(player.team_side)
        field_zones = self._calculate_field_zones(play_state, game_state, player.team_side,
                                                attacking_direction, ball_carrier_intended_position)

        # Account for player's role and individual playstyle
        player_role = player.position
        player_stats = player.get_effective_stats()


        # Determine optimal position considering:
        # 1. Team structure and tactics
        # 2. Current game situation
        # 3. Player's role and individual capabilities
        # 4. Congestion and opponent positions
        current_pos = player.current_position
        optimal_pos = self.get_optimal_position(
            current_pos,
            field_zones,
            player,
            play_state,
            game_state,
            avoid_congestion=True if player_stats.get('mental') > 10 else False
        )

        # Track opponents nearby for defensive awareness
        nearest_opponent = self._find_nearest_opponent(player, game_state)
        needs_to_mark = self._should_mark_opponent(player, nearest_opponent, game_state, team_tactics)

        # Consider defensive marking adjustments if needed
        if needs_to_mark and nearest_opponent:
            marking_pos = self._calculate_marking_position(player, nearest_opponent, optimal_pos, game_state)
            # Blend the marking position with optimal position based on defensive discipline
            defensive_discipline = player_stats.get('tactical') / 20
            optimal_pos = self._blend_positions(optimal_pos, marking_pos, defensive_discipline)

        # Account for player momentum and physical characteristics
        path = self._calculate_realistic_movement_path(
            current_pos,
            optimal_pos,
            game_state,
            player=player,
            consider_momentum=True
        )

        # Update player's movement state with realistic acceleration/deceleration
        self._update_movement_state_with_physics(player, path, game_state)

        # Get next position considering stamina, speed, and acceleration
        next_pos = self._get_next_position_with_physics(player, game_state.quarter_time)

        # Ensure position is legal
        if not self.ground.is_position_legal(next_pos):
            # Find nearest legal position
            next_pos = self._find_nearest_legal_position(next_pos, game_state)

        # Update player fatigue based on movement intensity
        movement_intensity = self._calculate_movement_intensity(current_pos, next_pos, player.movement_state)
        player.update_fatigue(movement_intensity * game_state.quarter_time)

        # Add ball position to updates
        #next_pos.append({
        #    'player_name': 'ball',
        #    'team': None,
        #    'position': {
        #        'x': game_state.ball_position.position[0],
        #        'y': game_state.ball_position.position[1],
        #    }
        #})


        return next_pos

    def _get_play_state(self, player: Player, game_state: 'GameState', ball_carrier_intended_position: Tuple[float, float] = None):
        """Determine the current play state based on ball position and possession."""
        #deep_attacking_positions = ["FF", "LF", "RF"]
        #half_forward_attacking_positions = ["CHF", "LHF", "RHF"]
        #deep_defending_positions = ["FB", "LB", "RB"]
        #half_back_defending_positions = ["CHB", "LHB", "RHB"]
        #midfield_positions = ["Centre", "LWing", "RWing"]


        if ball_carrier_intended_position:
            ball_position = self.ground.get_position_from_coordinates(ball_carrier_intended_position)
            #self.ground.visualize_ground_with_position(ball_carrier_intended_position)
        else:
            ball_position = self.ground.get_position_from_coordinates(game_state.ball_position)
            #self.ground.visualize_ground_with_position(game_state.ball_position)
        print(f"Ball position: {ball_position}")
        print(f"Ball position Current Coords: {game_state.ball_position}")


        if game_state.ball_carrier:
            player_position = self.ground.get_position_from_coordinates(game_state.ball_carrier.current_position)
            print(f"Player {player.name} position: {player_position}")
            print(f"Player {player.name} position Current Coords: {game_state.ball_carrier.current_position}")

        center_x, center_y = self.ground.get_center()
        print(f"Center Coords: {center_x}, {center_y}")
        #get_position_from_coordinates

        field_position = self.ground.get_zone_for_position(game_state.ball_position, player.team_side)
        print(f"Field zone from {player.team_side} perspective: {field_position}")
        # For team1, attacking is in forward positions
        # For team2, attacking is in defensive positions (since they attack the opposite way)
        if game_state.ball_carrier:
            #print(f"Ball carrier: {game_state.ball_carrier.name} and team side: {game_state.ball_carrier.team_side} and position: {game_state.ball_carrier.current_position}")
            if game_state.ball_carrier.team_side == 'home' and player.team_side == 'home':
                if  field_position == 'deep_forward':
                    return "deep_attacking"
                elif field_position == 'forward_pocket_left':
                    return "deep_attacking"
                elif field_position == 'forward_pocket_right':
                    return "deep_attacking"
                elif field_position == 'high_forward':
                    return "half_forward_attacking"
                elif field_position == 'forward_left_flank':
                    return "half_forward_attacking"
                elif field_position == 'forward_right_flank':
                    return "half_forward_attacking"
                elif field_position == 'deep_defense':
                    return "midfield_attacking"
                elif field_position == 'defensive_left_flank':
                    return "midfield_attacking"
                elif field_position == 'defensive_right_flank':
                    return "midfield_attacking"
                elif field_position == 'high_defense':
                    return "half_back_attacking"
                elif field_position == 'forward_left_flank':
                    return "half_back_attacking"
                elif field_position == 'forward_right_flank':
                    return "half_back_attacking"
                elif field_position in 'center_corridor':
                    return "midfield_attacking"
                elif field_position in 'wing_left':
                    return "left_wing_attacking"
                elif field_position in 'wing_right':
                    return "right_wing_attacking"
                else:
                    print("Hit no ball posittion and should not occur exiting.")
                    sys.exit()
            elif game_state.ball_carrier.team_side == 'away' and player.team_side == 'away':
                if  field_position == 'deep_forward':
                    return "deep_attacking"
                elif field_position == 'forward_pocket_left':
                    return "deep_attacking"
                elif field_position == 'forward_pocket_right':
                    return "deep_attacking"
                elif field_position == 'high_forward':
                    return "half_forward_attacking"
                elif field_position == 'forward_left_flank':
                    return "half_forward_attacking"
                elif field_position == 'forward_right_flank':
                    return "half_forward_attacking"
                elif field_position == 'deep_defense':
                    return "midfield_attacking"
                elif field_position == 'defensive_left_flank':
                    return "midfield_attacking"
                elif field_position == 'defensive_right_flank':
                    return "midfield_attacking"
                elif field_position == 'high_defense':
                    return "half_back_attacking"
                elif field_position == 'forward_left_flank':
                    return "half_back_attacking"
                elif field_position == 'forward_right_flank':
                    return "half_back_attacking"
                elif field_position in 'center_corridor':
                    return "midfield_attacking"
                elif field_position in 'wing_left':
                    return "left_wing_attacking"
                elif field_position in 'wing_right':
                    return "right_wing_attacking"
                else:
                    print("Hit no ball posittion and should not occur exiting.")
                    sys.exit()
            #Must be defending
            else:
                if  field_position == 'deep_forward':
                    return "deep_forward_defending"
                elif field_position == 'forward_pocket_left':
                    return "deep_forward_defending"
                elif field_position == 'forward_pocket_right':
                    return "deep_forward_defending"
                elif field_position == 'high_forward':
                    return "half_forward_defending"
                elif field_position == 'forward_left_flank':
                    return "half_forward_defending"
                elif field_position == 'forward_right_flank':
                    return "half_forward_defending"
                elif field_position == 'deep_defense':
                    return "midfield_defending"
                elif field_position == 'defensive_left_flank':
                    return "midfield_defending"
                elif field_position == 'defensive_right_flank':
                    return "midfield_defending"
                elif field_position == 'high_defense':
                    return "half_back_defending"
                elif field_position == 'forward_left_flank':
                    return "half_back_defending"
                elif field_position == 'forward_right_flank':
                    return "half_back_defending"
                elif field_position in 'center_corridor':
                    return "midfield_defending"
                elif field_position in 'wing_left':
                    return "left_wing_defending"
                elif field_position in 'wing_right':
                    return "right_wing_defending"
                else:
                    print("Hit no ball defence positions and should not occur exiting.")
                    sys.exit()
        else:
            return "loose_ball"

    def _calculate_field_zones(self, situation, game_state: 'GameState', team_side: str,
                            attacking_direction: int, ball_carrier_intended_position: Tuple[float, float] = None):
        """Calculate sophisticated tactical zones based on game situation and team tactics."""
        # Get ball position
        if ball_carrier_intended_position:
            ball_x, ball_y = ball_carrier_intended_position
        else:
            ball_x, ball_y = game_state.ball_position

        grid_width = self.ground.grid_width
        grid_length = self.ground.grid_length
        center_x, center_y = self.ground.get_center()

        # If away team, mirror the ball position first
        if team_side == 'away':
            center_x = grid_length // 2
            ball_x = 2 * center_x - ball_x  # Mirror the ball position

        # Get team tactics
        team_tactics = game_state.home_team_tactics.tactics if team_side == 'home' else game_state.away_team_tactics.tactics
        offense_strategy = team_tactics['offense_strategy']
        defense_strategy = team_tactics['defense_strategy']
        push_factor = int(team_tactics['push_factor'])
        mentality = team_tactics['mentality']

        # Calculate tactical multipliers
        tactics_multiplier = 1.0 + (push_factor - 1) * 0.2
        if mentality == 'attacking':
            tactics_multiplier += 0.2
        elif mentality == 'defensive':
            tactics_multiplier -= 0.2

        # Define zone sizes based on team tactics and game situation
        contest_radius = 5
        support_radius = 10
        mobile_radius = 15

        # Adjust zone sizes based on tactics
        if offense_strategy == 'possession':
            support_radius = 8
            mobile_radius = 13
        elif offense_strategy == 'counter_attack':
            support_radius = 12
            mobile_radius = 18
        elif offense_strategy == 'long_bomb':
            support_radius = 13
            mobile_radius = 20

        # Calculate playing corridor vector based on ball position
        goal_x = grid_length if attacking_direction > 0 else 0
        corridor_vector = (goal_x - ball_x, center_y - ball_y)

        # Normalize corridor vector
        vector_length = math.sqrt(corridor_vector[0]**2 + corridor_vector[1]**2)
        if vector_length > 0:
            corridor_direction = (corridor_vector[0]/vector_length, corridor_vector[1]/vector_length)
        else:
            corridor_direction = (attacking_direction, 0)

        # Initialize zones as lists of coordinates
        contest_zone = []
        support_zone = []
        mobile_zone = []

        # CRITICAL FIX: Adjust angle calculations based on attacking direction
        # Convert attacking direction to base angle (0 for right-to-left, 180 for left-to-right)
        base_angle = 180 if attacking_direction > 0 else 0


        for angle in range(0, 360, 10):  # Every 10 degrees for fine control
            # Adjust angle based on attacking direction
            adjusted_angle = (angle + base_angle) % 360
            angle_rad = math.radians(adjusted_angle)

            # Apply tactical biases through probabilities
            tactical_bias = 1.0

            # Calculate angle relative to attacking direction
            relative_angle = abs((angle - base_angle) % 360)

            # Forward bias based on situation and attacking direction
            if "attacking" in situation:
                if relative_angle < 90 or relative_angle > 270:  # Forward positions relative to attack
                    tactical_bias *= (1.2 * tactics_multiplier)
                else:  # Backward positions
                    tactical_bias *= (0.8 * tactics_multiplier)
            elif "defending" in situation:
                if 90 < relative_angle < 270:  # Backward positions relative to attack
                    tactical_bias *= (1.2 * tactics_multiplier)
                else:  # Forward positions
                    tactical_bias *= (0.8 * tactics_multiplier)

            # Side bias for wing positions (adjusted for attacking direction)
            if "left" in situation:
                left_side = (angle + 90) % 360 if attacking_direction > 0 else (angle - 90) % 360
                left_bias = 1.2 if (90 < left_side < 270) else 0.8
                tactical_bias *= left_bias
            elif "right" in situation:
                right_side = (angle - 90) % 360 if attacking_direction > 0 else (angle + 90) % 360
                right_bias = 1.2 if (90 < right_side < 270) else 0.8
                tactical_bias *= right_bias

            # Generate positions for all three zones
            for dist in range(1, mobile_radius + 1):
                dx = int(dist * math.cos(angle_rad))
                dy = int(dist * math.sin(angle_rad))
                x = int(ball_x + dx)
                y = int(ball_y + dy)

                if 0 <= x < grid_length and 0 <= y < grid_width and self.ground.is_in_bounds((x, y)):
                    if random.random() < tactical_bias:
                        if dist <= contest_radius:
                            contest_zone.append((x, y))
                        elif dist <= support_radius:
                            support_zone.append((x, y))
                        else:
                            mobile_zone.append((x, y))

        # Ensure all zones have sufficient positions with balanced fallbacks
        min_positions_per_zone = 5

        if len(contest_zone) < min_positions_per_zone:
            # Add balanced fallback positions in all directions
            for angle in range(0, 360, 45):  # 8 directions
                angle_rad = math.radians(angle)
                dx = int(3 * math.cos(angle_rad))
                dy = int(3 * math.sin(angle_rad))
                x = int(ball_x + dx)
                y = int(ball_y + dy)
                if 0 <= x < grid_length and 0 <= y < grid_width and self.ground.is_in_bounds((x, y)):
                    contest_zone.append((x, y))

        if len(support_zone) < min_positions_per_zone:
            for angle in range(0, 360, 45):
                angle_rad = math.radians(angle)
                dx = int(7 * math.cos(angle_rad))
                dy = int(7 * math.sin(angle_rad))
                x = int(ball_x + dx)
                y = int(ball_y + dy)
                if 0 <= x < grid_length and 0 <= y < grid_width and self.ground.is_in_bounds((x, y)):
                    support_zone.append((x, y))

        if len(mobile_zone) < min_positions_per_zone:
            for angle in range(0, 360, 45):
                angle_rad = math.radians(angle)
                dx = int(12 * math.cos(angle_rad))
                dy = int(12 * math.sin(angle_rad))
                x = int(ball_x + dx)
                y = int(ball_y + dy)
                if 0 <= x < grid_length and 0 <= y < grid_width and self.ground.is_in_bounds((x, y)):
                    mobile_zone.append((x, y))

        return {
            'contest_zone': contest_zone,
            'support_zone': support_zone,
            'mobile_zone': mobile_zone,
            'situation': situation,
            'ball_position': (ball_x, ball_y),
            'attacking_direction': attacking_direction,
            'corridor_direction': corridor_direction
        }

    def get_optimal_position(self, current_pos: Tuple[int, int], field_zones, player, situation, game_state: 'GameState',
                  avoid_congestion: bool = True) -> Tuple[int, int]:
        """
        Find the optimal position for a player based on tactical considerations and player attributes.

        Args:
            current_pos: The player's current position
            field_zones: Tactical zones calculated for the current situation
            player: The player object
            situation: Current tactical situation (e.g., 'attacking_forward')
            game_state: Current game state
            avoid_congestion: Whether to avoid congested areas

        Returns:
            Tuple[int, int]: The optimal position coordinates
        """

        # Get player stats and attributes
        player_stats = player.get_effective_stats()
        team_side = player.team_side

        # Get appropriate zone based on player's role and situation
        player_role = player.position
        zone_type = self._determine_appropriate_zone_type(player_role, situation, game_state)

        # Get positions from the selected zone
        if zone_type == 'contest':
            available_positions = field_zones['contest_zone']
        elif zone_type == 'support':
            available_positions = field_zones['support_zone']
        else:  # mobile
            available_positions = field_zones['mobile_zone']

        # If no positions available, use fallback
        if not available_positions:
            print("Returning current Pos")
            time.sleep(0.5)
            return current_pos

        # Rate each position
        position_scores = {}
        for pos in available_positions:

            # Base score calculation
            score = self._calculate_position_score(pos, current_pos, avoid_congestion, game_state)

            # Adjust score based on tactical considerations
            score = self._adjust_score_for_tactics(score, pos, player, situation, game_state)

            # Adjust score based on player attributes
            score = self._adjust_score_for_player_attributes(score, pos, player, situation, game_state)

            # Adjust for team structure maintenance
            score = self._adjust_score_for_team_structure(score, pos, player, situation, game_state)

            # Store score
            position_scores[pos] = score


        # Select position with highest score
        if position_scores:

            # Introduce some randomness based on player intelligence/decision making
            intelligence = player_stats.get('mental')
            decision_making = player_stats.get('tactical')
            randomness = 1.0 - ((intelligence + decision_making) / 40)  # 0.0 to 1.0

            # Sort positions by score
            sorted_positions = sorted(position_scores.items(), key=lambda x: x[1], reverse=True)

            # Select from top positions with some randomness
            top_count = max(1, int(len(sorted_positions) * randomness))
            top_positions = sorted_positions[:top_count]

            # Weighted random choice - higher scores get higher probability
            weights = [score for _, score in top_positions]
            total_weight = sum(weights)

            if total_weight > 0:
                normalized_weights = [w/total_weight for w in weights]
                chosen_pos = random.choices([pos for pos, _ in top_positions], normalized_weights)[0]
                return chosen_pos
            else:
                # Fallback if all weights are zero
                return sorted_positions[0][0]

        # If no scores, return current position
        return current_pos

    def _calculate_realistic_movement_path(self, start: Tuple[int, int], end: Tuple[int, int],
                                        game_state: 'GameState', player=None, consider_momentum=True) -> List[Tuple[int, int]]:
        """
        Calculate a realistic movement path considering player momentum and physics.

        Args:
            start: Starting position
            end: Target position
            game_state: Current game state
            player: The player object
            consider_momentum: Whether to consider player momentum

        Returns:
            List[Tuple[int, int]]: Path as a list of coordinates
        """
        # Basic path using A* or similar algorithm
        base_path = self._calculate_movement_path(start, end, game_state, player)

        if not player or not consider_momentum:
            return base_path

        # Get player's current momentum vector from movement state
        movement_state = self._get_player_movement_state(player)
        momentum_x, momentum_y = movement_state['momentum']

        # Get player's physical characteristics
        agility = player.get_effective_stats().get('agility') / 20
        acceleration = player.get_effective_stats().get('speed') / 20

        # Adjust path based on momentum and physical characteristics
        adjusted_path = []

        # Current velocity magnitude
        velocity_magnitude = math.sqrt(momentum_x**2 + momentum_y**2)

        # Calculate turn radius based on velocity and agility
        # Higher velocity and lower agility = wider turns
        min_turn_radius = 1.0
        turn_radius = min_turn_radius + (velocity_magnitude * (1.0 - agility) * 0.5)

        # Previous direction
        prev_direction = None

        for i, point in enumerate(base_path):
            if i == 0:
                adjusted_path.append(point)
                continue

            # Calculate direction to next point
            prev_point = adjusted_path[-1]
            dx = point[0] - prev_point[0]
            dy = point[1] - prev_point[1]

            # Normalize direction
            direction_magnitude = math.sqrt(dx**2 + dy**2)
            if direction_magnitude > 0:
                direction_x = dx / direction_magnitude
                direction_y = dy / direction_magnitude
            else:
                direction_x, direction_y = 0, 0

            current_direction = (direction_x, direction_y)

            # If first segment, initialize previous direction
            if prev_direction is None:
                prev_direction = current_direction
                adjusted_path.append(point)
                continue

            # Calculate angle change
            dot_product = prev_direction[0] * current_direction[0] + prev_direction[1] * current_direction[1]
            angle_change = math.acos(max(-1.0, min(1.0, dot_product)))

            # If sharp turn, add intermediate points to smooth the turn
            if angle_change > math.radians(30):
                # Number of intermediate points based on angle change and turn radius
                num_intermediates = int(angle_change * turn_radius * 2)

                for j in range(1, num_intermediates + 1):
                    # Interpolate between previous and current direction
                    t = j / (num_intermediates + 1)
                    interp_x = prev_point[0] + (point[0] - prev_point[0]) * t
                    interp_y = prev_point[1] + (point[1] - prev_point[1]) * t

                    # Add some curvature based on turn radius
                    curve_factor = math.sin(t * math.pi) * turn_radius
                    normal_x = -direction_y
                    normal_y = direction_x

                    interp_x += normal_x * curve_factor
                    interp_y += normal_y * curve_factor

                    adjusted_path.append((int(interp_x), int(interp_y)))

            adjusted_path.append(point)
            prev_direction = current_direction

        return adjusted_path

    def _update_movement_state_with_physics(self, player: Player, path: List[Tuple[int, int]], game_state: 'GameState') -> None:
        """
        Update player's movement state with realistic physics including acceleration and momentum.

        Args:
            player: The player object
            path: Movement path as list of coordinates
            game_state: Current game state
        """
        # Get player's movement state
        movement_state = self._get_player_movement_state(player)

        if not path or len(path) < 2:
            # No movement or invalid path
            movement_state['path'] = []
            movement_state['target_position'] = player.current_position
            movement_state['path_index'] = 0
            movement_state['speed'] = 0
            movement_state['momentum'] = (0, 0)
            movement_state['is_moving'] = False
            return

        # Update movement state with new path
        movement_state['path'] = path
        movement_state['target_position'] = path[-1]  # Last point in path is target
        movement_state['path_index'] = 1  # Start at second point (first is current position)
        movement_state['is_moving'] = True

        # Calculate initial direction vector
        current_pos = player.current_position
        next_pos = path[1] if len(path) > 1 else path[0]

        dx = next_pos[0] - current_pos[0]
        dy = next_pos[1] - current_pos[1]
        distance = math.sqrt(dx*dx + dy*dy)

        # Normalize direction vector
        if distance > 0:
            dx /= distance
            dy /= distance

        # Calculate initial momentum based on current speed
        current_speed = movement_state.get('speed', 0)

        # If changing direction significantly, reduce speed based on agility
        if current_speed > 0:
            old_momentum = movement_state.get('momentum', (0, 0))
            if old_momentum != (0, 0):
                old_dx, old_dy = old_momentum
                old_magnitude = math.sqrt(old_dx*old_dx + old_dy*old_dy)
                if old_magnitude > 0:
                    old_dx /= old_magnitude
                    old_dy /= old_magnitude

                    # Calculate dot product to determine direction change
                    dot_product = old_dx * dx + old_dy * dy
                    # Convert to angle (dot product = cos(angle))
                    angle_change = math.acos(max(-1.0, min(1.0, dot_product)))
                    angle_degrees = math.degrees(angle_change)

                    # Reduce speed based on angle change and agility
                    if angle_degrees > 30:  # Only reduce for significant changes
                        agility_factor = movement_state.get('turning_rate', 30) / 45.0  # Normalize to 0-1 range
                        speed_reduction = (angle_degrees / 180.0) * (1.0 - agility_factor)
                        current_speed *= (1.0 - speed_reduction)

        # Update momentum
        momentum_x = dx * current_speed
        momentum_y = dy * current_speed
        movement_state['momentum'] = (momentum_x, momentum_y)

        # Get player stats
        stats = player.get_effective_stats()

        # Calculate max speed and acceleration based on player stats and fatigue
        stamina_factor = self._calculate_stamina_factor(player)
        max_speed = self._calculate_player_speed(player) * stamina_factor
        acceleration = max_speed * (stats.get('speed') / 20)
        deceleration = acceleration * 1.2  # Usually easier to slow down than speed up

        # Current momentum
        current_momentum = self._get_player_movement_state(player)
        momentum_x, momentum_y = current_momentum['momentum']

        # Target position is the end of the path
        target_position = path[-1]

        # Direction to next point in path
        next_point = path[1] if len(path) > 1 else target_position
        direction_x = next_point[0] - player.current_position[0]
        direction_y = next_point[1] - player.current_position[1]

        # Normalize direction
        direction_magnitude = math.sqrt(direction_x**2 + direction_y**2)
        if direction_magnitude > 0:
            direction_x /= direction_magnitude
            direction_y /= direction_magnitude

        # Calculate new momentum with realistic acceleration
        momentum_x, momentum_y = current_momentum['momentum']

        # Dot product to see how aligned current momentum is with desired direction
        alignment = momentum_x * direction_x + momentum_y * direction_y

        # If well aligned, accelerate; if poorly aligned, decelerate
        if alignment > 0.7:
            # Accelerate in desired direction
            momentum_x += direction_x * acceleration * game_state.quarter_time
            momentum_y += direction_y * acceleration * game_state.quarter_time
        else:
            # Need to change direction - first decelerate existing momentum
            momentum_magnitude = math.sqrt(momentum_x**2 + momentum_y**2)
            if momentum_magnitude > 0:
                decel_factor = max(0, momentum_magnitude - deceleration * game_state.quarter_time) / momentum_magnitude
                momentum_x *= decel_factor
                momentum_y *= decel_factor

            # Then add acceleration in new direction
            momentum_x += direction_x * acceleration * game_state.quarter_time * 0.5
            momentum_y += direction_y * acceleration * game_state.quarter_time * 0.5

        # Cap momentum at max speed
        momentum_magnitude = math.sqrt(momentum_x**2 + momentum_y**2)
        if momentum_magnitude > max_speed:
            momentum_x = momentum_x / momentum_magnitude * max_speed
            momentum_y = momentum_y / momentum_magnitude * max_speed

        # Update movement state
        player.movement_state = {
            'path': path,
            'target_position': target_position,
            'path_index': 1,  # Start at second point since we're at the first
            'speed': momentum_magnitude,
            'max_speed': max_speed,
            'acceleration': acceleration,
            'momentum': (momentum_x, momentum_y),
            'is_moving': True
        }

    def _get_next_position_with_physics(self, player: Player, delta_time: float) -> Tuple[int, int]:
        """
        Get the next position for the player considering momentum and physics.

        Args:
            player: The player object
            delta_time: Time step for movement calculation

        Returns:
            Tuple[int, int]: Next position coordinates
        """
        # Get movement state
        movement_state = self._get_player_movement_state(player)

        # If player isn't moving, stay in current position
        if not movement_state.get('is_moving', False):
            return player.current_position

        # Get current position
        current_x, current_y = player.current_position

        # Get target position
        target_pos = movement_state.get('target_position', player.current_position)
        target_x, target_y = target_pos

        # Calculate direction vector
        dx = target_x - current_x
        dy = target_y - current_y
        distance = math.sqrt(dx*dx + dy*dy)

        # If very close to target, just return target
        if distance < 0.5:
            movement_state['is_moving'] = False
            return target_pos

        # Normalize direction vector
        if distance > 0:
            dx /= distance
            dy /= distance

        # Get current speed and acceleration properties
        current_speed = movement_state.get('speed', 0)
        max_speed = movement_state.get('max_speed', 8.0) * movement_state.get('fatigue_factor', 1.0)
        acceleration = movement_state.get('acceleration', 2.0) * movement_state.get('fatigue_factor', 1.0)
        deceleration = movement_state.get('deceleration', 3.0)

        # Determine if we should accelerate or decelerate
        stopping_distance = (current_speed * current_speed) / (2 * deceleration) if deceleration > 0 else 0

        if distance > stopping_distance * 1.2:  # Add buffer for smoother deceleration
            # Accelerate
            new_speed = min(max_speed, current_speed + acceleration * delta_time)
        else:
            # Decelerate
            new_speed = max(0, current_speed - deceleration * delta_time)

        # Update speed in movement state
        movement_state['speed'] = new_speed

        # Calculate movement distance this frame
        move_distance = new_speed * delta_time

        # Ensure we don't overshoot
        if move_distance > distance:
            move_distance = distance

        # Calculate new position
        new_x = current_x + dx * move_distance
        new_y = current_y + dy * move_distance

        # Update momentum
        movement_state['momentum'] = (dx * new_speed, dy * new_speed)

        # Round to integers (grid coordinates)
        new_pos = (int(new_x), int(new_y))

        # Check if we've reached or passed the target in the path
        path = movement_state.get('path', [])
        path_index = movement_state.get('path_index', 0)

        if path and path_index < len(path):
            target = path[path_index]

            # Check if we've reached or passed this target
            distance_to_target = math.sqrt((new_x - target[0])**2 + (new_y - target[1])**2)

            if distance_to_target <= 1.0:  # Close enough to count as reached
                # Move to next target in path
                player.movement_state['path_index'] += 1

                # If we've reached the end of the path, stop moving
                if player.movement_state['path_index'] >= len(path):
                    player.movement_state['is_moving'] = False

        return new_pos

    def _find_nearest_opponent(self, player: Player, game_state: 'GameState') -> Optional[Player]:
        """Find the nearest opponent to the player."""
        team_side = player.team_side
        opponent_players = game_state.away_team_players.values() if team_side == 'home' else game_state.home_team_players.values()

        nearest_opponent = None
        min_distance = float('inf')

        for opponent in opponent_players:
            distance = self.ground._calculate_distance(player.current_position, opponent.current_position)
            if distance < min_distance:
                min_distance = distance
                nearest_opponent = opponent

        return nearest_opponent

    def _should_mark_opponent(self, player: Player, opponent: Optional[Player], game_state: 'GameState', team_tactics: Dict[str, Any]) -> bool:
        """Determine if the player should mark an opponent based on tactics and situation."""
        if not opponent:
            return False

        # Defensive players are more likely to mark opponents
        if player.position in ['RB', 'FB', 'CHB', 'LHB', 'LPB', 'RHB']:
            marking_probability = 0.7
        elif player.position in ['LWing', 'RWing', 'Centre', 'RuckRover', 'Rover']:
            marking_probability = 0.4
        else:
            marking_probability = 0.2

        # Adjust based on team tactics
        if team_tactics['defense_strategy'] == 'man_mark':
            marking_probability *= 1.5
        elif team_tactics['defense_strategy'] == 'zone_mark':
            marking_probability *= 0.7

        # Adjust based on game state
        ball_position = game_state.ball_position
        distance_to_ball = self.ground._calculate_distance(opponent.current_position, ball_position)

        # More likely to mark opponents near the ball
        if distance_to_ball < 10:
            marking_probability *= 1.3

        # Random decision weighted by probability
        return random.random() < marking_probability

    def _calculate_marking_position(self, player: Player, opponent: Player, optimal_pos: Tuple[int, int], game_state: 'GameState') -> Tuple[int, int]:
        """Calculate a position to mark an opponent, considering ball position."""
        # Get positions
        opponent_pos = opponent.current_position
        ball_pos = game_state.ball_position

        # Vector from opponent to ball
        vector_x = ball_pos[0] - opponent_pos[0]
        vector_y = ball_pos[1] - opponent_pos[1]

        # Normalize vector
        vector_length = math.sqrt(vector_x**2 + vector_y**2)
        if vector_length > 0:
            vector_x /= vector_length
            vector_y /= vector_length

        # Get player's defensive discipline from movement style
        movement_state = self._get_player_movement_state(player)
        defensive_discipline = movement_state['movement_style'].get('defensive_discipline', 0.5)

        # Adjust marking position based on defensive discipline
        # Higher discipline = position between opponent and ball
        # Lower discipline = position closer to opponent
        marking_distance = 3.0 - (defensive_discipline * 1.5)  # Range from 1.5 to 3.0

        # Position slightly toward the ball from the opponent
        marking_x = opponent_pos[0] + vector_x * marking_distance
        marking_y = opponent_pos[1] + vector_y * marking_distance

        # Add slight randomness to marking position for realism
        marking_x += random.uniform(-0.5, 0.5)
        marking_y += random.uniform(-0.5, 0.5)

        return (int(marking_x), int(marking_y))

    def _blend_positions(self, pos1: Tuple[int, int], pos2: Tuple[int, int], blend_factor: float) -> Tuple[int, int]:
        """Blend two positions together based on blend factor (0.0 to 1.0)."""
        x1, y1 = pos1
        x2, y2 = pos2

        blended_x = int(x1 * (1 - blend_factor) + x2 * blend_factor)
        blended_y = int(y1 * (1 - blend_factor) + y2 * blend_factor)

        return (blended_x, blended_y)

    def _calculate_leading_pattern(self, player: Player, game_state: 'GameState') -> List[Tuple[int, int]]:
        """Calculate a realistic AFL leading pattern for a forward player."""
        # Get current position and ball position
        current_pos = player.current_position
        ball_pos = game_state.ball_position

        # Get player's movement state and attributes
        movement_state = self._get_player_movement_state(player)
        leading_tendency = movement_state['movement_style'].get('leading_tendency', 0.5)

        # Get player stats
        stats = player.get_effective_stats() if hasattr(player, 'get_effective_stats') else {
            'speed': getattr(player.physical_stats, 'speed', 10),
            'agility': getattr(player.physical_stats, 'agility', 10),
            'stamina': getattr(player.physical_stats, 'stamina', 10),
            'tactical': getattr(player.ability_stats, 'tactical', 10)
        }

        # Calculate leading distance based on player speed and stamina
        speed_factor = stats.get('speed', 10) / 20.0
        stamina_factor = stats.get('stamina', 10) / 20.0
        tactical_factor = stats.get('tactical', 10) / 20.0

        # Base leading distance (5-15 meters)
        base_distance = 5 + (speed_factor * 10)

        # Adjust for fatigue
        fatigue_factor = movement_state.get('fatigue_factor', 1.0)
        adjusted_distance = base_distance * fatigue_factor

        # Calculate direction vector from ball to goal
        attacking_direction = self.ground.attacking_direction(player.team_side)
        goal_x = self.ground.grid_length if attacking_direction > 0 else 0
        goal_y = self.ground.grid_width // 2

        # Vector from ball to goal
        ball_to_goal_x = goal_x - ball_pos[0]
        ball_to_goal_y = goal_y - ball_pos[1]

        # Normalize vector
        ball_to_goal_dist = math.sqrt(ball_to_goal_x**2 + ball_to_goal_y**2)
        if ball_to_goal_dist > 0:
            ball_to_goal_x /= ball_to_goal_dist
            ball_to_goal_y /= ball_to_goal_dist

        # Calculate leading angle (slightly angled from direct line to goal)
        # More tactical players lead at better angles
        angle_variation = (1.0 - tactical_factor) * 30  # 0-30 degrees variation
        angle_offset = random.uniform(-angle_variation, angle_variation)

        # Convert to radians and rotate vector
        angle_rad = math.radians(angle_offset)
        rotated_x = ball_to_goal_x * math.cos(angle_rad) - ball_to_goal_y * math.sin(angle_rad)
        rotated_y = ball_to_goal_x * math.sin(angle_rad) + ball_to_goal_y * math.cos(angle_rad)

        # Calculate leading target position
        lead_target_x = current_pos[0] + rotated_x * adjusted_distance
        lead_target_y = current_pos[1] + rotated_y * adjusted_distance

        # Ensure position is in bounds
        lead_target_x = max(0, min(self.ground.grid_length - 1, lead_target_x))
        lead_target_y = max(0, min(self.ground.grid_width - 1, lead_target_y))

        # Create path with acceleration curve
        path = []
        num_steps = 5  # Number of points in the path

        # Create acceleration curve (slow start, fast middle, slow end)
        for i in range(num_steps):
            # Use sigmoid-like curve for acceleration/deceleration
            t = i / (num_steps - 1)  # 0 to 1
            # Adjust curve based on player's acceleration profile
            curve_factor = 3 + (leading_tendency * 2)  # 3-5 range
            progress = 1 / (1 + math.exp(-curve_factor * (t - 0.5)))  # Sigmoid curve

            # Calculate position along path
            x = current_pos[0] + (lead_target_x - current_pos[0]) * progress
            y = current_pos[1] + (lead_target_y - current_pos[1]) * progress

            path.append((int(x), int(y)))

        return path

    def _calculate_defensive_cover_pattern(self, player: Player, game_state: 'GameState') -> List[Tuple[int, int]]:
        """Calculate a defensive cover pattern for a defender."""
        # Get current position and ball position
        current_pos = player.current_position
        ball_pos = game_state.ball_position

        # Get player's movement state and attributes
        movement_state = self._get_player_movement_state(player)
        defensive_discipline = movement_state['movement_style'].get('defensive_discipline', 0.6)

        # Get player stats
        stats = player.get_effective_stats() if hasattr(player, 'get_effective_stats') else {
            'speed': getattr(player.physical_stats, 'speed', 10),
            'agility': getattr(player.physical_stats, 'agility', 10),
            'stamina': getattr(player.physical_stats, 'stamina', 10),
            'tactical': getattr(player.ability_stats, 'tactical', 10)
        }

        # Calculate defensive position based on ball position and goal
        attacking_direction = self.ground.attacking_direction(player.team_side)
        # Defending team's goal is in the opposite direction
        goal_x = 0 if attacking_direction > 0 else self.ground.grid_length
        goal_y = self.ground.grid_width // 2

        # Vector from goal to ball
        goal_to_ball_x = ball_pos[0] - goal_x
        goal_to_ball_y = ball_pos[1] - goal_y

        # Normalize vector
        goal_to_ball_dist = math.sqrt(goal_to_ball_x**2 + goal_to_ball_y**2)
        if goal_to_ball_dist > 0:
            goal_to_ball_x /= goal_to_ball_dist
            goal_to_ball_y /= goal_to_ball_dist

        # Calculate ideal defensive position - between ball and goal
        # Higher defensive discipline = closer to goal (zoning)
        # Lower defensive discipline = closer to ball (man-on-man)
        cover_distance = goal_to_ball_dist * (0.3 + (0.4 * (1 - defensive_discipline)))

        # Calculate cover position
        cover_x = goal_x + goal_to_ball_x * cover_distance
        cover_y = goal_y + goal_to_ball_y * cover_distance

        # Adjust based on tactical awareness - better players position slightly off the direct line
        tactical_factor = stats.get('tactical', 10) / 20.0
        if tactical_factor > 0.5:  # Only tactically aware players do this
            # Calculate perpendicular vector (for slight offset)
            perp_x = -goal_to_ball_y
            perp_y = goal_to_ball_x

            # Add slight offset based on tactical awareness
            offset_magnitude = 2 + (tactical_factor * 3)  # 2-5 range
            # Randomly choose side to offset to
            side_factor = 1 if random.random() > 0.5 else -1

            cover_x += perp_x * offset_magnitude * side_factor
            cover_y += perp_y * offset_magnitude * side_factor

        # Ensure position is in bounds
        cover_x = max(0, min(self.ground.grid_length - 1, cover_x))
        cover_y = max(0, min(self.ground.grid_width - 1, cover_y))

        # Create path with defensive movement pattern
        path = []
        num_steps = 4  # Fewer steps for defensive movement

        # Create movement curve (quick initial movement, then stabilize)
        for i in range(num_steps):
            t = i / (num_steps - 1)  # 0 to 1
            # Defensive players move quickly to position then hold
            progress = math.sqrt(t)  # Square root curve for quick initial movement

            # Calculate position along path
            x = current_pos[0] + (cover_x - current_pos[0]) * progress
            y = current_pos[1] + (cover_y - current_pos[1]) * progress

            path.append((int(x), int(y)))

        return path

    def _calculate_space_creation_pattern(self, player: Player, game_state: 'GameState') -> List[Tuple[int, int]]:
        """Calculate a space creation pattern for midfielders and forwards."""
        # Get current position and ball position
        current_pos = player.current_position
        ball_pos = game_state.ball_position

        # Get player's movement state and attributes
        movement_state = self._get_player_movement_state(player)

        # Get player stats
        stats = player.get_effective_stats() if hasattr(player, 'get_effective_stats') else {
            'speed': getattr(player.physical_stats, 'speed', 10),
            'agility': getattr(player.physical_stats, 'agility', 10),
            'stamina': getattr(player.physical_stats, 'stamina', 10),
            'tactical': getattr(player.ability_stats, 'tactical', 10)
        }

        # Calculate direction away from congestion
        # Find all players within a certain radius
        congestion_radius = 15
        nearby_players = []

        # Check home team players
        for p in game_state.home_team_players.values():
            if p.id != player.id and p.current_position:  # Skip self and players without position
                dx = p.current_position[0] - current_pos[0]
                dy = p.current_position[1] - current_pos[1]
                dist = math.sqrt(dx*dx + dy*dy)
                if dist < congestion_radius:
                    nearby_players.append(p.current_position)

        # Check away team players
        for p in game_state.away_team_players.values():
            if p.id != player.id and p.current_position:  # Skip self and players without position
                dx = p.current_position[0] - current_pos[0]
                dy = p.current_position[1] - current_pos[1]
                dist = math.sqrt(dx*dx + dy*dy)
                if dist < congestion_radius:
                    nearby_players.append(p.current_position)

        # Calculate average position of nearby players (center of congestion)
        if nearby_players:
            avg_x = sum(p[0] for p in nearby_players) / len(nearby_players)
            avg_y = sum(p[1] for p in nearby_players) / len(nearby_players)

            # Vector from congestion to player
            congestion_to_player_x = current_pos[0] - avg_x
            congestion_to_player_y = current_pos[1] - avg_y

            # Normalize vector
            congestion_dist = math.sqrt(congestion_to_player_x**2 + congestion_to_player_y**2)
            if congestion_dist > 0:
                congestion_to_player_x /= congestion_dist
                congestion_to_player_y /= congestion_dist

            # Calculate space creation distance based on player speed and tactical awareness
            speed_factor = stats.get('speed', 10) / 20.0
            tactical_factor = stats.get('tactical', 10) / 20.0

            # Base distance (5-12 meters)
            base_distance = 5 + (speed_factor * 7)

            # Adjust for tactical awareness - better players find better space
            adjusted_distance = base_distance * (0.8 + (tactical_factor * 0.4))  # 0.8-1.2 multiplier

            # Calculate space target position
            space_x = current_pos[0] + congestion_to_player_x * adjusted_distance
            space_y = current_pos[1] + congestion_to_player_y * adjusted_distance
        else:
            # If no congestion, move to a tactically advantageous position
            attacking_direction = self.ground.attacking_direction(player.team_side)
            # Move slightly forward and to the side
            forward_factor = 8 * attacking_direction  # Forward movement
            side_factor = 5 * (1 if random.random() > 0.5 else -1)  # Random side movement

            space_x = current_pos[0] + forward_factor
            space_y = current_pos[1] + side_factor

        # Ensure position is in bounds
        space_x = max(0, min(self.ground.grid_length - 1, space_x))
        space_y = max(0, min(self.ground.grid_width - 1, space_y))

        # Create path with space creation movement pattern
        path = []
        num_steps = 4

        # Create movement curve (accelerate out of congestion)
        for i in range(num_steps):
            t = i / (num_steps - 1)  # 0 to 1
            # Quick initial burst to create space
            progress = t**0.7  # Power curve for quick initial movement

            # Calculate position along path
            x = current_pos[0] + (space_x - current_pos[0]) * progress
            y = current_pos[1] + (space_y - current_pos[1]) * progress

            path.append((int(x), int(y)))

        return path

    def _calculate_corridor_run_pattern(self, player: Player, game_state: 'GameState') -> List[Tuple[int, int]]:
        """Calculate a corridor run pattern for midfielders and runners."""
        # Get current position and ball position
        current_pos = player.current_position
        ball_pos = game_state.ball_position

        # Get player's movement state and attributes
        movement_state = self._get_player_movement_state(player)

        # Get player stats
        stats = player.get_effective_stats() if hasattr(player, 'get_effective_stats') else {
            'speed': getattr(player.physical_stats, 'speed', 10),
            'agility': getattr(player.physical_stats, 'agility', 10),
            'stamina': getattr(player.physical_stats, 'stamina', 10),
            'tactical': getattr(player.ability_stats, 'tactical', 10)
        }

        # Calculate corridor run based on attacking direction
        attacking_direction = self.ground.attacking_direction(player.team_side)

        # Get center corridor boundaries
        corridor_width = self.ground.grid_width // 3  # Divide field into thirds
        corridor_center = self.ground.grid_width // 2
        corridor_left = corridor_center - (corridor_width // 2)
        corridor_right = corridor_center + (corridor_width // 2)

        # Calculate run distance based on player stamina and speed
        speed_factor = stats.get('speed', 10) / 20.0
        stamina_factor = stats.get('stamina', 10) / 20.0

        # Base run distance (10-20 meters)
        base_distance = 10 + (speed_factor * 10)

        # Adjust for stamina
        fatigue_factor = movement_state.get('fatigue_factor', 1.0)
        adjusted_distance = base_distance * fatigue_factor * (0.8 + (stamina_factor * 0.4))  # 0.8-1.2 multiplier

        # Calculate corridor target position
        # Move forward in attacking direction
        corridor_x = current_pos[0] + (attacking_direction * adjusted_distance)

        # Adjust y-position to stay in corridor
        if current_pos[1] < corridor_left:
            # If left of corridor, angle slightly inward
            corridor_y = current_pos[1] + min(corridor_left - current_pos[1], 5)
        elif current_pos[1] > corridor_right:
            # If right of corridor, angle slightly inward
            corridor_y = current_pos[1] - min(current_pos[1] - corridor_right, 5)
        else:
            # If in corridor, maintain position with slight variation
            corridor_y = current_pos[1] + random.uniform(-2, 2)

        # Ensure position is in bounds
        corridor_x = max(0, min(self.ground.grid_length - 1, corridor_x))
        corridor_y = max(0, min(self.ground.grid_width - 1, corridor_y))

        # Create path with corridor run movement pattern
        path = []
        num_steps = 6  # More steps for longer run

        # Create movement curve (steady pace for corridor run)
        for i in range(num_steps):
            t = i / (num_steps - 1)  # 0 to 1
            # Linear movement for corridor run
            progress = t

            # Calculate position along path
            x = current_pos[0] + (corridor_x - current_pos[0]) * progress
            y = current_pos[1] + (corridor_y - current_pos[1]) * progress

            path.append((int(x), int(y)))

        return path

    def _calculate_wing_run_pattern(self, player: Player, game_state: 'GameState') -> List[Tuple[int, int]]:
        """Calculate a wing run pattern for wing players."""
        # Get current position and ball position
        current_pos = player.current_position
        ball_pos = game_state.ball_position

        # Get player's movement state and attributes
        movement_state = self._get_player_movement_state(player)

        # Get player stats
        stats = player.get_effective_stats() if hasattr(player, 'get_effective_stats') else {
            'speed': getattr(player.physical_stats, 'speed', 10),
            'agility': getattr(player.physical_stats, 'agility', 10),
            'stamina': getattr(player.physical_stats, 'stamina', 10),
            'tactical': getattr(player.ability_stats, 'tactical', 10)
        }

        # Calculate wing run based on attacking direction and player position
        attacking_direction = self.ground.attacking_direction(player.team_side)

        # Determine which wing the player is on
        field_center_y = self.ground.grid_width // 2
        is_left_wing = current_pos[1] < field_center_y

        # Calculate wing boundaries
        wing_width = self.ground.grid_width // 4  # Quarter of field width

        if is_left_wing:
            wing_center = wing_width // 2  # Center of left wing
        else:
            wing_center = self.ground.grid_width - (wing_width // 2)  # Center of right wing

        # Calculate run distance based on player stamina and speed
        speed_factor = stats.get('speed', 10) / 20.0
        stamina_factor = stats.get('stamina', 10) / 20.0

        # Base run distance (12-22 meters) - wings typically run longer distances
        base_distance = 12 + (speed_factor * 10)

        # Adjust for stamina
        fatigue_factor = movement_state.get('fatigue_factor', 1.0)
        adjusted_distance = base_distance * fatigue_factor * (0.8 + (stamina_factor * 0.4))  # 0.8-1.2 multiplier

        # Calculate wing target position
        # Move forward in attacking direction
        wing_x = current_pos[0] + (attacking_direction * adjusted_distance)

        # Adjust y-position to stay on wing
        wing_drift = (wing_center - current_pos[1]) * 0.3  # Drift 30% toward wing center
        wing_y = current_pos[1] + wing_drift

        # Add slight variation for realism
        wing_y += random.uniform(-2, 2)

        # Ensure position is in bounds
        wing_x = max(0, min(self.ground.grid_length - 1, wing_x))
        wing_y = max(0, min(self.ground.grid_width - 1, wing_y))

        # Create path with wing run movement pattern
        path = []
        num_steps = 6  # More steps for longer run

        # Create movement curve (steady pace with slight acceleration)
        for i in range(num_steps):
            t = i / (num_steps - 1)  # 0 to 1
            # Slight acceleration curve for wing run
            progress = t**0.9

            # Calculate position along path
            x = current_pos[0] + (wing_x - current_pos[0]) * progress
            y = current_pos[1] + (wing_y - current_pos[1]) * progress

            path.append((int(x), int(y)))

        return path

    def _calculate_marking_contest_pattern(self, player: Player, game_state: 'GameState') -> List[Tuple[int, int]]:
        """Calculate a marking contest pattern for players contesting a mark."""
        # Get current position and ball position
        current_pos = player.current_position
        ball_pos = game_state.ball_position

        # Get player's movement state and attributes
        movement_state = self._get_player_movement_state(player)

        # Get player stats
        stats = player.get_effective_stats() if hasattr(player, 'get_effective_stats') else {
            'speed': getattr(player.physical_stats, 'speed', 10),
            'agility': getattr(player.physical_stats, 'agility', 10),
            'stamina': getattr(player.physical_stats, 'stamina', 10),
            'tactical': getattr(player.ability_stats, 'tactical', 10),
            'marking': getattr(player.ability_stats, 'marking', 10),
            'strength': getattr(player.physical_stats, 'strength', 10)
        }

        # Calculate vector to ball
        ball_vector_x = ball_pos[0] - current_pos[0]
        ball_vector_y = ball_pos[1] - current_pos[1]

        # Normalize vector
        ball_distance = math.sqrt(ball_vector_x**2 + ball_vector_y**2)
        if ball_distance > 0:
            ball_vector_x /= ball_distance
            ball_vector_y /= ball_distance

        # Calculate marking position based on player attributes
        marking_ability = stats.get('marking', 10) / 20.0
        strength = stats.get('strength', 10) / 20.0

        # Better markers position slightly in front of the ball drop
        # Stronger players push closer to the ideal position
        positioning_factor = 0.5 + (marking_ability * 0.5)  # 0.5-1.0 range
        strength_factor = 0.7 + (strength * 0.3)  # 0.7-1.0 range

        # Calculate ideal marking position (slightly in front of ball drop)
        # For simplicity, assume ball is dropping straight down
        ideal_x = ball_pos[0] - ball_vector_x * 1  # 1 meter in front of ball
        ideal_y = ball_pos[1] - ball_vector_y * 1

        # Adjust based on player attributes
        mark_x = current_pos[0] + (ideal_x - current_pos[0]) * positioning_factor * strength_factor
        mark_y = current_pos[1] + (ideal_y - current_pos[1]) * positioning_factor * strength_factor

        # Ensure position is in bounds
        mark_x = max(0, min(self.ground.grid_length - 1, mark_x))
        mark_y = max(0, min(self.ground.grid_width - 1, mark_y))

        # Create path with marking contest movement pattern
        path = []
        num_steps = 3  # Fewer steps for quick marking movement

        # Create movement curve (explosive movement to marking position)
        for i in range(num_steps):
            t = i / (num_steps - 1)  # 0 to 1
            # Explosive acceleration for marking contest
            progress = t**0.5  # Square root for explosive start

            # Calculate position along path
            x = current_pos[0] + (mark_x - current_pos[0]) * progress
            y = current_pos[1] + (mark_y - current_pos[1]) * progress

            path.append((int(x), int(y)))

        return path

    def _determine_appropriate_zone_type(self, player_role: str, situation: str, game_state: 'GameState') -> str:
        """Determine which zone type is most appropriate for the player based on role and situation."""
        # Get team tactics and player stats
        team_side = 'home' if player_role in game_state.home_team_players else 'away'
        team_tactics = game_state.home_team_tactics.tactics if team_side == 'home' else game_state.away_team_tactics.tactics

        # Get player object to access stats
        players = game_state.home_team_players if team_side == 'home' else game_state.away_team_players
        player = next((p for p in players.values() if p.position == player_role), None)

        if not player:
            print(f"Not Player, return support: {player}")
            #time.sleep(1)
            return 'support'  # fallback

        player_stats = player.get_effective_stats()

        # Get tactical context
        mentality = team_tactics.get('mentality')
        push_factor = team_tactics.get('push_factor')

        # Calculate zone probabilities based on role, situation, and player attributes
        zone_probabilities = {
            'contest': 0.0,
            'support': 0.0,
            'mobile': 0.0
        }

        if 'attacking' in situation:
            print("Attacking in situation")
        if 'defending' in situation:
            print("Defending in situation")

        # Base probabilities by role
        if player_role in ['FF', 'CHF', 'RF', 'LF', 'LHF', 'RHF']:  # Forwards
            if 'attacking' in situation:
                zone_probabilities.update({
                    'contest': 0.3,  # Forwards can contest in attack
                    'support': 0.3,
                    'mobile': 0.4
                })
            elif 'defending' in situation:
                # Forwards help in defense based on work rate and team tactics
                work_rate = player_stats.get('consistency') / 20
                if mentality == 'defensive' or int(push_factor) < 1:
                    zone_probabilities.update({
                        'contest': 0.1 * work_rate,
                        'support': 0.5 * work_rate,
                        'mobile': 0.4 * (2 - work_rate)  # Stay forward if low work rate
                    })
                else:
                    zone_probabilities.update({
                        'mobile': 0.7,  # Stay more forward
                        'support': 0.3 * work_rate
                    })

        elif player_role in ['RB', 'FB', 'CHB', 'LHB', 'LB', 'RHB']:  # Defenders
            if 'defending' in situation:
                zone_probabilities.update({
                    'contest': 0.4,
                    'support': 0.4,
                    'mobile': 0.2
                })
            elif 'attacking' in situation:
                # Defenders push up based on mentality and push factor
                if mentality == 'attacking' or int(push_factor) > 1:
                    zone_probabilities.update({
                        'mobile': 0.5,
                        'support': 0.3,
                        'contest': 0.2
                    })
                else:
                    zone_probabilities.update({
                        'support': 0.6,
                        'mobile': 0.4
                    })

        elif player_role in ['LWing', 'RWing', 'Centre', 'RuckRover', 'Rover']:  # Midfielders
            # Midfielders are most versatile
            if 'contest' in situation:
                zone_probabilities.update({
                    'contest': 0.5,
                    'support': 0.3,
                    'mobile': 0.2
                })
            else:
                zone_probabilities.update({
                    'contest': 0.3,
                    'support': 0.4,
                    'mobile': 0.3
                })

        elif player_role in ['Ruck']:  # Midfielders
            # Midfielders are most versatile
            if 'contest' in situation:
                zone_probabilities.update({
                    'contest': 0.5,
                    'support': 0.3,
                    'mobile': 0.1
                })
            else:
                zone_probabilities.update({
                    'contest': 0.3,
                    'support': 0.4,
                    'mobile': 0.1
                })

        # Adjust probabilities based on player attributes
        aggression = player_stats.get('strength') / 20
        intelligence = player_stats.get('mental') / 20

        # More aggressive players prefer contest zones
        zone_probabilities['contest'] *= (1 + (aggression - 0.5))
        # More intelligent players better at support positioning
        zone_probabilities['support'] *= (1 + (intelligence - 0.5))

        # Adjust for specific situations
        if 'deep' in situation:
            zone_probabilities['contest'] *= 1.3
        elif 'transition' in situation:
            zone_probabilities['mobile'] *= 1.3

        print(f"Player Role {player_role}")
        print(f"Situation {situation}")
        print(f"Zone Probability {zone_probabilities.values()}")
        # Select zone type based on weighted probabilities
        zones = list(zone_probabilities.keys())
        print(f"Zones {zones}")
        weights = list(zone_probabilities.values())

        return random.choices(zones, weights=weights)[0]

    def _calculate_stamina_factor(self, player: Player) -> float:
        """Calculate stamina factor affecting player speed and acceleration."""
        # Get current fatigue level (0-100 where 0 is fresh, 100 is exhausted)
        fatigue = player.fatigue

        # Get player's endurance stat
        endurance = player.get_effective_stats().get('stamina')

        # Calculate stamina factor - higher endurance reduces the impact of fatigue
        endurance_bonus = endurance / 20.0
        fatigue_impact = fatigue / 100.0

        # Formula: high endurance reduces fatigue impact
        # A player with 100 endurance at 50 fatigue will still be at ~75% speed
        # A player with 50 endurance at 50 fatigue will be at ~50% speed
        stamina_factor = 1.0 - (fatigue_impact * (1.0 - endurance_bonus))

        # Ensure minimum speed (even exhausted players can jog)
        stamina_factor = max(0.3, stamina_factor)

        return stamina_factor

    def _calculate_movement_intensity(self, start_pos: Tuple[int, int], end_pos: Tuple[int, int], movement_state: Dict[str, Any]) -> float:
        """Calculate movement intensity for fatigue updates."""
        # Calculate distance moved
        dx = end_pos[0] - start_pos[0]
        dy = end_pos[1] - start_pos[1]
        distance = math.sqrt(dx*dx + dy*dy)

        # Get player's current speed as a percentage of max speed
        current_speed = movement_state.get('speed', 0)
        max_speed = movement_state.get('max_speed', 1)
        speed_percentage = current_speed / max_speed if max_speed > 0 else 0

        # Calculate intensity based on distance and speed percentage
        # Sprinting (high speed) is more fatiguing than jogging
        base_intensity = distance * 0.01  # Base fatigue per distance unit
        speed_factor = 1.0 + (speed_percentage * 1.5)  # Higher speeds are more fatiguing

        # Check for rapid direction changes which increase fatigue
        momentum = movement_state.get('momentum', (0, 0))
        momentum_x, momentum_y = momentum

        # Calculate dot product between momentum and movement direction
        direction_x = dx / distance if distance > 0 else 0
        direction_y = dy / distance if distance > 0 else 0

        momentum_magnitude = math.sqrt(momentum_x**2 + momentum_y**2)
        if momentum_magnitude > 0:
            # Normalize momentum vector
            momentum_x /= momentum_magnitude
            momentum_y /= momentum_magnitude

            # Dot product (1 for same direction, -1 for opposite)
            alignment = momentum_x * direction_x + momentum_y * direction_y

            # Sharp turns (low alignment) increase fatigue
            direction_change_factor = 1.0 + max(0, (1.0 - alignment)) * 2.0
        else:
            # No momentum, no direction change penalty
            direction_change_factor = 1.0

        # Combine factors
        intensity = base_intensity * speed_factor * direction_change_factor

        return intensity

    def _find_nearest_legal_position(self, position: Tuple[int, int], game_state: 'GameState') -> Tuple[int, int]:
        """Find the nearest legal position to the given position."""
        x, y = position
        grid_length = self.ground.grid_length
        grid_width = self.ground.grid_width

        # Ensure position is within grid boundaries
        x = max(0, min(x, grid_length - 1))
        y = max(0, min(y, grid_width - 1))

        # If position is legal after boundary check, return it
        if self.ground.is_position_legal((x, y)):
            return (x, y)

        # Spiral search pattern for nearest legal position
        for radius in range(1, max(grid_length, grid_width)):
            # Check positions in a diamond pattern expanding outward
            for dx, dy in [
                (0, -radius),  # Top
                (radius, 0),   # Right
                (0, radius),   # Bottom
                (-radius, 0),  # Left
            ]:
                test_pos = (x + dx, y + dy)
                if (0 <= test_pos[0] < grid_length and
                    0 <= test_pos[1] < grid_width and
                    self.ground.is_position_legal(test_pos)):
                    return test_pos

            # Check diagonal positions
            for dx, dy in [
                (-radius, -radius),  # Top-left
                (radius, -radius),   # Top-right
                (radius, radius),    # Bottom-right
                (-radius, radius),   # Bottom-left
            ]:
                test_pos = (x + dx, y + dy)
                if (0 <= test_pos[0] < grid_length and
                    0 <= test_pos[1] < grid_width and
                    self.ground.is_position_legal(test_pos)):
                    return test_pos

        # Fallback to ground center if no legal position found
        return self.ground.get_center()

    def _calculate_position_score(self, pos: Tuple[int, int], current_pos: Tuple[int, int],
                                avoid_congestion: bool, game_state: 'GameState') -> float:
        """
        Calculate a comprehensive score for a potential position.

        Args:
            pos: Position to evaluate
            current_pos: Player's current position
            avoid_congestion: Whether to avoid congested areas
            game_state: Current game state

        Returns:
            float: Score for the position (higher is better)
        """
        # Base score starts at 10
        score = 10.0

        # Distance factor - penalize positions too far away
        distance = math.sqrt((pos[0] - current_pos[0])**2 + (pos[1] - current_pos[1])**2)

        # Medium distances are better than very close or very far
        # This creates more natural movement
        if distance < 2:
            # Very close positions get lower score
            distance_score = distance * 2
        elif distance < 10:
            # Medium distances get bonus (sweet spot)
            distance_score = 5.0
        else:
            # Far distances get increasingly penalized
            distance_score = max(0, 5.0 - (distance - 10) * 0.5)

        score += distance_score

        # Congestion factor - avoid crowded areas if required
        if avoid_congestion:
            congestion = self._get_point_congestion(pos, self.ground.congestion_map)
            congestion_penalty = congestion * 10.0  # Scale congestion (0-1) to penalty (0-10)
            score -= congestion_penalty

        # Field position quality - reward tactically advantageous positions
        # For example, being in space, having good angles to goal, etc.
        field_position_quality = self._assess_field_position_quality(pos, game_state)
        score += field_position_quality * 5.0

        # Line of sight to ball - reward positions with clear view of ball
        ball_pos = game_state.ball_position
        line_of_sight_score = self._calculate_line_of_sight_score(pos, ball_pos, game_state)
        score += line_of_sight_score * 3.0

        # Add randomness to create more natural, less predictable movement
        # Small random factor (±1.0)
        score += (random.random() * 2.0 - 1.0)

        return score

    def _adjust_score_for_tactics(self, score: float, pos: Tuple[int, int], player: Player, situation: str, game_state: 'GameState') -> float:
        """
        Adjust position score based on team tactics and game situation.
        """
        team_side = player.team_side
        team_tactics = game_state.home_team_tactics.tactics if team_side == 'home' else game_state.away_team_tactics.tactics

        # Get tactical preferences
        formation = team_tactics.get('formation', 'balanced')
        offense_strategy = team_tactics.get('offense_strategy')
        defense_strategy = team_tactics.get('defense_strategy')
        push_factor = team_tactics.get('push_factor')

        # Get ball position and direction
        ball_pos = game_state.ball_position
        attacking_direction = self.ground.attacking_direction(team_side)

        # Adjust score based on formation preference
        if formation == 'wide':
            # Reward wider positions
            distance_from_center = abs(pos[1] - self.ground.get_center()[1])
            score += (distance_from_center / self.ground.grid_width) * 3.0
        elif formation == 'compact':
            # Reward central positions
            distance_from_center = abs(pos[1] - self.ground.get_center()[1])
            score += (1 - (distance_from_center / self.ground.grid_width)) * 3.0

        # Adjust for offensive strategy
        if 'attacking' in situation:
            if offense_strategy == 'possession':
                # Reward positions with good passing options
                score += self._calculate_passing_options_score(pos, game_state) * 2.0
            elif offense_strategy == 'direct':
                # Reward forward positions
                forward_progress = (pos[0] - ball_pos[0]) * attacking_direction
                score += max(0, forward_progress / 10) * 2.0

        # Adjust for defensive strategy
        if 'defending' in situation:
            if defense_strategy == 'zone_mark':
                # Reward positions that maintain defensive structure
                score += self._calculate_zone_coverage_score(pos, game_state) * 2.0
            elif defense_strategy == 'man_on_man':
                # Reward positions closer to nearest opponent
                nearest_opponent = self._find_nearest_opponent(player, game_state)
                if nearest_opponent:
                    distance = self.ground._calculate_distance(pos, nearest_opponent.current_position)
                    score += max(0, 5 - distance) * 0.5

        # Apply push factor
        if int(push_factor) > 1:
            # Reward more aggressive positioning
            forward_position = (pos[0] - ball_pos[0]) * attacking_direction
            score += forward_position * (int(push_factor) - 1) * 0.5

        return score

    def _adjust_score_for_player_attributes(self, score: float, pos: Tuple[int, int], player: Player, situation: str, game_state: 'GameState') -> float:
        """
        Adjust position score based on player's individual attributes and role.
        """
        # Get player stats
        stats = player.get_effective_stats()
        print("_adjust_score_for_player_attributes")
        print(f"Player Pos 1 {player.position}")
        # Position-specific adjustments
        if player.position in ['FF', 'CHF', 'RF', 'LF', 'LHF', 'RHF']:
            print(f"Player Pos 2 {player.position}")
            # Forwards with good marking should prefer positions suitable for marks
            marking_ability = stats.get('marking') / 20
            score += self._calculate_marking_opportunity_score(pos, game_state) * marking_ability

            # Goal-scoring positions more valuable for accurate kicks
            if self.ground.is_in_range_for_shot(pos, player.team_side):
                #print(f"Goal Kicking {stats}")
                goal_kicking = stats.get('goal_kicking') / 20
                score += self._calculate_shot_quality_score(pos, game_state) * goal_kicking

        elif player.position in ['LWing', 'RWing', 'Centre', 'RuckRover', 'Rover']:
            # Midfielders with good handball skills prefer close support positions
            handball_skill = stats.get('handball') / 20
            score += self._calculate_support_position_score(pos, game_state) * handball_skill

            # Endurance affects willingness to move to distant positions
            endurance = stats.get('stamina') / 20
            distance_penalty = self.ground._calculate_distance(pos, player.current_position) * (1 - endurance)
            score -= distance_penalty * 0.2

        # Universal attribute adjustments

        # Speed affects willingness to move to distant positions
        speed = stats.get('speed') / 20
        distance_to_pos = self.ground._calculate_distance(pos, player.current_position)
        score += speed * max(0, 10 - distance_to_pos) * 0.3

        # Agility affects preference for space
        agility = stats.get('agility') / 20
        congestion = self._get_point_congestion(pos, self.ground.congestion_map)
        score += agility * (1 - congestion) * 2.0

        # Intelligence affects tactical positioning
        intelligence = stats.get('mental') / 20
        tactical_value = self._calculate_tactical_position_value(pos, player, game_state)
        score += intelligence * tactical_value

        return score

    def _adjust_score_for_team_structure(self, score: float, pos: Tuple[int, int], player: Player, situation: str, game_state: 'GameState') -> float:
        """
        Adjust position score based on maintaining team structure and formation.
        """
        team_side = player.team_side
        players = game_state.home_team_players if team_side == 'home' else game_state.away_team_players

        # Create team structure from players
        team_structure = {
            'forwards': [],
            'midfielders': [],
            'defenders': [],
            'rucks': []
        }

        # Categorize players by position
        for p in players.values():
            if p.position in ['FF', 'CHF', 'RF', 'LF', 'LHF', 'RHF']:
                team_structure['forwards'].append(p)
            elif p.position in ['LWing', 'RWing', 'Centre', 'RuckRover', 'Rover']:
                team_structure['midfielders'].append(p)
            elif p.position in ['RB', 'FB', 'CHB', 'LHB', 'LB', 'RHB']:
                team_structure['defenders'].append(p)
            elif p.position == 'Ruck':
                team_structure['rucks'].append(p)

        # Find player's line
        player_line = next((line for line, players in team_structure.items() if player in players), None)
        if not player_line:
            return score

        # Calculate average position of player's line
        line_positions = [p.current_position for p in team_structure[player_line] if p != player]
        if line_positions:
            avg_x = sum(p[0] for p in line_positions) / len(line_positions)
            avg_y = sum(p[1] for p in line_positions) / len(line_positions)

            # Calculate how well this position maintains line structure
            line_deviation = abs(pos[1] - avg_y)  # Lateral deviation
            vertical_deviation = abs(pos[0] - avg_x)  # Vertical deviation

            # Positions that maintain line structure are rewarded
            structure_score = max(0, 5 - line_deviation/2) + max(0, 5 - vertical_deviation/2)
            score += structure_score

        # Maintain spacing from teammates
        min_teammate_distance = min((self.ground._calculate_distance(pos, p.current_position)
                                for p in players.values() if p != player), default=0)

        # Reward positions that maintain good spacing (not too close, not too far)
        if min_teammate_distance < 3:
            score -= (3 - min_teammate_distance) * 2  # Penalize too close
        elif min_teammate_distance > 15:
            score -= (min_teammate_distance - 15) * 0.5  # Slight penalty for too far
        else:
            score += 2  # Reward good spacing

        return score

    def _assess_field_position_quality(self, pos: Tuple[int, int], game_state: 'GameState') -> float:
        """
        Assess the tactical quality of a field position.

        Args:
            pos: Position to evaluate
            game_state: Current game state

        Returns:
            float: Quality score (0.0 to 1.0)
        """
        # Reward positions with good spacing from teammates
        team_spacing_score = self._calculate_team_spacing(pos, game_state)

        # Reward positions that maintain formation structure
        formation_score = self._calculate_formation_adherence(pos, game_state)

        # Reward positions with good attacking options
        attacking_options_score = self._calculate_attacking_options(pos, game_state)

        # Combine scores with appropriate weights
        quality_score = (
            team_spacing_score * 0.4 +
            formation_score * 0.3 +
            attacking_options_score * 0.3
        )

        return quality_score

    def _calculate_team_spacing(self, pos: Tuple[int, int], game_state: 'GameState') -> float:
        """Calculate how well-spaced this position is from teammates."""
        # Get all player positions
        home_positions = [p.current_position for p in game_state.home_team_players.values()]
        away_positions = [p.current_position for p in game_state.away_team_players.values()]

        #print(f"Home Team positions: {home_positions}")

        # Determine which team's positions to use
        team_positions = home_positions if game_state.ball_carrier and game_state.ball_carrier.team_side == 'home' else away_positions

        # Calculate average distance to closest 3 teammates
        distances = [math.sqrt((p[0] - pos[0])**2 + (p[1] - pos[1])**2) for p in team_positions]
        distances.sort()

        # Skip the first distance (distance to self or very close teammate)
        if len(distances) >= 4:
            closest_distances = distances[1:4]  # Next 3 closest
            avg_distance = sum(closest_distances) / len(closest_distances)

            # Optimal spacing is around 10-15 grid cells
            if avg_distance < 5:
                # Too close
                return 0.2
            elif 5 <= avg_distance < 10:
                # Getting better
                return 0.6
            elif 10 <= avg_distance < 20:
                # Optimal spacing
                return 1.0
            else:
                # Too far apart
                return 0.5

        return 0.5  # Default if not enough teammates

    def _calculate_zone_coverage_score(self, pos: Tuple[int, int], game_state: 'GameState') -> float:
        """
        Calculate how well a position contributes to zone defense coverage.

        Args:
            pos: Position to evaluate
            game_state: Current game state

        Returns:
            float: Score for zone coverage (0.0 to 1.0)
        """
        # Get ball position and attacking corridor
        ball_pos = game_state.ball_position
        corridor_boundaries = self.ground.get_corridor_boundaries()

        # Get defensive thirds
        defensive_third, forward_third = self.ground.get_thirds()

        # Calculate base coverage score based on position relative to ball
        distance_to_ball = self.ground._calculate_distance(pos, ball_pos)

        # Best zone defense positions are 10-15 meters from ball
        if distance_to_ball < 5:
            # Too close to ball for good zone coverage
            distance_score = 0.3
        elif 5 <= distance_to_ball <= 15:
            # Ideal zone coverage distance
            distance_score = 1.0 - ((distance_to_ball - 10) ** 2) / 100
        else:
            # Too far for effective zone coverage
            distance_score = max(0, 1.0 - (distance_to_ball - 15) / 20)

        # Calculate corridor coverage
        # Positions in or near the corridor are more valuable
        y_pos = pos[1]
        if corridor_boundaries[0] <= y_pos <= corridor_boundaries[1]:
            # In main corridor
            corridor_score = 1.0
        else:
            # Outside corridor - score decreases with distance from corridor
            distance_to_corridor = min(
                abs(y_pos - corridor_boundaries[0]),
                abs(y_pos - corridor_boundaries[1])
            )
            corridor_score = max(0, 1.0 - (distance_to_corridor / 10))

        # Calculate defensive positioning
        # Better scores for positions between ball and goal
        if pos[0] < ball_pos[0]:
            defensive_position_score = 1.0
        else:
            defensive_position_score = 0.5

        # Check spacing from other defenders
        team_side = 'home' if pos in [p.current_position for p in game_state.home_team_players.values()] else 'away'
        players = game_state.home_team_players if team_side == 'home' else game_state.away_team_players

        # Find nearest teammate
        min_teammate_distance = float('inf')
        for player in players.values():
            if player.current_position != pos:  # Don't compare with self
                dist = self.ground._calculate_distance(pos, player.current_position)
                min_teammate_distance = min(min_teammate_distance, dist)

        # Score spacing - want some space between defenders but not too much
        if min_teammate_distance < 5:
            spacing_score = 0.5  # Too close
        elif 5 <= min_teammate_distance <= 15:
            spacing_score = 1.0  # Good spacing
        else:
            spacing_score = max(0, 1.0 - (min_teammate_distance - 15) / 10)  # Too far

        # Calculate final score with weights
        final_score = (
            distance_score * 0.3 +        # Distance from ball
            corridor_score * 0.2 +        # Corridor coverage
            defensive_position_score * 0.3 + # Defensive positioning
            spacing_score * 0.2           # Spacing from teammates
        )

        return final_score

    def _calculate_formation_adherence(self, pos: Tuple[int, int], game_state: 'GameState') -> float:
        """
        Calculate how well a position adheres to team formation and structure.

        Args:
            pos: Position to evaluate
            game_state: Current game state

        Returns:
            float: Formation adherence score (0.0 to 1.0)
        """
        # Get team side for the position we're evaluating
        team_side = 'home' if pos in [p.current_position for p in game_state.home_team_players.values()] else 'away'
        team = game_state.home_team if team_side == 'home' else game_state.away_team
        team_tactics = game_state.home_team_tactics.tactics if team_side == 'home' else game_state.away_team_tactics.tactics

            # Debug info
        #print(f"Team side: {team_side}")
        #print(f"Team object type: {type(team)}")
        #print(f"Team object: {team}")
        #print(f"Available methods: {dir(team)}")

        players = game_state.home_team_players if team_side == 'home' else game_state.away_team_players
        team_structure = {
            'forwards': [],
            'midfielders': [],
            'defenders': [],
            'rucks': []
        }

        for player in players.values():
            if player.position in ['FF', 'CHF', 'RHF', 'LF', 'RHF', 'LHF']:
                team_structure['forwards'].append(player)
            elif player.position in ['Centre', 'LWing', 'RWing,' 'Rover', 'RuckRover']:
                team_structure['midfielders'].append(player)
            elif player.position in ['FB', 'CHB', 'RB', 'LB','LHB', 'RHB']:
                team_structure['defenders'].append(player)
            elif player.position == 'Ruck':
                #time.sleep(2)
                team_structure['rucks'].append(player)

        # Get current phase of play
        ball_pos = game_state.ball_position
        ball_zone = self.ground.get_zone(ball_pos)
        attacking_direction = self.ground.attacking_direction(team_side)

        # Calculate relative position scores
        structure_score = 0.0
        total_weights = 0.0

        # Get tactical formation type
        formation_type = team_tactics.get('formation', 'balanced')

        # Define ideal distances based on formation type
        if formation_type == 'wide':
            ideal_lateral_spacing = 15  # Wider spacing
            vertical_compression = 0.8  # Less vertical compression
        elif formation_type == 'compact':
            ideal_lateral_spacing = 8   # Tighter spacing
            vertical_compression = 1.2  # More vertical compression
        else:  # balanced
            ideal_lateral_spacing = 12
            vertical_compression = 1.0

        # Adjust for different phases of play
        if "attacking" in ball_zone:
            # In attack, forwards should push up, midfielders support
            vertical_bias = 1.2
            defensive_depth = 0.8
        elif "defending" in ball_zone:
            # In defense, team should compress
            vertical_bias = 0.8
            defensive_depth = 1.2
        else:
            # Balanced in midfield
            vertical_bias = 1.0
            defensive_depth = 1.0

        # Calculate position relative to team structure
        for line, players in team_structure.items():
            if not players:
                continue

            # Calculate average position for this line
            line_positions = [p.current_position for p in players]
            avg_x = sum(p[0] for p in line_positions) / len(line_positions)
            avg_y = sum(p[1] for p in line_positions) / len(line_positions)

            # Calculate ideal position based on line and tactics
            if line == 'forwards':
                ideal_x = ball_pos[0] + (20 * attacking_direction * vertical_bias)
                weight = 1.0
            elif line == 'midfielders':
                ideal_x = ball_pos[0] + (10 * attacking_direction)
                weight = 1.2  # Midfield structure is important
            elif line == 'defenders':
                ideal_x = ball_pos[0] - (15 * attacking_direction * defensive_depth)
                weight = 1.0
            else:  # rucks or others
                ideal_x = ball_pos[0]
                weight = 0.8

            # Calculate lateral (width) adherence
            lateral_deviation = abs(pos[1] - avg_y)
            lateral_score = max(0, 1 - (lateral_deviation / ideal_lateral_spacing))

            # Calculate vertical (length) adherence
            vertical_deviation = abs(pos[0] - ideal_x)
            vertical_score = max(0, 1 - (vertical_deviation / (20 * vertical_compression)))

            # Combine scores with emphasis on maintaining vertical structure
            line_score = (vertical_score * 0.6) + (lateral_score * 0.4)

            # Add to total score with weight
            structure_score += line_score * weight
            total_weights += weight

        # Calculate final weighted score
        if total_weights > 0:
            final_score = structure_score / total_weights
        else:
            final_score = 0.5  # Default if no valid structure references

        # Apply tactical modifiers
        push_factor = team_tactics.get('push_factor', 1.0)
        if int(push_factor) > 1:
            # Reward more aggressive positioning for high push factor
            final_score = final_score * (1 + (int(push_factor) - 1) * 0.2)

        # Ensure score stays in 0-1 range
        final_score = max(0.0, min(1.0, final_score))

        return final_score

    def _calculate_marking_opportunity_score(self, pos: Tuple[int, int], game_state: 'GameState') -> float:
        """
        Calculate how good a position is for marking opportunities.

        Args:
            pos: Position to evaluate
            game_state: Current game state

        Returns:
            float: Score for marking opportunity (0.0 to 1.0)
        """
        # Get ball position and direction
        ball_pos = game_state.ball_position

        # Calculate distance from ball
        distance_to_ball = self.ground._calculate_distance(pos, ball_pos)

        # Best marking positions are 10-20 meters ahead of the ball
        if distance_to_ball < 5:
            # Too close for marking
            distance_score = 0.2
        elif 5 <= distance_to_ball <= 20:
            # Ideal marking distance
            distance_score = 1.0 - ((distance_to_ball - 10) ** 2) / 200
        else:
            # Too far for effective marking
            distance_score = max(0, 1.0 - (distance_to_ball - 20) / 20)

        # Check line of sight to ball
        line_of_sight_score = self._calculate_line_of_sight_score(pos, ball_pos, game_state)

        # Check congestion - marking opportunities better with less congestion
        congestion = self._get_point_congestion(pos, game_state.ground.congestion_map)
        space_score = 1.0 - congestion

        # Combine scores with weights
        final_score = (
            distance_score * 0.4 +
            line_of_sight_score * 0.3 +
            space_score * 0.3
        )

        return final_score

    def _calculate_shot_quality_score(self, pos: Tuple[int, int], game_state: 'GameState') -> float:
        """
        Calculate how good a position is for shot attempts.

        Args:
            pos: Position to evaluate
            game_state: Current game state

        Returns:
            float: Score for shot quality (0.0 to 1.0)
        """
        team_side = 'home' if pos in [p.current_position for p in game_state.home_team_players.values()] else 'away'

        # Calculate distance to goal
        goal_distance = self.ground.get_distance_to_goal(pos, team_side)

        # Calculate shot angle
        shot_angle = self.ground._calculate_shot_angle(pos, team_side)

        # Distance scoring - optimal distance around 30-40 meters
        if goal_distance < 15:
            # Too close, might be rushed
            distance_score = 0.5
        elif 15 <= goal_distance <= 40:
            # Optimal distance
            distance_score = 1.0 - ((goal_distance - 30) ** 2) / 1000
        else:
            # Too far, decreasing probability
            distance_score = max(0, 1.0 - (goal_distance - 40) / 20)

        # Angle scoring - normalize to 0-1
        angle_score = shot_angle / 90.0

        # Check for congestion/pressure
        congestion = self._get_point_congestion(pos, game_state.ground.congestion_map)
        pressure_score = 1.0 - congestion

        # Combine scores with weights
        final_score = (
            distance_score * 0.4 +
            angle_score * 0.4 +
            pressure_score * 0.2
        )

        return final_score

    def _calculate_support_position_score(self, pos: Tuple[int, int], game_state: 'GameState') -> float:
        """
        Calculate how good a position is for providing support/handball options.

        Args:
            pos: Position to evaluate
            game_state: Current game state

        Returns:
            float: Score for support position quality (0.0 to 1.0)
        """
        if not game_state.ball_carrier:
            return 0.5  # Neutral score if no ball carrier

        ball_carrier_pos = game_state.ball_carrier.current_position

        # Calculate distance from ball carrier
        distance = self.ground._calculate_distance(pos, ball_carrier_pos)

        # Best support positions are 5-10 meters from ball carrier
        if distance < 3:
            # Too close
            distance_score = 0.3
        elif 3 <= distance <= 10:
            # Ideal support distance
            distance_score = 1.0 - ((distance - 5) ** 2) / 50
        else:
            # Too far for effective support
            distance_score = max(0, 1.0 - (distance - 10) / 10)

        # Check line of sight to ball carrier
        line_of_sight_score = self._calculate_line_of_sight_score(pos, ball_carrier_pos, game_state)

        # Check space availability
        congestion = self._get_point_congestion(pos, game_state.ground.congestion_map)
        space_score = 1.0 - congestion

        # Check if position is in good handball receiving direction
        # (slightly forward of ball carrier)
        attacking_direction = self.ground.attacking_direction(game_state.ball_carrier.team_side)
        forward_progress = (pos[0] - ball_carrier_pos[0]) * attacking_direction
        direction_score = 1.0 if 0 <= forward_progress <= 10 else max(0, 1.0 - abs(forward_progress - 5) / 10)

        # Combine scores with weights
        final_score = (
            distance_score * 0.3 +
            line_of_sight_score * 0.2 +
            space_score * 0.3 +
            direction_score * 0.2
        )

        return final_score

    def _calculate_tactical_position_value(self, pos: Tuple[int, int], player: Player, game_state: 'GameState') -> float:
        """
        Calculate the tactical value of a position considering game state and player role.

        Args:
            pos: Position to evaluate
            player: Player being evaluated
            game_state: Current game state

        Returns:
            float: Tactical value score (0.0 to 1.0)
        """
        team_side = player.team_side
        attacking_direction = self.ground.attacking_direction(team_side)
        ball_pos = game_state.ball_position

        # Get zone information
        current_zone = self.ground.get_zone_for_position(player.current_position, player.team_side)
        ball_zone = self.ground.get_zone_for_position(ball_pos, player.team_side)

        # Base tactical value
        tactical_value = 0.0

        # Position-specific tactical considerations
        if player.position in ['FF', 'CHF', 'RHF', 'LF', 'RHF', 'LHF']:
            # Forwards should value positions that:
            # 1. Maintain forward structure
            # 2. Create good leading opportunities
            # 3. Provide goal-scoring opportunities

            # Value positions ahead of the ball in attacking direction
            forward_progress = (pos[0] - ball_pos[0]) * attacking_direction
            if forward_progress > 0:
                tactical_value += min(forward_progress / 20, 1.0) * 0.4

            # Value positions within scoring range
            if self.ground.is_in_range_for_shot(pos, team_side):
                shot_angle = self.ground._calculate_shot_angle(pos, team_side)
                tactical_value += (shot_angle / 90.0) * 0.4

        elif player.position in ['FB', 'CHB', 'RB', 'LB','LHB', 'RHB']:
            # Defenders should value positions that:
            # 1. Maintain defensive structure
            # 2. Cover dangerous spaces
            # 3. Provide good intercepting positions

            # Value positions between ball and goal
            if game_state.ball_carrier and game_state.ball_carrier.team_side != team_side:
                goal_pos = (0 if attacking_direction > 0 else self.ground.grid_length, self.ground.grid_width // 2)
                defender_to_goal = self.ground._calculate_distance(pos, goal_pos)
                ball_to_goal = self.ground._calculate_distance(ball_pos, goal_pos)

                # Reward positions between ball and goal
                if defender_to_goal < ball_to_goal:
                    tactical_value += 0.5

                # Extra value for covering the corridor
                corridor_boundaries = self.ground.get_corridor_boundaries()
                if corridor_boundaries[0] <= pos[1] <= corridor_boundaries[1]:
                    tactical_value += 0.3

        elif player.position in ['Centre', 'LWing', 'RWing,' 'Rover', 'RuckRover']:
            # Midfielders should value positions that:
            # 1. Provide good support options
            # 2. Maintain midfield structure
            # 3. Enable quick transitions

            # Value positions near the ball but not too close
            distance_to_ball = self.ground._calculate_distance(pos, ball_pos)
            if 5 <= distance_to_ball <= 15:
                tactical_value += 0.4

            # Value positions that maintain midfield width
            center_y = self.ground.get_center()[1]
            width_position = abs(pos[1] - center_y) / (self.ground.grid_width / 2)
            if 0.3 <= width_position <= 0.7:  # Not too wide, not too narrow
                tactical_value += 0.3

        elif player.position == 'Ruck':
            # Rucks should value positions that:
            # 1. Provide good tap opportunities
            # 2. Support midfield structure
            # 3. Create marking targets

            # Value central positions
            center_y = self.ground.get_center()[1]
            center_distance = abs(pos[1] - center_y)
            tactical_value += max(0, 1 - (center_distance / 10)) * 0.4

            # Value positions near the ball for tap opportunities
            distance_to_ball = self.ground._calculate_distance(pos, ball_pos)
            if distance_to_ball < 5:
                tactical_value += 0.4

        # Universal tactical considerations

        # Value positions that maintain team balance
        thirds = self.ground.get_thirds()
        players_in_third = sum(1 for p in game_state.home_team_players.values() if p.team_side == team_side
                            and thirds[0] <= p.current_position[0] <= thirds[1])
        if players_in_third < 6:  # Avoid overcrowding in any third
            tactical_value += 0.2

        # Value positions based on game phase
        if "forward" in ball_zone:
            if "forward" in current_zone:
                tactical_value += 0.2
        elif "defense" in ball_zone:
            if "defense" in current_zone:
                tactical_value += 0.2


        # Normalize final value to 0-1 range
        tactical_value = max(0.0, min(1.0, tactical_value))

        return tactical_value

    def _calculate_attacking_options(self, pos: Tuple[int, int], game_state: 'GameState') -> float:
        """
        Calculate how good a position is for attacking options.

        Args:
            pos: Position to evaluate
            game_state: Current game state

        Returns:
            float: Score for attacking options (0.0 to 1.0)
        """
        # Get team side
        team_side = 'home' if pos in [p.current_position for p in game_state.home_team_players.values()] else 'away'
        attacking_direction = self.ground.attacking_direction(team_side)

        # Calculate distance to goal
        goal_distance = self.ground.get_distance_to_goal(pos, team_side)
        max_distance = math.sqrt(self.ground.grid_length**2 + self.ground.grid_width**2)
        distance_score = 1 - (goal_distance / max_distance)

        # Calculate shot angle if in scoring range
        if self.ground.is_in_range_for_shot(pos, team_side):
            shot_angle = self.ground._calculate_shot_angle(pos, team_side)
            angle_score = shot_angle / 90.0  # Normalize to 0-1
        else:
            angle_score = 0.0

        # Calculate space in front of position
        forward_space = 0.0
        check_distance = 15  # Look 15 cells ahead

        for d in range(1, check_distance + 1):
            check_x = pos[0] + (d * attacking_direction)
            if 0 <= check_x < self.ground.grid_length:
                check_pos = (check_x, pos[1])
                if self.ground.is_position_legal(check_pos):
                    congestion = self._get_point_congestion(check_pos, self.ground.congestion_map)
                    forward_space += (1 - congestion) * (1 - (d / check_distance))

        forward_space = forward_space / check_distance  # Normalize to 0-1

        # Weight the components based on position on ground
        if goal_distance < 50:  # In scoring range
            final_score = (
                distance_score * 0.3 +
                angle_score * 0.4 +
                forward_space * 0.3
            )
        else:  # Outside scoring range
            final_score = (
                distance_score * 0.2 +
                forward_space * 0.8
            )

        return final_score

    def _calculate_line_of_sight_score(self, pos: Tuple[int, int], target_pos: Tuple[int, int], game_state: 'GameState') -> float:
        """Calculate how clear the line of sight is to a target position."""
        # Draw a line between positions
        dx = target_pos[0] - pos[0]
        dy = target_pos[1] - pos[1]
        distance = math.sqrt(dx**2 + dy**2)

        if distance < 1:
            return 1.0  # Very close, perfect line of sight

        # Create normalized direction vector
        direction_x = dx / distance
        direction_y = dy / distance

        # Sample points along the line
        obstacles = 0
        sample_count = min(10, int(distance))

        if sample_count > 0:
            for i in range(1, sample_count + 1):
                # Calculate sample point
                t = i / sample_count
                sample_x = int(pos[0] + dx * t)
                sample_y = int(pos[1] + dy * t)

                # Check congestion at sample point
                congestion = self._get_point_congestion((sample_x, sample_y), game_state.ground.congestion_map)

                # If congestion is high, count as obstacle
                if congestion > 0.6:
                    obstacles += 1

        # Calculate line of sight score
        if sample_count > 0:
            line_of_sight_score = 1.0 - (obstacles / sample_count)
        else:
            line_of_sight_score = 1.0

        return line_of_sight_score


    def _calculate_player_speed(self, player: Player) -> float:
        """Calculate player's current movement speed."""
        base_speed = player.physical_stats.speed / 20

        # Adjust for fatigue
        fatigue_factor = max(0.5, 1.0 - (player.fatigue / 200))

        return base_speed * fatigue_factor


    def _calculate_movement_path(self, start: Tuple[int, int], end: Tuple[int, int],
                            game_state: 'GameState', player=None) -> List[Tuple[int, int]]:
        """Calculate optimal movement path using AFL player movement logic."""
        # Start with direct path as baseline
        direct_path = [start, end]

        # If player object is provided, use their attributes for decision-making
        if player:
            # Get player attributes
            speed = getattr(player.physical_stats, 'speed')
            agility = getattr(player.physical_stats, 'agility')
            tactical = getattr(player.ability_stats, 'tactical')
            fatigue = getattr(player, 'fatigue', 0)

            # Calculate player's movement intelligence (0-1 scale)
            movement_intelligence = ((tactical + agility) / 40) * (1 - (fatigue / 100))
            print(f"Player {player.name} movement intelligence: {movement_intelligence:.2f}")
        else:
            # Default values if no player object
            movement_intelligence = 0.5

        # Get congestion map
        congestion_map = self.ground.congestion_map

        # Calculate direct distance
        direct_distance = math.sqrt((end[0] - start[0])**2 + (end[1] - start[1])**2)

        # For very short movements, just use direct path
        if direct_distance < 5:  # ~10m
            return self._smooth_path(direct_path)

        # For longer movements, find better path
        # AFL players typically:
        # 1. Avoid congested areas
        # 2. Use space on the wings when available
        # 3. Take wider arcs when running at speed
        # 4. Cut angles when changing direction

        # Determine if we should use a curved path based on distance and player intelligence
        use_curved_path = random.random() < movement_intelligence and direct_distance > 10

        if use_curved_path:
            # Create a curved path that avoids congestion
            path = self._create_curved_path(start, end, congestion_map, movement_intelligence)
        else:
            # Use direct path with slight adjustments to avoid heavy congestion
            path = self._avoid_congestion(direct_path, congestion_map, movement_intelligence)

        # Smooth the path for natural movement
        return self._smooth_path(path)

    def _create_curved_path(self, start: Tuple[int, int], end: Tuple[int, int],
                        congestion_map: List[List[float]], intelligence: float) -> List[Tuple[int, int]]:
        """Create a curved path that avoids congestion using AFL movement patterns."""
        # Get center of ground
        center_x, center_y = self.ground.get_center()

        # Determine if we should curve toward or away from center
        # Players often use the wings (away from center) when moving long distances
        start_to_center = math.sqrt((start[0] - center_x)**2 + (start[1] - center_y)**2)
        end_to_center = math.sqrt((end[0] - center_x)**2 + (end[1] - center_y)**2)

        # Calculate midpoint of direct line
        mid_x = (start[0] + end[0]) / 2
        mid_y = (start[1] + end[1]) / 2

        # Vector from start to end
        dx = end[0] - start[0]
        dy = end[1] - start[1]

        # Perpendicular vector (for curve control point)
        perp_x = -dy
        perp_y = dx

        # Normalize perpendicular vector
        length = math.sqrt(perp_x**2 + perp_y**2)
        if length > 0:
            perp_x /= length
            perp_y /= length

        # Determine curve direction (toward or away from center)
        mid_to_center_x = center_x - mid_x
        mid_to_center_y = center_y - mid_y

        # Dot product to determine if perpendicular vector points toward center
        dot_product = perp_x * mid_to_center_x + perp_y * mid_to_center_y

        # If in congested area, curve away from center (use wings)
        # If in open area, take more direct path
        avg_congestion = self._get_path_congestion([start, (mid_x, mid_y), end], congestion_map)

        # Curve magnitude based on distance, intelligence and congestion
        distance = math.sqrt(dx**2 + dy**2)
        curve_magnitude = distance * 0.3 * intelligence * (1 + avg_congestion)

        # Adjust direction based on congestion and field position
        if avg_congestion > 0.5 or (start_to_center < 15 and end_to_center < 15):
            # In congested areas or near center, curve toward wings
            if dot_product > 0:
                perp_x = -perp_x
                perp_y = -perp_y
        else:
            # In open areas, use intelligence to pick better path
            # Check both potential curves and pick less congested one
            curve1_x = mid_x + perp_x * curve_magnitude
            curve1_y = mid_y + perp_y * curve_magnitude
            curve2_x = mid_x - perp_x * curve_magnitude
            curve2_y = mid_y - perp_y * curve_magnitude

            # Check congestion at both potential curve points
            congestion1 = self._get_point_congestion((int(curve1_x), int(curve1_y)), congestion_map)
            congestion2 = self._get_point_congestion((int(curve2_x), int(curve2_y)), congestion_map)

            # Choose less congested path
            if congestion2 < congestion1:
                perp_x = -perp_x
                perp_y = -perp_y

        # Calculate control point for curve
        control_x = mid_x + perp_x * curve_magnitude
        control_y = mid_y + perp_y * curve_magnitude

        # Create path with quadratic Bezier curve
        path = []
        steps = max(int(distance / 2), 5)  # More steps for longer distances

        for i in range(steps + 1):
            t = i / steps
            # Quadratic Bezier formula
            x = (1-t)**2 * start[0] + 2*(1-t)*t * control_x + t**2 * end[0]
            y = (1-t)**2 * start[1] + 2*(1-t)*t * control_y + t**2 * end[1]
            path.append((int(x), int(y)))

        return path

    def _avoid_congestion(self, path: List[Tuple[int, int]], congestion_map: List[List[float]],
                        intelligence: float) -> List[Tuple[int, int]]:
        """Adjust path to avoid heavily congested areas."""
        if len(path) < 2:
            return path

        start, end = path[0], path[-1]

        # Check if direct path goes through heavy congestion
        avg_congestion = self._get_path_congestion(path, congestion_map)

        # If congestion is low or intelligence is low, just use direct path
        if avg_congestion < 0.3 or intelligence < 0.3:
            return path

        # For higher congestion, try to find a better path
        # Sample a few potential midpoints and pick the least congested
        best_path = path
        best_congestion = avg_congestion

        # Try different offsets to avoid congestion
        for offset_pct in [0.2, 0.3, 0.4]:
            # Calculate perpendicular vector
            dx = end[0] - start[0]
            dy = end[1] - start[1]

            # Perpendicular vector
            perp_x = -dy
            perp_y = dx

            # Normalize
            length = math.sqrt(perp_x**2 + perp_y**2)
            if length > 0:
                perp_x /= length
                perp_y /= length

            # Calculate midpoint with offset
            mid_x = (start[0] + end[0]) / 2 + perp_x * (offset_pct * length)
            mid_y = (start[1] + end[1]) / 2 + perp_y * (offset_pct * length)

            # Create path through this midpoint
            test_path = [start, (int(mid_x), int(mid_y)), end]

            # Check congestion
            test_congestion = self._get_path_congestion(test_path, congestion_map)

            # Update if better
            if test_congestion < best_congestion:
                best_path = test_path
                best_congestion = test_congestion

            # Try the other direction too
            mid_x = (start[0] + end[0]) / 2 - perp_x * (offset_pct * length)
            mid_y = (start[1] + end[1]) / 2 - perp_y * (offset_pct * length)

            test_path = [start, (int(mid_x), int(mid_y)), end]
            test_congestion = self._get_path_congestion(test_path, congestion_map)

            if test_congestion < best_congestion:
                best_path = test_path
                best_congestion = test_congestion

        return best_path

    def _get_path_congestion(self, path: List[Tuple[int, int]], congestion_map: List[List[float]]) -> float:
        """Calculate average congestion along a path."""
        if not path:
            return 0

        total_congestion = 0
        valid_points = 0

        for point in path:
            # Convert coordinates to integers if they're floats
            x = int(point[0]) if isinstance(point[0], float) else point[0]
            y = int(point[1]) if isinstance(point[1], float) else point[1]

            if 0 <= x < len(congestion_map) and 0 <= y < len(congestion_map[0]):
                total_congestion += congestion_map[x][y]
                valid_points += 1

        return total_congestion / max(1, valid_points)

    def _get_point_congestion(self, point: Tuple[int, int], congestion_map: List[List[float]]) -> float:
        """Get congestion at a specific point."""
        x, y = point
        if 0 <= x < len(congestion_map) and 0 <= y < len(congestion_map[0]):
            return congestion_map[x][y]
        return 0  # Default for out of bounds

    def _smooth_path(self, path: List[Tuple[int, int]]) -> List[Tuple[int, int]]:
        """Create a smooth path with intermediate points."""
        if len(path) < 2:
            return path

        smooth_path = [path[0]]  # Start with first point

        # Add intermediate points between each pair of points
        for i in range(len(path) - 1):
            current = path[i]
            next_pos = path[i + 1]

            # Add intermediate points
            dx = next_pos[0] - current[0]
            dy = next_pos[1] - current[1]
            distance = math.sqrt(dx*dx + dy*dy)

            # Add points every 2 grid cells
            if distance > 2:
                steps = int(distance / 2)
                for j in range(1, steps):
                    x = current[0] + (dx * j / steps)
                    y = current[1] + (dy * j / steps)
                    smooth_path.append((int(x), int(y)))

            # Add the endpoint
            if i < len(path) - 2:  # Don't add the final endpoint yet
                smooth_path.append(next_pos)

        # Add the final endpoint
        smooth_path.append(path[-1])

        return smooth_path


    def _get_next_position(self, player: Player) -> Tuple[int, int]:
        """Get next position along player's movement path with improved progression."""
        if player not in self.movement_states:
            return player.current_position

        state = self.movement_states[player]

        # If we've reached end of path, stay there
        if state['path_index'] >= len(state['path']):
            # Mark that we've completed this path
            state['path_completed'] = True
            return player.current_position

        # Get next position
        next_pos = state['path'][state['path_index']]

        # Calculate how far to move based on player's speed and stamina
        movement_rate = state['speed'] * state['stamina_factor']

        # Update path index - more intelligent progression
        # If player is very close to target, move directly to it
        current_pos = player.current_position
        distance_to_target = math.sqrt(
            (next_pos[0] - current_pos[0])**2 +
            (next_pos[1] - current_pos[1])**2
        )

        if distance_to_target < movement_rate:
            # We can reach the next waypoint in this step
            state['path_index'] += 1

            # If we've reached the end of the path, return the final position
            if state['path_index'] >= len(state['path']):
                state['path_completed'] = True
                return state['path'][-1]

            # Otherwise, recursively get the next position
            # This allows fast players to move through multiple waypoints in one update
            return self._get_next_position(player)
        else:
            # Move toward the next waypoint at the player's speed
            direction_x = (next_pos[0] - current_pos[0]) / distance_to_target
            direction_y = (next_pos[1] - current_pos[1]) / distance_to_target

            new_x = current_pos[0] + direction_x * movement_rate
            new_y = current_pos[1] + direction_y * movement_rate

            return (int(new_x), int(new_y))

    def should_recalculate_paths(self, game_state: 'GameState', previous_ball_position: Tuple[int, int]) -> bool:
        """Determine if all player paths should be recalculated based on ball movement."""
        if not previous_ball_position or not game_state.ball_position:
            return True

        # Calculate how far the ball has moved
        ball_movement = self.ground._calculate_distance(previous_ball_position, game_state.ball_position)

        # If ball has moved significantly, recalculate paths
        if ball_movement > 10:  # Threshold for significant movement (10 grid cells)
            return True

        # If ball carrier has changed, recalculate paths
        if hasattr(game_state, 'previous_ball_carrier') and game_state.previous_ball_carrier != game_state.ball_carrier:
            return True

        # If game phase has changed, recalculate paths
        if hasattr(game_state, 'previous_phase') and game_state.previous_phase != game_state.phase:
            return True

        return False

    def _calculate_intended_movement(self, player, disposer_data, attacking_direction):
        """Calculate the intended movement path for a player."""
        # Get player's effective stats (accounting for fatigue)
        effective_speed = player.physical_stats.speed * (1 - player.fatigue/100)
        effective_agility = player.physical_stats.agility * (1 - player.fatigue/100)
        effective_mental = player.ability_stats.mental * (1 - player.fatigue/100)

        # Base distance calculation (meters)
        base_distance = effective_speed * 0.5  # 0.5 meters per speed point

        # Adjust for pressure - players under pressure take shorter runs
        if 'disposer_pressure' in disposer_data:
            pressure_factor = 1 - disposer_data['disposer_pressure'] * 0.7
            base_distance *= pressure_factor

        # Adjust for field position - players closer to goal might take more direct paths
        field_position_x = player.current_position[0]
        attacking_direction = attacking_direction if player.team_side == 'home' else attacking_direction
        print(f"player.team_side: {player.team_side}")
        # If in attacking position and close to goal, be more direct
        goal_proximity_factor = 1.0
        if (attacking_direction > 0 and field_position_x > 0.7) or (attacking_direction < 0 and field_position_x < 0.3):
            goal_proximity_factor = 1.3  # More direct run

        # Calculate final distance
        final_distance = base_distance * goal_proximity_factor
        print(f"final_distance: {final_distance}")
        # Determine direction (prefer toward goal but consider space)
        # Basic direction vector
        direction_x = attacking_direction

        # Create congestion map
        congestion_map = self.ground.congestion_map

        # Calculate new position
        new_x = player.current_position[0] + (direction_x * final_distance / 2)  # Convert meters to grid units
        new_y = player.current_position[1] + random.uniform(-0.3, 0.3) * final_distance / 2

        # Ensure within bounds
        new_x = int(max(0, min(self.ground.grid_length - 1, new_x)))
        new_y = int(max(0, min(self.ground.grid_width - 1, new_y)))

        intended_position = (new_x, new_y)

        # Use existing path creation functions to generate a realistic path
        # First create a direct path
        direct_path = [player.current_position, intended_position]

        # Then create a curved path that avoids congestion
        curved_path = self._create_curved_path(
            player.current_position,
            intended_position,
            congestion_map,
            player.ability_stats.mental / 20  # Intelligence factor (0-1)
        )

        # Further avoid congestion
        adjusted_path = self._avoid_congestion(
            curved_path,
            congestion_map,
            player.ability_stats.mental / 20  # Intelligence factor (0-1)
        )

        # Smooth the path for natural movement
        final_path = self._smooth_path(adjusted_path)
        #print(f"final path: {final_path}")


        # Remove duplicate points from the path
        deduplicated_path = []
        for point in final_path:
            # Convert to integers if they're floats
            point = (int(point[0]), int(point[1]))
            # Only add if it's not the same as the last point
            if not deduplicated_path or point != deduplicated_path[-1]:
                deduplicated_path.append(point)

        # Ensure we have at least 2 points (start and end)
        if len(deduplicated_path) < 2:
            deduplicated_path = [player.current_position, intended_position]


        #print(f"intended_position: {intended_position}")
        #print(f"Deduplicated path: {deduplicated_path}")  # Add this to verify deduplication
        #sys.exit()
        # Return both the final intended position and the path points
        return intended_position, deduplicated_path


    def _get_center_bounce_position(self, player, team_side):
        """Get exact coordinates for player position during center bounce based on AFL rules."""
        # Get center of ground
        center_x, center_y = self.ground.get_center()

        # Team direction factor (home team attacks right side, away team attacks left)
        team_factor = 1 if team_side == "home" else -1

        # Centre square size (approximately 50m x 50m in ground coordinates)
        square_radius = 10  # Grid cells from center to edge of center square

        # Position players according to their roles
        if player.position == "Ruck":
            if team_side == "home":
                #print(f"Positioning {player.name} for home team")
                return (center_x - random.randint(1, 2), center_y)  # Slightly offset
            else:
                #print(f"Positioning {player.name} for away team")
                return (center_x + random.randint(1, 2), center_y)  # Slightly offset

        elif player.position == "Rover":
            # Rover slightly offset in center square
            return (center_x + (random.randint(2, 4) * team_factor), center_y - random.randint(2, 4))

        elif player.position == "RuckRover":
            # Ruck Rover slightly offset in center square
            return (center_x + (random.randint(2, 4) * team_factor), center_y - random.randint(2, 4))

        elif player.position == "Centre":
            # Centre slightly back from center in square
            return (center_x + (random.randint(2, 4) * team_factor), center_y - random.randint(2, 4))

        elif player.position == "LWing":
            # Left Wing outside square on wing
            return (center_x - random.randint(-2, 2), center_y - 15)

        elif player.position == "RWing":
            # Right Wing outside square on wing
            return (center_x + random.randint(-2, 2), center_y + 15)

        elif player.position == "CHF":
            # Centre Half Forward
            return (center_x + (15 * team_factor), center_y)

        elif player.position == "CHB":
            # Centre Half Back
            return (center_x - (15 * team_factor), center_y)

        elif player.position == "LHF":
            # Left Half Forward
            return (center_x + (15 * team_factor), center_y - 10)

        elif player.position == "RHF":
            # Right Half Forward
            return (center_x + (15 * team_factor), center_y + 10)

        elif player.position == "LHB":
            # Left Half Back
            return (center_x - (15 * team_factor), center_y - 10)

        elif player.position == "RHB":
            # Right Half Back
            return (center_x - (15 * team_factor), center_y + 10)

        elif player.position == "FF":
            # Full Forward
            return (center_x + (25 * team_factor), center_y)

        elif player.position == "FB":
            # Full Back
            return (center_x - (25 * team_factor), center_y)

        elif player.position == "LF":
            # Left Forward Pocket
            return (center_x + (25 * team_factor), center_y - 12)

        elif player.position == "RF":
            # Right Forward Pocket
            return (center_x + (25 * team_factor), center_y + 12)

        elif player.position == "LB":
            # Left Back Pocket
            return (center_x - (25 * team_factor), center_y - 12)

        elif player.position == "RB":
            # Right Back Pocket
            return (center_x - (25 * team_factor), center_y + 12)

        # Default for unrecognized positions
        return (center_x, center_y)










class GameState:
    """
    Tracks current state of the match.
    Integrates GameClock functionality and maintains compatibility with working AI version.
    Handles:
    - Game timing and quarters
    - Ball and player states
    - Score tracking
    - Statistics
    - Phase management
    - Movement interpolation support
    """
    def __init__(
        self,
        ground: Ground,
        movement_engine: MovementEngine,
        home_team,
        away_team,
        home_team_players: Dict[str, Player],
        away_team_players: Dict[str, Player],
        home_team_tactics: Dict[str, Any],
        away_team_tactics: Dict[str, Any]
    ):
        self.ground = ground
        self.movement_engine = movement_engine
        self.home_team = home_team
        self.away_team = away_team
        self.home_team_score = {'goals': 0, 'behinds': 0, 'total': 0}
        self.away_team_score = {'goals': 0, 'behinds': 0, 'total': 0}
        self.home_team_players = home_team_players
        self.away_team_players = away_team_players
        self.home_team_tactics = home_team_tactics
        self.away_team_tactics = away_team_tactics
        self.home_team.attacking_direction = self.ground.attacking_direction("home")
        self.away_team.attacking_direction = self.ground.attacking_direction("away")
        #print(f"Home team attack direction: {home_team.attacking_direction}")
        #print(f"Away team attack direction: {away_team.attacking_direction}")

        # Game timing (integrated from GameClock)
        self.current_quarter = 1
        self.quarter_length = 20 * 60  # 20 minutes in seconds
        self.quarter_time = 0.0  # Time in current quarter
        self.total_time = 0.0  # Total game time
        self.in_break = False
        self.break_time = 0.0
        self.quarter_break_length = 5 * 60  # 5 minutes
        self.half_time_break_length = 20 * 60  # 20 minutes

        # Ball state
        self.ball_carrier = None
        self.ball_position = self.ground.get_center()
        self.ball_height = 0.0  # Height in meters
        self.ball_velocity = (0.0, 0.0)  # Velocity vector (x, y)
        self.phase = None



    def update(self, delta_time: float) -> None:
        """Update game state"""
        # Store previous state for comparison
        self.previous_ball_position = self.ball_position
        self.previous_ball_carrier = self.ball_carrier
        self.previous_phase = self.phase

        if not self.in_break:
            # Update quarter time
            self.quarter_time += delta_time
            self.total_time += delta_time

            # Update ball position if in flight
            if not self.ball_carrier and self.ball_velocity != (0.0, 0.0):
                self._update_ball_physics(delta_time)

            # Update movement interpolation
            self._update_positions()
        else:
            # Update break time
            self.break_time += delta_time

    def start_quarter(self) -> None:
        """Start a new quarter"""
        self.quarter_time = 0.0
        self.in_break = False
        self.break_time = 0.0
        self.phase = "center_bounce"
        self.ball_position = self.ground.get_center()
        self.ball_height = 0.0
        self.ball_velocity = (0.0, 0.0)

    def end_quarter(self) -> None:
        """End current quarter"""
        self.in_break = True
        self.break_time = 0.0

    def start_break(self) -> None:
        """Start quarter/half time break"""
        self.in_break = True
        self.break_time = 0.0

    def end_break(self) -> None:
        """End quarter/half time break"""
        self.in_break = False
        self.next_quarter()

    def next_quarter(self) -> None:
        """Move to next quarter"""
        self.current_quarter += 1
        self.start_quarter()

    def should_end_quarter(self) -> bool:
        """Check if quarter should end"""
        return self.quarter_time >= self.quarter_length

    def should_end_break(self) -> bool:
        """Check if break should end"""
        if not self.in_break:
            return False

        break_length = (
            self.half_time_break_length if self.current_quarter == 2
            else self.quarter_break_length
        )

        return self.break_time >= break_length

    def is_game_over(self) -> bool:
        """Check if game is over"""
        return self.current_quarter > 4 and self.should_end_quarter()

    def get_quarter_name(self) -> str:
        """Get current quarter name"""
        if self.current_quarter == 1:
            return "First Quarter"
        elif self.current_quarter == 2:
            return "Second Quarter"
        elif self.current_quarter == 3:
            return "Third Quarter"
        elif self.current_quarter == 4:
            return "Final Quarter"
        else:
            return f"Quarter {self.current_quarter}"

    def get_break_name(self) -> str:
        """Get current break name"""
        if not self.in_break:
            return ""

        if self.current_quarter == 1:
            return "Quarter Time"
        elif self.current_quarter == 2:
            return "Half Time"
        elif self.current_quarter == 3:
            return "Three Quarter Time"
        else:
            return "Break"

    def get_quarter_time_str(self) -> str:
        """Get formatted quarter time string"""
        minutes = int(self.quarter_time // 60)
        seconds = int(self.quarter_time % 60)
        return f"{minutes}:{seconds:02d}"

    def get_break_time_str(self) -> str:
        """Get formatted break time string"""
        if not self.in_break:
            return ""

        break_length = (
            self.half_time_break_length if self.current_quarter == 2
            else self.quarter_break_length
        )

        time_remaining = max(0, break_length - self.break_time)
        minutes = int(time_remaining // 60)
        seconds = int(time_remaining % 60)

        return f"{minutes}:{seconds:02d}"


    def set_ball_carrier(self, player: Optional[Player]) -> None:
        """Set ball carrier"""
        self.ball_carrier = player

        if player:
            # Update possession
            self.team_in_possession = (
                "Home" if player.team == self.team1_players["Ruck"].team
                else "Away"
            )
            self.possession_time = 0.0

            # Update ball state
            self.ball_position = player.current_position
            self.ball_height = 0.0
            self.ball_velocity = (0.0, 0.0)
        else:
            self.team_in_possession = None


    def update_score(
        self,
        team_side: str,
        score_type: str
    ) -> None:
        """Update team score"""
        score = self.home_team_score if team_side == "home" else self.away_team_score

        if score_type == "goal":
            score["goals"] += 1
            score["total"] += 6
        elif score_type == "behind":
            score["behinds"] += 1
            score["total"] += 1

    def get_score_string(self) -> str:
        """Get formatted score string"""
        return (
            f"{self.home_team_score['goals']}.{self.home_team_score['behinds']}"
            f" ({self.home_team_score['total']}) vs "
            f"{self.away_team_score['goals']}.{self.away_team_score['behinds']}"
            f" ({self.away_team_score['total']})"
        )

    def _update_ball_physics(self, delta_time: float) -> None:
        """Update ball position based on physics"""
        # Update position
        self.ball_position = (
            self.ball_position[0] + self.ball_velocity[0] * delta_time,
            self.ball_position[1] + self.ball_velocity[1] * delta_time
        )

        # Apply gravity to height
        gravity = -9.81  # m/s^2
        self.ball_height = max(0.0, self.ball_height + gravity * delta_time)

        # Check if ball has landed
        if self.ball_height <= 0.0:
            self.ball_height = 0.0
            self.ball_velocity = (0.0, 0.0)

        # Ensure ball stays in bounds
        self.ball_position = (
            max(0, min(self.ball_position[0], self.ground.length)),
            max(0, min(self.ball_position[1], self.ground.width))
        )


    def _update_positions(self) -> List[Dict[str, Any]]:
        #Update all player positions.
        position_updates = []
        player_combine = None
        # Process both teams
        for team_side, team_players in [
            ("home", self.home_team_players),
            ("away", self.away_team_players)
        ]:

            # Update each player's position
            for player_name, player in team_players.items():
                # Skip ball carrier if there is one
                #if player == self.ball_carrier:
                    #continue

                # Get new position from movement engine
                new_position = self.movement_engine.update_player_position(
                    player=player,
                    game_state=self
                )
                player_combine=player.name+"-"+player.position

                position_updates.append({
                    'player_name': player_combine,
                    'team': player.team_side,
                    'position': {
                        'x': new_position[0],
                        'y': new_position[1]
                    }
                })


            position_updates.append({
            'player_name': 'ball',
            'team': None,
            'position': {
                'x': self.ball_position[0],
                'y': self.ball_position[1],
            }
            })

        #print(f"position_updates: {position_updates}")
        return position_updates
    """

    def _update_positions(self) -> List[Dict[str, Any]]:
        #Update and track all player positions.
        position_updates = []

        # Special case for center bounce - position players according to AFL rules
        if self.phase == "center_bounce":
            # Get center of ground
            center_x, center_y = self.ground.get_center()

            # Position the ball at center
            self.ball_position = (center_x, center_y)

            # Get all players from both teams
            home_players = list(self.home_team_players.items())
            away_players = list(self.away_team_players.items())

            # Find Rucks and position them first (they should be at center)


            # Now position remaining players according to their roles
            # Process home team players
            for name, player in home_players:
                # Position based on player role in AFL
                position = self._get_center_bounce_position(player, "home")
                player.current_position = position

                position_updates.append({
                    'player_name': player.name,
                    'team': player.team_side,
                    'position': {
                        'x': position[0],
                        'y': position[1]
                    }
                })
            # Process away team players
            for name, player in away_players:

                # Position based on player role in AFL
                position = self._get_center_bounce_position(player, "away")
                player.current_position = position

                position_updates.append({
                    'player_name': player.name,
                    'team': player.team_side,
                    'position': {
                        'x': position[0],
                        'y': position[1]
                    }
                })

            # Add ball position to updates
            position_updates.append({
                'player_name': 'ball',
                'team': None,
                'position': {
                    'x': center_x,
                    'y': center_y
                }
            })

            return position_updates

        # For non-center bounce phases, use the movement engine (existing logic)
        # Determine team in possession
        team_in_possession = None
        if self.ball_carrier:
            team_in_possession = (
                self.home_team if self.ball_carrier in self.home_team_players.values()
                else self.away_team
            )

        # Get all player positions for congestion calculation
        all_positions = []
        for team_players in [self.home_team_players, self.away_team_players]:
            for player in team_players.values():
                if player.current_position:
                    all_positions.append(player.current_position)

        # Update ground's congestion map
        self.ground.update_congestion(all_positions)

        # Update positions for each team
        for team_players, team in [(self.home_team_players, self.home_team),
                                 (self.away_team_players, self.away_team)]:

            # Get team tactics (assuming they're stored in team object)
            team_tactics = getattr(team, 'tactics', {
                'offense_strategy': 'balanced',
                'defense_strategy': 'balanced'
            })

            for player_name, player in team_players.items():
                if player == self.ball_carrier:
                    continue  # Skip ball carrier as they have special movement rules

                # Use movement engine to calculate new position
                new_position = self.movement_engine._update_player_position(
                    player=player,
                    game_state=self,
                    team_tactics=team_tactics
                )

                # Update player's position
                player.current_position = new_position

                # Add to position updates for broadcast
                position_updates.append({
                    'player_name': player.name,
                    'team': player.team_side,
                    'position': {
                        'x': position[0],
                        'y': position[1]
                    }
                })

        # Add ball position to updates
        if self.ball_position:
            position_updates.append({
                'player_name': 'ball',
                'team': None,
                'position': {
                    'x': self.ball_position[0],
                    'y': self.ball_position[1]
                }
            })

        return position_updates

    def _get_center_bounce_position(self, player, team_side):
        #Get exact coordinates for player position during center bounce based on AFL rules.
        # Get center of ground
        center_x, center_y = self.ground.get_center()

        # Team direction factor (home team attacks right side, away team attacks left)
        team_factor = 1 if team_side == "home" else -1

        # Centre square size (approximately 50m x 50m in ground coordinates)
        square_radius = 10  # Grid cells from center to edge of center square

        # Position players according to their roles
        if player.position == "Ruck":
            if team_side == "home":
                #print(f"Positioning {player.name} for home team")
                return (center_x - random.randint(1, 2), center_y)  # Slightly offset
            else:
                #print(f"Positioning {player.name} for away team")
                return (center_x + random.randint(1, 2), center_y)  # Slightly offset

        elif player.position == "Rover":
            # Rover slightly offset in center square
            return (center_x + (random.randint(2, 4) * team_factor), center_y - random.randint(2, 4))

        elif player.position == "RuckRover":
            # Ruck Rover slightly offset in center square
            return (center_x + (random.randint(2, 4) * team_factor), center_y - random.randint(2, 4))

        elif player.position == "Centre":
            # Centre slightly back from center in square
            return (center_x + (random.randint(2, 4) * team_factor), center_y - random.randint(2, 4))

        elif player.position == "LWing":
            # Left Wing outside square on wing
            return (center_x - random.randint(-2, 2), center_y - 15)

        elif player.position == "RWing":
            # Right Wing outside square on wing
            return (center_x + random.randint(-2, 2), center_y + 15)

        elif player.position == "CHF":
            # Centre Half Forward
            return (center_x + (15 * team_factor), center_y)

        elif player.position == "CHB":
            # Centre Half Back
            return (center_x - (15 * team_factor), center_y)

        elif player.position == "LHF":
            # Left Half Forward
            return (center_x + (15 * team_factor), center_y - 10)

        elif player.position == "RHF":
            # Right Half Forward
            return (center_x + (15 * team_factor), center_y + 10)

        elif player.position == "LHB":
            # Left Half Back
            return (center_x - (15 * team_factor), center_y - 10)

        elif player.position == "RHB":
            # Right Half Back
            return (center_x - (15 * team_factor), center_y + 10)

        elif player.position == "FF":
            # Full Forward
            return (center_x + (25 * team_factor), center_y)

        elif player.position == "FB":
            # Full Back
            return (center_x - (25 * team_factor), center_y)

        elif player.position == "LF":
            # Left Forward Pocket
            return (center_x + (25 * team_factor), center_y - 12)

        elif player.position == "RF":
            # Right Forward Pocket
            return (center_x + (25 * team_factor), center_y + 12)

        elif player.position == "LB":
            # Left Back Pocket
            return (center_x - (25 * team_factor), center_y - 12)

        elif player.position == "RB":
            # Right Back Pocket
            return (center_x - (25 * team_factor), center_y + 12)

        # Default for unrecognized positions
        return (center_x, center_y)
    """

    def get_opponent(self, team):
        if team == self.team2:
            opponent = self.team1
            #print(f"Opponent Team: {self.team1}")
            #print(f"Current Team: {self.team2}")
        else:
            opponent = self.team2
            #print(f"Opponent Team: {self.team2}")
            #print(f"Current Team: {self.team1}")
            #print(f" self.team1_players { self.team1_players}")
        return opponent

    def get_opponent_players(self, team):

        if team == self.team2:
            opponent_players = self.team1_players
            print(f"Opponent players: {opponent_players}")
            print(f"Current players: {self.team2_players}")
        else:
            opponent_players = self.team2_players
            print(f"Opponent players: {opponent_players}")
            print(f"Current players: {self.team1_players}")
            #print(f" self.team1_players { self.team1_players}")

        return opponent_players




# TODO: Implement these classes with proper functionality later
class CommentaryEngine:
    """
    Generates match commentary.
    Potential implementation:
    - Different commentary styles
    - Context-aware comments
    - Player-specific remarks
    - Excitement levels
    - Historical context
    - Multiple commentators
    - Special event commentary
    """
    def __init__(self):
        self.phrases = {
            'mark': [
                "{player} takes a strong grab!",
                "What a mark by {player}!",
                "{player} climbs high and takes a beauty!",
                "Specky from {player}! That'll be on the highlights reel!",
                "{player} shows great hands with that mark."
            ],
            'goal': [
                "{player} slots it through for a major!",
                "GOAL! {player} makes no mistake!",
                "That's six points! Beautiful finish from {player}!",
                "{player} threads the needle for a goal!",
                "The crowd goes wild as {player} kicks truly!"
            ],
            'behind': [
                "Just a minor score for {player}",
                "{player} pushes it a bit wide for a behind",
                "One point as {player}'s shot drifts to the right",
                "Not quite straight enough from {player}",
                "A rushed behind as the defense scrambles"
            ],
            'center_bounce': [
                "We're back in the middle with {ruck1} and {ruck2} going at it",
                "The big men {ruck1} and {ruck2} face off in the center",
                "Back to the center we go, {ruck1} versus {ruck2}",
                "{ruck1} and {ruck2} ready for another ruck contest",
                "The umpire holds the ball aloft as {ruck1} and {ruck2} prepare to do battle"
            ],
            'tackle': [
                "Brilliant tackle from {player}!",
                "{player} wraps them up perfectly!",
                "No getting out of that one! Great tackle by {player}",
                "{player} shows perfect technique in that tackle",
                "Bone-crunching tackle from {player}!"
            ],
            'handball': [
                "Quick hands from {player}",
                "{player} dishes it off nicely",
                "Clever handball from {player}",
                "{player} gets it away under pressure",
                "Slick handball delivery from {player}"
            ],
            'kick': [
                "{player} sends it long",
                "Beautiful kick from {player}",
                "{player} puts it into space",
                "Great field kick by {player}",
                "{player} launches it forward"
            ],
            'hitout': [
                "{player} taps it down perfectly",
                "Great tap work from {player}",
                "{player} gives first use to the rovers",
                "Textbook ruck work from {player}",
                "{player} dominates the hitout"
            ],
            'clearance': [
                "{player} bursts clear from the stoppage",
                "Brilliant clearance work from {player}",
                "{player} extracts it from the congestion",
                "Clean hands in traffic from {player}",
                "{player} breaks through the pack"
            ],
            'quarter_start': [
                "And we're underway in the {quarter} quarter!",
                "The {quarter} quarter begins!",
                "We're back for the {quarter} quarter of this contest",
                "The umpire bounces the ball to start the {quarter} quarter",
                "Here we go for the {quarter} quarter!"
            ],
            'quarter_end': [
                "That's the end of the {quarter} quarter!",
                "The siren sounds to end the {quarter} quarter",
                "{quarter} quarter complete!",
                "And there's the siren! End of the {quarter} quarter",
                "The {quarter} quarter comes to a close"
            ]
        }

        self.jokes = [
            "That kick was so bad it might get nominated for the Brownlow!",
            "He's been quieter than a Collingwood supporter after a Grand Final loss!",
            "That was a bigger flop than the Gold Coast Suns' premiership aspirations!",
            "He's got more space than a Carlton defender in September!",
            "That was more confusing than the AFL's rule changes!",
            "He's been more elusive than a Richmond player at a nightclub!",
            "That was more dramatic than an Eddie McGuire press conference!",
            "He's got more moves than a Dusty Martin don't argue!",
            "That was shakier than Essendon's supplement program!",
            "He's got more pressure than a Fremantle player in front of goal!"
        ]

    def generate_commentary(self, event_type: str, **data: Any) -> str:
        """Generate commentary for an event"""
        if event_type not in self.phrases:
            return ""

        # Get random phrase for this event type
        phrase = random.choice(self.phrases[event_type])

        # Add a joke occasionally
        if random.random() < 0.05:  # 5% chance of a joke
            phrase += " " + random.choice(self.jokes)

        # Format with event data
        try:
            return phrase.format(**data)
        except KeyError:
            return phrase

class EventManager:
        """Handles event creation and broadcasting"""
        def __init__(self, channel_layer, match_group_name):
            self.commentary = CommentaryEngine()
            self.channel_layer = channel_layer
            self.match_group_name = match_group_name

        def create_event(self, event_type: str, quarter: int, event_category: str = "match_event", **data: Any) -> Dict[str, Any]:
            """Create a game event"""
            event = {
                'type': event_category,
                'quarter': quarter,
                'timestamp': time.time(),
                'event_type': event_type,
                'data': data
            }

            # Add commentary if applicable
            commentary = self.commentary.generate_commentary(event_type, **data)
            if commentary:
                event['data']['commentary'] = commentary

            return event

        async def broadcast_event(self, event: Dict[str, Any]) -> None:
            """Broadcast event to all connected clients"""
            try:
                formatted_event = {
                    "type": "send_match_event",
                    "data": event
                }

                await self.channel_layer.group_send(
                    self.match_group_name,
                    formatted_event
                )

            except Exception as e:
                print(f"Broadcast error: {e}")
                print(traceback.format_exc())

class MatchEngine:
    """Main match simulation engine"""
    def __init__(
        self,
        home_team: Any,
        away_team: Any,
        home_team_players: Dict[str, Any],
        away_team_players: Dict[str, Any],
        home_team_tactics: Dict[str, Any],
        away_team_tactics: Dict[str, Any],
        match_group_name: str = None,
        channel_layer: Any = None,
        ground_size: Any = None,
    ):

        if ground_size:
            self.ground_size = ground_size
        else:
            #leangh then width
            self.ground_size = [160, 130]

        self.home_team = home_team
        self.away_team = away_team
        #self.home_team_tactics = home_team_tactics
        #self.away_team_tactics = away_team_tactics

        # Update team tactics
        #self.home_team.update_tactics(home_team_tactics)
        #self.away_team.update_tactics(away_team_tactics)

        stats_slider = 2.0 # 2.0 Stats will be boosted by 100%, capped at 20, then reduced by fatigue, 1.5 Stats will be boosted by 50%

        self.home_team_players = {
            pos: Player(player.name, self.home_team, 'home', pos, player.ability_stats, player.physical_stats, stats_slider)
            for pos, player in home_team_players.items()
        }
        self.away_team_players = {
            pos: Player(player.name, self.away_team, 'away', pos, player.ability_stats, player.physical_stats, stats_slider)
            for pos, player in away_team_players.items()
        }

        self.home_team_tactics = Team(self.home_team.name, home_team_tactics, self.home_team_players, 'home')
        self.away_team_tactics = Team(self.away_team.name, away_team_tactics, self.away_team_players, 'away')

        # Initialize engines
        self.stats_manager = StatsManager()
        self.ground = Ground(self.ground_size)
        self.movement_engine = MovementEngine(self.ground)
        self.game_state = GameState(self.ground, self.movement_engine, self.home_team, self.away_team, self.home_team_players, self.away_team_players, self.home_team_tactics, self.away_team_tactics)

        self.weather_system = WeatherSystem()
        self.event_manager = EventManager(channel_layer, match_group_name)
        self.action_engine = ActionEngine(self.ground, self.game_state, self.stats_manager)
        self.contest_engine = ContestEngine(self.ground, self.action_engine, self.game_state)
        self.possession_engine = PossessionEngine(self.ground, self.game_state, self.stats_manager, self.movement_engine, self.action_engine)





        #self.positions_zones = self.ground.define_dynamic_zones(self.grid_width, self.grid_length)
        for team_players in [self.home_team_players, self.away_team_players]:
            for player in team_players.values():
                # Get the zone for this player's position
                zone = self.ground.positions_zones.get(player.position, [])
                if zone:
                    # Take a central position from the zone
                    player.current_position = zone[len(zone)//2]  # Use middle point of zone
                else:
                    # Fallback to center if position not found
                    player.current_position = self.ground.get_center()
                print(f"Player {player.name} is at {player.current_position}")

        # Track last update time
        self.last_update = time.time()

    async def simulate_match(self) -> Generator[Dict[str, Any], None, None]:
        """Run the match simulation"""
        # Initialize positions
        #self._initialize_positions()

        # Main game loop
        while not self.game_state.is_game_over():
            # Get time delta
            current_time = time.time()
            delta_time = current_time - self.last_update
            self.last_update = current_time

            # Update game clock
            #self.game_state.phase = "center_bounce"
            self.game_state.start_quarter()
            self.game_state.update(delta_time)

            yield self.event_manager.create_event(
                'quarter_start',
                self.game_state.current_quarter,
                event_category='match_event',
                name=self._get_quarter_name(self.game_state.current_quarter)
            )

            async for event in self._simulate_quarter():
                yield event


                # Update and broadcast positions
            #position_event = self._update_positions()
            #if position_event:
                #yield position_event

            # Update and broadcast stats
            player_stats = self.stats_manager.collect_player_stats(
                self.home_team_players,
                self.away_team_players
            )

            yield self.event_manager.create_event(
                'player_stats',
                self.game_state.current_quarter,
                event_category='player_stats',
                player_stats=player_stats
            )

            # Process quarter break
            if not self.game_state.is_game_over():
                self.game_state.next_quarter()
                await self._process_quarter_break()

    async def _simulate_quarter(self) -> Generator[Dict[str, Any], None, None]:
        """Simulate a quarter of play"""
        # Start with center bounce
        current_time = time.time()
        delta_time = current_time - self.last_update

        event = None

        position_event = self._update_positions()
        if position_event:
            print(f"Position event: {position_event}")
            yield position_event

        #Test Phase Count
        phase_count = 0
        while not self.game_state.should_end_quarter():
            # Process current phase

            event = await self.process_phase(self.game_state, event)
            print(f"event {event}")
            if event:
                yield self.event_manager.create_event(
                event_type=event['type'],
                quarter=self.game_state.current_quarter,
                event_category=event['event_category'],
                commentary=event['commentary']  # Pass as keyword argument
            )


            # Update and broadcast positions after each event
            position_event = self._update_positions()
            if position_event:
                print(f"Position event: {position_event}")
                yield position_event

            # Update game state
            self.game_state.update(delta_time)

            phase_count += 1
            print(f"Phase count: {phase_count}")
            # Small delay for real-time simulation
            await asyncio.sleep(0.1)

    async def _process_quarter_break(self) -> None:
        """Process quarter break"""
        # Update stats
        stats = self.stats_manager.collect_player_stats(
            self.team1_players,
            self.team2_players
        )

        # Create stats event
        await self.event_manager.create_event({
            'type': 'player_stats',
            'quarter': self.game_clock.current_quarter,
            'data': {
                'player_stats': stats
            }
        })

        # Short delay
        await asyncio.sleep(1.0)

    def _get_quarter_name(self, quarter: int) -> str:
        """Get quarter name"""
        names = {
            1: "First Quarter",
            2: "Second Quarter",
            3: "Third Quarter",
            4: "Final Quarter"
        }
        return names.get(quarter, f"Quarter {quarter}")


    async def process_phase(self, game_state: Any, event: Optional[Dict[str, Any]] = None) -> Optional[Dict[str, Any]]:
        """Simulate a single contest within the game."""
        result = None
        # Start with center bounce
        print(f"Processing phase: {self.game_state.phase}")
        if self.game_state.phase == "center_bounce":
            result = self.contest_engine.simulate_center_bounce(self.home_team, self.away_team, self.home_team_players, self.away_team_players) # Make sure this is awaited
            if result:
                self.game_state.phase = "handle_possession"
                """
                yield {
                    'type': 'match_event',
                    'quarter': self.quarter,
                    'event': result['result'] if 'result' in result else str(result)
                }"""
                return result


        # Handle different action types
        if self.game_state.phase == 'handle_possession' or self.game_state.phase == "loose_ball":
            result = self.possession_engine.handle_possession()
            #print(f"Result: {result}")
            #time.sleep(10)
            if result:
                return result

        if self.game_state.phase == "advance_ball":
            #self.update_game_state(result['team'])
            print(f"Result/event: {event}")
            #time.sleep(10)
            result = self.action_engine.advance_ball(
                event.get('receiver'),
                event.get('next_position'),
                event.get('disp_type'),
                event.get('disposal_options'),
                event.get('disposer_data')
            )
            if result:
                return result

        if self.game_state.phase == "attempt_goal":
            result = self.action_engine.attempt_goal()
            if result:
                return result

        #elif result['next_action'] in ["kick_in", "out_on_full", "consider_pass"]:
        if self.game_state.phase == "consider_pass" or self.game_state.phase == "out_on_full":
            if self.game_state.phase == "out_on_full":
                print("Phase out on full")
                sys.exit()

            next_position, receiver, action, disposal_options, disposer_data, no_options = self.possession_engine.handle_disposal()
            print(f"Phase State: {self.game_state.phase}")

            if receiver != None and no_options == None:
                #self.game_state.phase = "advance_ball"
                #team_players = self.team2_players if receiver.team == self.team1 else self.team1_players
                print(f"Returnning from consider_pass, receiver not None and no_options is None")

                print(f"Phase State: {self.game_state.phase}")
                result = {
                "commentary": f"{receiver['receiver']} Chosen From {action}",
                "result": f"{receiver['receiver']} Chosen From {action}",
                "next_action": "advance_ball",
                "receiver": receiver,
                "next_position": next_position,
                "disp_type": action,
                "disposal_options": disposal_options,
                "disposer_data": disposer_data,
                "type": "match_event",
                "event_category": "match_event",
                }
                return result
            elif no_options != None:
                print(f"Phase State: {self.game_state.phase}")
                print(f"no_option contains data")
                #sys.exit()

                print(f"Returnning from consider_pass, no_options not None")
                result = {
                "result": f"{no_options['result']}",
                "commentary": f"{no_options['commentary']}",
                "next_action": no_options['next_action'],
                "type": "match_event",
                "event_category": "match_event",
                }
                return result
            else:
                print(f"Returnning from consider_pass, no receiver or no_options")
                sys.exit()
                result = {
                "next_action": "handle_possession"
                }
                return result


        elif result['next_action'] in ["turnover"]:
            print(f"Turnover")
            sys.exit()

        #await asyncio.sleep(0.1)
        """
        yield {
            'type': 'match_event',
            'quarter': self.quarter,
            'event': f"Contest ended after {action_count} actions"
        }
        """
        return None


    def _update_positions(self) -> Optional[Dict[str, Any]]:
        """Update and broadcast position changes using the new movement system."""
        position_updates = self.game_state._update_positions()


        return self.event_manager.create_event(
            'position_update',
            self.game_state.current_quarter,
            'position_update',
            positions=position_updates
            )

class ActionEngine:
    """Handles player actions and decisions, maintaining compatibility with original AI version."""

    def __init__(self, ground: Ground, game_state: GameState, stats_manager: StatsManager):
        self.ground = ground
        self.game_state = game_state
        self.stats_manager = stats_manager



    def advance_ball(self, receiver=None, known_next_position=None, disp_type=None, disposal_options=None, disposer_data=None):

        print(f"Disposer: {self.game_state.ball_carrier}")
        #self.stats_manager.record_stat(disposer.name, 'kicks')
        #time.sleep(0.5)

        if known_next_position:
            print(f"Known Next Position {known_next_position}")
            next_position = known_next_position
            known_next_position=None
        else:
            #TODO: Pass kick/handball distance from PossessionEngine and dont recalculate it here.
            if disp_type == "kick":


                max_kick_cells = disposer_data['kick_range']
                print(f"Max Kick Cells {max_kick_cells}")
                if receiver is None:
                    next_position = self.get_dynamic_possible_positions(self.game_state.ball_carrier.current_position, max_kick_cells)
                    next_position = random.choices(
                    next_position,
                    #weights=[1.2 if pos == "FF" else 1.0 for pos in next_position],
                    k=1
                    )[0]
                else:
                    next_position = receiver.current_position
                # Choose a position based on performance and tactical decision

                print(f"Next Position was not Known, now: {next_position}")
            elif disp_type == "handball":
                if receiver is None:
                    max_handball_cells = disposer_data['handball_range']
                    next_position = self.get_dynamic_possible_positions(self.game_state.ball_carrier.current_position, max_handball_cells)
                    next_position = random.choices(
                    next_position,
                    #weights=[1.2 if pos == "Centre" else 1.0 for pos in next_position],
                    k=1
                    )[0]
                else:
                    next_position = receiver.current_position

                print(f"Next Position not Known {next_position}")

        print(f"Closest Opponent to Ball Carrier {disposer_data['closest_opponent']}")
        print(f"Receiver {receiver}")

        opponent_team = self.game_state.away_team if self.game_state.ball_carrier.team == self.game_state.home_team else self.game_state.home_team
        opponent_players = self.game_state.away_team_players if self.game_state.ball_carrier.team == self.game_state.home_team else self.game_state.home_team_players

           # Find all opponents within reasonable distance of the receiver
        potential_contesters = []
        max_contest_distance = 20
        #ball_travel_distance = math.sqrt(dx*dx + dy*dy) * 2
        ball_travel_distance = receiver['distance']
        highest_pressure = 0.0
        best_contester = None

        for opp in opponent_players.values():
            if receiver:

                dx = receiver['receiver'].current_position[0] - opp.current_position[0]
                dy = receiver['receiver'].current_position[1] - opp.current_position[1]
                print(f"dx {dx} dy {dy}")
                distance_to_receiver = math.sqrt(dx*dx + dy*dy) * 2  # Convert to meters

                if distance_to_receiver <= max_contest_distance:
                    # Calculate if this opponent can contest
                    adjusted_pressure, final_distance, can_contest = self.calculate_contest_pressure(
                        receiver['receiver'], opp, ball_travel_distance, disp_type
                    )

                    # Track the opponent who creates the most pressure
                    if adjusted_pressure == 1.0:
                        highest_pressure = adjusted_pressure
                        print(f"Highest Pressure Adjusted {highest_pressure}")
                        opponent = opp

                    potential_contesters.append({
                        'opponent': opp,
                        'initial_distance': distance_to_receiver,
                        'final_distance': final_distance,
                        'can_contest': can_contest,
                        'pressure': adjusted_pressure
                    })

        # Update positions for all opponents who attempt to contest
        for contest_data in potential_contesters:
            opp = contest_data['opponent']
            final_distance = contest_data['final_distance']
            can_contest = contest_data['can_contest']

            if can_contest or final_distance < 8:  # If they get reasonably close
                # Calculate direction vector from opponent to receiver
                dx = receiver['receiver'].current_position[0] - opp.current_position[0]
                dy = receiver['receiver'].current_position[1] - opp.current_position[1]
                distance = math.sqrt(dx*dx + dy*dy) * 2
                print(f"Distance {distance}")

                if distance > 0 and distance < 6:
                    # Normalize direction vector
                    dx /= distance
                    dy /= distance

                    # Calculate how far the opponent moves
                    move_distance = distance - (final_distance / 2)  # Convert to grid units

                    # Calculate new position
                    new_x = opp.current_position[0] + (dx * move_distance)
                    new_y = opp.current_position[1] + (dy * move_distance)

                    # Update opponent position
                    opp.current_position = (int(new_x), int(new_y))
                    print(f"Moved opponent {opp.name} to {opp.current_position}")

        # Use no pressure for the contest
        if highest_pressure == 0.0:
            print(f"No opponent found, {receiver['receiver'].name} advances unopposed!")
            if random.random() < 0.005:  # 2% chance of a risky move
                risky_event = random.choice([
                    f"{receiver['receiver'].name} tries a fancy trick shot!",
                    f"{receiver['receiver'].name} does a blind turn and risks losing balance!",
                    f"{receiver['receiver'].name} decides to show off with a banana kick!"
                ])
                print(f"Risky Event: {risky_event}")
                #self.events.append({'quarter': self.quarter, 'event': risky_event})

                if random.random() < 0.5:  # 50% chance of success
                    print(f"{receiver['receiver'].name} successfully pulls off the risky move!")
                    self.game_state.ball_position = next_position
                    #self.ball_position_name = self.get_ball_position()
                    self.stats_manager.record_stat(self.disposer.name, disp_type.lower()+'s')
                    self.game_state.ball_carrier = receiver['receiver']
                    self.game_state.ball_carrier.current_position = next_position
                    #self.disposer.current_position = next_position
                    goal_distance = self.ground.is_in_range_for_shot(self.game_state.ball_position, self.game_state.ball_carrier.team_side)
                    receiver=None
                    known_next_position=None
                    disp_type=None
                    disposal_options=None
                    disposer_data=None
                    if goal_distance:
                        self.game_state.phase = "attempt_goal"
                    else:
                        self.game_state.phase = "handle_possession"
                    return {
                        "result": f"{risky_event} ...and pulls it off!",
                        "commentary": f"{risky_event} ...and pulls it off!",
                        "next_action": "handle_possession",
                        "type":"match_event",
                        "event_category":"match_event",
                    }
                else:
                    print(f"{receiver.name} fails the risky move and fumbles the ball!")
                    self.game_state.ball_position = next_position
                    opponent = disposer_data['closest_opponent']
                    #self.mirrored_position = self.get_mirrored_position(self.ball_position)
                    #self.ball_position = self.mirrored_position
                    self.game_state.ball_position = next_position
                    self.stats_manager.record_stat(opponent.name, 'turnovers_won')
                    #self.ball_position_name = self.get_ball_position()
                    self.game_state.ball_carrier = opponent
                    self.game_state.ball_carrier.current_position = next_position
                    #self.disposer.current_position = self.ball_position_name
                    goal_distance = self.ground.is_in_range_for_shot(self.game_state.ball_position, self.game_state.ball_carrier.team_side)
                    receiver=None
                    known_next_position=None
                    disp_type=None
                    disposal_options=None
                    disposer_data=None
                    if goal_distance:
                        self.game_state.phase = "attempt_goal"
                    else:
                        self.game_state.phase = "handle_possession"
                    return {
                        "result": f"{receiver.name} fails the risky move and fumbles the ball!",
                        "commentary": f"{receiver.name} fails the risky move and fumbles the ball!",
                        "next_action": "handle_possession",
                        "type":"match_event",
                        "event_category":"match_event",
                    }
            else:
                self.game_state.ball_position = next_position
                #self.ball_position_name = self.get_ball_position()
                print(f"Ball Position {self.game_state.ball_position}")
                #time.sleep(1)
                disposer = self.game_state.ball_carrier
                self.stats_manager.record_stat(self.game_state.ball_carrier.name, disp_type.lower()+'s')
                self.game_state.ball_carrier = receiver['receiver']
                self.game_state.ball_carrier.current_position = next_position
                goal_distance = self.ground.is_in_range_for_shot(self.game_state.ball_position, self.game_state.ball_carrier.team_side)
                receiver=None
                known_next_position=None
                disp_type=None
                disposal_options=None
                disposer_data=None
                if goal_distance:
                    self.game_state.phase = "attempt_goal"
                else:
                    self.game_state.phase = "handle_possession"
                return {
                        "result": f"{self.game_state.ball_carrier} Has no opponent so play on",
                        "commentary": f"{self.game_state.ball_carrier} Has no opponent so takes possession, disposer was: {disposer.name}.",
                        "next_action": "handle_possession",
                        "type":"match_event",
                        "event_category":"match_event",
                    }

        """
        if disposal_options[0]['direction'] == "backward":
            # Increase pass success probability for backward passes
            #receiver_performance *= 1.2
            print(f"Backward pass from {self.game_state.ball_carrier.current_position} to {next_position}, increased receiver performance")
            receiver_performance = 1
            opponent_performance = 0
        else:
            print("Pass not backwars")
                    """
        receiver_performance, opponent_performance = self.possession_contest(receiver['receiver'], disposer_data['closest_opponent'])

        Current_team_tactics = self.game_state.home_team_tactics.tactics if self.game_state.ball_carrier.team == self.game_state.home_team else self.game_state.away_team_tactics.tactics
        opponent_team_tactics = self.game_state.away_team_tactics.tactics if self.game_state.ball_carrier.team == self.game_state.home_team else self.game_state.home_team_tactics.tactics
        print(f"Current Team Tactics {Current_team_tactics}")
        print(f"Opponent Team Tactics {opponent_team_tactics}")

        receiver_performance = Team.Tactics_handler (self.game_state.ball_carrier.team, next_position, receiver, disposer_data['closest_opponent'].team, receiver_performance, opponent_performance, Current_team_tactics, opponent_team_tactics)
        print(f"rec Performance {receiver_performance} Opp Performomarnce {opponent_performance} ")
        #opponent_performance = Team.Tactics_handler (team, self.ball_position, receiver, opponent_team, receiver_performance, opponent_performance, opponent_team_tactics)

        if random.random() < 0.005:  # 2% chance of a unique or fun event
            unique_event = random.choice([
                f"{receiver['receiver'].name} is temporarily blinded by the sun, misjudging the ball!",
                "A seagull swoops down and distracts the players!",
                "A sudden gust of wind changes the ball's trajectory!",
                f"The crowd starts chanting {receiver['receiver'].name}'s name, boosting his confidence!",
                f"{receiver['receiver'].name} is distracted by a rogue balloon floating onto the field!",
                f"{receiver['receiver'].name} decides to taunt the opponent, losing focus!",
                f"{receiver['receiver'].name} is distracted by a streaker on the field!",
                f"{receiver['receiver'].name} takes a moment to adjust his boots, risking a turnover!",
                f"{receiver['receiver'].name} gets caught in a spider-web of passes and fumbles the ball!",
                "A gust of wind blows the ball off course, surprising everyone!"
            ])
            print(f"Unique Event: {unique_event}")
            #event = {'quarter': self.quarter, 'event': unique_event}
            #self.events.append({'quarter': self.quarter, 'event': unique_event})
            #self.broadcast_event(event)
            #time.sleep(30)

            # Implement the effects of the unique event
            if "blinded by the sun" in unique_event or "distracted" in unique_event:
                print(f"{receiver['receiver'].name} misjudges the ball due to the distraction!")
                turnover_probability = (
                    opponent.ability_stats.tackling * 1.5 +
                    opponent.ability_stats.mental * 0.3 +
                    opponent.ability_stats.tactical * 0.3
                ) / 60 + random.uniform(0.01, 0.5)

                if random.random() < turnover_probability:
                    print(f"{opponent['opponent'].name} capitalizes on the mistake, causing a turnover!")
                    self.game_state.ball_position = next_position
                    self.stats_manager.record_stat(opponent.name, 'turnovers_won')
                    #self.mirrored_position = self.get_mirrored_position(self.ball_position)
                    #self.ball_position = self.mirrored_position
                    #self.ball_position_name = self.get_ball_position()
                    self.game_state.ball_carrier = opponent
                    self.game_state.ball_carrier.current_position = next_position
                    #self.disposer.current_position = self.ball_position_name
                    goal_distance = self.ground.is_in_range_for_shot(self.game_state.ball_position, self.game_state.ball_carrier.team_side)
                    receiver=None
                    known_next_position=None
                    disp_type=None
                    disposal_options=None
                    disposer_data=None
                    if goal_distance:
                        self.game_state.phase = "attempt_goal"
                    else:
                        self.game_state.phase = "handle_possession"
                    return {
                        "result": f"{opponent.name} capitalizes on the mistake, causing a turnover!",
                        "commentary": f"{unique_event} ...and {opponent.name} causes a turnover!",
                        "next_action": "handle_possession",
                        "type":"match_event",
                        "event_category":"match_event",
                    }
                else:
                    print(f"{receiver['receiver'].name} recovers just in time and retains possession!")
                    self.game_state.ball_position = next_position
                    self.stats_manager.record_stat(receiver['receiver'].name, 'marks')
                    #self.ball_position_name = self.get_ball_position()
                    self.game_state.ball_carrier = receiver['receiver']
                    self.game_state.ball_carrier.current_position = next_position
                    #self.disposer.current_position = self.ball_position_name
                    goal_distance = self.ground.is_in_range_for_shot(self.game_state.ball_position, self.game_state.ball_carrier.team_side)
                    receiver=None
                    known_next_position=None
                    disp_type=None
                    disposal_options=None
                    disposer_data=None
                    if goal_distance:
                        self.game_state.phase = "attempt_goal"
                    else:
                        self.game_state.phase = "handle_possession"
                    return {
                        "result": f"{receiver['receiver'].name} recovers just in time and retains possession!",
                        "commentary": f"{unique_event} ...and {receiver['receiver'].name} recovers just in time and keeps possession!",
                        "next_action": "handle_possession",
                        "type":"match_event",
                        "event_category":"match_event",
                    }
            elif "boosting his confidence" in unique_event:
                # Temporarily boost receiver's performance
                receiver_performance *= 1.5
                print(f"{receiver['receiver'].name}'s performance is temporarily boosted!")

            # Example: Mental stats affecting response to distractions
            #if "distracted" in unique_event:
                #distraction_penalty = max(0.2, 1 - (receiver['receiver'].ability_stats.mental * 0.05))
                #receiver_performance *= distraction_penalty
                #print(f"{receiver['receiver'].name}'s performance is reduced due to distraction: {receiver_performance}")

            # Introduce player fatigue and errors
        fatigue_effect = receiver['receiver'].fatigue * 0.02
        if random.random() < fatigue_effect:
            print(f"{receiver['receiver'].name} makes a fatigue-induced error! Fatigue Effect {fatigue_effect}")
            #self.stats_manager.record_stat(receiver.name, 'turnovers_won')
            #self.position = self.get_ball_position()
            #self.disposer = opponent.name
            #return self.handle_possession(opponent_team, opponent_players)

        #print(f"Disp type {disp_type}")
        if disp_type=="kick":
            # Determine the outcome of the contest
            if receiver_performance > opponent_performance:
                # Calculate the probability of dropping the mark based on the receiver's attributes
                print (f"Receiver Performance {receiver_performance} Opponent Performance {opponent_performance}")
                drop_mark_probability = (
                        receiver['receiver'].ability_stats.marking * 1.5 +
                        receiver['receiver'].ability_stats.consistency * 0.7 +
                        receiver['receiver'].ability_stats.mental * 0.6
                    ) / 60 + random.uniform(0.01, 0.02)
                if disposal_options[0]['direction'] == "backward":
                    # Decrease the probability of dropping the mark for backward passes
                    drop_mark_probability *= 0.2  # Reduce by 80%
                    print(f"Drop Mark Prob adjusted for backward pass: {drop_mark_probability}")

                print(f"Drop Mark Prob: {drop_mark_probability}")
                if random.random() < drop_mark_probability:  # Receiver might drop the mark
                    turnover_probability = (
                        opponent.ability_stats.tackling * 1.5 +
                        opponent.ability_stats.mental * 0.3 +
                        opponent.ability_stats.tactical * 0.3
                    ) / 60 + random.uniform(0.01, 0.5)
                    print(f"Turnover Prob: {turnover_probability}")
                    #if disposal_options[0]['direction'] == "backward":
                        # Decrease the probability of dropping the mark for backward passes
                        #print(f"Drops mark but Backwars pass so continue")
                        #self.game_state.ball_position = next_position
                        #self.stats_manager.record_stat(self.game_state.ball_carrier.name, 'kicks')
                        #self.game_state.ball_carrier = receiver['receiver']
                        #self.game_state.ball_carrier.current_position = next_position
                        #return {
                        #    "result": f"{receiver['receiver'].name} Drops mark but Backwars pass so continue",
                        #    "commentary": f"{receiver['receiver'].name} Drops mark but Backwars pass so continue",
                        #    "next_action": "handle_possession",
                        #}
                    if random.random() < turnover_probability:  # Opponent might cause a turnover
                        print(f"{opponent.name} tackles {receiver['receiver'].name}, causing a turnover")
                        self.game_state.ball_position = next_position
                        #self.mirrored_position = self.get_mirrored_position(self.ball_position)
                        #self.ball_position = self.mirrored_position
                        self.stats_manager.record_stat(self.game_state.ball_carrier.name, 'kicks')
                        self.stats_manager.record_stat(opponent.name, 'turnovers_won')
                        self.game_state.ball_carrier = opponent
                        self.game_state.ball_carrier.current_position = next_position
                        goal_distance = self.ground.is_in_range_for_shot(self.game_state.ball_position, self.game_state.ball_carrier.team_side)
                        receiver=None
                        known_next_position=None
                        disp_type=None
                        disposal_options=None
                        disposer_data=None
                        if goal_distance:
                            self.game_state.phase = "attempt_goal"
                        else:
                            self.game_state.phase = "handle_possession"
                        return {
                            "result": f"{opponent.name} tackles {self.game_state.ball_carrier.name}, causing a turnover",
                            "commentary": f"{opponent.name} tackles {self.game_state.ball_carrier.name}, causing a turnover",
                            "next_action": "handle_possession",
                            "type":"match_event",
                            "event_category":"match_event",
                        }
                    else:
                        print(f"{receiver['receiver'].name} drops the mark but holds possession")
                        self.game_state.ball_position = next_position
                        self.stats_manager.record_stat(self.game_state.ball_carrier.name, 'kicks')
                        self.game_state.ball_carrier = receiver['receiver']
                        self.game_state.ball_carrier.current_position = next_position
                        goal_distance = self.ground.is_in_range_for_shot(self.game_state.ball_position, self.game_state.ball_carrier.team_side)
                        receiver=None
                        known_next_position=None
                        disp_type=None
                        disposal_options=None
                        disposer_data=None
                        if goal_distance:
                            self.game_state.phase = "attempt_goal"
                        else:
                            self.game_state.phase = "handle_possession"
                        return {
                            "result": f"{self.game_state.ball_carrier.name} drops the mark but holds possession",
                            "commentary": f"{self.game_state.ball_carrier.name} drops the mark but holds possession",
                            "next_action": "handle_possession",
                            "type":"match_event",
                            "event_category":"match_event",
                        }
                else:
                    print(f"{receiver['receiver'].name} successfully marks the ball")
                    self.stats_manager.record_stat(receiver['receiver'].name, 'marks')
                    self.stats_manager.record_stat(self.game_state.ball_carrier.name, 'kicks')
                    self.game_state.ball_position = next_position
                    print("Return from update_ball_position")
                    print(f"ball position {self.game_state.ball_position}")
                    #self.game_state.ball_position_name = self.get_ball_position()
                    self.game_state.ball_carrier = receiver['receiver']
                    self.game_state.ball_carrier.current_position = next_position
                    goal_distance = self.ground.is_in_range_for_shot(self.game_state.ball_position, self.game_state.ball_carrier.team_side)
                    receiver=None
                    known_next_position=None
                    disp_type=None
                    disposal_options=None
                    disposer_data=None
                    if goal_distance:
                        self.game_state.phase = "attempt_goal"
                    else:
                        self.game_state.phase = "handle_possession"
                    return {
                            "result": f"{self.game_state.ball_carrier.name} successfully marks the ball",
                            "commentary": f"{self.game_state.ball_carrier.name} successfully marks the ball",
                            "next_action": "handle_possession",
                            "type":"match_event",
                            "event_category":"match_event",
                        }
            else:
                interception_probability = (
                    opponent.ability_stats.marking * 0.8 +
                    opponent.ability_stats.mental * 0.3 +
                    opponent.ability_stats.tactical * 0.3
                ) / 60 + random.uniform(0.01, 1.0)
                print(f"intercept Prob: {interception_probability}")
                if random.random() < interception_probability:  # Opponent might intercept the mark
                    print(f"{opponent.name} intercepts the mark, causing a turnover")
                    self.game_state.ball_position = next_position
                    #self.mirrored_position = self.get_mirrored_position(self.ball_position)
                    #self.game_state.ball_position = self.mirrored_position
                    self.stats_manager.record_stat(self.game_state.ball_carrier.name, 'kicks')
                    self.stats_manager.record_stat(opponent.name, 'marks')
                    self.stats_manager.record_stat(opponent.name, 'interceptions_won')
                    #self.game_state.ball_position_name = self.get_ball_position()
                    self.game_state.ball_carrier = opponent
                    self.game_state.ball_carrier.current_position = next_position
                    #self.game_state.ball_carrier.current_position = self.game_state.ball_position_name
                    goal_distance = self.ground.is_in_range_for_shot(self.game_state.ball_position, self.game_state.ball_carrier.team_side)
                    receiver=None
                    known_next_position=None
                    disp_type=None
                    disposal_options=None
                    disposer_data=None
                    if goal_distance:
                        self.game_state.phase = "attempt_goal"
                    else:
                        self.game_state.phase = "handle_possession"
                    return {
                        "result": f"{opponent.name} intercepts the mark, causing a turnover",
                        "commentary": f"{opponent.name} intercepts the mark, causing a turnover",
                        "next_action": "handle_possession",
                        "type":"match_event",
                        "event_category":"match_event",
                    }
                else:
                    print(f"{opponent.name} fails to intercept, {receiver['receiver'].name} holds possession")
                    self.game_state.ball_position = next_position
                    print("Return from update_ball_position")
                    print(f"ball position {self.game_state.ball_position}")
                    self.stats_manager.record_stat(self.game_state.ball_carrier.name, 'kicks')
                    #self.game_state.ball_position_name = self.get_ball_position()
                    self.game_state.ball_carrier = receiver['receiver']
                    self.game_state.ball_carrier.current_position = next_position
                    goal_distance = self.ground.is_in_range_for_shot(self.game_state.ball_position, self.game_state.ball_carrier.team_side)
                    receiver=None
                    known_next_position=None
                    disp_type=None
                    disposal_options=None
                    disposer_data=None
                    if goal_distance:
                        self.game_state.phase = "attempt_goal"
                    else:
                        self.game_state.phase = "handle_possession"
                    return {
                            "result": f"{opponent.name} fails to intercept, {self.game_state.ball_carrier.name} holds possession",
                            "commentary": f"{opponent.name} fails to intercept, {self.game_state.ball_carrier.name} holds possession",
                            "next_action": "handle_possession",
                            "type":"match_event",
                            "event_category":"match_event",
                        }

        if disp_type=="handball":
            if receiver_performance > opponent_performance:
                if disposal_options[0]['direction'] == "backward":
                        # Decrease the probability of dropping the mark for backward passes
                        print(f"Drops mark but Backwars pass so continue")
                        self.game_state.ball_position = next_position
                        self.stats_manager.record_stat(self.game_state.ball_carrier.name, 'handballs')
                        self.game_state.ball_carrier = receiver['receiver']
                        self.game_state.ball_carrier.current_position = next_position
                        goal_distance = self.ground.is_in_range_for_shot(self.game_state.ball_position, self.game_state.ball_carrier.team_side)
                        receiver=None
                        known_next_position=None
                        disp_type=None
                        disposal_options=None
                        disposer_data=None
                        if goal_distance:
                            self.game_state.phase = "attempt_goal"
                        else:
                            self.game_state.phase = "handle_possession"
                        return {
                                "result": f"{self.game_state.ball_carrier.name} Drops mark but Backwars pass so continue",
                                "commentary": f"{self.game_state.ball_carrier.name} Drops mark but Backwars pass so continue",
                                "next_action": "handle_possession",
                                "type":"match_event",
                                "event_category":"match_event",
                            }
                else:
                        print(f"{receiver['receiver'].name} Takes possession")
                        self.game_state.ball_position = next_position
                        self.stats_manager.record_stat(self.game_state.ball_carrier.name, 'handballs')
                        self.game_state.ball_carrier = receiver['receiver']
                        self.game_state.ball_carrier.current_position = next_position
                        goal_distance = self.ground.is_in_range_for_shot(self.game_state.ball_position, self.game_state.ball_carrier.team_side)
                        receiver=None
                        known_next_position=None
                        disp_type=None
                        disposal_options=None
                        disposer_data=None
                        if goal_distance:
                            self.game_state.phase = "attempt_goal"
                        else:
                            self.game_state.phase = "handle_possession"
                        return {
                                "result": f"{self.game_state.ball_carrier.name} Takes possession",
                                "commentary": f"{self.game_state.ball_carrier.name} Takes possession",
                                "next_action": "handle_possession",
                                "type":"match_event",
                                "event_category":"match_event",
                            }
            else:
                interception_probability = (
                    opponent.ability_stats.marking * 0.8 +
                    opponent.ability_stats.mental * 0.3 +
                    opponent.ability_stats.tactical * 0.3
                ) / 60 + random.uniform(0.01, 1.0)
                print(f"intercept Prob: {interception_probability}")
                if random.random() < interception_probability:  # Opponent might intercept the mark
                    print(f"{opponent.name} intercepts the handball, causing a turnover")
                    self.game_state.ball_position = opponent.current_position
                    #self.mirrored_position = self.get_mirrored_position(self.ball_position)
                    #self.game_state.ball_position = self.mirrored_position
                    #self.stats_manager.record_stat(opponent.name, 'marks')
                    self.stats_manager.record_stat(self.game_state.ball_carrier.name, 'handballs')
                    self.stats_manager.record_stat(opponent.name, 'interceptions_won')
                    #self.ball_position_name = self.get_ball_position()
                    self.game_state.ball_carrier = opponent
                    self.game_state.ball_carrier.current_position = next_position
                    #self.game_state.ball_carrier.current_position = self.game_state.ball_position_name
                    goal_distance = self.ground.is_in_range_for_shot(self.game_state.ball_position, self.game_state.ball_carrier.team_side)
                    receiver=None
                    known_next_position=None
                    disp_type=None
                    disposal_options=None
                    disposer_data=None
                    if goal_distance:
                        self.game_state.phase = "attempt_goal"
                    else:
                        self.game_state.phase = "handle_possession"
                    return {
                        "result": f"{opponent.name} intercepts the handball, causing a turnover",
                        "commentary": f"{opponent.name} intercepts the handball, causing a turnover",
                        "next_action": "handle_possession",
                        "type":"match_event",
                        "event_category":"match_event",
                    }
                else:
                    print(f"{opponent.name} fails to intercept, {receiver['receiver'].name} holds possession")
                    self.stats_manager.record_stat(self.game_state.ball_carrier.name, 'handballs')
                    self.game_state.ball_position = next_position
                    print("Return from update_ball_position")
                    print(f"ball position {self.game_state.ball_position}")
                    #self.ball_position_name = self.get_ball_position()
                    self.game_state.ball_carrier = receiver['receiver']
                    self.game_state.ball_carrier.current_position = next_position
                    goal_distance = self.ground.is_in_range_for_shot(self.game_state.ball_position, self.game_state.ball_carrier.team_side)
                    if goal_distance:
                        self.game_state.phase = "attempt_goal"
                    else:
                        self.game_state.phase = "handle_possession"
                    return {
                            "result": f"{opponent.name} fails to intercept, {receiver['receiver'].name} holds possession",
                            "commentary": f"{opponent.name} fails to intercept, {receiver['receiver'].name} holds possession",
                            "next_action": "handle_possession",
                            "type":"match_event",
                            "event_category":"match_event",
                        }


    def possession_contest(self, receiver, opponent):
            print("Simulating possession contest")

            receiver_weights = {
                'marking': 1.5,
                'strength': 1.4,
                'consistency': 1.3,
                'agility': 1.2,
                'age': 1.1,
                'stamina': 1.0
            }

            opponent_weights = {
                'tackling': 1.5,
                'strength': 1.4,
                'consistency': 1.3,
                'speed': 1.2,
                'age': 1.1,
                'stamina': 1.0
            }
            print(f"Reciever {receiver} Opponent {opponent}")
            receiver_performance = self.calculate_performance(receiver, receiver_weights)
            opponent_performance = self.calculate_performance(opponent, opponent_weights)

            print(f"{receiver.name} performance: {receiver_performance:.2f}")
            print(f"{opponent.name} performance: {opponent_performance:.2f}")

            return receiver_performance, opponent_performance

    def calculate_performance(self, player, weights):
        performance = 0
        for attribute, weight in weights.items():
            performance += getattr(player.ability_stats, attribute, 0) * weight
            performance += getattr(player.physical_stats, attribute, 0) * weight

        random_factor = random.uniform(0.8, 1.2)  # Random factor between 0.8 and 1.2
        performance *= random_factor

        print(f"Calculated performance for {player.name}: {performance:.2f}")
        return performance


        # Add this function to calculate if an opponent can reach the contest
    def calculate_contest_pressure(self, receiver, opponent, ball_travel_distance, disposal_type):
        """
        Calculate if an opponent can reach the contest point and what the resulting pressure will be.

        Args:
            receiver: The player receiving the ball
            opponent: The closest opponent to the receiver
            ball_travel_distance: Distance the ball will travel in meters
            disposal_type: "kick" or "handball"

        Returns:
            tuple: (adjusted_pressure, new_opponent_distance, can_contest)
        """
        if not opponent:
            return 0.0, float('inf'), False

        # Get current positions
        receiver_pos = receiver.current_position
        opponent_pos = opponent.current_position

        # Calculate current distance between opponent and receiver
        dx = receiver_pos[0] - opponent_pos[0]
        dy = receiver_pos[1] - opponent_pos[1]
        current_distance = math.sqrt(dx*dx + dy*dy) * 2  # Convert to meters

        # Calculate time the ball will take to travel
        # Kicks travel faster than handballs
        ball_speed = 10.0 if disposal_type == "kick" else 5.0  # meters per second
        ball_travel_time = ball_travel_distance / ball_speed  # seconds

        # Calculate opponent's movement capabilities
        speed_rating = opponent.physical_stats.speed / 20.0  # 0.0 to 1.0
        stamina_rating = opponent.physical_stats.stamina / 20.0  # 0.0 to 1.0
        fatigue_factor = 1.0 - (opponent.fatigue / 100.0)  # 1.0 to 0.0 as fatigue increases

        # Base speed in meters per second (top AFL players run ~9m/s)
        base_speed = 6.0 + (speed_rating * 3.0)  # 6-9 m/s based on speed rating

        # Adjust for stamina and fatigue
        effective_speed = base_speed * stamina_rating * fatigue_factor

        # Calculate how far the opponent can move in the time the ball is traveling
        max_distance_covered = effective_speed * ball_travel_time

        # Calculate if opponent can reach the contest
        if max_distance_covered >= current_distance:
            # Opponent can reach the contest point
            can_contest = True
            # Calculate how much time they have to set up (earlier arrival = more pressure)
            time_to_spare = (max_distance_covered - current_distance) / effective_speed

            # Calculate final distance (could be 0 if they reach exactly)
            final_distance = 0.0

            # Adjust pressure based on time to spare
            if time_to_spare > 1.0:  # More than 1 second to set up
                adjusted_pressure = 1.0  # Maximum pressure
            elif time_to_spare > 0.5:  # 0.5-1 second to set up
                adjusted_pressure = 0.8
            elif time_to_spare > 0.2:  # 0.2-0.5 second to set up
                adjusted_pressure = 0.5
            else:  # Just barely making it
                adjusted_pressure = 0.2
        else:
            # Opponent cannot fully reach the contest point
            can_contest = False
            # Calculate how close they'll get
            final_distance = current_distance - max_distance_covered

            # Convert final distance to pressure
            if final_distance >= 8:
                adjusted_pressure = 0.0
            elif final_distance >= 6:
                adjusted_pressure = 0.2
            elif final_distance >= 4:
                adjusted_pressure = 0.5
            elif final_distance >= 2:
                adjusted_pressure = 0.8
            else:
                adjusted_pressure = 1.0

        print(f"Opponent {opponent.name}, position {opponent.current_position}, distance: {current_distance:.1f}m")
        print(f"Ball travel distance: {ball_travel_distance:.1f}m at {ball_speed:.1f}m/s ({ball_travel_time:.2f}s)")
        print(f"Opponent speed: {effective_speed:.1f}m/s, can cover: {max_distance_covered:.1f}m")
        print(f"Can contest: {can_contest}, final distance: {final_distance:.1f}m")
        print(f"Adjusted pressure: {adjusted_pressure}")

        return adjusted_pressure, final_distance, can_contest


    def attempt_goal(self):
        print(f"{self.game_state.ball_carrier} attempting goal from {self.game_state.ball_position}")


        min_kick_distance_base = 17  # Base minimum kick distance
        min_kick_variation = 2      # Variation range (+/-)
        max_kick_distance_base = 55  # Base maximum kick distance
        max_kick_variation = 5      # Variation range (+/-)

        # Apply randomness to min and max distances
        min_kick_distance = min_kick_distance_base + random.uniform(-min_kick_variation, min_kick_variation)
        max_kick_distance = max_kick_distance_base + random.uniform(-max_kick_variation, max_kick_variation)

        # Calculate player's kick distance based on ability
        kicking_ability = self.game_state.ball_carrier.ability_stats.goal_kicking
        kick_distance = min_kick_distance + ((kicking_ability - 1) / 19) * (max_kick_distance - min_kick_distance)

        # Apply small random variation to final distance (to simulate day-to-day variation)
        kick_variation = 0.1  # 10% variation
        kick_distance *= random.uniform(1 - kick_variation, 1 + kick_variation)


        # Calculate the goal kicking performance
        performance = self.calculate_goal_kicking_performance(self.game_state.ball_carrier)


        distance_to_goal = self.ground.get_distance_to_goal(self.game_state.ball_position, self.game_state.ball_carrier.team_side)
        angle_to_goal = self.ground._calculate_shot_angle(self.game_state.ball_position, self.game_state.ball_carrier.team_side)


        print(f"Distance to goal: {distance_to_goal}, Angle to goal: {angle_to_goal}")
        if kick_distance >= distance_to_goal:
            # Adjust difficulty based on position
            if distance_to_goal >= 55 and angle_to_goal >= 30 and kick_distance >= 55:
                performance *= 0.20

            # Adjust difficulty based on position
            if distance_to_goal >= 55 and angle_to_goal < 30 and kick_distance >= 55:
                performance *= 0.35

            if distance_to_goal > 49 and distance_to_goal < 55 and angle_to_goal >= 30 and kick_distance >= 49:
                performance *= 0.55  #

            if distance_to_goal > 49 and distance_to_goal < 55 and angle_to_goal < 30 and kick_distance >= 49:
                performance *= 0.70

            if distance_to_goal > 30 and distance_to_goal <= 49 and angle_to_goal >= 30 and kick_distance >= 30:
                performance *= 0.85

            if distance_to_goal > 30 and distance_to_goal <= 49 and angle_to_goal < 30 and kick_distance >= 30:
                performance *= 0.95

            if distance_to_goal <= 30 and angle_to_goal >= 30 and kick_distance <= 30:
                performance *= 1.0

            if distance_to_goal <= 30 and angle_to_goal < 30 and kick_distance <= 30:
                performance *= 1.2

            print(f"Performance: {performance}")
            print(f"Kick distance: {kick_distance}")


            # Goal attempt
            if performance > random.uniform(50, 120):
                print(f"{self.game_state.ball_carrier.name} scores a goal!")
                self.update_score(self.game_state.ball_carrier.team_side, "goal")
                self.game_state.ball_position = self.get_center_of_ground()
                self.stats_manager.record_stat(self.game_state.ball_carrier.name, 'goals')
                self.game_state.phase = "goal"

                print(f"Return from Goal")

                return {"result": f"{self.game_state.ball_carrier.name} scores a goal!",
                        "commentary": f"{self.game_state.ball_carrier.name} scores a goal!",
                        "next_action": "center_bounce",
                        "type":"match_event",
                        "event_category":"match_event",
                        }

            else:
                # Consider random events like skewing the kick
                if random.random() < 0.1:  # 10% chance of kicking out on the full
                    print(f"{self.game_state.ball_carrier.name}'s kick skewed off the foot, out on the full")
                    possible_positions = ["LB", "RB"]
                    ball_position = random.choice(possible_positions)
                    self.game_state.ball_position = ball_position
                    self.game_state.phase = "out_on_full"

                    print(f"Return from out on the full")

                    return {"result": f"{self.game_state.ball_carrier.name}'s kick skewed off the foot, out on the full",
                            "commentary": f"{self.game_state.ball_carrier.name}'s kick skewed off the foot, out on the full",
                            "next_action": "out_on_full",
                            "type":"match_event",
                            "event_category":"match_event",
                            }
                else:
                    print(f"{self.game_state.ball_carrier.name} scores a behind")
                    self.update_score(self.game_state.ball_carrier.team_side, "behind")
                    ball_position = "FB"
                    self.game_state.ball_position = ball_position
                    self.stats_manager.record_stat(self.game_state.ball_carrier.name, 'behinds')
                    self.game_state.phase = "kick_in"

                    print(f"Return from behind")

                    return {"result": f"{self.game_state.ball_carrier.name} scores a behind",
                            "commentary": f"{self.game_state.ball_carrier.name} scores a behind",
                            "next_action": "kick_in",
                            "type":"match_event",
                            "event_category":"match_event",
                            }

        else:
            self.game_state.phase = "consider_pass"
            print(f"Return from consider pass")
            # Consider passing the ball instead of attempting a difficult goal
            return {
                    "result": f"{self.game_state.ball_carrier.name} Choose to pass instead of attempt goal from {self.game_state.ball_position}",
                    "commentary": f"{self.game_state.ball_carrier.name} Choose to pass instead of attempt goal from {self.game_state.ball_position}",
                    "next_action": "consider_pass",
                    "player": self.game_state.ball_carrier,
                    "position": self.game_state.ball_position,
                    "type":"match_event",
                    "event_category":"match_event",
                }

    def calculate_goal_kicking_performance(self, player):
        goal_kicking_weight = 1.5
        mental_weight = 1.4
        strength_weight = 1.3
        consistency_weight = 1.2
        age_weight = 1.1
        stamina_weight = 1.0
        fatigue_weight = 1.0

        performance = (
            (player.ability_stats.goal_kicking * goal_kicking_weight) +
            (player.ability_stats.mental * mental_weight) +
            (player.physical_stats.strength * strength_weight) +
            (player.ability_stats.consistency * consistency_weight) +
            (player.physical_stats.age * age_weight) +
            (player.physical_stats.stamina * stamina_weight) +
            (player.fatigue * fatigue_weight)
        )

        # Add a small random factor to simulate unpredictability
        random_factor = random.uniform(0.9, 1.1)
        performance *= random_factor

        print(f"Calculated goal kicking performance for {player.name}: {performance:.2f}")

        return performance




"""
    def process_action(
        self,
        player: Any,
        action_type: str,
        target: Optional[Any] = None,
        game_state: Optional[Any] = None
    ) -> Dict[str, Any]:
        #Process a player action.
        # Get base chance of success
        base_chance = self._get_base_chance(player, action_type)

        # Apply modifiers
        final_chance = self._apply_modifiers(base_chance, player, action_type, target, game_state)

        # Determine success
        success = random.random() < final_chance

        # Process result
        result = {
            'success': success,
            'player': player,
            'action': action_type,
            'chance': final_chance
        }

        if target:
            result['target'] = target

        return result

    def _get_base_chance(self, player: Any, action_type: str) -> float:
        #Get base success chance for an action.
        # Base chances from original AI
        base_chances = {
            'kick': 0.75,
            'handball': 0.8,
            'mark': 0.6,
            'tackle': 0.5,
            'bounce': 0.85,
            'pickup': 0.7
        }

        base = base_chances.get(action_type, 0.5)

        # Adjust based on relevant attributes
        if action_type == 'kick':
            base *= (0.5 + (player.ability_stats.kicking / 100) * 0.5)
        elif action_type == 'handball':
            base *= (0.5 + (player.ability_stats.handballing / 100) * 0.5)
        elif action_type == 'mark':
            base *= (0.5 + (player.ability_stats.marking / 100) * 0.5)
        elif action_type == 'tackle':
            base *= (0.5 + (player.ability_stats.tackling / 100) * 0.5)

        return base

    def _apply_modifiers(
        self,
        base_chance: float,
        player: Any,
        action_type: str,
        target: Optional[Any],
        game_state: Optional[Any]
    ) -> float:
        #Apply situational modifiers to base chance.
        chance = base_chance

        # Pressure modifier
        if game_state:
            pressure = self._calculate_pressure(player, game_state)
            chance *= max(0.5, 1.0 - pressure)

        # Fatigue modifier
        if hasattr(player, 'fatigue'):
            chance *= max(0.6, 1.0 - (player.fatigue / 200))

        # Weather modifier (if available)
        if game_state and hasattr(game_state, 'weather'):
            weather_mod = game_state.weather.get_modifiers().get(action_type, 1.0)
            chance *= weather_mod

        # Distance modifier for kicks
        if action_type == 'kick' and target:
            distance = self.ground._calculate_distance(player.current_position, target.current_position)
            max_range = 50.0  # Maximum effective kicking range
            if distance > max_range:
                chance *= max(0.5, 1.0 - ((distance - max_range) / max_range))

        return max(0.1, min(0.95, chance))  # Keep between 10% and 95%

    def _calculate_pressure(self, player: Any, game_state: Any) -> float:
        #Calculate pressure on a player from opponents.
        if not player or not player.current_position or not game_state:
            return 0.0

        pressure = 0.0

        # Get the player's team and opponent team
        if player.team_id == game_state.home_team_id:
            opponent_team = game_state.away_team_players
            team = game_state.home_team
        else:
            opponent_team = game_state.home_team_players
            team = game_state.away_team

        # Get team tactics
        defense_strategy = 'zone_mark'  # Default
        if hasattr(team, 'tactics') and team.tactics:
            defense_strategy = team.tactics.get('defense_strategy', 'zone_mark')

        # Calculate pressure based on distance to opponents
        for opponent in opponent_team.values():
            if not opponent.current_position:
                continue

            # Calculate distance between player and opponent
            distance = self.ground._calculate_distance(
                player.current_position,
                opponent.current_position
            )

            # Apply pressure based on defense strategy
            if defense_strategy == 'man_mark':
                # Man marking applies more pressure at closer distances
                if distance < 10.0:
                    # Exponential pressure increase for man marking
                    pressure_factor = max(0, (10.0 - distance) / 10.0) ** 1.5
                    pressure += pressure_factor
            else:  # zone_mark
                # Zone marking applies more distributed pressure
                if distance < 15.0:
                    # Linear pressure increase for zone marking
                    pressure_factor = max(0, (15.0 - distance) / 15.0)
                    pressure += pressure_factor * 0.8  # Slightly less intense than man marking

        # Normalize pressure to a 0-1 scale
        pressure = min(1.0, pressure)

        return pressure


    def handle_disposal(
        self,
        player: Any,
        current_position: Tuple[float, float],
        team_players: Dict[str, Any],
        opponent_players: Dict[str, Any],
        team_tactics: Dict[str, Any]
    ) -> Tuple[Any, Any, str]:
        #Handle player disposal decisions.
        # Find disposal options
        options, disposer_pressure = self.find_disposal_options(
            player,
            current_position,
            team_players,
            opponent_players,
            team_tactics
        )

        # Choose best option
        return self.choose_disposal_option(player, options, team_tactics, disposer_pressure)

    def find_disposal_options(
        self,
        player: Any,
        current_position: Tuple[float, float],
        team_players: Dict[str, Any],
        opponent_players: Dict[str, Any],
        team_tactics: Dict[str, Any]
    ) -> Tuple[List[Dict[str, Any]], float]:
        #Find viable disposal options for a player.
        options = []
        disposer_pressure = 0.0

        # Get defense strategy from opponent tactics
        defense_strategy = 'zone_mark'  # Default
        if team_tactics and 'opponent_tactics' in team_tactics:
            opponent_tactics = team_tactics.get('opponent_tactics', {})
            defense_strategy = opponent_tactics.get('defense_strategy', 'zone_mark')

        # Calculate pressure on the disposer
        for opponent in opponent_players.values():
            if not opponent.current_position:
                continue

            distance = self.ground._calculate_distance(current_position, opponent.current_position)

            # Apply pressure based on defense strategy
            if defense_strategy == 'man_mark':
                # Man marking applies more pressure at closer distances
                if distance < 10.0:
                    # Exponential pressure increase for man marking
                    pressure_factor = max(0, (10.0 - distance) / 10.0) ** 1.5
                    disposer_pressure += pressure_factor
            else:  # zone_mark
                # Zone marking applies more distributed pressure
                if distance < 15.0:
                    # Linear pressure increase for zone marking
                    pressure_factor = max(0, (15.0 - distance) / 15.0)
                    disposer_pressure += pressure_factor * 0.8  # Slightly less intense

        # Find potential targets
        for teammate in team_players.values():
            if teammate == player or not teammate.current_position:
                continue

            target_position = teammate.current_position
            distance_to_target = self.ground._calculate_distance(current_position, target_position)

            # Check if target is within reasonable disposal range
            if distance_to_target > 50.0:  # Maximum disposal distance
                continue

            # Calculate pressure on the target
            target_pressure = 0.0
            for opponent in opponent_players.values():
                if not opponent.current_position:
                    continue

                distance = self.ground._calculate_distance(target_position, opponent.current_position)

                # Apply pressure based on defense strategy
                if defense_strategy == 'man_mark':
                    if distance < 8.0:  # Tighter marking radius for man marking
                        pressure_factor = max(0, (8.0 - distance) / 8.0) ** 1.5
                        target_pressure += pressure_factor
                else:  # zone_mark
                    if distance < 12.0:  # Wider marking radius for zone marking
                        pressure_factor = max(0, (12.0 - distance) / 12.0)
                        target_pressure += pressure_factor * 0.8

            # Add to options
            options.append({
                'player': teammate,
                'position': target_position,
                'distance': distance_to_target,
                'pressure': min(1.0, target_pressure)
            })

        return options, min(1.0, disposer_pressure)

    def choose_disposal_option(
        self,
        player: Any,
        options: List[Dict[str, Any]],
        team_tactics: Dict[str, Any],
        disposer_pressure: float
    ) -> Tuple[Any, Any, str]:
        #Choose best disposal option.
        if not options:
            if disposer_pressure > 0.7:
                return None, None, 'blind_handball'
            else:
                return None, None, 'kick_to_space'

        # Rate each option
        rated_options = []
        for option in options:
            rating = self._rate_disposal_option(player, option, team_tactics, disposer_pressure)
            rated_options.append((rating, option))

        # Choose best option
        rated_options.sort(reverse=True, key=lambda x: x[0])
        best_option = rated_options[0][1]

        # Decide disposal type
        if best_option['distance'] < 15.0:
            return best_option['target'], None, 'handball'
        else:
            return best_option['target'], None, 'kick'

    def _rate_disposal_option(
        self,
        player: Any,
        option: Dict[str, Any],
        team_tactics: Dict[str, Any],
        disposer_pressure: float
    ) -> float:
        #Rate a disposal option based on situation and tactics.
        # Calculate tactical vision and visibility
        tactical_vision = player.ability_stats.tactical
        effective_vision = tactical_vision - disposer_pressure

        # Base visibility chance
        visibility_chance = effective_vision
        visibility_chance *= (1 + option['space_rating'])
        distance_factor = max(0.2, 1 - (option['distance'] / 10))
        visibility_chance *= distance_factor

        # If player can't "see" this option, rate it very low
        if random.random() >= visibility_chance:
            return 0.1

        # Base weight
        weight = 1.0

        # Prefer options that align with team tactics
        weight *= (1 + option['tactical_rating'])

        # Prefer open players
        weight *= (1 + option['space_rating'] * 2)

        # Adjust for risk/reward based on mentality
        if team_tactics['mentality'] == 'attacking':
            # Reward forward movement more
            if option['target'].position in ["FF", "CHF", "LF", "RF", 'LHF', 'RHF']:
                weight *= 1.3
        elif team_tactics['mentality'] == 'defensive':
            # Reward safer options
            weight *= (1 + option['space_rating'])

        # Disposal type preference based on skills and situation
        if option['can_kick'] and option['can_handball']:
            kick_pref = player.ability_stats.kicking / 100
            handball_pref = player.ability_stats.handball / 100

            # Under pressure, prefer handballs
            if disposer_pressure > 0.5:
                handball_pref *= 1.5

            # For longer distances, prefer kicks
            if option['distance'] > 2:
                kick_pref *= 1.3

            # For open targets, prefer kicks
            if option['space_rating'] > 0.7:
                kick_pref *= 1.2

            # Add appropriate skill weighting
            weight *= max(kick_pref, handball_pref)

        return weight
"""

class PossessionEngine:
    """Handles player position updates."""
    def __init__(self, ground: Ground, game_state: 'GameState', stats_manager: StatsManager, movement_engine: MovementEngine, action_engine: ActionEngine):
        self.ground = ground
        self.game_state = game_state
        self.stats_manager = stats_manager
        self.movement_engine = movement_engine
        self.action_engine = action_engine


    def handle_possession(self):
        if not self.game_state.ball_carrier:
            #print(f"Player {player.name} not in position {self.game_state.ball_position}")
            #time.sleep(2)
            all_players = list(self.game_state.home_team_players.values()) + list(self.game_state.away_team_players.values())
            closest_player = Player.determine_closest_player_to_ball(self, self.game_state.ball_position, all_players, exclude_player=player)
            print(f"{closest_player.name} gets to the ball based on proximity and speed/agility")
            team = self.game_state.home_team if closest_player.team == self.game_state.home_team else self.game_state.away_team
            player = closest_player
            print(f'Team: {team}')
            return {
                "result": f"{closest_player.name} gets to the ball based on proximity and speed/agility",
                "next_action": "advance_ball",
                "team": closest_player.team,
                "team_players": team_players,
                #"player": player,
                "position": self.ball_position,
                "receiver": closest_player
            }
        #time.sleep(1)
            # Handle scoring positions
        goal_distance = self.ground.is_in_range_for_shot(self.game_state.ball_position, self.game_state.ball_carrier.team_side)
        print(f"Goal distance: {goal_distance}")
        if goal_distance and self.game_state.phase not in ["consider_pass"]:
            print("Attempt Goal")
            #time.sleep(3)
            result = self.action_engine.attempt_goal()
            if self.game_state.phase == "goal":

                return {
                "result": result,
                "next_action": action,
                }
            if self.game_state.phase == "behind":

                return {
                "result": result,
                "next_action": action,
                }
            if self.game_state.phase == "consider_pass":
                return {
                "commentary": f"{result['commentary']}",
                "type": "match_event",
                "event_category": "match_event",
                "next_action": "consider_pass",
                }
            if self.game_state.phase == "out_on_the_full":

                return {
                "result": result,
                "next_action": action,
                }

        else:
            next_position, receiver, action, disposal_options, disposer_data, no_options = self.handle_disposal()
            #print(f"Next Position {next_position} Receiver {receiver['receiver'].name} Action {action}")
            if receiver != None and no_options == None:
                #self.game_state.phase = "advance_ball"
                #team_players = self.team2_players if receiver.team == self.team1 else self.team1_players
                print(f"Returnning from handle_possesion, receiver not None and no_options is None")

                print(f"Phase State: {self.game_state.phase}")
                result = {
                "commentary": f"Temp commentary - should now advance ball - {receiver['receiver']} Chosen {action}",
                "result": f"{receiver['receiver']} Chosen From {action}",
                "next_action": "advance_ball",
                "receiver": receiver,
                "next_position": next_position,
                "disp_type": action,
                "disposal_options": disposal_options,
                "disposer_data": disposer_data,
                "type": "match_event",
                "event_category": "match_event",
                }
                return result
            elif no_options != None:
                print(f"Phase State: {self.game_state.phase}")

                print(f"Returnning from consider_pass, no_options not None")
                result = {
                "result": f"{no_options['result']}",
                "commentary": f"{no_options['commentary']}",
                "next_action": no_options['next_action'],
                "type": "match_event",
                "event_category": "match_event",
                }
                return result
            else:
                print(f"Something went wrong")
                print(f"Phase State: {self.game_state.phase}")
                sys.exit()

        """
        if result in ["kick_in", "behind"]:
            self.ball_position_name = self.get_ball_position()
            team = self.team2 if team == self.team1 else self.team2
            team_players = self.team2_players if team == self.team1 else self.team2_players
            opponent_players = self.team2_players if team == self.team1 else self.team1_players
            self.update_game_state(team)
            player = next((player for player in team_players.values() if player.position == self.ball_position_name))
            team_tactics = self.team1_tactics if team == self.team1 else self.team2_tactics
            action = "kick_in"
            #next_position, receiver, action = self.handle_disposal(player, self.position, team_players, opponent_players, team_tactics)
            return {
                "result": f"{result}",
                "next_action": action,
                "player": player,
                "position": self.ball_position,
                "team_players": team_players,
                "opponent_players": opponent_players,
                "team_tactics": team_tactics,
            }
        if result == ("out_on_full"):
            self.ball_position_name = self.get_ball_position()
            team = self.team2 if team == self.team1 else self.team2
            team_players = self.team2_players if team == self.team1 else self.team2_players
            self.update_game_state(team)
            player = next((player for player in team_players.values() if player.position == self.ball_position_name))
            opponent_players = self.team2_players if team == self.team1 else self.team1_players
            team_tactics = self.team1_tactics if team == self.team1 else self.team2_tactics
            action = "out_on_full"
            return {
            "result": result,
            "next_action": action,
            "player": player,
            "position": self.ball_position_name,
            "team_players": team_players,
            "opponent_players": opponent_players,
            "team_tactics": team_tactics,
            }
        elif result  == ("consider_pass"):
            #team_players = self.team1_players if team == self.team1 else self.team2_players
            team_tactics = self.team1_tactics if team == self.team1 else self.team2_tactics
            team_players = self.team2_players if team == self.team1 else self.team2_players
            opponent_players = self.team2_players if team == self.team1 else self.team1_players
            return {
                "result": result.get('result'),
                "next_action": result.get('next_action'),
                "player": result.get('player'),
                "position": result.get('position'),
                "team_players": team_players,
                "opponent_players": opponent_players,
                "team_tactics": team_tactics,
            }

            # Handle defensive/midfield positions
        ball_position_from_coords = self.ground.get_position_from_coordinates(self.game_state.ball_position)
        print(f"Ball position from coords: {ball_position_from_coords}")
        if ball_position_from_coords in ["RBP", "LBP", "FB", "LHB", "RHB", "CHB", "LWing", "RWing", "Centre"]:
            next_position, receiver, action, disposal_options, disposer_data = self.handle_disposal()
            print(f"Next Position {next_position} Receiver {receiver['receiver'].name} Action {action}")
            if next_position and receiver and action != "play_on":
                self.game_state.phase = "advance_ball"
                #team_players = self.team2_players if receiver.team == self.team1 else self.team2_players
                return {
                    "commentary": f"{receiver['receiver'].name} Chosen From {action}",
                    "type":"match_event",
                    "event_category":"match_event",
                    "next_action": "advance_ball",
                    "team": {receiver['receiver'].team},
                    #"team_players": team_players,
                    "position": self.game_state.ball_position,
                    "receiver": receiver,
                    "next_position": next_position,
                    "disp_type": action,
                    "disposal_options": disposal_options,
                    "disposer_data": disposer_data
                }

            #elif action == "goal":
                self.ball_position_name = self.get_ball_position()
                self.update_game_state(team, "goal")
                action = "center_bounce"
                return {
                "result": result,
                "next_action": action,
                }
            #elif action in ["kick_in", "behind"]:
                self.ball_position_name = self.get_ball_position()
                team = self.team2 if team == self.team1 else self.team2
                team_players = self.team2_players if team == self.team1 else self.team2_players
                opponent_players = self.team2_players if team == self.team1 else self.team1_players
                self.update_game_state(team)
                player = next((player for player in team_players.values() if player.position == "FB"))
                team_tactics = self.team1_tactics if team == self.team1 else self.team2_tactics
                action = "kick_in"
                #next_position, receiver, action = self.handle_disposal(player, self.position, team_players, opponent_players, team_tactics)
                return {
                    "result": f"{result}",
                    "next_action": action,
                    "player": player,
                    "position": self.ball_position,
                    "team_players": team_players,
                    "opponent_players": opponent_players,
                    "team_tactics": team_tactics,
                }

            """
            # Default case - advance ball
        print("Hit Return")
        #time.sleep(1)
        return {
            "result": f"{player.name} looks to advance the ball",
            "next_action": "advance_ball",
            "team": team,
            "team_players": team_players,
            #"player": player,
            "position": self.ball_position
        }

    def handle_disposal(self):
            """Handle the disposal decision and execution."""
            result = None
            # Find all viable disposal options
            options, disposer_data = self.find_disposal_options()

            # Choose best option
            result = self.choose_disposal_option(
                self.game_state.ball_carrier,
                options,
                disposer_data
            )

            print(f"Result {result}")
            # Unpack the result if it's not None
            if isinstance(result, dict) and 'result' in result:
                print("Result is a dictionary event, returning it directly")
                return None, None, None, None, None, result
            else:
                print ("Handle disposal Return OK")
                #time.sleep(2)
                next_position, receiver, disposal_type = result
            #print(f"Next Position {next_position} Receiver {receiver} Disposal Type {disposal_type}")
            if not next_position or not receiver:
                print(f"{self.game_state.ball_carrier.name} has no clear disposal options!")
                #time.sleep(5)
                # Handle fallback logic
                sys.exit()
                return next_position, receiver, disposal_type

            print(f"{self.game_state.ball_carrier.name} decides to {disposal_type} to {receiver} at {next_position}")
            """"
            if disposal_type == "kick":
                return self.execute_kick(player, receiver, next_position)
            else:
                return self.execute_handball(player, receiver, next_position)
            """
            return next_position, receiver, disposal_type, options, disposer_data, None

    def find_disposal_options(self):
        """Analyze the field to find disposal options based on player's tactical ability."""

        # Calculate the maximum kick distance based on player's stats with randomness
        min_kick_distance_base = 17  # Base minimum kick distance
        min_kick_variation = 2      # Variation range (+/-)
        max_kick_distance_base = 55  # Base maximum kick distance
        max_kick_variation = 5      # Variation range (+/-)

        # Apply randomness to min and max distances
        min_kick_distance = min_kick_distance_base + random.uniform(-min_kick_variation, min_kick_variation)
        max_kick_distance = max_kick_distance_base + random.uniform(-max_kick_variation, max_kick_variation)

        # Calculate player's kick distance based on ability
        kicking_ability = self.game_state.ball_carrier.ability_stats.kicking
        kick_distance = min_kick_distance + ((kicking_ability - 1) / 19) * (max_kick_distance - min_kick_distance)

        # Apply small random variation to final distance (to simulate day-to-day variation)
        kick_variation = 0.1  # 10% variation
        kick_distance *= random.uniform(1 - kick_variation, 1 + kick_variation)

        print(f"Player name: {self.game_state.ball_carrier.name}")
        print(f"Player kicking ability: {kicking_ability}/20")
        print(f"Min kick distance: {min_kick_distance:.1f}m")
        print(f"Max kick distance: {max_kick_distance:.1f}m")
        print(f"Final kick distance: {kick_distance:.1f}m")

        # Convert the kick distance to grid cells
        #meters_per_cell_length = self.ground.length / self.ground.grid_length
        #meters_per_cell_width = self.ground.width / self.ground.grid_width
        #meters_per_cell = (meters_per_cell_length + meters_per_cell_width) / 2
        #max_kick_cells = max(1, int(kick_distance / meters_per_cell))

        #print(f"Meters per cell: {meters_per_cell:.1f}m")
        #print(f"Max kick cells: {max_kick_cells}")

        min_handball_distance = 5  # Minimum handball distance
        max_handball_distance = 15
        handball_distance = min_handball_distance + ((self.game_state.ball_carrier.ability_stats.handball - 1) / 19) * (max_handball_distance - min_handball_distance)
        print(f"Max handball Distance {handball_distance}")
        # Convert the kick distance to grid cells (1 cell = 10 meters)
        #max_handball_cells = max(1, int(handball_distance / meters_per_cell))  # Ensure at least 1 cell
        #print(f"Max handball Cells {max_handball_cells}")

        # Get possible positions within range based on player's kicking/handball abilities
        #kick_range = self.calculate_kick_range(player)
        #handball_range = self.calculate_handball_range(player)


        # Get all possible positions within range
        kick_positions = self.ground.get_dynamic_possible_positions(self.game_state.ball_carrier.current_position, kick_distance, self.game_state.ball_carrier.team_side, True)
        handball_positions = self.ground.get_dynamic_possible_positions(self.game_state.ball_carrier.current_position, handball_distance, self.game_state.ball_carrier.team_side, True)

        disposal_options = []
        print(f"Kick Positions {len(kick_positions)}")
        print(f"Handball Positions {len(handball_positions)}")
        tactical_vision = self.game_state.ball_carrier.ability_stats.tactical / 20  # How well they read the play

        # Analyze each possible position
        team_players = self.game_state.home_team_players if self.game_state.ball_carrier.team == self.game_state.home_team else self.game_state.away_team_players
        opponent_players = self.game_state.home_team_players if self.game_state.ball_carrier.team == self.game_state.away_team else self.game_state.away_team_players

        # Get player coordinates
        player_coords = {}
        for player in list(team_players.values()) + list(opponent_players.values()):
            player_coords[player] = player.current_position

        # Find all valid disposal positions
        all_valid_positions = set(kick_positions + handball_positions)
        teammates_at_positions = []

        # Get ball carrier position and attacking direction
        carrier_pos = player_coords.get(self.game_state.ball_carrier)
        closest_opponent_to_carrier = None
        min_opponent_distance_to_carrier = float('inf')

        for opp in opponent_players.values():
            opp_pos = player_coords.get(opp)

            if carrier_pos and opp_pos:
                dx = carrier_pos[0] - opp_pos[0]
                dy = carrier_pos[1] - opp_pos[1]
                distance = math.sqrt(dx*dx + dy*dy)
                distance_meters = distance * 2  # Convert to meters

                if distance < min_opponent_distance_to_carrier:
                    min_opponent_distance_to_carrier = distance
                    closest_opponent_to_carrier = opp

        # Convert to meters
        min_opponent_distance_to_carrier_meters = min_opponent_distance_to_carrier * 2

        # Calculate disposer pressure based on distance to closest opponent
        if min_opponent_distance_to_carrier_meters >= 8:
            disposer_pressure = 0.0
        elif min_opponent_distance_to_carrier_meters >= 6:
            disposer_pressure = 0.2
        elif min_opponent_distance_to_carrier_meters >= 4:
            disposer_pressure = 0.5
        elif min_opponent_distance_to_carrier_meters >= 2:
            disposer_pressure = 0.8
        else:
            disposer_pressure = 1.0

        print(f"Ball carrier {self.game_state.ball_carrier.name} under pressure: {disposer_pressure}")
        print(f"Closest opponent to carrier: {closest_opponent_to_carrier.name if closest_opponent_to_carrier else 'None'}")
        print(f"Distance to closest opponent: {min_opponent_distance_to_carrier_meters:.1f}m")
        attacking_forward = 1 if self.game_state.ball_carrier.team_side == 'home' else -1  # 1 for home (attacking right), -1 for away (attacking left)

        # Find all teammates whose position is in the valid disposal positions
        for player in team_players.values():
            if player == self.game_state.ball_carrier:
                continue

            player_pos = player_coords.get(player)
            if player_pos in all_valid_positions:
                # Calculate distance in meters (each grid cell is 2m)
                dx = player_pos[0] - carrier_pos[0]
                dy = player_pos[1] - carrier_pos[1]
                distance_meters = math.sqrt(dx*dx + dy*dy) * 2  # Convert to meters

                # Determine direction relative to ball carrier and attacking direction
                # For home team (attacking right), forward means x is increasing
                # For away team (attacking left), forward means x is decreasing

                # Calculate angle in degrees (0 = right, 90 = up, 180 = left, 270 = down)
                angle = math.degrees(math.atan2(dy, dx * attacking_forward))

                # Convert angle to direction
                if -45 <= angle <= 45:
                    direction = "forward"  # Toward goal
                elif 45 < angle <= 135:
                    direction = "right"
                elif -135 <= angle < -45:
                    direction = "left"
                else:
                    direction = "backward"

                teammates_at_positions.append((player, player_pos, distance_meters, direction))
                print(f"Found teammate {player.name} at position {player_pos}, {distance_meters:.1f}m {direction} from carrier")

        print(f"Found {len(teammates_at_positions)} teammates in valid disposal positions")

        # Process each teammate at a valid position
        for teammate, pos_coords, distance_meters, direction in teammates_at_positions:
            # Find closest opponent to this teammate
            closest_opponent = None
            min_opponent_distance = float('inf')

            teammate_pos = player_coords.get(teammate)

            for opp in opponent_players.values():
                opp_pos = player_coords.get(opp)

                if teammate_pos and opp_pos:
                    dx = teammate_pos[0] - opp_pos[0]
                    dy = teammate_pos[1] - opp_pos[1]
                    distance = math.sqrt(dx*dx + dy*dy)
                    distance_meters_opp = distance * 2  # Convert to meters

                    if distance < min_opponent_distance:
                        min_opponent_distance = distance
                        closest_opponent = opp

            # Convert min_opponent_distance to meters
            min_opponent_distance_meters = min_opponent_distance * 2

            # Improved pressure scale based on meters
            if min_opponent_distance_meters >= 8:
                space_rating = 1.0
                receiver_pressure = 0.0
            elif min_opponent_distance_meters >= 6:
                space_rating = 0.8
                receiver_pressure = 0.2
            elif min_opponent_distance_meters >= 4:
                space_rating = 0.5
                receiver_pressure = 0.5
            elif min_opponent_distance_meters >= 2:
                space_rating = 0.2
                receiver_pressure = 0.8
            else:
                space_rating = 0.0
                receiver_pressure = 1.0

            print(f"Teammate {teammate.name} at {pos_coords}")
            print(f"Closest opponent: {closest_opponent.name if closest_opponent else 'None'}")
            print(f"Opponent distance: {min_opponent_distance_meters:.1f}m")
            print(f"Space rating: {space_rating}")
            print(f"Disposer {self.game_state.ball_carrier.name} under pressure: {disposer_pressure}")

            # Calculate if position is in preferred tactical area
            position_name = self.ground.get_position_from_coordinates(pos_coords)
            # Get the team object based on the player's team
            #team = self.home_team if teammate.team == self.home_team else self.away_team
            """
            if self.game_state.ball_carrier.team == self.game_state.home_team:
                tactical_rating = self.game_state.home_team_tactics.calculate_tactical_rating(
                    position_name
                )
            else:
                tactical_rating = self.game_state.away_team_tactics.calculate_tactical_rating(
                    position_name
                )
            """
            tactical_rating = 0
            # Determine if in kick or handball range
            in_kick_range = pos_coords in kick_positions
            in_handball_range = pos_coords in handball_positions

            disposal_options.append({
                'position': pos_coords,
                'receiver': teammate,
                'opponent': closest_opponent,
                'space_rating': space_rating,
                'receiver_pressure': receiver_pressure,
                'tactical_rating': tactical_rating,
                'can_kick': in_kick_range,
                'can_handball': in_handball_range,
                'distance': distance_meters,
                'direction': direction
            })
        print(f"Disposal Options {disposal_options}")
        #time.sleep(2)
        # Filter options based on tactical ability
        vision_threshold = 1.0 - tactical_vision  # Better vision = lower threshold
        print(f"Tactical Vision {tactical_vision} Vision Threshold {vision_threshold}")
        disposal_options = [opt for opt in disposal_options]
                            #if opt['space_rating'] > vision_threshold]
        print(f"Disposal Options after vision filter {disposal_options} and Pressure: {disposer_pressure}")


        disposer_data = {
            'disposer_pressure': disposer_pressure,
            'closest_opponent': closest_opponent_to_carrier,
            'kick_range': kick_positions,
            'kick_distance': kick_distance,
            'handball_range': handball_positions,
            'handball_distance': handball_distance
        }

        #time.sleep(2)
        return disposal_options, disposer_data

    def choose_disposal_option(self, player, options, disposer_data):
        """Choose the best disposal option based on situation and tactics."""

        #print(f"Disposal Data: {disposer_data}")
        tactical_vision = player.ability_stats.tactical #/ 100
        print(f"pressure: {disposer_data['disposer_pressure']}")
        # Adjust vision based on pressure
        #effective_vision = tactical_vision * (1 - disposer_pressure)
        effective_vision = tactical_vision - disposer_data['disposer_pressure']
        print(f"Effective vision: {effective_vision} (base vision: {tactical_vision}, pressure: {disposer_data['disposer_pressure']}")
        #time.sleep(2)
        # First, determine which options the player can "see"
        visible_options = []
        for opt in options:
            # Base visibility chance on tactical vision
            visibility_chance = effective_vision

            # Adjust for how open the teammate is
            visibility_chance *= (1 + opt['space_rating'])

            # Adjust for distance - harder to spot far options
            distance_factor = max(0.2, 1 - (opt['distance'] / 10))
            visibility_chance *= distance_factor

            # Random check if player spots this option
            if random.random() < visibility_chance:
                visible_options.append(opt)
                print(f"Spotted option: {opt['receiver'].name} at {opt['position']}")

        if not visible_options:
            print("No options spotted, What will they do?")
            # Fall back to closest teammate if under pressure
            return self.handle_no_options(player, options, disposer_data)

        # Weight each option
        weighted_options = []
        for opt in options:
            weight = 1.0

            # Prefer options that align with team tactics
            weight *= (1 + opt['tactical_rating'])

            # Prefer open players
            weight *= (1 + opt['space_rating'] * 2)

            # Adjust for risk/reward based on mentality
            if self.game_state.ball_carrier.team == self.game_state.home_team:
                if self.game_state.home_team_tactics.tactics['mentality'] == 'attacking':
                    # Reward forward movement more
                    if opt['direction'] == 'forward':
                        weight *= 1.4
                    if opt['direction'] == 'backward':
                        weight *= 1.0
                    if opt['direction'] == 'left':
                        weight *= 1.1
                    if opt['direction'] == 'right':
                        weight *= 1.1
                if self.game_state.away_team_tactics.tactics['mentality'] == 'defensive':
                    # Reward safer options
                    weight *= (1 + opt['space_rating'])
            elif self.game_state.ball_carrier.team == self.game_state.away_team:
                if self.game_state.away_team_tactics.tactics['mentality'] == 'attacking':
                    # Reward forward movement more
                    if opt['direction'] == 'forward':
                        weight *= 1.4
                    if opt['direction'] == 'backward':
                        weight *= 1.0
                    if opt['direction'] == 'left':
                        weight *= 1.1
                    if opt['direction'] == 'right':
                        weight *= 1.1
                if self.game_state.home_team_tactics.tactics['mentality'] == 'defensive':
                    # Reward safer options
                    weight *= (1 + opt['space_rating'])

                   # Disposal type preference based on skills and situation
            if opt['can_kick'] or opt['can_handball']:
                kick_pref = player.ability_stats.kicking / 20
                handball_pref = player.ability_stats.handball / 20

                # Under pressure, prefer handballs
                if disposer_data['disposer_pressure'] > 0.5:
                    handball_pref *= 1.5

                # For longer distances, prefer kicks
                if opt['distance'] > 8:
                    kick_pref *= 1.3
                if opt['distance'] < 8:
                    handball_pref *= 2

                # Add appropriate skill weighting
                weight *= max(kick_pref, handball_pref)

            weighted_options.append((opt, weight))

        # Choose final option
        chosen = random.choices(
            weighted_options,
            weights=[w for _, w in weighted_options],
            k=1
        )[0][0]


        disposal_type = self.decide_disposal_type(player, chosen, disposer_data['disposer_pressure'])
        print(f"Chosen Position {chosen['position']} Receiver {chosen['receiver']} Disposal Type {disposal_type}")

        self.game_state.phase = "advance_ball"
        #self.game_state.ball_carrier = closest_opponent
        #self.game_state.ball_position = target_position

        #time.sleep(2)
        return chosen['position'], chosen, disposal_type


    def handle_no_options(self, player, options, disposer_data):
        """Handle situation when player can't spot clear options."""
        print(f"{player.name} can't spot any clear options...")

        # Get team tactics
        team_tactics = self.game_state.home_team_tactics.tactics if player.team == self.game_state.home_team else self.game_state.away_team_tactics.tactics

        # Different decisions based on pressure and player attributes
        decision_weights = {
            'play_on': 0.0,
            'kick_to_space': 0.0,
            'blind_handball': 0.0,
            'long_bomb': 0.0,
            'kick_to_boundary': 0.0,  # New option: deliberate kick to boundary for stoppage
            'snap_shot': 0.0          # New option: quick snap at goal if in range
        }

        # Base weights on player's strengths
        decision_weights['play_on'] = player.physical_stats.speed + player.physical_stats.agility * 0.5
        decision_weights['kick_to_space'] = player.ability_stats.kicking + player.ability_stats.mental * 0.5
        decision_weights['blind_handball'] = player.ability_stats.mental * 0.5
        decision_weights['long_bomb'] = player.ability_stats.kicking + player.ability_stats.mental * 0.4
        decision_weights['kick_to_boundary'] = player.ability_stats.kicking + player.ability_stats.mental * 0.5
        decision_weights['snap_shot'] = player.ability_stats.goal_kicking + player.physical_stats.agility * 0.4

        print(f"decision_weights: {decision_weights}")
        #time.sleep(3)
        # Adjust weights based on pressure
        pressure = disposer_data['disposer_pressure']

        if pressure >= 0.8:  # Heavy pressure
            decision_weights['blind_handball'] *= 3.0
            decision_weights['play_on'] *= 1.5
            decision_weights['kick_to_space'] *= 1.2
            decision_weights['long_bomb'] *= 0.5
            decision_weights['kick_to_boundary'] *= 1.2
            decision_weights['snap_shot'] *= 0.7
        elif pressure >= 0.5:  # Moderate pressure
            decision_weights['play_on'] *= 1.8
            decision_weights['kick_to_space'] *= 1.5
            decision_weights['blind_handball'] *= 2.0
            decision_weights['long_bomb'] *= 0.8
            decision_weights['kick_to_boundary'] *= 0.8
            decision_weights['snap_shot'] *= 1.0
        else:  # Light pressure
            decision_weights['long_bomb'] *= 1.2
            decision_weights['play_on'] *= 1.3
            decision_weights['kick_to_space'] *= 1.2
            decision_weights['blind_handball'] *= 2.0
            decision_weights['kick_to_boundary'] *= 0.5
            decision_weights['snap_shot'] *= 1.2

        # Adjust for field position
        field_position = self.ground.get_zone_for_position(player.current_position, player.team_side)

        if field_position == "deep_defense" or field_position == "defensive_50":
            decision_weights['kick_to_space'] *= 1.8
            decision_weights['kick_to_boundary'] *= 1.5
            decision_weights['blind_handball'] *= 0.7
            decision_weights['long_bomb'] *= 1.2
            decision_weights['play_on'] *= 1.2
        elif field_position == "center_corridor":
            decision_weights['play_on'] *= 1.3
            decision_weights['long_bomb'] *= 1.1
            decision_weights['blind_handball'] *= 0.9
        elif field_position == "forward_50":
            decision_weights['play_on'] *= 1.3
            decision_weights['blind_handball'] *= 1.0
            decision_weights['long_bomb'] *= 0.0
            decision_weights['kick_to_boundary'] *= 0.0
        elif field_position == "wing_left" or field_position == "wing_right":
            decision_weights['play_on'] *= 1.5
            decision_weights['long_bomb'] *= 1.1
            decision_weights['kick_to_boundary'] *= 1.2
        elif field_position == "high_forward":
            decision_weights['long_bomb'] *= 0.0
            decision_weights['kick_to_boundary'] *= 0.0  # Less likely to kick to boundary in forward

        if self.ground.is_in_range_for_shot(player.current_position, player.team_side):
            decision_weights['snap_shot'] *= 1.0

        # Adjust for team tactics
        if team_tactics['mentality'] == 'attacking':
            decision_weights['play_on'] *= 1.4
            decision_weights['long_bomb'] *= 1.4
            decision_weights['snap_shot'] *= 1.5
            decision_weights['kick_to_boundary'] *= 0.7
        elif team_tactics['mentality'] == 'defensive':
            decision_weights['kick_to_space'] *= 1.3
            decision_weights['kick_to_boundary'] *= 1.5
            decision_weights['play_on'] *= 0.8
            decision_weights['long_bomb'] *= 0.7

        # Adjust for player role/position
        if player.position in ['Ruck']:
            decision_weights['kick_to_space'] *= 1.2  # Rucks often try to break tackles
        elif player.position in ['LF', 'RF']:
            decision_weights['snap_shot'] *= 1.5  # Forwards more likely to shoot
        #elif player.position in ['Midfielder', 'Wing']:
            #decision_weights['play_on'] *= 1.3  # Midfielders more likely to run
        elif player.position in ['RBP', 'LBP']:
            decision_weights['kick_to_space'] *= 1.3  # Defenders more likely to clear


        print(f"decision_weights after adjustments: {decision_weights}")


        # Adjust for game context
        score_difference = self.game_state.home_team_score['total'] - self.game_state.away_team_score['total']
        if player.team == self.game_state.away_team:
            score_difference = -score_difference

        total_time = self.game_state.quarter_time
        quarter = self.game_state.current_quarter

        print(f"Score difference: {score_difference}, Quarter: {quarter}, Total time: {total_time}")

        # If behind late in the game, take more risks
        if score_difference < 0 and quarter >= 4 and total_time >1200:  # Last 5 minutes
            decision_weights['play_on'] *= 1.5
            decision_weights['long_bomb'] *= 1.8
            decision_weights['snap_shot'] *= 2.0
            decision_weights['kick_to_boundary'] *= 0.3

        # If ahead late in the game, play safer
        if score_difference > 12 and quarter >= 4 and total_time >1200:  # Last 5 minutes
            decision_weights['kick_to_boundary'] *= 2.0
            decision_weights['kick_to_space'] *= 1.5
            decision_weights['play_on'] *= 0.7
            decision_weights['long_bomb'] *= 0.5

        if not self.ground.is_in_range_for_shot(player.current_position, player.team_side):
            decision_weights['snap_shot'] = 0.0
        if field_position == "high_forward":
            #decision_weights['long_bomb'] *= 1.5
            decision_weights['kick_to_boundary'] *= 0.0  # Less likely to kick to boundary in forward
        if field_position == "center_corridor":
            decision_weights['kick_to_boundary'] *= 0.0



        contrast_factor = 2.0
        # Remove options with zero weight
        boosted_weights = {k: v ** contrast_factor for k, v in decision_weights.items()}

        # Choose action based on weights
        #if not decision_weights:
            # Fallback if all weights are zero
        #    action = 'kick_to_space'
        #else:
        # Option 1: Keep the weighted random choice (current implementation)
        action = random.choices(
            list(boosted_weights.keys()),
            weights=list(boosted_weights.values()),
            k=1
        )[0]

        # Option 2: Always choose the highest weight (deterministic)
        #action = max(decision_weights.items(), key=lambda x: x[1])[0]

        print(f"Chosen action: {action} with weights: {decision_weights}")
        #time.sleep(5)
        # Execute chosen action
        if action == 'play_on':
            return self.handle_play_on(player, disposer_data)
        elif action == 'kick_to_space':
            return self.handle_kick_to_space(player, disposer_data)
        elif action == 'blind_handball':
            return self.handle_blind_handball(player, disposer_data, options)
        elif action == 'long_bomb':
            return self.handle_long_bomb(player, disposer_data)
        elif action == 'kick_to_boundary':
            return self.handle_kick_to_boundary(player, disposer_data)
        elif action == 'snap_shot':
            return self.handle_snap_shot(player, disposer_data)
        else:
            # Fallback
            return self.handle_kick_to_space(player, disposer_data)

    def handle_play_on(self, player, disposer_data):
        """Player attempts to break tackle/evade and run with the ball."""
        print(f"Player {player.name} is attempting to play on")

        # Calculate intended movement for ball carrier
        attacking_direction = self.game_state.home_team.attacking_direction if player.team_side == 'home' else self.game_state.home_team.attacking_direction
        intended_position, intended_path = self.movement_engine._calculate_intended_movement(player, disposer_data, attacking_direction)
        print(f"Intended position: {intended_position}")
        print(f"Intended path: {intended_path}")
        #time.sleep(2)

        # Get team tactics
        team_tactics = self.game_state.home_team_tactics.tactics if player.team_side == 'home' else self.game_state.away_team_tactics.tactics
        print(f"Team tactics: {team_tactics}")
        #time.sleep(2)

        # Calculate movement for all players including ball carrier
        all_player_movements = self.movement_engine.update_player_position(
            player,
            self.game_state,
            include_ball_carrier=True,
            ball_carrier_intended_position=intended_position
        )

        # Check for potential tackles along the path
        tackle_events = self._check_for_tackles_along_path(
            player,
            intended_path,
            all_player_movements
        )

        # If there are tackle events, resolve the first one
        if tackle_events:
            tackle_event = tackle_events[0]  # Get the first tackle event (earliest in path)
            tackler = tackle_event['tackler']
            tackle_position = tackle_event['position']
            tackle_chance = tackle_event['tackle_chance']

            # Update positions to the tackle point
            player.current_position = tackle_position
            self.game_state.ball_position = tackle_position
            tackler.current_position = tackle_position

            # Determine outcome of the tackle
            if random.random() < tackle_chance:
                # Tackle successful - determine if it's holding the ball
                if random.random() < 0.4:  # 40% chance of holding the ball decision
                    self.game_state.phase = "holding_the_ball"
                    self.stats_manager.record_stat(tackler.name, 'tackles')
                    self.stats_manager.record_stat(player.name, 'turnovers')

                    return {
                        "result": f"{player.name} caught holding the ball! Free kick to {tackler.name}.",
                        "commentary": f"{tackler.name} wraps up {player.name} who had no prior opportunity. Holding the ball!",
                        "next_action": "holding_the_ball",
                        "type": "match_event",
                        "event_category": "match_event",
                    }
                else:
                    # Ball-up/stoppage
                    self.game_state.phase = "ball_up"
                    self.stats_manager.record_stat(tackler.name, 'tackles')

                    return {
                        "result": f"{player.name} tackled by {tackler.name}! Ball-up.",
                        "commentary": f"Great tackle by {tackler.name}, locking the ball in. We'll have a ball-up.",
                        "next_action": "ball_up",
                        "type": "match_event",
                        "event_category": "match_event",
                    }
            else:
                # Player breaks free from this tackle attempt
                self.game_state.phase = "advance_ball"

                return {
                    "result": f"{player.name} breaks free from {tackler.name}!",
                    "commentary": f"Brilliant evasion by {player.name}, showing great agility to break the tackle!",
                    "next_action": "handle_possession",
                    "type": "match_event",
                    "event_category": "match_event",
                }
        else:
            # No tackle events - player moves successfully to intended position
            player.current_position = intended_position
            self.game_state.ball_carrier.current_position = intended_position
            self.game_state.ball_position = intended_position
            self.stats_manager.record_stat(player.name, 'running_bounces')
            self.game_state.phase = "handle_possession"

            return {
                "result": f"{player.name} takes off with a burst of speed!",
                "commentary": f"{player.name} sees space and accelerates away, taking ground!",
                "next_action": "handle_possession",
                "type": "match_event",
                "event_category": "match_event",
            }

    def _check_for_tackles_along_path(self, player, path_points, all_player_movements):
        """Check for potential tackles along the player's path using existing MovementEngine functions."""
        tackle_events = []

        # Get opponent team players
        opponent_team = 'away' if player.team_side == 'home' else 'home'
        opponent_players = self.game_state.home_team_players if self.game_state.ball_carrier.team == self.game_state.away_team else self.game_state.away_team_players

        # Create a congestion map for the path
        congestion_map = self.ground.congestion_map

        # For each point along the path
        for i, point in enumerate(path_points):
            # Skip the first point (starting position)
            if i == 0:
                continue

            # Calculate how far along the path we are (0-1)
            path_progress = i / (len(path_points) - 1)

            # For each opponent
            for opponent_data in opponent_players.values():
                print(f"Opponent Data {opponent_data}")
                opponent_name = opponent_data.name
                opponent_position = (opponent_data.current_position[0], opponent_data.current_position[1])

                # Get the actual opponent player object
                opponent = None
                if opponent_team == 'home':
                    opponent = self.game_state.home_team_players.get(opponent_name)
                else:
                    opponent = self.game_state.away_team_players.get(opponent_name)

                if not opponent:
                    continue

                # Calculate distance to this path point
                distance = math.sqrt(
                    (point[0] - opponent_position[0])**2 +
                    (point[1] - opponent_position[1])**2
                )

                # Convert to meters
                distance_meters = distance * 2

                # Calculate tackle radius based on opponent's attributes
                tackle_radius = self._calculate_tackle_radius(opponent)

                # If opponent is within tackle radius of this path point
                if distance_meters <= tackle_radius:
                    # Calculate tackle chance
                    tackle_chance = self._calculate_tackle_chance(
                        player,
                        opponent,
                        distance_meters,
                        path_progress,
                        congestion_map
                    )

                    # Add to tackle events
                    tackle_events.append({
                        'tackler': opponent,
                        'position': point,
                        'distance': distance_meters,
                        'path_progress': path_progress,
                        'tackle_chance': tackle_chance
                    })

        # Sort by path_progress to get earliest tackle first
        tackle_events.sort(key=lambda x: x['path_progress'])

        return tackle_events

    def _calculate_tackle_radius(self, player):
        """Calculate the radius within which a player can attempt a tackle."""
        # Base radius
        base_radius = 2.0  # meters

        # Adjust for player attributes
        speed_factor = player.physical_stats.speed / 20  # 0-1 scale
        agility_factor = player.physical_stats.agility / 20  # 0-1 scale

        # Adjust for fatigue
        fatigue_penalty = player.fatigue / 100  # 0-1 scale

        # Calculate final radius
        tackle_radius = base_radius * (1 + speed_factor * 0.5 + agility_factor * 0.3) * (1 - fatigue_penalty * 0.3)

        return tackle_radius

    def _calculate_tackle_chance(self, ball_carrier, tackler, distance, path_progress, congestion_map):
        """Calculate the chance of a successful tackle."""
        # Base chance based on distance
        base_chance = 1.0 - (distance / 4.0)  # 1.0 at 0m, 0.0 at 4m
        base_chance = max(0.1, min(0.9, base_chance))  # Clamp between 0.1 and 0.9

        # Get congestion at the tackle point
        tackle_point_congestion = self.movement_engine._get_point_congestion(
            ball_carrier.current_position,
            congestion_map
        )

        # Adjust for congestion - tackles more likely in congested areas
        congestion_factor = 1.0 + (tackle_point_congestion * 0.3)

        # Adjust for player attributes
        # Tackler attributes that help
        tackler_strength = tackler.physical_stats.strength / 20  # 0-1 scale
        tackler_tackling = tackler.ability_stats.tackling / 20 if hasattr(tackler.ability_stats, 'tackling') else 0.5  # 0-1 scale
        tackler_speed = tackler.physical_stats.speed / 20  # 0-1 scale

        # Ball carrier attributes that help evade
        carrier_agility = ball_carrier.physical_stats.agility / 20  # 0-1 scale
        carrier_speed = ball_carrier.physical_stats.speed / 20  # 0-1 scale
        carrier_mental = ball_carrier.ability_stats.mental / 20  # 0-1 scale

        # Fatigue factors
        tackler_fatigue = tackler.fatigue / 100  # 0-1 scale
        carrier_fatigue = ball_carrier.fatigue / 100  # 0-1 scale

        # Calculate attribute modifiers
        tackler_modifier = (tackler_strength * 0.4 + tackler_tackling * 0.4 + tackler_speed * 0.2) * (1 - tackler_fatigue * 0.3)
        carrier_modifier = (carrier_agility * 0.4 + carrier_speed * 0.3 + carrier_mental * 0.3) * (1 - carrier_fatigue * 0.3)

        # Path progress factor - tackles later in the path are harder
        progress_factor = 1.0 - (path_progress * 0.3)  # 1.0 at start, 0.7 at end

        # Calculate final chance
        final_chance = base_chance * congestion_factor * (1 + tackler_modifier * 0.5) * (1 - carrier_modifier * 0.4) * progress_factor

        # Clamp between 0.1 and 0.9
        final_chance = max(0.1, min(0.9, final_chance))

        return final_chance


    def handle_kick_to_space(self, player, disposer_data):
        """Kick to a strategic position in space based on field position and tactics."""
        # Get team tactics
        team_tactics = self.game_state.home_team_tactics.tactics if player.team == self.game_state.home_team else self.game_state.away_team_tactics.tactics

        # Calculate kick distance based on player's kicking ability

        kick_distance = disposer_data['kick_distance']

        # Add some variation
        #kick_distance *= random.uniform(0.8, 1.2)

        # Determine field position category
        field_position = self.ground.get_zone_for_position(player.current_position, player.team_side)

        print(f"Field position: {field_position}")
        print(f"Player position: {player.current_position}")

        # Determine target position based on field position and tactics
        target_position = None

        if field_position == "defensive_50":
            # In defense, prefer to kick wide or to the corridor based on tactics
            if team_tactics['offense_strategy'] == 'stay_wide':
                # Kick wide to the boundary
                side = random.choice([-1, 1])  # Left or right
                target_x = player.current_position[0] + (kick_distance * 0.6 / 2)  # Forward but not too far
                target_y = player.current_position[1] + (side * kick_distance * 0.4 / 2)  # Wide
            else:
                # Kick to the corridor
                target_x = player.current_position[0] + (kick_distance * 0.8 / 2)  # More forward
                target_y = player.current_position[1] + random.uniform(-0.2, 0.2) * kick_distance / 2  # Slight variation

        elif field_position == "center_corridor":
            # In midfield, kick forward with direction based on tactics
            if team_tactics['offense_strategy'] == 'stay_wide':
                # Kick to the wings
                side = random.choice([-1, 1])  # Left or right
                target_x = player.current_position[0] + (kick_distance * 0.7 / 2)  # Forward
                target_y = player.current_position[1] + (side * kick_distance * 0.3 / 2)  # Wide
            else:
                # Kick to the corridor
                target_x = player.current_position[0] + (kick_distance * 0.9 / 2)  # More forward
                target_y = player.current_position[1] + random.uniform(-0.1, 0.1) * kick_distance / 2  # Slight variation

        else:  # forward
            # In forward, kick toward goal
            attacking_direction = self.game_state.home_team.attacking_direction if player.team_side == 'home' else self.game_state.home_team.attacking_direction
            target_x = player.current_position[0] + (attacking_direction * kick_distance * 0.9 / 2)

            # Aim more centrally in forward line
            current_y = player.current_position[1]
            center_y = self.ground.grid_width / 2
            target_y = current_y + (center_y - current_y) * 0.3  # Slight correction toward center

        # Ensure target is within bounds
        target_x = max(0, min(self.ground.grid_length - 1, target_x))
        target_y = max(0, min(self.ground.grid_width - 1, target_y))
        target_position = (int(target_x), int(target_y))

        # Determine if kick goes out of bounds
        is_boundary = self.ground.is_in_bounds(target_position)

        # Calculate accuracy based on kicking skill and pressure
        accuracy = player.ability_stats.kicking / 20

        # Adjust for pressure
        if hasattr(player, 'under_pressure'):
            accuracy *= (1 - player.under_pressure * 0.5)

        # Determine outcome
        if not is_boundary:
            if random.random() > accuracy:
                # Out on the full - turnover
                self.game_state.phase = "free_kick"
                self.game_state.free_kick_team = self.game_state.away_team if player.team == self.game_state.home_team else self.game_state.home_team

                # Find closest opponent
                closest_opponent = None
                min_distance = float('inf')
                for opp in self.game_state.away_team_players.values() if player.team == self.game_state.home_team else self.game_state.home_team_players.values():
                    dx = target_position[0] - opp.current_position[0]
                    dy = target_position[1] - opp.current_position[1]
                    distance = math.sqrt(dx*dx + dy*dy)

                    if distance < min_distance:
                        min_distance = distance
                        closest_opponent = opp

                self.game_state.ball_carrier = closest_opponent
                self.game_state.ball_position = target_position
                self.game_state.ball_carrier = None

                self.stats_manager.record_stat(player.name, 'turnovers')

                self.game_state.phase = "handle_possession"

                return {
                    "result": f"{player.name}'s kick goes out on the full! Free kick to {closest_opponent.name}.",
                    "commentary": f"Poor execution from {player.name}, the kick sails out on the full.",
                    "next_action": "free_kick",
                    "type": "match_event",
                    "event_category": "match_event",
                }
            else:
                # Throw-in
                print("No throm-in defined")
                sys.exit()

                self.game_state.phase = "throw_in"
                self.game_state.ball_position = target_position
                self.game_state.ball_carrier = None
                self.stats_manager.record_stat(player.name, 'kicks')

                return {
                    "result": f"{player.name}'s kick goes out of bounds. Throw-in.",
                    "commentary": f"{player.name} kicks to the boundary, we'll have a throw-in.",
                    "next_action": "throw_in",
                    "type": "match_event",
                    "event_category": "match_event",
                }
        else:
            # Ball lands in play - determine if any player gets to it
            self.game_state.ball_position = target_position

            # Find closest players to the landing spot
            closest_teammate = None
            closest_opponent = None
            min_teammate_distance = float('inf')
            min_opponent_distance = float('inf')

            for teammate in self.game_state.home_team_players.values() if player.team == self.game_state.home_team else self.game_state.away_team_players.values():
                if teammate != player:
                    dx = target_position[0] - teammate.current_position[0]
                    dy = target_position[1] - teammate.current_position[1]
                    distance = math.sqrt(dx*dx + dy*dy)

                    if distance < min_teammate_distance:
                        min_teammate_distance = distance
                        closest_teammate = teammate

            for opponent in self.game_state.away_team_players.values() if player.team == self.game_state.home_team else self.game_state.home_team_players.values():
                dx = target_position[0] - opponent.current_position[0]
                dy = target_position[1] - opponent.current_position[1]
                distance = math.sqrt(dx*dx + dy*dy)

                if distance < min_opponent_distance:
                    min_opponent_distance = distance
                    closest_opponent = opponent

            # Convert to meters
            min_teammate_distance_meters = min_teammate_distance * 2
            min_opponent_distance_meters = min_opponent_distance * 2

            # Determine who gets to the ball first
            if min_teammate_distance_meters < min_opponent_distance_meters:
                # Teammate gets to it first
                self.game_state.ball_carrier = closest_teammate
                closest_teammate.current_position = target_position

                self.stats_manager.record_stat(player.name, 'kicks')
                self.game_state.phase = "handle_possession"

                return {
                    "result": f"{player.name} kicks to space and {closest_teammate.name} runs onto it!",
                    "commentary": f"Smart kick from {player.name}, finding {closest_teammate.name} in space.",
                    "next_action": "handle_possession",
                    "type": "match_event",
                    "event_category": "match_event",
                }
            else:
                # Opponent gets to it first - turnover
                self.game_state.ball_carrier = closest_opponent
                closest_opponent.current_position = target_position

                self.stats_manager.record_stat(player.name, 'kicks')
                self.stats_manager.record_stat(player.name, 'turnovers')
                self.game_state.phase = "handle_possession"

                return {
                    "result": f"{player.name} kicks to space but {closest_opponent.name} intercepts!",
                    "commentary": f"{closest_opponent.name} reads the play perfectly and cuts off {player.name}'s kick.",
                    "next_action": "handle_possession",
                    "type": "match_event",
                    "event_category": "match_event",
                }

    def handle_blind_handball(self, player, disposer_data, receiver_options):
        """Quick handball in hope of finding teammate under pressure."""
        # Calculate handball distance

        handball_distance = disposer_data['handball_distance']

        # Under pressure, distance is reduced
        if disposer_data['disposer_pressure'] >= 0.8:
            handball_distance *= 0.8

        # Find teammates within handball distance
        nearby_teammates = []



        for opt in receiver_options:
            if opt.get('can_handball', False) and opt.get('receiver'):
                teammate = opt['receiver']
                distance = opt.get('distance', float('inf'))
                receiver_pressure = opt.get('receiver_pressure', 1.0)

                if distance <= handball_distance:
                    nearby_teammates.append((teammate, distance, receiver_pressure))

        # Sort by distance
        nearby_teammates.sort(key=lambda x: x[1])

        # Calculate success chance based on handball skill and pressure
        success_chance = player.ability_stats.handball / 20

        # Adjust for pressure
        #TODO: This is a bit of a hack, we should probably have a more sophisticated way of handling pressure
        if disposer_data['disposer_pressure'] >= 0.8:
            success_chance *= (1 - disposer_data['disposer_pressure'] * 0.2)

        # Determine outcome
        if random.random() < success_chance and nearby_teammates:
            # Successful handball to closest teammate
            receiver, _ = nearby_teammates[0]
            self.game_state.ball_carrier = receiver

            self.stats_manager.record_stat(player.name, 'handballs')
            self.game_state.phase = "handle_possession"

            return {
                "result": f"{player.name} quickly handballs to {receiver.name}!",
                "commentary": f"Quick hands, somehow {player.name} finds {receiver.name} nearby. how do they do that?",
                "next_action": "handle_possession",
                "type": "match_event",
                "event_category": "match_event",
            }
        else:
            # Failed handball - determine outcome
            #if random.random() < 0.6:  # 60% chance of dropped handball
            max_distance = disposer_data['handball_distance'] * 0.8
            attacking_direction = self.game_state.home_team.attacking_direction if player.team_side == 'home' else self.game_state.home_team.attacking_direction

            # Calculate a random position in a 140-degree forward arc
            angle_range = 140  # degrees
            base_angle = 0  # straight ahead
            random_angle = random.uniform(-angle_range/2, angle_range/2)
            angle_radians = math.radians(base_angle + random_angle)

            # Calculate random distance (between 50% and 100% of max)
            random_distance = random.uniform(0.5 * max_distance, max_distance)

            # Calculate new position
            dx = attacking_direction * math.cos(angle_radians) * random_distance / 2  # Convert meters to grid units
            dy = math.sin(angle_radians) * random_distance / 2

            new_x = player.current_position[0] + dx
            new_y = player.current_position[1] + dy

            # Ensure within bounds
            new_x = max(0, min(self.ground.grid_length - 1, new_x))
            new_y = max(0, min(self.ground.grid_width - 1, new_y))

            spill_position = (int(new_x), int(new_y))
            self.game_state.ball_position = spill_position
            self.game_state.ball_carrier = None

            self.stats_manager.record_stat(player.name, 'handballs')
            self.stats_manager.record_stat(player.name, 'turnovers')


            self.game_state.phase = "loose_ball"

            return {
                "result": f"{player.name}'s blind handball goes to ground!",
                "commentary": f"{player.name} Shits himself and handballs it wherevere he can! Didn't know Ben Keays was still playing.",
                "next_action": "loose_ball",
                "type": "match_event",
                "event_category": "match_event",
            }
            """
            else:  # 40% chance of intercepted handball
                # Find closest opponent
                closest_opponent = None
                min_distance = float('inf')

                for opponent in self.game_state.away_team_players.values() if player.team == self.game_state.home_team else self.game_state.home_team_players.values():
                    dx = opponent.current_position[0] - player.current_position[0]
                    dy = opponent.current_position[1] - player.current_position[1]
                    distance = math.sqrt(dx*dx + dy*dy)

                    if distance < min_distance:
                        min_distance = distance
                        closest_opponent = opponent

                self.game_state.ball_carrier = closest_opponent

                self.stats_manager.record_stat(player.name, 'handballs')
                self.stats_manager.record_stat(player.name, 'turnovers')
                self.stats_manager.record_stat(closest_opponent.name, 'intercepts')
                self.game_state.phase = "handle_possession"

                return {
                    "result": f"{player.name}'s blind handball is intercepted by {closest_opponent.name}!",
                    "commentary": f"Poor decision by {player.name}, and {closest_opponent.name} pounces on the mistake!",
                    "next_action": "handle_possession",
                    "type": "match_event",
                    "event_category": "match_event",
                }
            """

    def handle_long_bomb(self, player, disposer_data):
        """Kick long towards goal or advantage."""
        # Calculate kick distance
        kick_distance = disposer_data['kick_distance'] *1.1

        # Determine attacking direction
        attacking_direction = self.game_state.home_team.attacking_direction if player.team_side == 'home' else self.game_state.home_team.attacking_direction

        # Calculate target position - long bomb is more direct forward with variation
        # Use a wider angle for variation (30 degrees each side)
        angle_variation = random.uniform(-15, 15)
        angle_radians = math.radians(angle_variation)

        # Calculate forward vector with variation
        dx = attacking_direction * math.cos(angle_radians) * kick_distance * 0.9 / 2
        dy = math.sin(angle_radians) * kick_distance * 0.9 / 2

        target_x = player.current_position[0] + dx
        target_y = player.current_position[1] + dy

        # Ensure target is within bounds
        target_x = max(0, min(self.ground.grid_length - 1, target_x))
        target_y = max(0, min(self.ground.grid_width - 1, target_y))
        target_position = (int(target_x), int(target_y))

        # Check if kick goes out of bounds
        is_in_bounds = self.ground.is_in_bounds(target_position)

        # Calculate accuracy based on kicking skill and pressure
        accuracy = player.ability_stats.kicking / 20

        # Long bombs are harder
        accuracy *= 0.7

        # Adjust for pressure
        if disposer_data['disposer_pressure'] >= 0.8:
            accuracy *= (1 - disposer_data['disposer_pressure'] * 0.3)

        # Check if kick goes out on the full (more likely with lower accuracy)
        if not is_in_bounds or random.random() > accuracy * 1.2:
            # Out on the full - turnover
            # Find closest opponent to the boundary position
            closest_opponent = None
            min_distance = float('inf')

            for opponent in self.game_state.away_team_players.values() if player.team == self.game_state.home_team else self.game_state.home_team_players.values():
                dx = target_position[0] - opponent.current_position[0]
                dy = target_position[1] - opponent.current_position[1]
                distance = math.sqrt(dx*dx + dy*dy)


                nearest_inbounds_position = self.ground.adjust_to_oval(dx, dy)


                if distance < min_distance:
                    min_distance = distance
                    closest_opponent = opponent

            self.game_state.phase = "out_on_full"
            #self.game_state.free_kick_team = self.game_state.away_team if player.team == self.game_state.home_team else self.game_state.home_team
            self.game_state.ball_carrier = closest_opponent
            self.game_state.ball_position = nearest_inbounds_position

            self.stats_manager.record_stat(player.name, 'kicks')
            self.stats_manager.record_stat(player.name, 'turnovers')

            return {
                "result": f"{player.name}'s long bomb goes out on the full!",
                "commentary": f"Poor execution from {player.name}, the kick sails out on the full. Free kick to {closest_opponent.name}.",
                "next_action": "out_on_full",
                "type": "match_event",
                "event_category": "match_event",
            }

        # Check if in scoring range
        goal_distance = self.ground.is_in_range_for_shot(target_position, player.team_side)

        if goal_distance:
            # In scoring range - attempt at goal
            self.game_state.ball_position = target_position
            self.game_state.ball_carrier = player
            self.game_state.phase = "attempt_goal"

            self.stats_manager.record_stat(player.name, 'kicks')

            return {
                "result": f"{player.name} launches a long bomb toward goal!",
                "commentary": f"{player.name} goes for the big kick! This could be a spectacular goal if it comes off!",
                "next_action": "attempt_goal",
                "type": "match_event",
                "event_category": "match_event",
            }
        else:
            # Not in scoring range - calculate who can mark the ball
            self.game_state.ball_position = target_position

            # Calculate ball travel time (assuming ~20m/s for a kick)
            ball_travel_time = kick_distance / 10  # seconds

            # Find players who might be able to mark based on their distance and speed
            potential_markers = []

            # Check teammates
            for teammate in self.game_state.home_team_players.values() if player.team == self.game_state.home_team else self.game_state.away_team_players.values():
                if teammate != player:
                    dx = target_position[0] - teammate.current_position[0]
                    dy = target_position[1] - teammate.current_position[1]
                    distance = math.sqrt(dx*dx + dy*dy) * 2  # Convert to meters

                    # Calculate how long it would take player to reach the spot
                    player_speed = teammate.physical_stats.speed / 20  # m/s (5-8 m/s)
                    stamina_rating = teammate.physical_stats.stamina / 20
                    fatigue_factor = 1.0 - teammate.fatigue / 100

                    base_speed = 6.0 + (player_speed * 3.0)

                    effective_speed = base_speed * stamina_rating * fatigue_factor

                    # Calculate how far the opponent can move in the time the ball is traveling
                    max_distance_covered = effective_speed * ball_travel_time


                    # If player can reach in time, add to potential markers
                    if max_distance_covered >= distance:  # Give 10% extra time as buffer
                        #mark_chance = 0.4 + (teammate.ability_stats.marking / 20) * 0.5  # 0.4-0.9
                        potential_markers.append((teammate, True))  # True = teammate

            # Check opponents
            for opponent in self.game_state.away_team_players.values() if player.team == self.game_state.home_team else self.game_state.home_team_players.values():
                dx = target_position[0] - opponent.current_position[0]
                dy = target_position[1] - opponent.current_position[1]
                distance = math.sqrt(dx*dx + dy*dy) * 2  # Convert to meters

                # Calculate how long it would take player to reach the spot
                player_speed = opponent.physical_stats.speed / 20  # m/s (5-8 m/s)
                stamina_rating = opponent.physical_stats.stamina / 20
                fatigue_factor = 1.0 - opponent.fatigue / 100

                base_speed = 6.0 + (player_speed * 3.0)

                effective_speed = base_speed * stamina_rating * fatigue_factor

                # Calculate how far the opponent can move in the time the ball is traveling
                max_distance_covered = effective_speed * ball_travel_time

                # If player can reach in time, add to potential markers
                if max_distance_covered >= distance:  # Give 10% extra time as buffer
                    #mark_chance = 0.4 + (opponent.ability_stats.marking / 20) * 0.5  # 0.4-0.9
                    potential_markers.append((opponent, False))  # False = opponent

            # Sort by distance (who gets there first)
            potential_markers.sort(key=lambda x: x[1])

            # Move all potential markers to the target position (they tried to mark)
            for marker, _ in potential_markers:
                marker.current_position = target_position

            # If no one can mark, it's a loose ball
            if not potential_markers:
                self.game_state.ball_carrier = None

                self.stats_manager.record_stat(player.name, 'kicks')
                self.game_state.phase = "loose_ball"

                return {
                    "result": f"{player.name}'s long bomb lands in space!",
                    "commentary": f"The kick from {player.name} lands with no one around it. It's a foot race!",
                    "next_action": "loose_ball",
                    "type": "match_event",
                    "event_category": "match_event",
                }

            # If only one player can mark, they take it unopposed
            if len(potential_markers) == 1:
                marker, is_teammate = potential_markers[0]
                self.game_state.ball_carrier = marker

                self.stats_manager.record_stat(player.name, 'kicks')
                if not is_teammate:
                    self.stats_manager.record_stat(player.name, 'turnovers')
                    self.stats_manager.record_stat(marker.name, 'intercepts')
                else:
                    self.stats_manager.record_stat(marker.name, 'marks')

                self.game_state.phase = "handle_possession"

                if is_teammate:
                    return {
                        "result": f"{player.name}'s long bomb is marked by {marker.name}!",
                        "commentary": f"That was some kick by {player.name}, {marker.name} marks, did he mean that?",
                        "next_action": "handle_possession",
                        "type": "match_event",
                        "event_category": "match_event",
                    }
                else:
                    return {
                        "result": f"{player.name}'s long bomb is intercepted by {marker.name}!",
                        "commentary": f"{marker.name} reads the flight perfectly and takes an intercept mark!",
                        "next_action": "handle_possession",
                        "type": "match_event",
                        "event_category": "match_event",
                    }

            # Multiple players can mark - it's a contest
            # Get the two best positioned players (one from each team if possible)
            best_teammate = None
            best_opponent = None

            for marker, is_teammate in potential_markers:
                if is_teammate and not best_teammate:
                    best_teammate = marker
                elif not is_teammate and not best_opponent:
                    best_opponent = marker

                if best_teammate and best_opponent:
                    break

            # If we don't have one from each team, take the two best overall
            if not best_teammate or not best_opponent:
                if len(potential_markers) >= 2:
                    player1, _ = potential_markers[0]
                    player2, _ = potential_markers[1]

                    if not best_teammate:
                        best_teammate = player1
                    if not best_opponent:
                        best_opponent = player2
                else:
                    # Should never happen as we already checked for len=1
                    marker, _ = potential_markers[0]
                    best_teammate = marker if marker.team == player.team else None
                    best_opponent = marker if marker.team != player.team else None

            # Set up for contest
            #self.stats_manager.record_stat(player.name, 'kicks')

            self.game_state.phase = "advance_ball"

            # Return advance_ball for contest
            return {
                "result": f"{player.name}'s long bomb creates a marking contest!",
                "commentary": f"The kick from {player.name} is looooong,  created a contest between {best_teammate.name} and {best_opponent.name}!",
                "next_action": "advance_ball",
                "type": "match_event",
                "event_category": "match_event",
                "disponer": player,
                "reciever": best_teammate,
                "opponent": best_opponent,
                "position": target_position
            }

    def handle_kick_to_boundary(self, player, disposer_data):
        """Deliberately kick to boundary for a stoppage."""
        # Find nearest boundary
        x, y = player.current_position
        grid_width = self.ground.grid_width
        grid_length = self.ground.grid_length

        # Calculate distances to each boundary
        dist_to_left = y
        dist_to_right = grid_width - y
        dist_to_top = x
        dist_to_bottom = grid_length - x

        # Find closest boundary
        min_dist = min(dist_to_left, dist_to_right, dist_to_top, dist_to_bottom)

        # Calculate kick accuracy based on player skill and pressure
        accuracy = player.ability_stats.kicking / 20

        # Calculate target position near boundary
        if min_dist == dist_to_left:
            # Left boundary
            target_x = x + random.uniform(-5, 5)
            target_y = 0
        elif min_dist == dist_to_right:
            # Right boundary
            target_x = x + random.uniform(-5, 5)
            target_y = grid_width - 1
        elif min_dist == dist_to_top:
            # Top boundary
            target_x = 0
            target_y = y + random.uniform(-5, 5)
        else:
            # Bottom boundary
            target_x = grid_length - 1
            target_y = y + random.uniform(-5, 5)

        # Ensure within bounds
        target_x = max(0, min(grid_length - 1, target_x))
        target_y = max(0, min(grid_width - 1, target_y))

        target_position = (int(target_x), int(target_y))

        is_in_bounds = self.ground.is_in_bounds(target_position)
        nearest_inbounds_position = self.ground.adjust_to_oval(target_position[0], target_position[1])

        # Calculate accuracy based on kicking skill and pressure
        accuracy = player.ability_stats.kicking / 20

        # Long bombs are harder
        accuracy *= 0.7

        # Adjust for pressure
        if disposer_data['disposer_pressure'] >= 0.8:
            accuracy *= (1 - disposer_data['disposer_pressure'] * 0.3)

        # Check if kick goes out on the full (more likely with lower accuracy)
        if not is_in_bounds or random.random() > accuracy * 1.2:
                        # Out on the full - turnover
            # Find closest opponent to the boundary position
            closest_opponent = None
            min_distance = float('inf')



            for opponent in self.game_state.away_team_players.values() if player.team == self.game_state.home_team else self.game_state.home_team_players.values():
                dx = target_position[0] - opponent.current_position[0]
                dy = target_position[1] - opponent.current_position[1]
                distance = math.sqrt(dx*dx + dy*dy)

                if distance < min_distance:
                    min_distance = distance
                    closest_opponent = opponent

            self.game_state.phase = "out_on_full"
            #self.game_state.free_kick_team = self.game_state.away_team if player.team == self.game_state.home_team else self.game_state.home_team
            self.game_state.ball_carrier = closest_opponent
            self.game_state.ball_position = nearest_inbounds_position

            self.stats_manager.record_stat(player.name, 'kicks')
            self.stats_manager.record_stat(player.name, 'turnovers')

            return {
                "result": f"{player.name}'s long bomb goes out on the full!",
                "commentary": f"Poor execution from {player.name}, the kick sails out on the full. Free kick to {closest_opponent.name}.",
                "next_action": "out_on_full",
                "type": "match_event",
                "event_category": "match_event",
            }


        # Determine outcome - deliberate or throw-in
        if random.random() < 0.3:  # 30% chance of being called deliberate
            # Deliberate out of bounds - free kick to opposition
            opponent_team = self.game_state.away_team if player.team == self.game_state.home_team else self.game_state.home_team
            opponent_players = self.game_state.away_team_players if player.team == self.game_state.home_team else self.game_state.home_team_players

            # Find closest opponent to boundary position
            closest_opponent = None
            min_distance = float('inf')

            for opp in opponent_players.values():
                dx = target_position[0] - opp.current_position[0]
                dy = target_position[1] - opp.current_position[1]
                distance = math.sqrt(dx*dx + dy*dy)

                if distance < min_distance:
                    min_distance = distance
                    closest_opponent = opp

            self.game_state.phase = "free_kick"
            self.game_state.free_kick_team = opponent_team
            self.game_state.free_kick_player = closest_opponent
            closest_opponent.current_position = nearest_inbounds_position

            self.stats_manager.record_stat(player.name, 'kicks')
            self.stats_manager.record_stat(player.name, 'turnovers')

            return {
                "result": f"{player.name} kicks out of bounds on the full. Deliberate!",
                "commentary": f"The umpire has called that deliberate! Free kick to {closest_opponent.name}.",
                "next_action": "free_kick",
                "type": "match_event",
                "event_category": "match_event",
            }
        else:
            # Throw-in
            self.game_state.phase = "throw_in"
            self.game_state.ball_position = nearest_inbounds_position

            self.stats_manager.record_stat(player.name, 'kicks')

            return {
                "result": f"{player.name} kicks to the boundary. Throw-in.",
                "commentary": f"Smart play by {player.name} to force a stoppage. We'll have a throw-in.",
                "next_action": "throw_in",
                "type": "match_event",
                "event_category": "match_event",
            }

    def handle_snap_shot(self, player, disposer_data):
        """Quick snap shot at goal when in range."""
        # Check if in scoring range
        field_position = self.ground.get_distance_to_goal(self.game_state.ball_position, self.game_state.ball_carrier.team_side)

        if field_position > 35:
            # Not in scoring range, fall back to kick to space
            return self.handle_kick_to_space(player, disposer_data)

        # Calculate distance to goal
        x, y = player.current_position
        grid_length = self.ground.grid_length
        grid_width = self.ground.grid_width

        # Goal position depends on which way team is attacking
        if player.team_side == 'home':
            goal_x = grid_length - 1
        else:
            goal_x = 0

        goal_y = grid_width / 2

        dx = goal_x - x
        dy = goal_y - y
        distance_to_goal = math.sqrt(dx*dx + dy*dy) * 2  # Convert to meters

        # Calculate shot difficulty
        difficulty = 0.5

        # Adjust for distance
        if distance_to_goal > 40:
            difficulty += 0.4
        elif distance_to_goal > 30:
            difficulty += 0.2
        elif distance_to_goal > 20:
            difficulty += 0.1

        # Adjust for angle
        angle_to_goal = abs(math.degrees(math.atan2(dy, dx)))
        if angle_to_goal > 45:
            difficulty += 0.3
        elif angle_to_goal > 30:
            difficulty += 0.2
        elif angle_to_goal > 15:
            difficulty += 0.1

        # Calculate success chance
        goal_kicking_ability = player.ability_stats.goal_kicking / 20
        success_chance = (1 - difficulty) * goal_kicking_ability

        # Adjust for pressure
        success_chance *= (1 - disposer_data['disposer_pressure'] * 0.5)

        # Determine outcome
        outcome = random.random()

        self.game_state.phase = "attempt_goal"
        self.game_state.ball_carrier = player

        self.stats_manager.record_stat(player.name, 'shots_at_goal')

        if outcome < success_chance:
            # Goal!
            return {
                "result": f"{player.name} snaps and kicks a goal!",
                "commentary": f"Brilliant snap from {player.name}! That's a goal!",
                "next_action": "goal_scored",
                "type": "match_event",
                "event_category": "match_event",
            }
        elif outcome < success_chance + 0.2:
            # Behind
            return {
                "result": f"{player.name} snaps but it's just a behind.",
                "commentary": f"Good effort from {player.name} but it's just off line. One behind.",
                "next_action": "behind_scored",
                "type": "match_event",
                "event_category": "match_event",
            }
        else:
            # Miss
            return {
                "result": f"{player.name} snaps but misses everything.",
                "commentary": f"{player.name} tries to snap under pressure but misses everything. Goal kick.",
                "next_action": "goal_kick",
                "type": "match_event",
                "event_category": "match_event",
            }

    # Helper methods
    #def get_field_position_category(self, position):
        """Determine if position is in defensive, midfield, or forward area."""
        x, y = position
        grid_length = self.ground.grid_length

        if x < grid_length * 0.3:
            return "defensive"
        elif x < grid_length * 0.7:
            return "midfield"
        else:
            return "forward"

    def is_boundary_position(self, position):
        """Check if position is on or near the boundary."""
        x, y = position
        grid_width = self.ground.grid_width
        grid_length = self.ground.grid_length

        # Check if near the sideline
        if y <= 1 or y >= grid_width - 2:
            return True

        # Check if behind the goal line
        if x <= 1 or x >= grid_length - 2:
            return True

        return False

    def is_defensive_position(self, position):
        """Check if position is in defensive half."""
        team_side = self.game_state.ball_carrier.team_side
        x, y = position
        grid_length = self.ground.grid_length

        if team_side == 'home':
            return x < grid_length * 0.4
        else:
            return x > grid_length * 0.6

    def is_forward_position(self, position):
        """Check if position is in forward half."""
        team_side = self.game_state.ball_carrier.team_side
        x, y = position
        grid_length = self.ground.grid_length

        if team_side == 'home':
            return x > grid_length * 0.6
        else:
            return x < grid_length * 0.4

    def decide_disposal_type(self, player, option, disposer_data):
        """Decide whether to kick or handball based on situation."""
        if not option['can_kick']:
            return "handball"
        if not option['can_handball']:
            return "kick"

        # Calculate preference for each disposal type
        kick_pref = player.ability_stats.kicking / 20
        handball_pref = player.ability_stats.handball / 20

        print(f"Disposer Data: {disposer_data}")
        #time.sleep(2)

        # Adjust for pressure
        if disposer_data > 0.5:
            if player.ability_stats.mental < 10:
                handball_pref *= 1.5  # Prefer handballs under pressure

        # Adjust for distance
        if option['distance'] > 8:
            kick_pref *= 1.3  # Prefer kicks for longer distances

        # Adjust for how open the target is
        if option['space_rating'] > 0.7:
            kick_pref *= 1.3  # Prefer kicks to open targets

        return "kick" if kick_pref > handball_pref else "handball"

    def find_attacking_space(self, player):
        """Find space to kick to in attacking moves."""
        current_coords = self.get_coordinates(player.current_position)
        x, y = current_coords

        # Calculate maximum kick distance based on player's kicking ability
        #max_kick_distance = 2 + (player.ability_stats.kicking / 25)
        min_kick_distance = 15  # Minimum kick distance (1 cell)
        max_kick_distance = 60
        kick_distance = min_kick_distance + ((player.ability_stats.kicking - 1) / 19) * (max_kick_distance - min_kick_distance)

        potential_positions = []

        # Check positions in forward direction
        for dx in range(-1, 2):  # Left, Centre, Right
            for dy in range(0, int(kick_distance) + 1):
                new_x = x + dx
                new_y = min(4, y + dy)  # Cap at forward line (y=4)
                new_pos = (new_x, new_y)

                if self.get_position_from_coordinates(new_pos):
                    # Calculate position rating
                    rating = self.rate_attacking_position(
                        player, new_pos, current_coords
                    )
                    potential_positions.append((new_pos, rating))

        if potential_positions:
            chosen_pos = random.choices(
                [pos for pos, _ in potential_positions],
                weights=[rating for _, rating in potential_positions],
                k=1
            )[0]
            return self.get_position_from_coordinates(chosen_pos)

        # Fallback to straight ahead
        return self.get_position_from_coordinates((x, min(y + 1, 4)))





class ContestEngine:
    """Handles physical contests between players, maintaining exact compatibility with working AI version."""

    def __init__(self, ground: Ground, action_engine: ActionEngine, game_state: GameState):
        self.ground = ground
        self.game_state = game_state
        self.action_engine = action_engine
        self.stats_manager = StatsManager()

        # Required instance variables from working AI
        self.ball_position = None
        self.play_state = None
        self.field_zones = None
        #self.disposer = None
        self.home_team = None
        self.away_team = None
        self.home_team_players = None
        self.away_team_players = None
        self.home_team_tactics = None
        self.away_team_tactics = None

    def initialize_teams(self, home_team: Any, away_team: Any, home_team_players: Dict[str, Any], away_team_players: Dict[str, Any], home_team_tactics: Dict[str, Any], away_team_tactics: Dict[str, Any]) -> None:
        """Initialize team-related instance variables."""
        self.home_team = home_team
        self.away_team = away_team
        self.home_team_players = home_team_players
        self.away_team_players = away_team_players
        self.home_team_tactics = home_team_tactics
        self.away_team_tactics = away_team_tactics



    def simulate_center_bounce(self, home_team: Any, away_team: Any, home_team_players: Dict[str, Any], away_team_players: Dict[str, Any]) -> Dict[str, Any]:
        """Direct migration of original simulate_center_bounce from working AI."""
        print("Simulating center bounce")
        home_team_ruck = home_team_players["Ruck"]
        away_team_ruck = away_team_players["Ruck"]
        winner = self._ruck_contest(home_team_ruck, away_team_ruck)

        if winner == home_team_ruck:
            print(f"{home_team.name} wins the center bounce")
            self.game_state.ball_carrier = home_team_ruck
            self.stats_manager.record_stat(home_team_ruck.name, 'hitouts')
            # Continue with clearance but store result for next contest
            winner_team, team_players = self.clearance(home_team, away_team, home_team_players, away_team_players, home_team_ruck)

            return {
                "commentary": f"{winner_team.name} wins center bounce through {self.game_state.ball_carrier.name}",
                "next_action": "handle_possession",
                "type":"match_event",
                "event_category":"match_event",
                "team": winner_team,
                "team_players": team_players
            }
        else:
            print(f"{away_team.name} wins the center bounce")
            self.game_state.ball_carrier = away_team_ruck
            self.stats_manager.record_stat(away_team_ruck.name, 'hitouts')
            # Continue with clearance but store result for next contest
            winner_team, team_players = self.clearance(home_team, away_team, home_team_players, away_team_players, away_team_ruck)
            return {
                "commentary": f"{winner_team.name} wins center bounce through {self.game_state.ball_carrier.name}",
                "next_action": "handle_possession",
                "type":"match_event",
                "event_category":"match_event",
                "team": winner_team,
                "team_players": team_players
            }


    def _ruck_contest(self, home_team_ruck: Any, away_team_ruck: Any) -> Any:
        """Direct migration of original ruck_contest from working AI."""
        print(f"Ruck contest between {home_team_ruck.name} and {away_team_ruck.name}")
        home_team_ruck_score = self._calculate_ruck_performance(home_team_ruck)
        away_team_ruck_score = self._calculate_ruck_performance(away_team_ruck)
        print(f"{home_team_ruck.name} score: {home_team_ruck_score}, {away_team_ruck.name} score: {away_team_ruck_score}")
        return home_team_ruck if home_team_ruck_score > away_team_ruck_score else away_team_ruck

    def _calculate_ruck_performance(self, ruck: Any) -> float:
        """Direct migration of original calculate_ruck_performance from working AI."""
        height_weight = 1.5
        strength_weight = 1.4
        agility_weight = 1.3
        mental_weight = 1.2
        tactical_weight = 1.1
        consistency_weight = 1.0
        age_weight = 0.9
        stamina_weight = 0.8

        performance = (
            (ruck.physical_stats.height * height_weight) +
            (ruck.physical_stats.strength * strength_weight) +
            (ruck.physical_stats.agility * agility_weight) +
            (ruck.ability_stats.mental * mental_weight) +
            (ruck.ability_stats.tactical * tactical_weight) +
            (ruck.ability_stats.consistency * consistency_weight) +
            (ruck.physical_stats.age * age_weight) +
            (ruck.physical_stats.stamina * stamina_weight)
        )

        # Add a random element to simulate the unpredictability of sports
        random_factor = random.uniform(0.8, 1.2)  # Random factor between 0.8 and 1.2
        performance *= random_factor

        # Print the calculated performance for debugging
        print(f"Calculated performance for {ruck.name}: {performance:.2f}")

        return performance

    def clearance(self, home_team: Any, away_team: Any, home_team_players: Dict[str, Any], away_team_players: Dict[str, Any], ruck_winner: Any) -> Tuple[Any, Dict[str, Any]]:
        """Direct migration of original clearance from working AI."""
        print("Simulating clearance")
        midfielders_home_team = [home_team_players["Rover"], home_team_players["RuckRover"], home_team_players["Centre"]]
        midfielders_away_team = [away_team_players["Rover"], away_team_players["RuckRover"], away_team_players["Centre"]]

        home_team_score = sum(self.calculate_midfielder_performance(player) for player in midfielders_home_team)
        away_team_score = sum(self.calculate_midfielder_performance(player) for player in midfielders_away_team)

        # Add extra weight to the team whose ruck won the center bounce
        if ruck_winner == home_team:
            home_team_score *= 1.1
        else:
            away_team_score *= 1.1

        print(f"Home team clearance score: {home_team_score:.2f}, Away team clearance score: {away_team_score:.2f}")

        if home_team_score > away_team_score:
            winner_team = home_team
            winner_players = midfielders_home_team
        else:
            winner_team = away_team
            winner_players = midfielders_away_team

        # Determine the player who gets the ball
        player_with_ball = random.choices(
            winner_players,
            weights=[self.calculate_midfielder_performance(player) for player in winner_players],
            k=1
        )[0]

        self.stats_manager.record_stat(player_with_ball.name, 'clearances')
        self.game_state.ball_carrier = player_with_ball
        self.game_state.ball_position = self.game_state.ball_carrier.current_position
        print(f"Ball position: {self.game_state.ball_position}")
        #time.sleep(2)
        print(f"{self.game_state.ball_carrier.name} from {winner_team.name} gets the ball after clearance, and Carrier team is {self.game_state.ball_carrier.team_side}")

        if self.game_state.ball_carrier.team_side == 'home':
            team_players = home_team_players
        else:
            team_players = away_team_players

        return winner_team, team_players

    def calculate_midfielder_performance(self, player: Any) -> float:
        """Direct migration of original calculate_midfielder_performance from working AI."""
        speed_weight = 1.5
        agility_weight = 1.4
        stamina_weight = 1.3
        strength_weight = 1.2
        mental_weight = 1.1
        tactical_weight = 1.0
        consistency_weight = 0.9
        age_weight = 0.8

        performance = (
            (player.physical_stats.speed * speed_weight) +
            (player.physical_stats.agility * agility_weight) +
            (player.physical_stats.stamina * stamina_weight) +
            (player.physical_stats.strength * strength_weight) +
            (player.ability_stats.mental * mental_weight) +
            (player.ability_stats.tactical * tactical_weight) +
            (player.ability_stats.consistency * consistency_weight) +
            (player.physical_stats.age * age_weight)
        )

        # Add a random element to simulate the unpredictability of sports
        random_factor = random.uniform(0.8, 1.2)  # Random factor between 0.8 and 1.2
        performance *= random_factor

        # Print the calculated performance for debugging
        print(f"Calculated performance for {player.name}: {performance:.2f}")

        return performance

    def possession_contest(self, receiver: Any, opponent: Any) -> Tuple[float, float]:
        """Direct migration of original possession_contest from working AI."""
        print("Simulating possession contest")

        receiver_weights = {
            'marking': 1.5,
            'strength': 1.4,
            'consistency': 1.3,
            'agility': 1.2,
            'age': 1.1,
            'stamina': 1.0
        }

        opponent_weights = {
            'tackling': 1.5,
            'strength': 1.4,
            'consistency': 1.3,
            'speed': 1.2,
            'age': 1.1,
            'stamina': 1.0
        }
        print(f"Reciever {receiver} Opponent {opponent}")
        receiver_performance = self.calculate_performance(receiver, receiver_weights)
        opponent_performance = self.calculate_performance(opponent, opponent_weights)

        print(f"{receiver.name} performance: {receiver_performance:.2f}")
        print(f"{opponent.name} performance: {opponent_performance:.2f}")

        return receiver_performance, opponent_performance

    def calculate_performance(self, player: Any, weights: Dict[str, float]) -> float:
        """Direct migration of original calculate_performance from working AI."""
        performance = 0
        for attribute, weight in weights.items():
            performance += getattr(player.ability_stats, attribute, 0) * weight
            performance += getattr(player.physical_stats, attribute, 0) * weight

        random_factor = random.uniform(0.8, 1.2)  # Random factor between 0.8 and 1.2
        performance *= random_factor

        print(f"Calculated performance for {player.name}: {performance:.2f}")
        return performance


class Team:
    """Represents a team in the match."""

    def __init__(self, name: str, tactics: Dict[str, Any], players: Dict[str, Any], team_side: str):
        self.name = name
        self.tactics = tactics
        #print(f"Team {self.name} tactics: {self.tactics}")
        print(f"Teams initialized")
        #time.sleep(3)
        self.players = players
        self.team_side = team_side  # 'home' or 'away'
        self.score = 0
        self.behinds = 0
        self.total_score = 0

        # Initialize default tactics
        #self.tactics = {
        #    'mentality': 'balanced',  # 'defensive', 'balanced', or 'attacking'
        #    'defense_strategy': 'balanced',  # 'zone_mark', 'man_mark', or 'balanced'
        #    'offense_strategy': 'balanced',  # 'direct', 'stay_wide', or 'balanced'
        #    'push_factor': 1  # 1, 2, or 3 - how far defenders push up the field
        #}

    #def __str__(self) -> str:
    #    """String representation of team."""
    #    return f"{self.name}: {self.score}.{self.behinds} ({self.total_score})"

    def Tactics_handler(
        self,
        position: str,
        receiver: Any,
        opponent_team: Any,
        receiver_performance: float,
        opponent_performance: float,
        Current_team_tactics: Dict[str, Any],
        opponent_team_tactics: Dict[str, Any]
    ) -> float:
        """Handle team tactics and their effect on player performance."""
        tactics = Current_team_tactics
        opponent_tactics = opponent_team_tactics

        if tactics['mentality'] == 'attacking':
            if position in ["LF", "FF", "RF"]:
                receiver_performance *= 1.1
            turnover_risk = 0.1
            if random.random() < turnover_risk:
                print(f"Turnover risk due to attacking mentality!")

        elif tactics['mentality'] == 'defensive':
            if opponent_tactics['mentality'] == 'attacking':
                opponent_performance *= 0.9

            if position in ["LF", "FF", "RF"]:
                receiver_performance *= 0.9

        return receiver_performance

    def update_tactics(self, new_tactics: Dict[str, Any]) -> None:
        """Update team tactics."""
        self.tactics.update(new_tactics)

    def get_team_structure(self) -> Dict[str, List[Any]]:
        """Get team structure by lines."""
        structure = {
            'forwards': [],
            'midfielders': [],
            'defenders': []
        }

        for player in self.players.values():
            if player.position in ['FF', 'CHF', 'LHF', 'RHF', 'LF', 'RF']:
                structure['forwards'].append(player)
            elif player.position in ['Centre', 'Rover', 'RuckRover', 'LWing', 'RWing', 'Ruck']:
                structure['midfielders'].append(player)
            elif player.position in ['FB', 'CHB', 'LHB', 'RHB', 'LB', 'RB']:
                structure['defenders'].append(player)

        return structure

    def get_team_balance(self) -> Dict[str, float]:
        """
        Get team balance metrics across different areas of the field.
        Provides a comprehensive analysis of team strengths and weaknesses.
        """
        structure = self.get_team_structure()

        # Calculate basic strength metrics
        forward_strength = sum(p.ability_stats.goal_kicking for p in structure['forwards']) / len(structure['forwards']) if structure['forwards'] else 0
        midfield_strength = sum(p.ability_stats.tactical for p in structure['midfielders']) / len(structure['midfielders']) if structure['midfielders'] else 0
        defense_strength = sum(p.ability_stats.marking for p in structure['defenders']) / len(structure['defenders']) if structure['defenders'] else 0

        # Calculate additional balance metrics
        forward_mobility = sum(p.physical_stats.speed for p in structure['forwards']) / len(structure['forwards']) if structure['forwards'] else 0
        midfield_mental = sum(p.physical_stats.mental for p in structure['midfielders']) / len(structure['midfielders']) if structure['midfielders'] else 0
        defense_tackling = sum(p.ability_stats.tackling for p in structure['defenders']) / len(structure['defenders']) if structure['defenders'] else 0

        # Calculate team balance ratios
        attack_defense_ratio = forward_strength / defense_strength if defense_strength > 0 else 1.0
        midfield_control = midfield_strength / ((forward_strength + defense_strength) / 2) if (forward_strength + defense_strength) > 0 else 1.0

        return {
            'forward_strength': forward_strength,
            'midfield_strength': midfield_strength,
            'defense_strength': defense_strength,
            'forward_mobility': forward_mobility,
            'midfield_endurance': midfield_mental,
            'defense_positioning': defense_tackling,
            'attack_defense_ratio': attack_defense_ratio,
            'midfield_control': midfield_control
        }

    def get_team_fatigue(self) -> float:
        """
        Get average team fatigue directly from player stats.
        Accounts for different player roles and their importance.
        """
        if not self.players:
            return 0.0

        # Get fatigue from each player
        total_fatigue = 0.0
        total_weight = 0.0

        for player in self.players.values():
            # Weight by position importance (midfielders fatigue matters more)
            weight = 1.0
            if player.position in ['Centre', 'Rover', 'RuckRover']:
                weight = 1.5  # Midfielders
            elif player.position in ['Ruck']:
                weight = 1.3  # Ruck

            total_fatigue += player.current_fatigue * weight
            total_weight += weight

        return total_fatigue / total_weight if total_weight > 0 else 0.0

    def get_team_morale(self) -> float:
        """
        Get team morale based on multiple factors including:
        - Score difference
        - Fatigue
        - Recent performance
        - Time left in game
        """
        if not hasattr(self, 'opponent'):
            return 1.0

        # Base morale starts at 1.0
        morale = 1.0

        # Score difference factor
        score_diff = self.total_score - getattr(self.opponent, 'total_score', 0)
        if score_diff > 0:
            morale += min(0.3, score_diff / 30)  # Bonus for leading (max +0.3)
        else:
            morale -= min(0.3, abs(score_diff) / 20)  # Penalty for trailing (max -0.3)

        # Fatigue factor
        fatigue_penalty = self.get_team_fatigue() / 200  # Max 0.5 reduction
        morale -= fatigue_penalty

        # Time factor - teams trailing get a morale boost late in game
        if hasattr(self, 'game_state') and hasattr(self.game_state, 'quarter'):
            quarter = self.game_state.quarter
            quarter_time = getattr(self.game_state, 'quarter_time', 0)

            # Late game comeback factor
            if quarter >= 3 and score_diff < 0:
                comeback_boost = min(0.2, abs(score_diff) / 40) * (quarter / 4)
                morale += comeback_boost

        # Ensure morale is within reasonable bounds
        return max(0.5, min(1.5, morale))

    def get_team_pressure(self) -> float:
        """
        Calculate team's overall pressure level based on player abilities,
        team tactics, and push factor.
        """
        structure = self.get_team_structure()

        # Calculate pressure from each line
        forward_pressure = sum(p.ability_stats.tackling for p in structure['forwards']) / len(structure['forwards']) if structure['forwards'] else 0
        midfield_pressure = sum(p.ability_stats.tackling for p in structure['midfielders']) / len(structure['midfielders']) if structure['midfielders'] else 0
        defense_pressure = sum(p.ability_stats.tackling for p in structure['defenders']) / len(structure['defenders']) if structure['defenders'] else 0

        # Weight the pressure (midfield pressure most important)
        weighted_pressure = (
            forward_pressure * 0.3 +
            midfield_pressure * 0.5 +
            defense_pressure * 0.2
        ) / 10  # Scale to 0-1 range

        # Apply push factor
        push_factor = self.tactics.get('push_factor', 1)
        push_multiplier = 0.8 + (push_factor * 0.2)  # 1.0, 1.2, or 1.4
        weighted_pressure *= push_multiplier

        # Adjust for tactics
        mentality = self.tactics.get('mentality', 'balanced')
        if mentality == 'attacking':
            weighted_pressure *= 1.2  # More aggressive pressing
        elif mentality == 'defensive':
            weighted_pressure *= 0.9  # More conservative pressing

        defense_strategy = self.tactics.get('defense_strategy', 'balanced')
        if defense_strategy == 'man_mark':
            weighted_pressure *= 1.2  # Man marking increases pressure
        elif defense_strategy == 'zone_mark':
            weighted_pressure *= 0.9  # Zone marking is more conservative

        return min(1.0, weighted_pressure)

    def get_team_possession_style(self) -> Dict[str, float]:
        """
        Calculate team's possession style metrics based on team tactics.
        Returns metrics for short vs long game, width, and directness.
        """
        # Base values
        short_game = 0.5
        long_game = 0.5
        width = 0.5
        directness = 0.5

        # Adjust based on tactics
        mentality = self.tactics.get('mentality', 'balanced')
        offense_strategy = self.tactics.get('offense_strategy', 'balanced')
        defense_strategy = self.tactics.get('defense_strategy', 'balanced')
        push_factor = self.tactics.get('push_factor', 1)

        # Mentality affects directness and short/long game balance
        if mentality == 'attacking':
            directness += 0.2
            long_game += 0.1
        elif mentality == 'defensive':
            directness -= 0.1
            short_game += 0.1

        # Offense strategy affects width and short/long game
        if offense_strategy == 'direct':
            long_game += 0.2
            directness += 0.2
            width -= 0.1
        elif offense_strategy == 'stay_wide':
            width += 0.3
            short_game += 0.1

        # Defense strategy affects overall possession approach
        if defense_strategy == 'man_mark':
            directness += 0.1  # More direct when winning ball from man marking
        elif defense_strategy == 'zone_mark':
            short_game += 0.1  # More controlled buildup from zone defense

        # Push factor affects directness
        directness += (push_factor - 1) * 0.1

        # Ensure values are in range 0-1
        short_game = max(0.1, min(0.9, short_game))
        long_game = max(0.1, min(0.9, long_game))
        width = max(0.1, min(0.9, width))
        directness = max(0.1, min(0.9, directness))

        # Normalize short_game and long_game to sum to 1
        total = short_game + long_game
        short_game /= total
        long_game /= total

        return {
            'short_game': short_game,
            'long_game': long_game,
            'width': width,
            'directness': directness
        }

    def get_team_performance(self) -> float:
        """
        Calculate team's overall performance rating based on comprehensive metrics.
        Returns a rating from 0-10.
        """
        stats = self.get_team_stats()

        # Base rating components
        score_rating = self.total_score * 0.05  # Each point worth 0.05 rating

        # Team balance components
        forward_rating = stats['balance'].get('forward_strength', 0) * 0.5
        midfield_rating = stats['balance'].get('midfield_strength', 0) * 0.7  # Midfield weighted more
        defense_rating = stats['balance'].get('defense_strength', 0) * 0.5

        # Team cohesion components
        tactical_rating = 0.0
        if 'tactics_effectiveness' in stats:
            tactical_rating = sum(stats['tactics_effectiveness'].values()) / len(stats['tactics_effectiveness']) * 2

        # Calculate base performance
        base_performance = score_rating + forward_rating + midfield_rating + defense_rating + tactical_rating

        # Adjust for fatigue and morale
        fatigue_factor = 1.0 - (stats.get('fatigue', 0) / 200)  # Max 50% reduction
        morale_factor = stats.get('morale', 1.0)

        # Calculate final performance
        performance = base_performance * fatigue_factor * morale_factor

        # Scale to 0-10 range
        return min(10.0, max(0.0, performance))

    def get_team_momentum(self) -> float:
        """Calculate team's current momentum."""
        if not hasattr(self, 'opponent'):
            return 1.0

        # Base momentum from morale
        momentum = self.get_team_morale()

        # Adjust for recent scoring (if available)
        if hasattr(self, 'recent_scores'):
            recent_diff = sum(1 if score > 0 else -1 for score in self.recent_scores[-5:])
            momentum *= (1.0 + recent_diff * 0.1)  # ±10% per recent score

        # Adjust for field position (if available)
        if hasattr(self, 'field_position'):
            if self.field_position > 0.7:  # In forward 50
                momentum *= 1.2
            elif self.field_position < 0.3:  # In defensive 50
                momentum *= 0.8

        return max(0.5, min(2.0, momentum))

    def get_team_tactics_effectiveness(self) -> Dict[str, float]:
        """Calculate effectiveness of current tactics."""
        stats = self.get_team_stats()
        effectiveness = {}

        # Mentality effectiveness
        if self.tactics['mentality'] == 'attacking':
            effectiveness['mentality'] = stats['balance']['forward_strength'] / 10
        elif self.tactics['mentality'] == 'defensive':
            effectiveness['mentality'] = stats['balance']['defense_strength'] / 10
        else:
            effectiveness['mentality'] = (stats['balance']['midfield_strength'] / 10)

        # Offense strategy effectiveness
        if self.tactics['offense_strategy'] == 'direct':
            effectiveness['offense'] = stats['possession_style']['long_game'] / 10
        elif self.tactics['offense_strategy'] == 'stay_wide':
            effectiveness['offense'] = (
                stats['formation_balance']['forward_width'] +
                stats['formation_balance']['midfield_width']
            ) / 20
        else:
            effectiveness['offense'] = stats['possession_style']['short_game'] / 10

        # Defense strategy effectiveness
        if self.tactics['defense_strategy'] == 'man_mark':
            effectiveness['defense'] = stats['pressure'] / 10
        elif self.tactics['defense_strategy'] == 'zone_mark':
            effectiveness['defense'] = (1.0 - stats['formation_balance']['defense_depth'])
        else:
            effectiveness['defense'] = stats['balance']['defense_strength'] / 10

        return effectiveness

    def adjust_tactics_based_on_performance(self) -> None:
        """Adjust team tactics based on performance."""
        effectiveness = self.get_team_tactics_effectiveness()

        # Adjust mentality
        if effectiveness['mentality'] < 0.5:
            if self.tactics['mentality'] == 'attacking':
                self.tactics['mentality'] = 'balanced'
            elif self.tactics['mentality'] == 'defensive':
                self.tactics['mentality'] = 'balanced'

        # Adjust offense strategy
        if effectiveness['offense'] < 0.5:
            if self.tactics['offense_strategy'] == 'direct':
                self.tactics['offense_strategy'] = 'balanced'
            elif self.tactics['offense_strategy'] == 'stay_wide':
                self.tactics['offense_strategy'] = 'balanced'

        # Adjust defense strategy
        if effectiveness['defense'] < 0.5:
            if self.tactics['defense_strategy'] == 'zone_mark':
                self.tactics['defense_strategy'] = 'balanced'
            elif self.tactics['defense_strategy'] == 'man_mark':
                self.tactics['defense_strategy'] = 'balanced'

    def get_optimal_tactics(self) -> Dict[str, str]:
        """Calculate optimal tactics based on team attributes."""
        #ToDo not needed? Tactics are sent from frontend.
        stats = self.get_team_stats()

        # Determine optimal mentality
        if stats['balance']['forward_strength'] > stats['balance']['defense_strength']:
            mentality = 'attacking'
        elif stats['balance']['defense_strength'] > stats['balance']['forward_strength']:
            mentality = 'defensive'
        else:
            mentality = 'balanced'

        # Determine optimal offense strategy
        if stats['possession_style']['long_game'] > stats['possession_style']['short_game']:
            offense = 'direct'
        elif stats['formation_balance']['forward_width'] > 0.7:
            offense = 'stay_wide'
        else:
            offense = 'balanced'

        # Determine optimal defense strategy
        if stats['pressure'] > 7.0:
            defense = 'high_press'
        elif stats['balance']['defense_strength'] > 7.0:
            defense = 'deep_block'
        else:
            defense = 'balanced'

        return {
            'mentality': mentality,
            'offense_strategy': offense,
            'defense_strategy': defense
        }

    def get_team_weaknesses(self) -> Dict[str, float]:
        """Identify team weaknesses based on stats."""
        stats = self.get_team_stats()
        weaknesses = {}

        # Forward line weaknesses
        if stats['balance']['forward_strength'] < 6.0:
            weaknesses['forward_strength'] = 6.0 - stats['balance']['forward_strength']

        # Midfield weaknesses
        if stats['balance']['midfield_strength'] < 6.0:
            weaknesses['midfield_strength'] = 6.0 - stats['balance']['midfield_strength']

        # Defense weaknesses
        if stats['balance']['defense_strength'] < 6.0:
            weaknesses['defense_strength'] = 6.0 - stats['balance']['defense_strength']

        # Formation weaknesses
        if stats['formation_balance']['forward_width'] < 0.4:
            weaknesses['forward_width'] = 0.4 - stats['formation_balance']['forward_width']

        if stats['formation_balance']['defense_width'] < 0.4:
            weaknesses['defense_width'] = 0.4 - stats['formation_balance']['defense_width']

        # Style weaknesses
        if stats['possession_style']['short_game'] < 5.0 and stats['possession_style']['long_game'] < 5.0:
            weaknesses['ball_movement'] = 5.0 - max(stats['possession_style']['short_game'], stats['possession_style']['long_game'])

        return weaknesses

    def get_team_strengths(self) -> Dict[str, float]:
        """Identify team strengths based on stats."""
        stats = self.get_team_stats()
        strengths = {}

        # Forward line strengths
        if stats['balance']['forward_strength'] > 7.0:
            strengths['forward_strength'] = stats['balance']['forward_strength'] - 7.0

        # Midfield strengths
        if stats['balance']['midfield_strength'] > 7.0:
            strengths['midfield_strength'] = stats['balance']['midfield_strength'] - 7.0

        # Defense strengths
        if stats['balance']['defense_strength'] > 7.0:
            strengths['defense_strength'] = stats['balance']['defense_strength'] - 7.0

        # Formation strengths
        if stats['formation_balance']['forward_width'] > 0.7:
            strengths['forward_width'] = stats['formation_balance']['forward_width'] - 0.7

        if stats['formation_balance']['defense_width'] > 0.7:
            strengths['defense_width'] = stats['formation_balance']['defense_width'] - 0.7

        # Style strengths
        if stats['possession_style']['short_game'] > 7.0:
            strengths['short_game'] = stats['possession_style']['short_game'] - 7.0

        if stats['possession_style']['long_game'] > 7.0:
            strengths['long_game'] = stats['possession_style']['long_game'] - 7.0

        return strengths

    def get_team_matchups(self, opponent: Any) -> Dict[str, float]:
        """Calculate matchup ratings against opponent."""
        our_stats = self.get_team_stats()
        opp_stats = opponent.get_team_stats()

        matchups = {}

        # Forward line matchup
        matchups['forward_vs_defense'] = (
            our_stats['balance']['forward_strength'] /
            opp_stats['balance']['defense_strength']
        )

        # Midfield matchup
        matchups['midfield'] = (
            our_stats['balance']['midfield_strength'] /
            opp_stats['balance']['midfield_strength']
        )

        # Defense matchup
        matchups['defense_vs_forward'] = (
            our_stats['balance']['defense_strength'] /
            opp_stats['balance']['forward_strength']
        )

        # Style matchups
        if our_stats['possession_style']['short_game'] > our_stats['possession_style']['long_game']:
            matchups['ball_movement'] = (
                our_stats['possession_style']['short_game'] /
                opp_stats['pressure']
            )
        else:
            matchups['ball_movement'] = (
                our_stats['possession_style']['long_game'] /
                opp_stats['balance']['defense_strength']
            )

        return matchups

    def adjust_tactics_for_matchup(self, opponent: Any) -> None:
        """Adjust tactics based on matchup against opponent."""
        #TODO: dont do this automatically, this should be done by the player and sent from frontend.
        matchups = self.get_team_matchups(opponent)

        # Adjust mentality based on matchups
        if matchups['forward_vs_defense'] > 1.2:
            self.tactics['mentality'] = 'attacking'
        elif matchups['defense_vs_forward'] < 0.8:
            self.tactics['mentality'] = 'defensive'

        # Adjust offense strategy based on matchups
        if matchups['ball_movement'] > 1.2:
            if self.get_team_stats()['possession_style']['short_game'] > self.get_team_stats()['possession_style']['long_game']:
                self.tactics['offense_strategy'] = 'possession'
            else:
                self.tactics['offense_strategy'] = 'direct'
        else:
            self.tactics['offense_strategy'] = 'balanced'

        # Adjust defense strategy based on matchups
        if matchups['defense_vs_forward'] > 1.2:
            self.tactics['defense_strategy'] = 'high_press'
        elif matchups['defense_vs_forward'] < 0.8:
            self.tactics['defense_strategy'] = 'deep_block'

    def rotate_squad(self) -> None:
        """Rotate squad to manage fatigue."""
        #TODO: Implement squad rotation logic for players on the bench.
        pass

    def calculate_tactical_rating(self, position):
        """
        Calculate tactical rating for a player based on team tactics.

        Args:
            position: Player's position
            offense_strategy: Team's offensive strategy ('direct' or 'stay_wide')
            defense_strategy: Team's defensive strategy ('zone_mark' or 'man_mark')
            mentality: Team's mentality ('defensive' or 'attacking')

        Returns:
            Tactical rating as a float
        """
        base_rating = 1.0

        # Adjust for position and offense strategy
        if self.tactics['offense_strategy'] == 'direct':
            if position in ['FF', 'CHF']:  # Key forwards benefit from direct play
                base_rating *= 1.2
            elif position in ['LWing', 'RWing']:  # Wings less involved in direct play
                base_rating *= 0.9
        elif self.tactics['offense_strategy'] == 'stay_wide':
            if position in ['LWing', 'RWing', 'LHF', 'RHF']:  # Wide players benefit
                base_rating *= 1.3
            elif position in ['FF', 'CHF']:  # Central forwards less involved
                base_rating *= 0.9

        # Adjust for position and defense strategy
        if self.tactics['defense_strategy'] == 'man_mark':
            if position in ['FB', 'CHB', 'LB', 'RB']:  # Defenders benefit from man marking
                base_rating *= 1.25
            elif position in ['Centre', 'Rover']:  # Midfielders slightly benefit
                base_rating *= 1.1
        elif self.tactics['defense_strategy'] == 'zone_mark':
            if position in ['LHB', 'RHB', 'LWing', 'RWing']:  # Wide defenders benefit from zone
                base_rating *= 1.2
            elif position in ['FB', 'CHB']:  # Key defenders slightly less effective
                base_rating *= 0.95

        # Adjust for position and mentality
        if self.tactics['mentality'] == 'defensive':
            if position in ['FB', 'CHB', 'LB', 'RB', 'LHB', 'RHB']:  # Defenders benefit
                base_rating *= 1.2
            elif position in ['FF', 'CHF', 'LF', 'RF']:  # Forwards less effective
                base_rating *= 0.85
        elif self.tactics['mentality'] == 'attacking':
            if position in ['FF', 'CHF', 'LF', 'RF', 'LHF', 'RHF']:  # Forwards benefit
                base_rating *= 1.2
            elif position in ['FB', 'CHB', 'LB', 'RB']:  # Defenders less effective
                base_rating *= 0.9

        # Apply push factor
        base_rating = self.apply_push_factor(base_rating, is_defensive=(position in ['FB', 'CHB', 'LB', 'RB', 'LHB', 'RHB']))

        return base_rating

    def apply_push_factor(self, value, is_defensive=False):
        """Apply push_factor to a value.

        Args:
            value: The base value to adjust
            is_defensive: If True, higher push_factor reduces the value (for defensive positions)
                         If False, higher push_factor increases the value (for offensive positions)

        Returns:
            The adjusted value based on push_factor
        """
        push_factor = int(self.tactics['push_factor'])

        if is_defensive:
            # For defensive values, higher push_factor means less defensive focus
            multiplier = 1.0 - ((push_factor - 1) * 0.15)  # 1.0, 0.85, or 0.7
        else:
            # For offensive values, higher push_factor means more offensive focus
            multiplier = 1.0 + ((push_factor - 1) * 0.2)  # 1.0, 1.2, or 1.4

        return value * multiplier

    def get_tactical_pressure(self) -> float:
        """
        Calculate the team's tactical pressure based on tactics.

        Returns:
            Pressure rating as a float (0.0-1.0)
        """
        if not hasattr(self, 'tactics') or not self.tactics:
            return 0.5  # Default pressure

        # Base pressure from mentality
        base_pressure = 0.5
        mentality = self.tactics['mentality']
        if mentality == 'defensive':
            base_pressure += 0.2  # More defensive pressure
        elif mentality == 'attacking':
            base_pressure -= 0.1  # Less defensive pressure

        # Adjust based on defense strategy
        defense_strategy = self.tactics['defense_strategy']
        if defense_strategy == 'man_mark':
            base_pressure += 0.15  # Man marking increases pressure
        elif defense_strategy == 'zone_mark':
            base_pressure -= 0.05  # Zone marking is more conservative

        # Apply push factor
        push_factor = int(self.tactics['push_factor'])
        pressure = self.apply_push_factor(base_pressure, is_defensive=True)

        # Ensure pressure is within bounds
        return max(0.1, min(1.0, pressure))

    def get_tactical_possession_style(self) -> Dict[str, float]:
        """Calculate team's possession style metrics based on tactics."""
        # Base style metrics from player attributes
        short_game_base = sum(p.ability_stats.handballing for p in self.players.values()) / len(self.players)
        long_game_base = sum(p.ability_stats.kicking for p in self.players.values()) / len(self.players)
        contested_base = sum(p.physical_stats.strength for p in self.players.values()) / len(self.players)
        uncontested_base = sum(p.physical_stats.speed for p in self.players.values()) / len(self.players)

        # Apply tactical adjustments

        # Offense strategy affects short vs long game
        if self.tactics['offense_strategy'] == 'direct':
            long_game = self.apply_push_factor(long_game_base, is_defensive=False)
            short_game = short_game_base * 0.8  # Reduce short game focus
        elif self.tactics['offense_strategy'] == 'stay_wide':
            long_game = long_game_base * 0.9
            short_game = self.apply_push_factor(short_game_base, is_defensive=False)
        else:  # balanced
            long_game = long_game_base
            short_game = short_game_base

        # Mentality affects contested vs uncontested
        if self.tactics['mentality'] == 'attacking':
            contested = contested_base * 0.9  # Less focus on contested possessions
            uncontested = self.apply_push_factor(uncontested_base, is_defensive=False)  # More focus on speed and space
        elif self.tactics['mentality'] == 'defensive':
            contested = self.apply_push_factor(contested_base, is_defensive=True)  # More focus on contested possessions
            uncontested = uncontested_base * 0.9  # Less focus on speed
        else:  # balanced
            contested = contested_base
            uncontested = uncontested_base

        return {
            'short_game': short_game,
            'long_game': long_game,
            'contested': contested,
            'uncontested': uncontested
        }


class TeamMovementCoordinator:
    """Coordinates movement patterns between teammates"""

    def __init__(self, ground: Ground, team_tactics: Dict[str, Any]):
        self.ground = ground
        self.team_tactics = team_tactics
        self.team_side = None

    def _get_position_priority(self, position: Tuple[float, float], state: Dict[str, Any]) -> float:
        """Calculate priority score for a support position"""
        score = 0.0

    def get_weather_description(self) -> str:
        """Get human-readable weather description."""
        description = []

        # Temperature
        temp = self.conditions['temperature']
        if temp < 10:
            description.append("Cold")
        elif temp > 30:
            description.append("Hot")
        else:
            description.append("Mild")

        # Rain
        rain = self.conditions['rain']
        if rain > 0.7:
            description.append("Heavy Rain")
        elif rain > 0.3:
            description.append("Light Rain")

        # Wind
        wind = self.conditions['wind']
        if wind > 0.7:
            description.append("Strong Wind")
        elif wind > 0.3:
            description.append("Breezy")

        # Humidity
        humidity = self.conditions['humidity']
        if humidity > 0.7:
            description.append("Humid")

        return ", ".join(description)



