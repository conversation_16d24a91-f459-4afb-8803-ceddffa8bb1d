import random
from teams.models import Team, Player

class MatchEngine:
    def __init__(self, team1, team2, ruck1=None, ruck2=None):
        self.team1 = team1
        self.team2 = team2
        self.ruck1 = ruck1
        self.ruck2 = ruck2
        self.team1_score = 0
        self.team2_score = 0
        self.quarter_results = []

    def calculate_performance(self, player, context):
        context_stats = {
            'ruckwork': [
                player.physical_stats.height,
                player.physical_stats.strength,
                player.physical_stats.agility,
                player.ability_stats.mental,
                player.ability_stats.tactical,
                player.ability_stats.consistency,
                player.physical_stats.age,
                player.physical_stats.stamina
            ],
            'marking': [
                player.ability_stats.marking,
                player.physical_stats.height,
                player.physical_stats.strength,
                player.ability_stats.mental,
                player.ability_stats.tactical,
                player.ability_stats.consistency
            ],
            'general_play': [
                player.physical_stats.speed,
                player.physical_stats.agility,
                player.ability_stats.kicking,
                player.ability_stats.handball,
                player.ability_stats.tackling,
                player.ability_stats.mental,
                player.ability_stats.tactical,
                player.ability_stats.consistency
            ]
        }
        return sum(context_stats[context]) + random.randint(0, 20)

    def simulate_contest(self, player1, player2, context):
        performance1 = self.calculate_performance(player1, context)
        performance2 = self.calculate_performance(player2, context)
        return player1 if performance1 > performance2 else player2

    def simulate_throw_in(self):
        winner = self.simulate_contest(self.ruck1, self.ruck2, 'ruckwork')
        return self.team1 if winner == self.ruck1 else self.team2

    def simulate_center_bounce(self):
        winner = self.simulate_contest(self.ruck1, self.ruck2, 'ruckwork')
        return self.team1 if winner == self.ruck1 else self.team2

    def simulate_transition(self, team):
        ball_carrier = random.choice(team.players.all())
        performance = self.calculate_performance(ball_carrier, 'general_play')
        if performance > 150:
            forward_player = random.choice(team.players.all())
            forward_performance = self.calculate_performance(forward_player, 'general_play')
            if forward_performance > 150:
                return random.randint(1, 6)
        return 0

    def simulate_defensive_effort(self, team, context):
        defender = random.choice(team.players.all())
        defensive_performance = self.calculate_performance(defender, context)
        return defensive_performance

    def simulate_marking_contest(self, forward, defender):
        return self.simulate_contest(forward, defender, 'marking')

    def simulate_scoring_attempt(self, forward):
        return random.randint(1, 6) if forward.ability_stats.goal_kicking > random.randint(1, 10) else 1

    def simulate_play(self, attacking_team, defending_team, context):
        ball_carrier = random.choice(attacking_team.players.all())
        if self.calculate_performance(ball_carrier, 'general_play') > 100:
            forward = random.choice(attacking_team.players.all())
            defender = random.choice(defending_team.players.all())
            if self.simulate_marking_contest(forward, defender) == forward:
                return self.simulate_scoring_attempt(forward)
        return 0

    def simulate_quarter(self):
        quarter_score1 = 0
        quarter_score2 = 0
        for _ in range(6):
            winning_team = self.simulate_center_bounce()
            if winning_team == self.team1:
                quarter_score1 += self.simulate_play(self.team1, self.team2, 'general_play')
            else:
                quarter_score2 += self.simulate_play(self.team2, self.team1, 'general_play')
        return quarter_score1, quarter_score2

    def simulate_match(self):
        for _ in range(4):
            q_score1, q_score2 = self.simulate_quarter()
            self.team1_score += q_score1
            self.team2_score += q_score2
            self.quarter_results.append((q_score1, q_score2))
        return self.team1_score, self.team2_score, self.quarter_results
