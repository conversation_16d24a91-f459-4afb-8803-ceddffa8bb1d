import random
from typing import Any, Dict, Tuple, Optional

class ContestEngine:
    """Handles physical contests between players, maintaining exact compatibility with working AI version."""
    
    def __init__(self, ground: Ground):
        self.ground = ground
        self.stats_manager = StatsManager()

    def possession_contest(self, receiver, opponent):
        """Direct migration of original possession_contest from working AI."""
        print("Simulating possession contest")

        receiver_weights = {
            'marking': 1.5,
            'strength': 1.4,
            'consistency': 1.3,
            'agility': 1.2,
            'age': 1.1,
            'stamina': 1.0
        }

        opponent_weights = {
            'tackling': 1.5,
            'strength': 1.4,
            'consistency': 1.3,
            'speed': 1.2,
            'age': 1.1,
            'stamina': 1.0
        }
        print(f"Reciever {receiver} Opponent {opponent}")
        receiver_performance = self.calculate_performance(receiver, receiver_weights)
        opponent_performance = self.calculate_performance(opponent, opponent_weights)

        print(f"{receiver.name} performance: {receiver_performance:.2f}")
        print(f"{opponent.name} performance: {opponent_performance:.2f}")

        return receiver_performance, opponent_performance

    def calculate_performance(self, player, weights):
        """Direct migration of original calculate_performance from working AI."""
        performance = 0
        for attribute, weight in weights.items():
            performance += getattr(player.ability_stats, attribute, 0) * weight
            performance += getattr(player.physical_stats, attribute, 0) * weight

        random_factor = random.uniform(0.8, 1.2)  # Random factor between 0.8 and 1.2
        performance *= random_factor

        print(f"Calculated performance for {player.name}: {performance:.2f}")
        return performance

    def simulate_center_bounce(self, team1, team2, team1_players, team2_players):
        """Direct migration of original simulate_center_bounce from working AI."""
        print("Simulating center bounce")
        ruck1 = team1_players["Ruck"]
        ruck2 = team2_players["Ruck"]
        winner = self.ruck_contest(ruck1, ruck2)
        
        if winner == team1:
            print(f"{team1.name} wins the center bounce")
            self.stats_manager.record_stat(ruck1.name, 'hitouts')
            # Continue with clearance but store result for next contest
            winner_team, team_players = self.clearance(team1, team2, team1_players, team2_players, team1)
            return {
                "result": f"{winner_team} wins center bounce through {self.disposer}",
                "next_action": "handle_possession",
                "team": winner_team,
                "team_players": team_players
            }
        else:
            print(f"{team2.name} wins the center bounce")
            self.stats_manager.record_stat(ruck2.name, 'hitouts')
            # Continue with clearance but store result for next contest
            winner_team, team_players = self.clearance(team1, team2, team1_players, team2_players, team2)
            return {
                "result": f"{winner_team} wins center bounce through {self.disposer}",
                "next_action": "handle_possession",
                "team": winner_team,
                "team_players": team_players
            }

    def ruck_contest(self, ruck1: Any, ruck2: Any) -> Any:
        """Direct migration of original ruck_contest from working AI."""
        print(f"Ruck contest between {ruck1.name} and {ruck2.name}")
        ruck1_score = self.calculate_ruck_performance(ruck1)
        ruck2_score = self.calculate_ruck_performance(ruck2)
        print(f"{ruck1.name} score: {ruck1_score}, {ruck2.name} score: {ruck2_score}")
        return self.team1 if ruck1_score > ruck2_score else self.team2

    def calculate_ruck_performance(self, ruck: Any) -> float:
        """Direct migration of original calculate_ruck_performance from working AI."""
        height_weight = 1.5
        strength_weight = 1.4
        agility_weight = 1.3
        mental_weight = 1.2
        tactical_weight = 1.1
        consistency_weight = 1.0
        age_weight = 0.9
        stamina_weight = 0.8

        performance = (
            (ruck.physical_stats.height * height_weight) +
            (ruck.physical_stats.strength * strength_weight) +
            (ruck.physical_stats.agility * agility_weight) +
            (ruck.ability_stats.mental * mental_weight) +
            (ruck.ability_stats.tactical * tactical_weight) +
            (ruck.ability_stats.consistency * consistency_weight) +
            (ruck.physical_stats.age * age_weight) +
            (ruck.physical_stats.stamina * stamina_weight)
        )

        # Add a random element to simulate the unpredictability of sports
        random_factor = random.uniform(0.8, 1.2)  # Random factor between 0.8 and 1.2
        performance *= random_factor

        # Print the calculated performance for debugging
        print(f"Calculated performance for {ruck.name}: {performance:.2f}")

        return performance

    def clearance(self, team1: Any, team2: Any, team1_players: Dict[str, Any], team2_players: Dict[str, Any], ruck_winner: Any) -> Tuple[Any, Dict[str, Any]]:
        """Direct migration of original clearance from working AI."""
        print("Simulating clearance")
        midfielders_team1 = [team1_players["Rover"], team1_players["RuckRover"], team1_players["Centre"]]
        midfielders_team2 = [team2_players["Rover"], team2_players["RuckRover"], team2_players["Centre"]]

        team1_score = sum(self.calculate_midfielder_performance(player) for player in midfielders_team1)
        team2_score = sum(self.calculate_midfielder_performance(player) for player in midfielders_team2)

        # Add extra weight to the team whose ruck won the center bounce
        if ruck_winner == team1:
            team1_score *= 1.1
        else:
            team2_score *= 1.1

        print(f"Team 1 clearance score: {team1_score:.2f}, Team 2 clearance score: {team2_score:.2f}")

        if team1_score > team2_score:
            winner_team = team1
            winner_players = midfielders_team1
        else:
            winner_team = team2
            winner_players = midfielders_team2

        # Determine the player who gets the ball
        player_with_ball = random.choices(
            winner_players,
            weights=[self.calculate_midfielder_performance(player) for player in winner_players],
            k=1
        )[0]

        self.stats_manager.record_stat(player_with_ball.name, 'clearances')
        print(f"{player_with_ball.name} from {winner_team.name} gets the ball after clearance")

        if player_with_ball.team == team1:
            team_players = team1_players
        else:
            team_players = team2_players

        return winner_team, team_players

    def calculate_midfielder_performance(self, player: Any) -> float:
        """Direct migration of original calculate_midfielder_performance from working AI."""
        tactical_weight = 1.5
        mental_weight = 1.4
        strength_weight = 1.3
        versatility_weight = 1.2
        consistency_weight = 1.1
        age_weight = 1.0
        stamina_weight = 0.9

        performance = (
            (player.ability_stats.tactical * tactical_weight) +
            (player.ability_stats.mental * mental_weight) +
            (player.physical_stats.strength * strength_weight) +
            (player.ability_stats.versatility * versatility_weight) +
            (player.ability_stats.consistency * consistency_weight) +
            (player.physical_stats.age * age_weight) +
            (player.physical_stats.stamina * stamina_weight)
        )

        # Add a random element to simulate the unpredictability of sports
        random_factor = random.uniform(0.8, 1.2)  # Random factor between 0.8 and 1.2
        performance *= random_factor

        # Print the calculated performance for debugging
        print(f"Calculated performance for {player.name}: {performance:.2f}")

        return performance

    def get_ball_position(self) -> str:
        """Direct migration of original get_ball_position from working AI."""
        if not self.ball_position:
            return "Centre"
            
        x, y = self.ball_position
        
        # Define zone boundaries
        zones = {
            "LF": (x >= 120 and y < 40),
            "FF": (x >= 120 and y >= 40 and y < 80),
            "RF": (x >= 120 and y >= 80),
            "LHF": (x >= 80 and x < 120 and y < 40),
            "CHF": (x >= 80 and x < 120 and y >= 40 and y < 80),
            "RHF": (x >= 80 and x < 120 and y >= 80),
            "LWing": (x >= 40 and x < 80 and y < 40),
            "Centre": (x >= 40 and x < 80 and y >= 40 and y < 80),
            "RWing": (x >= 40 and x < 80 and y >= 80),
            "LHB": (x >= 0 and x < 40 and y < 40),
            "CHB": (x >= 0 and x < 40 and y >= 40 and y < 80),
            "RHB": (x >= 0 and x < 40 and y >= 80),
            "LBP": (x < 0 and y < 40),
            "FB": (x < 0 and y >= 40 and y < 80),
            "RBP": (x < 0 and y >= 80)
        }
        
        # Find matching zone
        for zone_name, condition in zones.items():
            if condition:
                return zone_name
                
        return "Centre"  # Default to centre if no match found 

    def update_game_state(self, team_in_possession: Any, goal: Optional[bool] = None) -> None:
        """Direct migration of original update_game_state from working AI."""
        # Get the play state based on current game situation
        play_state = self.get_play_state(team_in_possession, goal)
        
        # Calculate field zones based on ball position and play state
        field_zones = self.calculate_field_zones(self.ball_position, play_state)
        
        # Update positions for all players
        all_players = list(self.team1_players.values()) + list(self.team2_players.values())
        for player in all_players:
            is_possession_team = (
                (team_in_possession == self.team1 and player.team == self.team1) or
                (team_in_possession == self.team2 and player.team == self.team2)
            )
            self.update_individual_player_position(
                player,
                is_possession_team,
                play_state,
                field_zones,
                self.ball_position
            ) 