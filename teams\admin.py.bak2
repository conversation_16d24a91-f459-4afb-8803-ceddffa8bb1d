from django.contrib import admin
from django.urls import path
from django.http import JsonResponse, HttpResponseRedirect
from django.shortcuts import render
from .models import Team, Player
from .forms import MatchSimulationForm
from teams.match_engine.match_engine import MatchEngine

@admin.register(Team)
class TeamAdmin(admin.ModelAdmin):
    change_list_template = "admin/teams/change_list.html"

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('simulate_match/', self.admin_site.admin_view(self.simulate_match), name='simulate_match'),
            path('ajax/load-players/', self.admin_site.admin_view(self.load_players), name='ajax_load_players'),
        ]
        return custom_urls + urls

    def load_players(self, request):
        team_id = request.GET.get('team_id')
        players = Player.objects.filter(team_id=team_id).values('id', 'name')
        return JsonResponse(list(players), safe=False)

    def simulate_match(self, request):
        if request.method == 'POST':
            form = MatchSimulationForm(request.POST)
            if form.is_valid():
                team1 = form.cleaned_data['team1']
                team2 = form.cleaned_data['team2']
                team1_ruck = form.cleaned_data['team1_ruck']
                team2_ruck = form.cleaned_data['team2_ruck']
                
                if team1_ruck.team != team1 or team2_ruck.team != team2:
                    self.message_user(request, "Selected players do not belong to the chosen teams.", level='error')
                    return HttpResponseRedirect(request.path_info)

                engine = MatchEngine(team1, team2, team1_ruck, team2_ruck)
                team1_score, team2_score, quarter_results = engine.simulate_match()

                self.message_user(request, f"Final Score - {team1.name}: {team1_score}, {team2.name}: {team2_score}")
                self.message_user(request, f"Quarter Results: {quarter_results}")
                return HttpResponseRedirect(request.path_info)
        else:
            form = MatchSimulationForm()

        context = dict(
            self.admin_site.each_context(request),
            form=form,
        )
        return render(request, "admin/simulate_match.html", context)
