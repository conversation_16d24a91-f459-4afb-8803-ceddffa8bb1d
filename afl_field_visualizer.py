import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np
import json
import sys
import os
import math

class AFLFieldVisualizer:
    def __init__(self):
        # Standard AFL field dimensions (in meters)
        self.field_length = 160  # Length in meters
        self.field_width = 130   # Width in meters
        
        # Create figure and axis
        self.fig, self.ax = plt.subplots(figsize=(12, 10))
        self.fig.subplots_adjust(left=0.05, right=0.95, top=0.95, bottom=0.05)
        
        # Team colors
        self.team_colors = {
            'home': 'blue',
            'away': 'red',
            'ball': 'yellow'
        }
        
        # Player positions
        self.players = []
        self.ball_position = None
        
    def draw_field(self):
        """Draw the AFL field with appropriate markings"""
        # Field dimensions
        length = self.field_length
        width = self.field_width
        
        # Create the oval field
        # The AFL field is an oval, approximated by an ellipse
        field = patches.Ellipse((length/2, width/2), length, width, 
                               edgecolor='white', facecolor='#4a8f29', alpha=0.8)
        self.ax.add_patch(field)
        
        # Draw center circle (radius 3m)
        center_circle = patches.Circle((length/2, width/2), 3, 
                                     edgecolor='white', facecolor='none', linewidth=2)
        self.ax.add_patch(center_circle)
        
        # Draw center square (50m x 50m)
        center_square = patches.Rectangle((length/2 - 25, width/2 - 25), 50, 50, 
                                        edgecolor='white', facecolor='none', linewidth=2)
        self.ax.add_patch(center_square)
        
        # Draw 50m arcs
        # Left 50m arc
        left_50m_arc = patches.Arc((0, width/2), 100, 100, 
                                 theta1=270, theta2=90, 
                                 edgecolor='white', linewidth=2)
        self.ax.add_patch(left_50m_arc)
        
        # Right 50m arc
        right_50m_arc = patches.Arc((length, width/2), 100, 100, 
                                  theta1=90, theta2=270, 
                                  edgecolor='white', linewidth=2)
        self.ax.add_patch(right_50m_arc)
        
        # Draw goal posts
        # Left goal posts
        self.ax.plot([0, 0], [width/2 - 6.4, width/2 - 3.2], 'white', linewidth=4)  # Left behind post
        self.ax.plot([0, 0], [width/2 - 3.2, width/2 + 3.2], 'white', linewidth=8)  # Left goal post
        self.ax.plot([0, 0], [width/2 + 3.2, width/2 + 6.4], 'white', linewidth=4)  # Left behind post
        
        # Right goal posts
        self.ax.plot([length, length], [width/2 - 6.4, width/2 - 3.2], 'white', linewidth=4)  # Right behind post
        self.ax.plot([length, length], [width/2 - 3.2, width/2 + 3.2], 'white', linewidth=8)  # Right goal post
        self.ax.plot([length, length], [width/2 + 3.2, width/2 + 6.4], 'white', linewidth=4)  # Right behind post
        
        # Set axis limits
        self.ax.set_xlim(-10, length + 10)
        self.ax.set_ylim(-10, width + 10)
        
        # Remove axis ticks and labels
        self.ax.set_xticks([])
        self.ax.set_yticks([])
        
        # Add field markings
        self.ax.text(length/2, -5, 'CENTER', ha='center', color='white', fontsize=10)
        self.ax.text(0, -5, 'DEFENSIVE GOAL (HOME)', ha='center', color='white', fontsize=10)
        self.ax.text(length, -5, 'FORWARD GOAL (HOME)', ha='center', color='white', fontsize=10)
        
        # Add grid for reference (optional)
        self.ax.grid(False)
        
    def add_player(self, name, position, team):
        """Add a player to the visualization"""
        self.players.append({
            'name': name,
            'position': position,
            'team': team
        })
        
    def set_ball_position(self, position):
        """Set the ball position"""
        self.ball_position = position
        
    def draw_players(self):
        """Draw all players on the field"""
        for player in self.players:
            x, y = player['position']['x'], player['position']['y']
            
            # Scale coordinates to field dimensions
            x_scaled = x * self.field_length / 80  # Assuming x is in 0-80 range
            y_scaled = y * self.field_width / 65   # Assuming y is in 0-65 range
            
            # Draw player
            color = self.team_colors.get(player['team'], 'gray')
            self.ax.plot(x_scaled, y_scaled, 'o', markersize=10, color=color)
            
            # Add player name and position
            if '-' in player['name']:
                name, position = player['name'].split('-', 1)
                label = f"{name}\n{position}"
            else:
                label = player['name']
                
            self.ax.text(x_scaled, y_scaled + 3, label, ha='center', va='bottom', 
                       color='white', fontsize=8, bbox=dict(facecolor=color, alpha=0.7))
        
        # Draw ball if position is set
        if self.ball_position:
            x, y = self.ball_position['x'], self.ball_position['y']
            
            # Scale coordinates to field dimensions
            x_scaled = x * self.field_length / 80
            y_scaled = y * self.field_width / 65
            
            # Draw ball
            self.ax.plot(x_scaled, y_scaled, 'o', markersize=8, color=self.team_colors['ball'])
            self.ax.text(x_scaled, y_scaled + 3, 'BALL', ha='center', va='bottom', 
                       color='white', fontsize=8, bbox=dict(facecolor='black', alpha=0.7))
    
    def load_players_from_text(self, text):
        """Load player positions from text format"""
        lines = text.strip().split('\n')
        
        for line in lines:
            if not line.strip():
                continue
                
            if 'player_name' in line and 'position' in line:
                # Parse player data
                parts = line.split('\n')
                player_data = {}
                
                for part in parts:
                    if 'player_name' in part:
                        player_data['name'] = part.split(':"')[1].split('"')[0]
                    elif 'position' in part:
                        pos_part = part.split(':{')[1].split('}')[0]
                        x = float(pos_part.split('x:')[1].split(',')[0].strip())
                        y = float(pos_part.split('y:')[1].strip())
                        player_data['position'] = {'x': x, 'y': y}
                    elif 'team' in part:
                        player_data['team'] = part.split(':"')[1].split('"')[0]
                
                if 'name' in player_data and 'position' in player_data and 'team' in player_data:
                    if player_data['name'] == 'ball':
                        self.set_ball_position(player_data['position'])
                    else:
                        self.add_player(player_data['name'], player_data['position'], player_data['team'])
    
    def show(self):
        """Show the visualization"""
        self.draw_field()
        self.draw_players()
        plt.title('AFL Field Visualization - Out on Full Situation')
        plt.show()
    
    def save(self, filename):
        """Save the visualization to a file"""
        self.draw_field()
        self.draw_players()
        plt.title('AFL Field Visualization - Out on Full Situation')
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"Visualization saved to {filename}")

def parse_player_data(text):
    """Parse player data from the provided text format"""
    lines = text.strip().split('\n')
    players = []
    ball_position = None
    
    current_player = {}
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
            
        if line.startswith('player_name:'):
            # Start of a new player
            if current_player and 'name' in current_player and 'position' in current_player and 'team' in current_player:
                if current_player['name'] == 'ball':
                    ball_position = current_player['position']
                else:
                    players.append(current_player)
            
            current_player = {}
            current_player['name'] = line.split(':"')[1].split('"')[0]
        elif line.startswith('position:{'):
            # Position data
            pos_part = line.split(':{')[1].split('}')[0]
            x = float(pos_part.split('x:')[1].split(',')[0].strip())
            y = float(pos_part.split('y:')[1].strip())
            current_player['position'] = {'x': x, 'y': y}
        elif line.startswith('team:'):
            # Team data
            if '"' in line:
                current_player['team'] = line.split(':"')[1].split('"')[0]
            else:
                current_player['team'] = line.split(':')[1].strip()
    
    # Add the last player if there is one
    if current_player and 'name' in current_player and 'position' in current_player and 'team' in current_player:
        if current_player['name'] == 'ball':
            ball_position = current_player['position']
        else:
            players.append(current_player)
    
    return players, ball_position

if __name__ == "__main__":
    # Check if input file is provided
    if len(sys.argv) > 1:
        input_file = sys.argv[1]
        with open(input_file, 'r') as f:
            text = f.read()
    else:
        # Use sample data if no input file
        text = """
        player_name:"Andrew Tucker-LHB"
        position:{x: 1, y: 29}
        team:"home"

        player_name:"Terry Potter-CHF"
        position:{x: 29, y: 14}
        team:"home"

        player_name:"ball"
        position:{x: 0.5577563384241166, y: 33.906313338830266}
        team:
        """
    
    # Create visualizer
    visualizer = AFLFieldVisualizer()
    
    # Parse player data
    players, ball_position = parse_player_data(text)
    
    # Add players to visualizer
    for player in players:
        visualizer.add_player(player['name'], player['position'], player['team'])
    
    # Set ball position
    if ball_position:
        visualizer.set_ball_position(ball_position)
    
    # Show visualization
    visualizer.show()
    
    # Save visualization if output file is provided
    if len(sys.argv) > 2:
        output_file = sys.argv[2]
        visualizer.save(output_file)
