# ladder/utils.py
from .models import <PERSON><PERSON>

def record_match_result(team1, team2, team1_score, team2_score):
    team1_ladder = Ladder.objects.get(team=team1)
    team2_ladder = Ladder.objects.get(team=team2)
    print(f"Team 1: {team1}, Score: {team1_score}")
    print(f"Team 2: {team2}, Score: {team2_score}")

    if team1_score > team2_score:
        team1_ladder.update_ladder("win", team1_score, team2_score)
        team2_ladder.update_ladder("loss", team2_score, team1_score)
    elif team1_score < team2_score:
        team2_ladder.update_ladder("win", team2_score, team1_score)
        team1_ladder.update_ladder("loss", team1_score, team2_score)
    else:
        team1_ladder.update_ladder("draw", team1_score, team2_score)
        team2_ladder.update_ladder("draw", team2_score, team1_score)