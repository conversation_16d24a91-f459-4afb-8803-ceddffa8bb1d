document.addEventListener("DOMContentLoaded", function () {
    const sidebarLinks = document.querySelectorAll(".sidebar ul li a");
    const contentSection = document.querySelector(".main-content");

    // Function to handle fetching and updating content
    function fetchAndLoadContent(url) {
        fetch(url)
            .then(response => response.text())
            .then(data => {
                // Replace the content in the main content section
                contentSection.innerHTML = data;

                // Execute inline scripts in the loaded content
                executeInlineScripts(contentSection);

                // Reattach handlers for any new dynamic links
                attachDynamicLinkHandlers();
            })
            .catch(error => console.error("Error loading content:", error));
    }

    // Add click listeners for sidebar links
    sidebarLinks.forEach(link => {
        link.addEventListener("click", function (event) {
            event.preventDefault();

            // Fetch the content from the link's href attribute
            const url = this.getAttribute("href");

            // Fetch and load the content
            fetchAndLoadContent(url);
        });
    });

    // Function to handle dynamic links in loaded content
    function attachDynamicLinkHandlers() {
        const dynamicLinks = contentSection.querySelectorAll(".dynamic-link");

        dynamicLinks.forEach(link => {
            link.addEventListener("click", function (event) {
                const url = this.getAttribute("href");
                if (url) {
                    event.preventDefault();

                    // Fetch and load content for dynamic links
                    fetchAndLoadContent(url);
                }
            });
        });
    }

    // Function to execute inline scripts
    function executeInlineScripts(container) {
        const scripts = container.querySelectorAll("script");

        scripts.forEach(script => {
            const newScript = document.createElement("script");
            newScript.type = script.type || "text/javascript";

            if (script.src) {
                // For external scripts
                newScript.src = script.src;
                newScript.onload = () => console.log(`External script loaded: ${script.src}`);
            } else {
                // For inline scripts
                newScript.textContent = script.textContent;
                console.log("Inline script executed.");
            }

            document.body.appendChild(newScript);
            script.remove(); // Remove original script to avoid duplication
        });
    }

    // Initial call to attach handlers for any existing dynamic links
    attachDynamicLinkHandlers();
});
